/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.fund.dao.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * fund_account
 *
 * <AUTHOR>
 * @version $Id: FundAccount.java, v 0.1 2023-10-25 15:32:09 Exp $
 */
@Data
public class FundAccount {
    /** serialVersionUID */
    private static final long serialVersionUID = 174661422251487680L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 资金账户编号
     */
    private String accountNo;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 可用余额，单位：分
     */
    private Long availableAmount;

    /**
     * 冻结金额，单位：分
     */
    private Long frozenAmount;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 结算方ID
     */
    private Long settleId;

    /**
     * 状态：1正常 2冻结
     */
    private Integer state;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
    /**
     * 商户类型 1商家 2代理商
     */
    private Integer merchantType;

    // ==========================
    /**
     * 结算方ID
     */
    private List<Long> mchIds;
}