<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chargebolt</groupId>
    <artifactId>ezreal</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <artifactId>advanced</artifactId>
  <name>advanced</name>
  <version>1.0-SNAPSHOT</version>
  <packaging>jar</packaging>
  <url>http://maven.apache.org</url>
  <dependencies>
    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>basic</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>agent</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>com.chargebolt</groupId>-->
<!--      <artifactId>tenant</artifactId>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>bill</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>account</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>data-statistics</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>theseus</artifactId>
    </dependency>

    <!-- 应该在旗舰版中引入 -->


    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito2</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-testng</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-client</artifactId>
    </dependency>
      <dependency>
          <groupId>com.belerweb</groupId>
          <artifactId>pinyin4j</artifactId>
          <version>2.5.1</version>
          <scope>test</scope>
      </dependency>
  </dependencies>

  <build>
    <finalName>ezreal</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>3.4</version>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <!-- 输出路径 -->
          <!-- <outputDirectory>../target/</outputDirectory> -->
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>build-info</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
          <groupId>com.google.cloud.tools</groupId>
          <artifactId>jib-maven-plugin</artifactId>
          <version>3.4.1</version>
          <configuration>
              <containerizingMode>packaged</containerizingMode>
              <container>
                  <entrypoint>/opt/jboss/container/java/run/run-java.sh</entrypoint>
              </container>
              <extraDirectories>
                  <paths>
                      <path>
                          <from>target/</from>
                          <includes>${project.build.finalName}-exec.jar</includes>
                          <into>/deployments</into>
                      </path>
                  </paths>
              </extraDirectories>
              <pluginExtensions>
                  <pluginExtension>
                      <implementation>
                          com.google.cloud.tools.jib.maven.extension.layerfilter.JibLayerFilterExtension</implementation>
                      <configuration
                              implementation="com.google.cloud.tools.jib.maven.extension.layerfilter.Configuration">
                          <filters>
                              <filter>
                                  <!-- exclude all jib layers, which is basically anything in /app -->
                                  <glob>/app/**</glob>
                              </filter>
                              <filter>
                                  <!-- this is our fat jar, this should be kept by adding it into its own layer -->
                                  <glob>/deployments/${project.build.finalName}-exec.jar</glob>
                                  <toLayer>jib-custom-fatJar</toLayer>
                              </filter>
                          </filters>
                      </configuration>
                  </pluginExtension>
              </pluginExtensions>
              <from>
                  <image>
                      quay.xiaodiankeji.net/openjdk/openjdk-8-runtime-skywalking@sha256:4115001cdff162df855650863d5c23877e3e01032ba538dc34e960b0fadd580e</image>
                  <platforms>
                      <platform>
                          <architecture>arm64</architecture>
                          <os>linux</os>
                      </platform>
                      <platform>
                          <architecture>amd64</architecture>
                          <os>linux</os>
                      </platform>
                  </platforms>
              </from>
              <to>
                  <image>quay.xiaodiankeji.net/dian-dev/${project.build.finalName}</image>
                  <auth>
                      <username>${env.REGISTRY_USR}</username>
                      <password>${env.REGISTRY_PSW}</password>
                  </auth>
              </to>
          </configuration>
          <dependencies>
              <dependency>
                  <groupId>com.google.cloud.tools</groupId>
                  <artifactId>jib-layer-filter-extension-maven</artifactId>
                  <version>0.3.0</version>
              </dependency>
          </dependencies>
      </plugin>
      <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>2.43.0</version>
          <configuration>
              <ratchetFrom>origin/main</ratchetFrom>
              <formats>
                  <format>
                      <includes>
                          <include>*.md</include>
                          <include>.gitignore</include>
                      </includes>
                      <trimTrailingWhitespace></trimTrailingWhitespace>
                      <endWithNewline></endWithNewline>
                      <indent>
                          <tabs>true</tabs>
                          <spacesPerTab>4</spacesPerTab>
                      </indent>
                  </format>
              </formats>
              <java>
                  <includes>
                      <include>src/main/java/**/*.java</include>
                      <include>src/test/java/**/*.java</include>
                  </includes>
                  <googleJavaFormat>
                      <style>GOOGLE</style>
                      <reflowLongStrings>true</reflowLongStrings>
                  </googleJavaFormat>
                  <importOrder>
                      <wildcardsLast>false</wildcardsLast>
                      <order>java|javax|jakarta,org.springframework,org,com,so.dian,,\#so.dian,\#</order>
                      <semanticSort>false</semanticSort>
                  </importOrder>
                  <removeUnusedImports></removeUnusedImports>
                  <formatAnnotations></formatAnnotations>
              </java>
              <pom>
                  <includes>
                      <include>pom.xml</include>
                  </includes>
                  <sortPom></sortPom>
              </pom>
          </configuration>
      </plugin>
      <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>3.21.2</version>
      </plugin>
    </plugins>
  </build>
</project>
