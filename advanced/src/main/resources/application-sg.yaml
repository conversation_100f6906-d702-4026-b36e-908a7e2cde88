# 跨域域名配置
cors:
  domain-list:
    - '*.chargebolt.com'

# 设备扫码，分割地址pattern，新环境发布，需要根据域名配置
device-scan:
  url-check-pattern: '^https?://[a-zA-Z]{2}\d{2}\.chargebolt.com/.+$'
  url-split-pattern: '^https?://(lhc|venus|b|[a-zA-Z]{2}\d{2}).*/(lhc|Ihc)/'

# 跟随新增环境部署调整
ezreal:
  # 交付版本1.基础版本 2.高级版本
  app-version: 2
  profile: 'sg'
  redis_key_prefix: 'chargebolt'
  shop-export:
    enable: true
    exclude-shop-id:
      - 1

demeter:
  location: HK
  maintenance:
    deviceInfo:
      supportWifi: 4
      supportBle: ''
kafka:
  consume:
    enableAutoCommit: true
    sessionTimeout: 60000
    autoCommitInterval: 1000
    autoOffsetReset: latest
    concurrency: 1
    pbTakeoutPutinUri: kafka.middle-sg.svc.cluster.local:9092
    pbTakeoutPutinTopic: chargebolt_repair_biz_real
    pbTakeoutPutinGroup: chargebolt_oss_apollo
    deviceOnlineUri: kafka.middle-sg.svc.cluster.local:9092
    deviceOnlineTopic: adela_device_sync_oversea
    deviceOnlineGroup: ezreal_device_apollo
eros:
  location: SG
  language: en-gb
  mobilePrefix: 86
  mobileReg: ^1[\d]{10}$
  timezone: Asia/Shanghai

datasource:
  rds:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************
    user-name: chargebolt
    password: 5X0nPTEAQWSkRbJ
    # 下面为连接池的补充设置，应用到上面所有数据源中
    # 初始化大小，最小，最大
    initial-size: 5
    min-idle: 1
    max-active: 20
    # 配置获取连接等待超时的时间
    max-wait: 10000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    time-between-eviction-runs-millis: 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒
    min-evictable-idle-time-millis: 300000
    validation-query: SELECT 'x'
    validation-query-timeout: 30
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    # 打开PSCache，并且指定每个连接上PSCache的大小
    pool-prepared-statements: true
    max-pool-prepared-statement-per-connection-size: 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,slf4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

spring:
  main:
    allow-bean-definition-overriding: true
  rocketmq:
    name-server: rocketmq-nameserver.middle-sg.svc.cluster.local:9876
    producer-group-name: pg_chargebolt_ezreal_producer
    account:
      enable: true
      topic: chargebolt-account_${ezreal.profile}
      consumer-group: cg_chargebolt_account_consumer
      # 开通分成
      tag: OPEN_PERCENTAGE
    order-pay:
      enable: true
      topic: chargebolt-topic-kronos-order-biz_${ezreal.profile}
      consumer-group: cg_chargebolt_order_pay_consumer
      # 订单支付、退款
      tag: orderStatusChange_${ezreal.profile}
    # 自发自消费
    self-producer:
      topic: chargebolt-ezreal-self_${ezreal.profile}
      # 开通分成
      account:
        consumer-group: cg_chargebolt_account_consumer_${ezreal.profile}
        tag: OPEN_PERCENTAGE_${ezreal.profile}
      # 数据权限更新
      data-authority:
        consumer-group: cg_chargebolt_data_authority_consumer_${ezreal.profile}
        tag: DATA_AUTHORITY_${ezreal.profile}
      # 租户绑定
      agent-tenant:
        consumer-group: cg_chargebolt_agent_tenant_consumer_${ezreal.profile}
        tag: AGENT_TENANT_${ezreal.profile}
      # 角色同步
      user-role-sync:
        consumer-group: cg_chargebolt_user_role_consumer_${ezreal.profile}
        tag: USER_ROLE_SYNC_${ezreal.profile}
      # FAQ删除国际化清理
      faq-delete:
        consumer-group: cg_chargebolt_faq_delete_consumer_${ezreal.profile}
        tag: FAQ_DELETE_${ezreal.profile}
  redis:
    host: r-t4njbtgldcuwq0darg.redis.singapore.rds.aliyuncs.com
    password: Kongge789
    pool:
      max-active: 30
      max-idle: 5
      min-idle: 1
      max-wait: 30000
    port: 6379
    timeout: 30000
  datasource:
    druid:
      filter:
        slf4j:
          enabled: true
      filters: stat,wall,slf4j
      logSlowSql: false
      stat-view-servlet:
        enabled: true
        login-password: Druid7+
        login-username: ares
        url-pattern: /druid/*
      web-stat-filter:
        enabled: true
        profile-enable: true
        url-pattern: /druid/*
    filters: stat,wall,slf4j
    logSlowSql: false
  mail:
    host: smtp.mxhichina.com
    username: <EMAIL>
    password: Biz123456
    port: 80
    default-encoding: UTF-8
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: false
            required: false
hermes:
  depositAmount: 99
  taxPercent: 0
  currency: VND
  currency-symbol: VND

hera:
  refundSwitch: true
alilog:
  accessId: 'LTAI4GKf21z982dB2qcVxrbJ'
  accessKey: '******************************'
  host: 'http://oss-ap-southeast-1.aliyuncs.com%'
  project: ''
  logStore: ''
  envForUtil: ''
envUtil:
  evn: ''

biz:
  account:
    totalAmount: 6000
  checkAsset: false
  checkDeviceOwnerWhenInstall: false
  checkDeviceOwnerWhenRecycle: false


#OSS配置
oss:
  accessKeyId: 'LTAI4FxdmcKWvmRsAYtG11sp'
  accessKeySecret: '******************************'
  endpoint: 'oss-ap-southeast-1.aliyuncs.com'
  bucketName: 'chargebolt-sg-oss'
  file-domain-url: 'https://img-sg.chargebolt.com/'

remote:
  url:
    apollo: adela:8080
    hermes: chargebolt-kronos:8080
    ares: chargebolt-kronos:8080
    athena: chargebolt-kronos:8080
    hades:
    hera: chargebolt-hera:8080
    lvy: chargebolt-kronos:8080
    pontus: chargebolt-kronos:8080
    poseidon:
    prometheus: chargebolt-kronos:8080
    theseus: chargebolt-kronos:8080
    pheidi: pheidi:8080


swagger:
  enable: false

# 短信是否真实发送
sms:
  real-send: true
  general-code: 'qazw'

# 客服电话
contact:
  contactPhone: *********

dian:
  services:
    bleService: adela
xdcloud.ble:
  serviceName: adela
  clientClassName: so.dian.xdcloud.blesdk.client.BleServerFeignDirect

oneSignal:
  externalId:
    prefix: sg

message:
  client:
    # 可选配置（已提供默认值）
    connect-timeout: 3000        # 连接超时时间(ms)
    read-timeout: 5000          # 读取超时时间(ms)
    max-retries: 3              # 最大重试次数
    retry-interval: 1000        # 重试间隔(ms)
    log-enabled: true           # 是否启用日志
    app-name: ezreal            # 应用名称
    app-key: ezreal-1745835226842 # 应用密钥
    # 线程池配置（可选）
    thread-pool:
      core-pool-size: 5         # 核心线程数
      max-pool-size: 10         # 最大线程数
      queue-capacity: 100       # 队列容量
      thread-name-prefix: message-client-  # 线程名前缀