<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.demeter.dao.rds.DeviceTypeMapper">

  <resultMap id="BaseResultMap" type="so.dian.demeter.pojo.entity.DeviceTypeDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
    <result column="sub_device_type" jdbcType="VARCHAR" property="subDeviceType"/>
    <result column="apollo_device_type" jdbcType="VARCHAR" property="apolloDeviceType"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="deleted" jdbcType="TINYINT" property="deleted"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,device_type,sub_device_type,apollo_device_type,
    <include refid="Common.BASE_ROW" />
  </sql>

  <select id="getAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cb_demeter.device_type
    where deleted = 0
  </select>

  <select id="getByApolloDeviceType" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cb_demeter.device_type
    where deleted = 0
    and apollo_device_type = #{apolloDeviceType}
  </select>

  <!--
  插入：批量插入设备类型配置
  频次：10次/日（数据会缓存）
  数据量：100+
  -->
  <insert id="batchInsert" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
    insert into cb_demeter.device_type
    (device_type,sub_device_type,apollo_device_type,create_time,update_time,deleted)
    values
    <foreach collection="list" index="index" item="deviceType" separator=",">
      (#{deviceType.deviceType}, #{deviceType.subDeviceType}, #{deviceType.apolloDeviceType},
      #{deviceType.createTime}, #{deviceType.updateTime}, #{deviceType.deleted})
    </foreach>
  </insert>

  <update id="deleteByIdList" parameterType="java.util.List">
    update cb_demeter.device_type
    set deleted = 1,update_time=#{updateTime}
    where id in 
    <foreach collection="idList" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
  </update>
</mapper>
