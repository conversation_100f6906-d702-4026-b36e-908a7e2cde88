<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.aeacus.dao.MchUserMappingDAO">
  <resultMap id="BaseResultMap" type="com.chargebolt.aeacus.entity.dataobject.MchUserMapping">
    <id column="id" property="id" />
    <!-- 用户ID -->
    <result column="user_id" property="userId" />
    <!-- 商户ID -->
    <result column="mch_id" property="mchId" />
    <!-- 逻辑删除：0 未删除，1 已删除 -->
    <result column="deleted" property="deleted" />
    <!-- 创建时间 -->
    <result column="gmt_create" property="gmtCreate" />
    <!-- 更新时间 -->
    <result column="gmt_update" property="gmtUpdate" />
  </resultMap>
  
  <!-- ============ WHERE条件组装 ============ -->
  <sql id="Example_Where_Clause">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="userId != null">
        AND user_id = #{userId}
      </if>
      <if test="mchId != null">
        AND mch_id = #{mchId}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted}
      </if>
      <if test="gmtCreate != null">
        AND gmt_create = #{gmtCreate}
      </if>
      <if test="gmtUpdate != null">
        AND gmt_update = #{gmtUpdate}
      </if>
    </where>
  </sql>
  
  <!-- ============ 表基础字段 ============ -->
  <sql id="Base_Column_List">
    id, user_id, mch_id, deleted, gmt_create, gmt_update
  </sql>
  
  <!-- ============ listRecord 列表查询 ============ -->
  <select id="listRecord" parameterType="com.chargebolt.aeacus.entity.dataobject.MchUserMapping" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_aeacus.mch_user_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <!-- ============ getRecord 单条查询 ============ -->
  <select id="getRecord" parameterType="com.chargebolt.aeacus.entity.dataobject.MchUserMapping" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM cb_aeacus.mch_user_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- 不要删除、不要删除、不要删除 -->
     LIMIT 2
  </select>
  
  <!-- ============ saveRecord 单条插入 ============ -->
  <insert id="saveRecord" parameterType="com.chargebolt.aeacus.entity.dataobject.MchUserMapping">
    INSERT INTO cb_aeacus.mch_user_mapping (user_id, mch_id, deleted, gmt_create, gmt_update)
    VALUES (#{userId}, #{mchId}, #{deleted}, #{gmtCreate}, #{gmtUpdate})
  </insert>
  
  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeRecord" parameterType="com.chargebolt.aeacus.entity.dataobject.MchUserMapping">
    UPDATE cb_aeacus.mch_user_mapping SET deleted = 1 WHERE id = #{id}
  </update>
  
  <!-- ============ updateRecord 更新by 主键 ============ -->
  <update id="updateRecord" parameterType="com.chargebolt.aeacus.entity.dataobject.MchUserMapping">
    UPDATE cb_aeacus.mch_user_mapping
    <set>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="mchId != null">
        mch_id = #{mchId},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate},
      </if>
    </set>
    WHERE id = #{id}
  </update>
</mapper>