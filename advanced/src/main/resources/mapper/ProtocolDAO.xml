<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.tenant.dao.ProtocolDAO">
  <resultMap id="BaseResultMap" type="com.chargebolt.tenant.dao.model.Protocol">
    <id column="id" property="id" />
    <!-- 协议标题 -->
    <result column="title" property="title" />
    <!-- 协议code，对应有协议类型 -->
    <result column="code" property="code" />
    <!-- 适配端 1 H5 -->
    <result column="client_type" property="clientType" />
    <!-- 语言，BCP 47值 -->
    <result column="lang" property="lang" />
    <!-- 租户ID -->
    <result column="tenant_id" property="tenantId" />
    <!-- 逻辑删除：0 未删除，1 已删除 -->
    <result column="deleted" property="deleted" />
    <!-- 创建时间 -->
    <result column="gmt_create" property="gmtCreate" />
    <!-- 更新时间 -->
    <result column="gmt_update" property="gmtUpdate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.chargebolt.tenant.dao.model.Protocol">
    <result column="content" property="content" />
  </resultMap>
  
  <!-- ============ WHERE条件组装 ============ -->
  <sql id="Example_Where_Clause">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="title != null">
        AND title = #{title}
      </if>
      <if test="code != null">
        AND code = #{code}
      </if>
      <if test="clientType != null">
        AND client_type = #{clientType}
      </if>
      <if test="lang != null">
        AND lang = #{lang}
      </if>
      <if test="tenantId != null">
        AND tenant_id = #{tenantId}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted}
      </if>
      <if test="gmtCreate != null">
        AND gmt_create = #{gmtCreate}
      </if>
      <if test="gmtUpdate != null">
        AND gmt_update = #{gmtUpdate}
      </if>
      <if test="content != null">
        AND content = #{content}
      </if>

      <if test="codes != null and codes.size() > 0">
        and code in
        <foreach collection="codes" separator="," open="(" close=")" item="item">
          #{item}
        </foreach>
      </if>

    </where>
  </sql>
  
  <!-- ============ 表基础字段 ============ -->
  <sql id="Base_Column_List">
    id, title, code, client_type, lang, tenant_id, deleted, gmt_create, gmt_update
  </sql>
  
  <!-- ============ listRecord 列表查询 ============ -->
  <select id="listRecord" parameterType="com.chargebolt.tenant.dao.model.Protocol" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_talos.protocol
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <!-- ============ getRecord 单条查询 ============ -->
  <select id="getRecord" parameterType="com.chargebolt.tenant.dao.model.Protocol" resultMap="ResultMapWithBLOBs">
    SELECT 
    <include refid="Base_Column_List" />,content
    FROM cb_talos.protocol
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- 不要删除、不要删除、不要删除 -->
     LIMIT 2
  </select>
  
  <!-- ============ saveRecord 单条插入 ============ -->
  <insert id="saveRecord" parameterType="com.chargebolt.tenant.dao.model.Protocol">
    INSERT INTO cb_talos.protocol (title, code, client_type, lang, tenant_id, deleted, gmt_create, 
      gmt_update, content)
    VALUES (#{title}, #{code}, #{clientType}, #{lang}, #{tenantId}, #{deleted}, #{gmtCreate}, 
      #{gmtUpdate}, #{content})
  </insert>
  
  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeRecord" parameterType="com.chargebolt.tenant.dao.model.Protocol">
    UPDATE cb_talos.protocol SET deleted = 1 WHERE id = #{id}
  </update>
  
  <!-- ============ updateRecord 更新by 主键 ============ -->
  <update id="updateRecord" parameterType="com.chargebolt.tenant.dao.model.Protocol">
    UPDATE cb_talos.protocol
    <set>
      <if test="title != null">
        title = #{title},
      </if>
      <if test="code != null">
        code = #{code},
      </if>
      <if test="clientType != null">
        client_type = #{clientType},
      </if>
      <if test="lang != null">
        lang = #{lang},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate},
      </if>
      <if test="content != null">
        content = #{content},
      </if>
    </set>
    WHERE id = #{id}
  </update>
</mapper>