<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.dao.aeacus.RoleSourcesMappingDAO">
  <resultMap id="BaseResultMap" type="com.chargebolt.dao.aeacus.model.RoleSourcesMapping">
    <id column="id" property="id" />
    <!-- 角色ID -->
    <result column="role_id" property="roleId" />
    <!-- 资源ID -->
    <result column="resource_id" property="resourceId" />
    <!-- 逻辑删除：0 未删除，1 已删除 -->
    <result column="deleted" property="deleted" />
    <!-- 创建时间 -->
    <result column="gmt_create" property="gmtCreate" />
    <!-- 更新时间 -->
    <result column="gmt_update" property="gmtUpdate" />
  </resultMap>
  
  <!-- ============ WHERE条件组装 ============ -->
  <sql id="Example_Where_Clause">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="roleId != null">
        AND role_id = #{roleId}
      </if>
      <if test="resourceId != null">
        AND resource_id = #{resourceId}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted}
      </if>
      <if test="gmtCreate != null">
        AND gmt_create = #{gmtCreate}
      </if>
      <if test="gmtUpdate != null">
        AND gmt_update = #{gmtUpdate}
      </if>
    </where>
  </sql>
  
  <!-- ============ 表基础字段 ============ -->
  <sql id="Base_Column_List">
    id, role_id, resource_id, deleted, gmt_create, gmt_update
  </sql>
  
  <!-- ============ listRecord 列表查询 ============ -->
  <select id="listRecord" parameterType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_aeacus.role_sources_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <!-- ============ getRecord 单条查询 ============ -->
  <select id="selectByRoleIds" resultType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_aeacus.role_sources_mapping
    WHERE deleted=0
    <if test="roleIds != null and roleIds.size() > 0">
      and role_id IN
        <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </if>
  </select>
  <select id="getRecord" parameterType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM cb_aeacus.role_sources_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- 不要删除、不要删除、不要删除 -->
     LIMIT 2
  </select>

    <!-- ============ saveRecord 单条插入 ============ -->
  <insert id="saveRecord" parameterType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping">
    INSERT INTO cb_aeacus.role_sources_mapping (role_id, resource_id, deleted, gmt_create, gmt_update)
    VALUES (#{roleId}, #{resourceId}, #{deleted}, #{gmtCreate}, #{gmtUpdate})
  </insert>
  
  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeRecord" parameterType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping">
    UPDATE cb_aeacus.role_sources_mapping SET deleted = 1 WHERE id = #{id}
  </update>


  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeByRoleId" parameterType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping">
    UPDATE cb_aeacus.role_sources_mapping SET deleted = 1 WHERE role_id = #{roleId}
  </update>

  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeByResourceId" parameterType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping">
    UPDATE cb_aeacus.role_sources_mapping SET deleted = 1 WHERE resource_id = #{resourceId}
  </update>
  
  <!-- ============ updateRecord 更新by 主键 ============ -->
  <update id="updateRecord" parameterType="com.chargebolt.dao.aeacus.model.RoleSourcesMapping">
    UPDATE cb_aeacus.role_sources_mapping
    <set>
      <if test="roleId != null">
        role_id = #{roleId},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate},
      </if>
    </set>
    WHERE id = #{id}
  </update>

  <insert id="saveBatchRecord" parameterType="java.util.List">
    INSERT INTO cb_aeacus.role_sources_mapping (role_id, resource_id, deleted, gmt_create, gmt_update)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.roleId}, #{item.resourceId}, #{item.deleted}, #{item.gmtCreate}, #{item.gmtUpdate})
    </foreach>
  </insert>
</mapper>