<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.demeter.dao.rds.StorageDeviceOperateMapper">

  <resultMap id="BaseResultMap" type="so.dian.demeter.pojo.entity.StorageDeviceOperateDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="cooperator_id" jdbcType="BIGINT" property="cooperatorId"/>
    <result column="account_id" jdbcType="BIGINT" property="accountId"/>
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo"/>
    <result column="operate_type" jdbcType="TINYINT" property="operateType"/>
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
    <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
    <result column="reason" jdbcType="TINYINT" property="reason"/>
    <result column="reason_addition" jdbcType="VARCHAR" property="reasonAddition"/>
    <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="deleted" jdbcType="TINYINT" property="deleted"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,cooperator_id,account_id,apply_no,operate_type,operate_time,shop_id,reason,reason_addition,device_no,
    <include refid="Common.BASE_ROW" />
  </sql>

  <select id="listInstallOperate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cb_demeter.storage_device_operate
    where deleted = 0
    and operate_type = 1
    and cooperator_id = #{cooperatorId}
    and device_no in
    <foreach collection="deviceNoList" item="deviceNo" separator="," open="(" close=")">
      #{deviceNo}
    </foreach>
  </select>

  <select id="getOneInstallOperateByDeviceNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from cb_demeter.storage_device_operate
    where deleted = 0
    and operate_type = 1
    and device_no = #{deviceNo}
    limit 1
  </select>

  <insert id="batchInsert" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
    insert into cb_demeter.storage_device_operate
    (cooperator_id,account_id,apply_no,operate_type,operate_time,shop_id,reason,reason_addition,device_no,create_time,update_time,deleted)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.cooperatorId}, #{item.accountId},#{item.applyNo},#{item.operateType},#{item.operateTime},
      #{item.shopId},#{item.reason},#{item.reasonAddition},#{item.deviceNo},
      #{item.createTime}, #{item.updateTime}, #{item.deleted})
    </foreach>
  </insert>

  <select id="queryByShopId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cb_demeter.storage_device_operate
    where deleted = 0
    <if test="operateType != null">
      and operate_type = #{operateType}
    </if>
    <if test="cooperatorId != null">
      and cooperator_id = #{cooperatorId}
    </if>
    <if test="shopId != null">
      and shop_id = #{shopId}
    </if>
  </select>

  <select id="queryFirstInstalledInfo" parameterType="java.util.List" resultMap="BaseResultMap">
      select
          shop_id,
          device_no,
          min(create_time) as create_time
      from
      cb_demeter.storage_device_operate
      where
          deleted = 0
      and operate_type = 1
      and shop_id in
          <foreach collection="shopIds" item="id" separator="," open="(" close=")">
            #{id}
          </foreach>
      group by
          shop_id
  </select>

  <select id="queryFirstUnInstalledInfo" parameterType="java.util.List" resultMap="BaseResultMap">
    select
        shop_id,
        device_no,
        min(create_time) as create_time
    from
      cb_demeter.storage_device_operate
    where deleted = 0
    and shop_id in
        <foreach collection="shopIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    and device_no in
        <foreach collection="deviceNos" item="deviceNo" separator="," open="(" close=")">
            #{deviceNo}
        </foreach>
    and operate_type = 2
    GROUP BY shop_id
  </select>

    <select id="queryByDeviceNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cb_demeter.storage_device_operate
        where deleted = 0
        <if test="operateType != null">
            and operate_type = #{operateType}
        </if>
        <if test="deviceNo != null and deviceNo != ''">
            and device_no = #{deviceNo}
        </if>
        order by create_time desc
        limit #{startNum}, #{pageSize}
    </select>

    <select id="countByDeviceNo" resultType="java.lang.Integer">
        select
        count(1)
        from cb_demeter.storage_device_operate
        where deleted = 0
        <if test="operateType != null">
            and operate_type = #{operateType}
        </if>
        <if test="deviceNo != null and deviceNo != ''">
            and device_no = #{deviceNo}
        </if>
    </select>
</mapper>
