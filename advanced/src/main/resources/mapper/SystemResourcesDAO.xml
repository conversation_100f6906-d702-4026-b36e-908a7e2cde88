<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.dao.aeacus.SystemResourcesDAO">
  <resultMap id="BaseResultMap" type="com.chargebolt.dao.aeacus.model.SystemResources">
    <id column="id" property="id" />
    <!-- 父级资源code -->
    <result column="parent_code" property="parentCode" />
    <!-- 资源code -->
    <result column="code" property="code" />
    <!-- 资源名称 -->
    <result column="name" property="name" />
    <!-- 资源类型 1菜单 2功能 -->
    <result column="resource_type" property="resourceType" />
    <!-- 菜单logo -->
    <result column="icon" property="icon" />
    <!-- 页面地址 -->
    <result column="url" property="url" />
    <!-- 备注信息 -->
    <result column="notes" property="notes" />
    <!-- 排序 -->
    <result column="sort" property="sort" />
    <!-- 逻辑删除：0 未删除，1 已删除 -->
    <result column="deleted" property="deleted" />
    <!-- 创建时间 -->
    <result column="gmt_create" property="gmtCreate" />
    <!-- 更新时间 -->
    <result column="gmt_update" property="gmtUpdate" />
  </resultMap>
  
  <!-- ============ WHERE条件组装 ============ -->
  <sql id="Example_Where_Clause">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="parentCode != null">
        AND parent_code = #{parentCode}
      </if>
      <if test="code != null">
        AND code = #{code}
      </if>
      <if test="name != null">
        AND name = #{name}
      </if>
      <if test="resourceType != null">
        AND resource_type = #{resourceType}
      </if>
      <if test="icon != null">
        AND icon = #{icon}
      </if>
      <if test="url != null">
        AND url = #{url}
      </if>
      <if test="notes != null">
        AND notes = #{notes}
      </if>
      <if test="sort != null">
        AND sort = #{sort}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted}
      </if>
      <if test="gmtCreate != null">
        AND gmt_create = #{gmtCreate}
      </if>
      <if test="gmtUpdate != null">
        AND gmt_update = #{gmtUpdate}
      </if>
    </where>
  </sql>
  
  <!-- ============ 表基础字段 ============ -->
  <sql id="Base_Column_List">
    id, parent_code, code, name, resource_type, icon, url, notes, sort, deleted, gmt_create, 
    gmt_update
  </sql>
  
  <!-- ============ listRecord 列表查询 ============ -->
  <select id="listRecord" parameterType="com.chargebolt.dao.aeacus.model.SystemResources" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_aeacus.system_resources
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <!-- ============ getRecord 单条查询 ============ -->
  <select id="getRecord" parameterType="com.chargebolt.dao.aeacus.model.SystemResources" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM cb_aeacus.system_resources
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- 不要删除、不要删除、不要删除 -->
     LIMIT 2
  </select>
    <select id="selectByIds" resultType="com.chargebolt.dao.aeacus.model.SystemResources">
        SELECT
        <include refid="Base_Column_List" />
        FROM cb_aeacus.system_resources
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- ============ saveRecord 单条插入 ============ -->
  <insert id="saveRecord" parameterType="com.chargebolt.dao.aeacus.model.SystemResources">
    INSERT INTO cb_aeacus.system_resources (parent_code, code, name, resource_type, icon, url, notes, 
      sort, deleted, gmt_create, gmt_update)
    VALUES (#{parentCode}, #{code}, #{name}, #{resourceType}, #{icon}, #{url}, #{notes}, 
      #{sort}, #{deleted}, #{gmtCreate}, #{gmtUpdate})
  </insert>
  
  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeRecord" parameterType="com.chargebolt.dao.aeacus.model.SystemResources">
    UPDATE cb_aeacus.system_resources SET deleted = 1 WHERE code = #{code}
  </update>
  
  <!-- ============ updateRecord 更新by code ============ -->
  <update id="updateRecord" parameterType="com.chargebolt.dao.aeacus.model.SystemResources">
    UPDATE cb_aeacus.system_resources
    <set>
      <if test="parentCode != null">
        parent_code = #{parentCode},
      </if>
      <if test="name != null">
        name = #{name},
      </if>
      <if test="code != null">
        code = #{code},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType},
      </if>
      <if test="icon != null">
        icon = #{icon},
      </if>
      <if test="url != null">
        url = #{url},
      </if>
      <if test="notes != null">
        notes = #{notes},
      </if>
      <if test="sort != null">
        sort = #{sort},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate},
      </if>
    </set>
    WHERE  id = #{id}
  </update>
</mapper>