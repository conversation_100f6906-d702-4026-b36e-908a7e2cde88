<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.tenant.dao.TenantAgentRelatedDAO">
  <resultMap id="BaseResultMap" type="com.chargebolt.tenant.dao.model.TenantAgentRelated">
    <id column="id" property="id" />
    <!-- 代理商ID -->
    <result column="agent_id" property="agentId" />
    <!-- 租户ID -->
    <result column="tenant_id" property="tenantId" />
    <!-- 逻辑删除：0 未删除，1 已删除 -->
    <result column="deleted" property="deleted" />
    <!-- 创建时间 -->
    <result column="gmt_create" property="gmtCreate" />
    <!-- 更新时间 -->
    <result column="gmt_update" property="gmtUpdate" />
  </resultMap>
  
  <!-- ============ WHERE条件组装 ============ -->
  <sql id="Example_Where_Clause">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="agentId != null">
        AND agent_id = #{agentId}
      </if>
      <if test="tenantId != null">
        AND tenant_id = #{tenantId}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted}
      </if>
      <if test="gmtCreate != null">
        AND gmt_create = #{gmtCreate}
      </if>
      <if test="gmtUpdate != null">
        AND gmt_update = #{gmtUpdate}
      </if>
    </where>
  </sql>
  
  <!-- ============ 表基础字段 ============ -->
  <sql id="Base_Column_List">
    id, agent_id, tenant_id, deleted, gmt_create, gmt_update
  </sql>
  
  <!-- ============ listRecord 列表查询 ============ -->
  <select id="listRecord" parameterType="com.chargebolt.tenant.dao.model.TenantAgentRelated" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_talos.tenant_agent_related
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <!-- ============ getRecord 单条查询 ============ -->
  <select id="getRecord" parameterType="com.chargebolt.tenant.dao.model.TenantAgentRelated" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM cb_talos.tenant_agent_related
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- 不要删除、不要删除、不要删除 -->
     LIMIT 2
  </select>
  
  <!-- ============ saveRecord 单条插入 ============ -->
  <insert id="saveRecord" parameterType="com.chargebolt.tenant.dao.model.TenantAgentRelated">
    INSERT INTO cb_talos.tenant_agent_related (agent_id, tenant_id, deleted, gmt_create, gmt_update)
    VALUES (#{agentId}, #{tenantId}, #{deleted}, #{gmtCreate}, #{gmtUpdate})
  </insert>
  
  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeRecord" parameterType="com.chargebolt.tenant.dao.model.TenantAgentRelated">
    UPDATE cb_talos.tenant_agent_related SET deleted = 1 WHERE id = #{id}
  </update>
  
  <!-- ============ updateRecord 更新by 主键 ============ -->
  <update id="updateRecord" parameterType="com.chargebolt.tenant.dao.model.TenantAgentRelated">
    UPDATE cb_talos.tenant_agent_related
    <set>
      <if test="agentId != null">
        agent_id = #{agentId},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate},
      </if>
    </set>
    WHERE id = #{id}
  </update>
</mapper>