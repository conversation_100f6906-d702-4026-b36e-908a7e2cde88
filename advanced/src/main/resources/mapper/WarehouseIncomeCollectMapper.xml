<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.demeter.dao.rds.WarehouseIncomeCollectMapper">

  <resultMap id="BaseResultMap" type="so.dian.demeter.pojo.entity.WarehouseIncomeCollectDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="device_info_id" jdbcType="BIGINT" property="deviceInfoId"/>
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo"/>
    <result column="count" jdbcType="INTEGER" property="count"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="deleted" jdbcType="TINYINT" property="deleted"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,device_info_id,apply_no,count,
    <include refid="Common.BASE_ROW" />
  </sql>

  <select id="getApplyNoByDeviceNo" parameterType="java.lang.String" resultType="string">
    select apply_no
    from cb_demeter.warehouse_income_collect a,cb_demeter.warehouse_income_device b
    where a.id = b.warehouse_income_collect_id
    and a.deleted = 0
    and b.deleted = 0
    and b.device_no = #{deviceNo}
  </select>

  <select id="listByApplyNoList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from cb_demeter.warehouse_income_collect
    where deleted = 0
    and count > 0
    and apply_no in
    <foreach collection="applyNoList" item="applyNo" separator="," open="(" close=")">
      #{applyNo}
    </foreach>
  </select>

  <insert id="batchInsert" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
    insert into cb_demeter.warehouse_income_collect
    (device_info_id,apply_no,count,create_time,update_time,deleted)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.deviceInfoId}, #{item.applyNo}, #{item.count},
      #{item.createTime}, #{item.updateTime}, #{item.deleted})
    </foreach>
  </insert>
</mapper>
