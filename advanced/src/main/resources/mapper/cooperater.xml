<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.talos.dao.rds.CooperaterDAO">

  <select id="queryList" resultType="so.dian.talos.pojo.entity.CooperaterDO">
    select id, currency_symbol, currency, gmt_create, gmt_update from cb_talos.cooperater
  </select>

  <select id="queryById" resultType="so.dian.talos.pojo.entity.CooperaterDO">
    select id, currency_symbol, currency, gmt_create, gmt_update from cb_talos.cooperater where id = #{id}
  </select>
</mapper>