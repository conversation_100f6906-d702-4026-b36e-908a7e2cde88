<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chargebolt.aeacus.dao.OssDepartmentDAO" >
  <resultMap id="BaseResultMap" type="com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="TINYINT" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="deleted" property="deleted" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="agent_id" property="agentId" />
  </resultMap>
  <!--
    功能：新增部门
    场景：创建部门
    频次：10次/天
  -->
  <insert id="insert" parameterType="com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO"  useGeneratedKeys="true" keyProperty="id">
    insert into cb_aeacus.oss_department (name, code,
      level, parent_id, creator,create_time, agent_id)
    values (#{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
      #{level,jdbcType=TINYINT}, #{parentId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR}, 
      now(), #{agentId})

  </insert>

  <!--
    功能：修改部门名
    场景：修改部门名
    频次：10次/天
  -->
  <update id="updateById" parameterType="com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO">
    update cb_aeacus.oss_department
    SET
      <if test="name != null and name != ''">
        name = #{name},
      </if>
      update_time = now(),
      updator = #{updator}
    WHERE
      id = #{id}
  </update>

  <!--
    功能：删除部门
    场景：删除部门时deleted更新为1
    频次：10次/天
  -->
  <update id="deleteByIds" parameterType="com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO">
    update cb_aeacus.oss_department
    SET
    deleted = 1,
    update_time = now(),
    updator = #{deletor}
    WHERE
    id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>

  <!--
    功能：根据id查询部门
    场景：根据id查询部门
    频次：100次/天
  -->
  <select id="selectById" resultMap="BaseResultMap">
    SELECT * FROM cb_aeacus.oss_department WHERE `id`=#{id} and deleted = 0
  </select>

  <!--
    功能：查询所有部门code和level
    场景：应用重启，初始化部门结构时用到
    频次：1次/天
  -->
  <select id="selectByCode" resultMap="BaseResultMap">
    SELECT * FROM cb_aeacus.oss_department where code = #{code} and deleted = 0
  </select>

  <!--
    功能：查询所有部门
    场景：页面展示部门树
    频次：100次/天
  -->
  <select id="selectAll" resultMap="BaseResultMap">
    SELECT * FROM cb_aeacus.oss_department where deleted = 0
  </select>

  <!--
    功能：根据部门名搜索
    场景：根据部门名搜索
    频次：100次/天
  -->
  <select id="selectByName" resultMap="BaseResultMap">
    SELECT * FROM cb_aeacus.oss_department WHERE `name`=#{name} and agent_id=#{agentId} and deleted = 0
  </select>

  <!--
    功能：根据code查询部门
    场景：根据code查询部门
    频次：100次/天
  -->
  <select id="selectLikeCode" resultMap="BaseResultMap">
    SELECT * FROM cb_aeacus.oss_department WHERE `code`like CONCAT(#{code},'%') and deleted = 0
  </select>

  <!--
    功能：根据codeList查询部门
    场景：根据codeList查询部门
    频次：100次/天
  -->
  <select id="selectByCodeList" resultMap="BaseResultMap">
    SELECT * FROM cb_aeacus.oss_department WHERE `code` in
    <foreach collection="codeList" item="code" open="(" close=")" separator=",">
      <if test="code != null and code != ''">
        #{code}
      </if>
    </foreach>
    and deleted = 0
  </select>

</mapper>