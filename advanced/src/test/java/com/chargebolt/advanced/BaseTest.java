/*
 * Dian.com Inc.
 * Copyright (c) 2004-2017 All Rights Reserved.
 */
package com.chargebolt.advanced;
import com.chargebolt.AdvancedApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.testng.AbstractTransactionalTestNGSpringContextTests;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 测试基类
 *
 * <AUTHOR>
 * @version $Id: BaseController.java, v 0.1 2017年11月17日 下午9:03 Exp $
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdvancedApplication.class)
@ActiveProfiles("local")
@Slf4j
@Transactional
@Rollback
@TestExecutionListeners(MockitoTestExecutionListener.class)
public class BaseTest extends AbstractTransactionalTestNGSpringContextTests {
    /**
     * 线程池服务
     */
    static ExecutorService arynExecutorService = new ThreadPoolExecutor(10, 50, 0L,
            TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());


    @Autowired
    public org.springframework.web.context.ConfigurableWebApplicationContext context;
    public MockMvc mvc;

    @Before
    public void setUp() throws Exception {
        mvc = MockMvcBuilders.webAppContextSetup(context).build();//建议使用这种
    }

    @Before
    public void before() throws Exception {
        log.info("before ---- ");
    }

    @After
    public void after() throws Exception {
        log.info("after ---- ");
    }


}