/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.advanced;

import com.chargebolt.commons.enums.MultilingualEnum;
import com.chargebolt.commons.utils.ExtendDateUtil;
import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: MainTest.java, v 1.0 2023-10-26 4:46 PM Exp $
 */
public class MainTest {
    public static String generateHash(long timestamp) {
        try {
            // 将时间戳转换为字符串
            String timestampStr = Long.toString(timestamp);

            // 创建 SHA-256 摘要算法实例
            MessageDigest md = MessageDigest.getInstance("SHA-256");

            // 计算哈希值
            byte[] hashBytes = md.digest(timestampStr.getBytes());
            // 使用 StringBuilder 构建哈希字符串
            StringBuilder hashBuilder = new StringBuilder();
            for (int i = 0; i < 10; i++) {
                int value = hashBytes[i] & 0xFF; // 保持值在 0-255 范围内

                hashBuilder.append(value);
            }

            // 截取为 10 位数字
            String hash = hashBuilder.toString().substring(0, 10);

            return hash;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    // 模拟汇率数据，实际应用中应从可靠的数据源获取
    private static final Map<String, BigDecimal> exchangeRates = new HashMap<>();

    static {
        exchangeRates.put("USD", new BigDecimal("1.00"));
        exchangeRates.put("EUR", new BigDecimal("0.85"));
        exchangeRates.put("CNY", new BigDecimal("6.50"));
        exchangeRates.put("VND", new BigDecimal("1.00")); // 假设VND的基础汇率为1.00
        exchangeRates.put("HDK", new BigDecimal("0.0003"));
        // 添加更多汇率
    }

    /**
     * 将金额从一种货币转换为另一种货币
     *
     * @param amount       要转换的金额
     * @param fromCurrency 源货币代码
     * @param toCurrency   目标货币代码
     * @return 转换后的金额
     * @throws IllegalArgumentException 如果输入的币种代码无效或金额为负数
     */
    public static BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency) {
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Amount cannot be negative");
        }

        BigDecimal fromRate = exchangeRates.get(fromCurrency);
        BigDecimal toRate = exchangeRates.get(toCurrency);

        if (fromRate == null || toRate == null) {
            throw new IllegalArgumentException("Invalid currency code");
        }

        // 计算转换后的金额
        BigDecimal rate = toRate.divide(fromRate, 10, RoundingMode.HALF_UP);
        return amount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

    public static void main(String[] args) {
//        try {
//            BigDecimal amountInUSD = new BigDecimal("100.00");
//            BigDecimal amountInEUR = convertCurrency(amountInUSD, "USD", "EUR");
//            System.out.println("100 USD is " + amountInEUR + " EUR");
//        } catch (IllegalArgumentException e) {
//            System.err.println(e.getMessage());
//        }
        String json= "{\"exchangeRate\":3.0412E-4,\"targetAmount\":3,\"targetCurrency\":\"HKD\"}";
        CurrencyExchangeInfo currencyExchangeInfo = JsonUtil.jsonToBean(json, CurrencyExchangeInfo.class);
        System.out.println(currencyExchangeInfo);
        System.out.println(currencyExchangeInfo.getTargetAmount()/currencyExchangeInfo.getExchangeRate());
    }
}