/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.advanced;

import com.fasterxml.jackson.databind.ObjectMapper;
import so.dian.eros.common.util.ScanParseUtil;
import so.dian.eros.pojo.bo.ScanParseBO;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.lang.util.JsonUtil;

import java.time.Duration;
import java.util.regex.Pattern;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: MainTest1.java, v 1.0 2023-12-08 10:07 PM Exp $
 */
public class MainTest1 {
    public static void main(String[] args) {
//        // 假设这是开始时间和结束时间的毫秒时间戳
//        long startTime =1721030400000L; // 2024-07-15T00:00:00Z
//        long endTime = 1721034058000L; // 当前时间的毫秒时间戳
//
//        // 创建Duration对象
//        Duration duration = Duration.ofMillis(endTime - startTime);
//
//        // 转换时间差为小时、分钟和秒
//        long totalSeconds = duration.getSeconds();
//        long hours = totalSeconds / 3600;
//        long minutes = (totalSeconds % 3600) / 60;
//        long seconds = totalSeconds % 60;
//
//        // 输出时间差
//        System.out.println("Time difference: " + hours + "h" +
//                (minutes < 10 ? "0" + minutes : minutes) + "m" +
//                (seconds < 10 ? "0" + seconds : seconds)+"s");
//
//        StringBuilder timeStr = new StringBuilder();
//
//        // 如果有小时，显示小时和分钟（即使分钟为0）
//        if (hours > 0) {
//            timeStr.append(hours).append("h");
//            timeStr.append(minutes < 10 ? "0" + minutes : minutes).append("m");
//        }
//        // 如果只有分钟，显示分钟
//        else if (minutes > 0) {
//            timeStr.append(minutes < 10 ? "0" + minutes : minutes).append("m");
//        }
//
//        // 如果有秒数，总是显示秒数
//        if (seconds > 0 || timeStr.length() > 0) {
//            timeStr.append(seconds < 10 ? "0" + seconds : seconds).append("s");
//        }
//        System.out.println(timeStr.toString());

//        Pattern QRCodeUrl= java.util.regex.Pattern.compile("^https?://[a-zA-Z]{2}\\d{2}\\.chargebolt.com/.+$");
//        Pattern QRCodeUrlSpliter= java.util.regex.Pattern.compile("^https?://(lhc|venus|b|[a-zA-Z]{2}\\d{2}).*/(lhc|Ihc)/");
//        ScanParseBO scanParseBO1= ScanParseUtil.parseScanUrl("https://hk01.chargebolt.com/lhc/b/863253862433150", QRCodeUrl, QRCodeUrlSpliter);
//        System.out.println(JsonUtil.beanToJson(scanParseBO1));
//
//        ScanParseBO scanParseBO2= ScanParseUtil.parseScanUrl("https://ae01.chargebolt.com/lhc/b/863253862433150", QRCodeUrl, QRCodeUrlSpliter);
//        System.out.println(JsonUtil.beanToJson(scanParseBO2));
//
//        ScanParseBO scanParseBO3= ScanParseUtil.parseScanUrl("https://ph01.chargebolt.com/Ihc/b/863253862433150", QRCodeUrl, QRCodeUrlSpliter);
//        System.out.println(JsonUtil.beanToJson(scanParseBO3));
//
//        System.out.println(QRCodeUrl.toString());
//        System.out.println(QRCodeUrlSpliter.toString());
//        String json="{\"img\":{\"orderQrCodePageImg\":{\"img\":[{\"uid\":\"a84759d7-312a-4c84-b186-6a2ca5419ba9\",\"url\":\"string\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_F14DF1728989697.png\"},\"defaultShopImg\":{\"img\":[{\"uid\":\"d85c7247-9375-46ea-b0d3-6db8690cf797\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_585h_F4BA01729072825.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_717661728989759.png\"},\"returnPowerbankPageImg\":{\"img\":[],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/750w_1624h_3FB5F1729829090.png\"},\"papillonTopLogo\":{\"img\":[{\"uid\":\"d0bc4b49-b828-4714-851f-5805e39f1c64\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/450w_132h_1FDDD1729072520.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_235h_ECC891728989665.png\"},\"merchantLoginLogo\":{\"img\":[{\"uid\":\"88e8aaf1-a252-41c2-9d52-26450573e638\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/450w_132h_C0A561729072532.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_278h_EB0E51728989882.png\"},\"pointSelectedIcon\":{\"img\":[{\"uid\":\"6858a97f-b16d-4b2a-a263-2a6dbabfcfae\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/62w_69h_66F9C1729149862.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_1DA3F1728989336.png\"},\"customerHomeLogo\":{\"img\":[{\"uid\":\"e8c09d2a-b4f2-4287-b637-d9e92903920c\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/450w_132h_E28371729072598.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_278h_0F1981728990601.png\"},\"powerbankChargingImg\":{\"img\":[{\"uid\":\"6c6a5712-8408-46b6-b410-ed24a5e25859\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/172w_301h_414BA1729246760.gif\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_D437B1728989262.png\"},\"returnScanPageImg\":{\"img\":[{\"uid\":\"ec32ab8a-8c8b-48ed-bdd9-41c36f8429f2\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_714h_5D73C1729072756.gif\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_619D61728989728.png\"},\"powerbankEjectImg\":{\"img\":[{\"uid\":\"2d16c557-0d97-4daf-9701-d4a26211599e\",\"url\":\"string\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_BFFB51728989211.png\"},\"pointUnselectedIcon\":{\"img\":[{\"uid\":\"c3a25852-9a11-4650-a65a-1e9673296014\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/42w_41h_882A71729149856.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_9E2F41728989304.png\"},\"merchantAboutLogo\":{\"img\":[{\"uid\":\"4a2c583b-0cce-49a4-94e9-de374f35cd55\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/240w_240h_D3BE51729244921.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_476h_4B37B1728989932.png\"},\"merchantLoginBanner\":{\"img\":[{\"uid\":\"3fdeea43-4d49-4637-88d1-94b48320d3c0\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_657h_979801729244281.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_500h_89DDB1729237038.png\"},\"customerHomeOnLoanImg\":{\"img\":[{\"uid\":\"97e8ecca-e189-4b83-bf50-89aea526050c\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/114w_114h_1CAF91729075057.gif\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_4E7021728989963.png\"},\"customerQrCodeDownloadPageLogo\":{\"img\":[{\"uid\":\"254c5c92-f2c4-434f-a1b9-a68b505f6556\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/450w_132h_910131729242663.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_278h_98F2F1728990210.png\"},\"payingImg\":{\"img\":[{\"uid\":\"4d4a8dba-20bc-4568-9b90-33e49aa7fc8d\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_600h_B914E1729243563.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_4F5191728990660.png\"},\"customerLoginLogo\":{\"img\":[{\"uid\":\"ee0a9ce0-340c-46cd-a62f-2da737f21a92\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/450w_270h_6407B1729072592.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_278h_9A0B11728990036.png\"},\"customerAboutLogo\":{\"img\":[{\"uid\":\"52b1ea2a-ba3c-4b0d-8b2c-fa5e04f3fadb\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/240w_240h_E83F91729075053.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_278h_A450B1728990268.png\"},\"papillonLoginLogo\":{\"img\":[{\"uid\":\"90661958-0d1c-49e6-b9bd-82355fb1fa8f\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/450w_270h_BBCF81729072516.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_353h_9615B1728989557.png\"},\"paySuccessImg\":{\"img\":[{\"uid\":\"ffcf5011-dee4-4e8f-b04d-88c34fc699ef\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_600h_B914E1729243588.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_E80E41728990634.png\"},\"earthLogo\":{\"img\":[{\"uid\":\"53b3f99a-d7fd-4007-9591-d645d10995a8\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/24w_24h_F3E5D1729072470.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_234h_6D8551728990802.png\"},\"howToReturnImg\":{\"img\":[{\"uid\":\"c4d7dbe7-37e3-4d2d-a8c0-fd8135848848\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_810h_5DF541729072677.png\"},{\"uid\":\"42392f9b-9197-4d81-bdc7-f3ea12860a78\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_810h_BE9191729072681.png\"},{\"uid\":\"3fa94a8a-eaa9-4621-bc66-898bcc1a1b41\",\"url\":\"https://img.chargebolt.com/chargebolt/img/202410/1125w_810h_D92431729072684.png\"}],\"exampleUrl\":\"https://img.chargebolt.com/chargebolt/img/202410/375w_812h_649471728989853.png\"}},\"color\":{\"themeColor\":[{\"default\":1,\"color\":\"#24DA5C\",\"id\":\"6b820559-4dca-46cd-bc90-3976a5d4e01c\",\"selected\":1}],\"auxiliaryColor1\":[{\"default\":1,\"color\":\"#1ABB5B\",\"id\":\"beb2a92b-1b1d-4b2e-bb05-338e490504c3\",\"selected\":1}],\"auxiliaryColor2\":[{\"default\":1,\"color\":\"#FF9D00\",\"id\":\"a906534f-2349-4fb9-b002-9ba8b19daa0b\",\"selected\":1}],\"auxiliaryColor3\":[{\"default\":1,\"color\":\"#E44400\",\"id\":\"73861b5f-85f1-453a-9d0d-9748c34e2bce\",\"selected\":1}],\"gradientColor2\":[{\"default\":1,\"color\":[{\"rgba\":\"rgba(33, 228, 126, 1)\",\"color\":\"#21E47E\",\"opacity\":1},{\"rgba\":\"rgba(237, 247, 250, 1)\",\"color\":\"#EDF7FA\",\"opacity\":1}],\"id\":\"aa3ce1b1-ee5e-4134-9a0e-32394e485d89\",\"selected\":1}],\"gradientColor1\":[{\"default\":1,\"color\":[{\"rgba\":\"rgba(107, 245, 176, 1)\",\"color\":\"#6BF5B0\",\"opacity\":1},{\"rgba\":\"rgba(211, 250, 230, 1)\",\"color\":\"#D3FAE6\",\"opacity\":1}],\"id\":\"6ba32ac8-9ee1-4983-841a-ac2b6850bce3\",\"selected\":1}]}}";
//        System.out.println(json);

        String json="123";
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.readTree(json);
            System.out.println();
        } catch (Exception e) {
            throw new CheckParamException("Invalid JSON");
        }
    }
}