/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.advanced;

import com.chargebolt.aeacus.entity.dataobject.PermissionDO;
import com.chargebolt.aeacus.service.manager.PermissionManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PermissionTest.java, v 1.0 2023-11-13 2:45 PM Exp $
 */
@Slf4j
public class PermissionTest extends BaseTest{
    @Resource
    private PermissionManager permissionManager;
    @Test
    public void getCode(){
        long userId= 181L;

        List<PermissionDO> permissionDOS = permissionManager.getPermissionByUserId(userId);
        List codes= permissionDOS.stream()
                .map(PermissionDO::getCode)
                .sorted()
                .collect(Collectors.toList());
//        List<String> userPermissionList = permissionCache.getUserPermissions(userId);
        log.info("code =============:{}", JsonUtil.beanToJson(codes));
//        log.info("userPermissionList:{}", JsonUtil.beanToJson(userPermissionList.stream().sorted().collect(Collectors.toList())));
    }
}