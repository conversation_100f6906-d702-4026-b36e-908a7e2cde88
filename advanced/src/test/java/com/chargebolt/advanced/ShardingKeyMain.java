/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced;

import org.apache.commons.codec.digest.DigestUtils;
import com.google.common.collect.Lists;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.List;
import java.util.stream.Collectors;
/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShardingKeyMain.java, v 1.0 2024-09-24 下午5:47 Exp $
 */
public class ShardingKeyMain {
    private static final int TABLE_MASK = 0xFF;
    private static final String SUFFIX_FORMAT = "%04d";

    private static int shardingForString(String shardingValue) {
        String md5 = DigestUtils.md5Hex(shardingValue.getBytes());
        return Integer.valueOf(md5.substring(0, 2), 16);
    }

    private static String shardingInstanceSuffix(String shardingValue) {
        int result = shardingForString(shardingValue);
        int instanceSuffix = result >> 6;
        return String.format(SUFFIX_FORMAT, instanceSuffix);
    }

    private static String shardingSchemeSuffix(String shardingValue) {
        int result = shardingForString(shardingValue);
        int schemeSuffix = result >> 4;
        return String.format(SUFFIX_FORMAT, schemeSuffix);
    }

    private static String shardingTableSuffix(String shardingValue) {
        return String.format(SUFFIX_FORMAT, shardingForString(shardingValue));
    }

    private static long shardingForLong(long shardingValue) {
        return shardingValue & TABLE_MASK;
    }

    private static String shardingInstanceSuffix(long shardingValue) {
        long instanceSuffix = shardingForLong(shardingValue) >> 6;
        return String.format(SUFFIX_FORMAT, instanceSuffix);
    }

    private static String shardingSchemeSuffix(long shardingValue) {
        long schemeSuffix = shardingForLong(shardingValue) >> 4;
        return String.format(SUFFIX_FORMAT, schemeSuffix);
    }

    private static String shardingTableSuffix(long shardingValue) {
        return String.format(SUFFIX_FORMAT, shardingForLong(shardingValue));
    }

    public static void generateAddConstraintDDL(String baseTableName, String constraintName, String shardingColumn) {
        List<String> ddlStatements = Lists.newArrayList();

        for (int j = 0; j < 256; j++) {
            String schemeSuffix = shardingSchemeSuffix(j);
            String tableSuffix = shardingTableSuffix(j);
            String databaseName = "cb_dian_prometheus_" + schemeSuffix;
            String fullTableName = baseTableName + "_" + tableSuffix;

            // 生成添加唯一约束的DDL语句
            String addConstraintDDL = "ALTER TABLE `" + databaseName + "`.`" + fullTableName + "` MODIFY `" + constraintName + "` VARCHAR(20);\n";
//            String addConstraintDDL = "ALTER TABLE `" + databaseName + "`.`" + fullTableName + "` DROP INDEX `" + constraintName + "`;\n";
//            String addConstraintDDL1 = "ALTER TABLE `" + databaseName + "`.`" + fullTableName + "` ADD INDEX `idx_user_id` (`user_id`);\n";

            ddlStatements.add(addConstraintDDL);
//            ddlStatements.add(addConstraintDDL1);
        }

        // 输出所有的DDL语句
        String allDDLs = String.join("", ddlStatements);
        System.out.println(allDDLs);
    }

    public static void main(String[] args) {
        String baseTableName = "user";
        String constraintName = "mobile";
        String shardingColumn = "";

        generateAddConstraintDDL(baseTableName, constraintName, shardingColumn);
    }
}