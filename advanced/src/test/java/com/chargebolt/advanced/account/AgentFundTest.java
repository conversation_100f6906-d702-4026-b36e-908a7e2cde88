/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.account;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.agent.response.AgentFundAccountResponse;
import com.chargebolt.agent.service.AgentFundService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentFundTest.java, v 1.0 2024-03-22 10:04 AM Exp $
 */
@Slf4j
public class AgentFundTest extends BaseTest {
    @Autowired
    private AgentFundService agentFundService;
    @Test
    public void getAgentFund(){
        AgentFundAccountResponse agentFundAccountResponse= agentFundService.getFundAccount(1001L);
        log.info("{}", JsonUtil.beanToJson(agentFundAccountResponse));
    }
}