/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.advanced.account;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.fund.dao.FundAccountDAO;
import com.chargebolt.fund.dao.model.FundAccount;
import com.chargebolt.fund.dao.model.FundBalance;
import com.chargebolt.fund.service.FundAccountService;
import com.chargebolt.fund.service.FundBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: BalanceAccountTest.java, v 1.0 2023-10-30 10:42 AM Exp $
 */
@Slf4j
public class BalanceAccountTest  extends BaseTest {
    @Autowired
    private FundAccountService fundAccountService;

    @Test
    public void getAccount(){
        FundAccount fundAccount= new FundAccount();
        fundAccount.setId(1L);
        fundAccount= fundAccountService.getRecord(fundAccount);
        log.info("fundBalance:{}", JsonUtil.beanToJson(fundAccount));
    }
}