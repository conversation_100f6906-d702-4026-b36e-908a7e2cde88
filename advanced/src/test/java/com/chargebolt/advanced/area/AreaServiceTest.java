/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.area;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.basic.response.area.Area;
import com.chargebolt.service.area.AreaService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.JsonUtil;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AreaServiceTest.java, v 1.0 2024-07-23 下午3:26 Exp $
 */
@Slf4j
public class AreaServiceTest extends BaseTest {
    @Autowired
    private AreaService areaService;
    @Test
    public void cityCodeTest() {
        List<Area> area = areaService.getArea("HK");
        log.info("{}", JsonUtil.beanToJson(area));
        Area area1 = areaService.getAreaByCode("9078134");
        log.info("{}", JsonUtil.beanToJson(area1));
    }
//    1594446
}