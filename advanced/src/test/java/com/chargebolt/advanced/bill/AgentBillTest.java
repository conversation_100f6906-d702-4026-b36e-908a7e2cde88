/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.bill;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.agent.request.BillSearchRequest;
import com.chargebolt.agent.response.AgentPercentageSummaryResponse;
import com.chargebolt.agent.service.AgentBillService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentBillTest.java, v 1.0 2024-03-22 4:15 PM Exp $
 */
@Slf4j
public class AgentBillTest extends BaseTest {
    @Autowired
    private AgentBillService agentBillService;
    @Test
    public void agentPercentageList(){
        BillSearchRequest request= new BillSearchRequest();
        request.setAgentId(1017L);
        request.setStartTime(new DateBuild().addToMonth(-5).start().toDate().getTime());
        request.setEndTime(new DateBuild().end().toDate().getTime());
        AgentPercentageSummaryResponse response= agentBillService.agentPercentageList(request);
        log.info("{}", JsonUtil.beanToJson(response));
    }
}