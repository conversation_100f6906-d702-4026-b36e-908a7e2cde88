/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.countries;

import com.chargebolt.aeacus.entity.dataobject.CountryDO;
import com.fasterxml.jackson.core.type.TypeReference;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import so.dian.mofa3.lang.util.JsonUtil;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: MainTest.java, v 1.0 2024-08-30 上午10:07 Exp $
 */
public class MainTest {
    public static void main(String[] args) {
        String filePath= "/Users/<USER>/workspaces/ezreal/basic/src/main/resources/multilingual/resources/countries-test.json";
        try {
            // 使用Files工具类读取整个文件内容到一个字符串中
            String content = new String(Files.readAllBytes(Paths.get(filePath)));

            List<CountryDO> countryDOList= JsonUtil.jsonToBean(content, new TypeReference<List<CountryDO>>() {});
            Collections.sort(countryDOList, new Comparator<CountryDO>() {
                @Override
                public int compare(CountryDO o1, CountryDO o2) {
                    return o1.getEnglishName().compareTo(o2.getEnglishName());
                }
            });
            // 使用 List.sort() 方法进行排序
//            countryDOList.sort(Comparator.comparing(MainTest::getFirstLetterOfChineseName));

            List<Map<String, String>> output= new ArrayList<>();
            // 打印排序后的结果
            Map<String, String> map= new HashMap<>();
            long i=100L;
            for (CountryDO country : countryDOList) {
//                System.out.println(JsonUtil.beanToJson(country));
                map.put(country.getCountryCode()+country.getPhoneCode(), country.getEnglishName());
//                output.add(map);
                System.out.println("INSERT INTO cb_icarus.regionals (name, default_lang, regional_code, phone_code, currency_code," +
                        " regional_emoji, deleted, gmt_create, gmt_update)" +
                        " VALUES ('"+country.getEnglishName()+"', '"+country.getDefaultLang()+"', '"+country.getCountryCode()
                        +"', '"+country.getPhoneCode()+"', '"+country.getCurrencyCode()+"'," +
                        "'"+country.getRegionalEmoji()+"', 0, "+(System.currentTimeMillis()+i)+", "+(System.currentTimeMillis()+i)+");");
                i++;
            }
            System.out.println(countryDOList.size());
            System.out.println(map.size());
            System.out.println(JsonUtil.beanToJson(map));

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    private static String getFirstLetterOfChineseName(CountryDO country) {
        String chineseName = country.getChineseName();
        StringBuilder firstLetters = new StringBuilder();

        for (char c : chineseName.toCharArray()) {
            if (c >= '\u4e00' && c <= '\u9fa5') { // 判断是否为汉字
                String pinyin = getPinyinWithFirstLetter(c);
                if (!pinyin.isEmpty()) {
                    firstLetters.append(pinyin.charAt(0));
                }
            } else {
                firstLetters.append(c);
            }
        }

        return firstLetters.toString();
    }

    private static String getPinyinWithFirstLetter(char hanzi) {
        try {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(hanzi);
            if (pinyinArray != null && pinyinArray.length > 0) {
                return pinyinArray[0];
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}