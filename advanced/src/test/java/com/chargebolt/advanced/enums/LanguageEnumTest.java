/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.enums;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: LanguageEnum.java, v 1.0 2024-01-10 2:20 PM Exp $
 */
public class LanguageEnumTest {
    public static void main(String[] args) {
//        String currentLanguage = "zh-CN"; // 或者 "en-us"
        String currentLanguage = "en-US"; // 或者 "en-us"
        System.out.println("=======1");
        System.out.println(StatusTestEnum.NEW.getTranslation(currentLanguage));
        System.out.println("=======2");
        System.out.println(StatusTestEnum.RUNNING.getTranslation(currentLanguage));
        System.out.println("=======3");
        System.out.println(StatusTestEnum.getTranslationByValue(2, currentLanguage));
    }

}

