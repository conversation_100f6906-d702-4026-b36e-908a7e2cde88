/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.paymethod;

import com.chargebolt.tenant.service.PaywayService;
import com.chargebolt.theseus.dto.PayMethodOrderDTO;
import so.dian.mofa3.lang.util.JsonUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PaymethodTest.java, v 1.0 2024-11-01 下午5:17 Exp $
 */
public class PaymethodTest {
    public static void main(String[] args) {
        String json= "[\n" +
                "        {\n" +
                "            \"uid\": \"2\",\n" +
                "            \"id\": 2,\n" +
                "            \"name\": \"APM_PAY_DEPOSIT\",\n" +
                "            \"methods\": [\n" +
                "                {\n" +
                "                    \"uid\": \"209-58\",\n" +
                "                    \"name\": null,\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://img.chargebolt.com/chargebolt/img/202410/105w_72h_EC7EA1729840536.png\"\n" +
                "                    },\n" +
                "                    \"id\": 209,\n" +
                "                    \"payway\": 58,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"210-58\",\n" +
                "                    \"name\": null,\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://img.chargebolt.com/chargebolt/img/202410/105w_72h_EC7EA1729840536.png\"\n" +
                "                    },\n" +
                "                    \"id\": 210,\n" +
                "                    \"payway\": 58,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"211-58\",\n" +
                "                    \"name\": null,\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://img.chargebolt.com/chargebolt/img/202410/105w_72h_EC7EA1729840536.png\"\n" +
                "                    },\n" +
                "                    \"id\": 211,\n" +
                "                    \"payway\": 58,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"202-53\",\n" +
                "                    \"name\": \"AlipayHK\",\n" +
                "                    \"paywayName\": \"Pingpong APM Wallet\",\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_19CC81719285925.png\"\n" +
                "                    },\n" +
                "                    \"id\": 202,\n" +
                "                    \"payway\": 53,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"203-53\",\n" +
                "                    \"name\": \"Wechat\",\n" +
                "                    \"paywayName\": \"Pingpong APM Wallet\",\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_B5ECD1719285940.png\"\n" +
                "                    },\n" +
                "                    \"id\": 203,\n" +
                "                    \"payway\": 53,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"205-53\",\n" +
                "                    \"name\": \"PromptPay\",\n" +
                "                    \"paywayName\": \"Pingpong APM Wallet\",\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_DC0811719285970.png\"\n" +
                "                    },\n" +
                "                    \"id\": 205,\n" +
                "                    \"payway\": 53,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"206-53\",\n" +
                "                    \"name\": \"RabbitLinePay\",\n" +
                "                    \"paywayName\": \"Pingpong APM Wallet\",\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_052651719285985.png\"\n" +
                "                    },\n" +
                "                    \"id\": 206,\n" +
                "                    \"payway\": 53,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"204-53\",\n" +
                "                    \"name\": \"GCash\",\n" +
                "                    \"paywayName\": \"Pingpong APM Wallet\",\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_FF26E1719285955.png\"\n" +
                "                    },\n" +
                "                    \"id\": 204,\n" +
                "                    \"payway\": 53,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"201-53\",\n" +
                "                    \"name\": \"Alipay\",\n" +
                "                    \"paywayName\": \"Pingpong APM Wallet\",\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_7EA0D1719285909.png\"\n" +
                "                    },\n" +
                "                    \"id\": 201,\n" +
                "                    \"payway\": 53,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"201-62\",\n" +
                "                    \"name\": \"Alipay\",\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_7EA0D1719285909.png\"\n" +
                "                    },\n" +
                "                    \"id\": 201,\n" +
                "                    \"payway\": 62,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"202-62\",\n" +
                "                    \"name\": \"AlipayHK\",\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_19CC81719285925.png\"\n" +
                "                    },\n" +
                "                    \"id\": 202,\n" +
                "                    \"payway\": 62,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"204-62\",\n" +
                "                    \"name\": \"GCash\",\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_FF26E1719285955.png\"\n" +
                "                    },\n" +
                "                    \"id\": 204,\n" +
                "                    \"payway\": 62,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"207-62\",\n" +
                "                    \"name\": \"KakaoPay\",\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/07/18/140w_96h_7D3241721293112.png\"\n" +
                "                    },\n" +
                "                    \"id\": 207,\n" +
                "                    \"payway\": 62,\n" +
                "                    \"status\": 0\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"uid\": \"3\",\n" +
                "            \"id\": 3,\n" +
                "            \"name\": \"APM_AUTHORIZATION\",\n" +
                "            \"methods\": [\n" +
                "                {\n" +
                "                    \"uid\": \"201-54\",\n" +
                "                    \"name\": \"Alipay\",\n" +
                "                    \"paywayName\": \"Pingpong One-Click\",\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_7EA0D1719285909.png\"\n" +
                "                    },\n" +
                "                    \"id\": 201,\n" +
                "                    \"payway\": 54,\n" +
                "                    \"status\": 0\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"uid\": \"1\",\n" +
                "            \"id\": 1,\n" +
                "            \"name\": \"CARD_AUTHORIZATION\",\n" +
                "            \"methods\": [\n" +
                "                {\n" +
                "                    \"uid\": \"102-61\",\n" +
                "                    \"name\": \"VISA\",\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_0E5991719285843.png\"\n" +
                "                    },\n" +
                "                    \"id\": 102,\n" +
                "                    \"payway\": 61,\n" +
                "                    \"status\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"uid\": \"101-61\",\n" +
                "                    \"name\": \"MasterCard\",\n" +
                "                    \"paywayName\": null,\n" +
                "                    \"logo\": {\n" +
                "                        \"width\": 140,\n" +
                "                        \"height\": 96,\n" +
                "                        \"img\": \"https://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2024/06/25/140w_96h_21CA01719285812.png\"\n" +
                "                    },\n" +
                "                    \"id\": 101,\n" +
                "                    \"payway\": 61,\n" +
                "                    \"status\": 1\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]";

        List<PaywayService.PayToolGroupDTO> initPaymentMethod= JsonUtil.jsonToArray(json, PaywayService.PayToolGroupDTO.class);
        List<PaywayService.PayToolGroupDTO> filteredGroups = initPaymentMethod.stream()
                .map(group -> {
                    List<PaywayService.PayMethodDTO> filteredMethods = group.getMethods().stream()
                            .filter(method -> method.getStatus() == 1)
                            .collect(Collectors.toList());
                    group.setMethods(filteredMethods);
                    return group;
                })
                .filter(group -> !group.getMethods().isEmpty()) // 过滤掉没有方法的组
                .collect(Collectors.toList());
        List<PayMethodOrderDTO> pay= JsonUtil.jsonToArray(JsonUtil.beanToJson(filteredGroups), PayMethodOrderDTO.class);
        List<PayMethodOrderDTO> result= pay.stream().peek(method->{
            method.setStatus(1);
        }).collect(Collectors.toList());
        System.out.println(JsonUtil.beanToJson(result));
    }
}