/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.advanced.percentage;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.bill.dao.model.PercentageRecord;
import com.chargebolt.bill.service.PercentageRecordService;
import com.chargebolt.dao.statistics.rds.MchPercentageStatisticsDAO;
import com.chargebolt.dao.statistics.rds.dto.MchPercentageAvgDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PercentageTest.java, v 1.0 2023-10-30 11:33 AM Exp $
 */
@Slf4j
public class PercentageTest extends BaseTest {
    @Autowired
    private PercentageRecordService percentageRecordService;
    @Autowired
    private MchPercentageStatisticsDAO mchPercentageStatisticsDAO;

    @Test
    public void get(){
        PercentageRecord record= new PercentageRecord();
        record.setId(1L);
        record= percentageRecordService.getRecord(record);
        log.info("record：{}", JsonUtil.beanToJson(record));
    }

    @Test
    public void mchDivideAvg(){
        MchPercentageAvgDTO record= mchPercentageStatisticsDAO.mchDivideAvg(null);
        log.info("record：{}", JsonUtil.beanToJson(record));
    }
}