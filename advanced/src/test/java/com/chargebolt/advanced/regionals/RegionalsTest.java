/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.regionals;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.dao.common.model.Regionals;
import com.chargebolt.service.area.RegionalsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.mofa3.lang.util.JsonUtil;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RegionalsTest.java, v 1.0 2024-09-05 下午6:51 Exp $
 */
@Slf4j
public class RegionalsTest extends BaseTest {
    @Autowired
    private RegionalsService regionalsService;
    @Test
    public void test() {
        ThreadLanguageHolder.setCurrentLang("vi-VN");
        List<Regionals> regionals = regionalsService.listRecord(new Regionals());
        log.info("regionals: {}", JsonUtil.beanToJson(regionals));

        Regionals regionals1= new Regionals();
        regionals1.setPhoneCode("+86");
         log.info("{}", JsonUtil.beanToJson(regionalsService.listRecord(regionals1)));

    }
}