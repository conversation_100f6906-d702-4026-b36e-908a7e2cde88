/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.statistics;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.dao.statistics.rds.ShopMerchantDeviceStatisticsDAO;
import com.chargebolt.dao.statistics.rds.dto.DeviceSummaryDTO;
import com.chargebolt.dao.statistics.rds.model.DeviceSummarySearchModel;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;

import java.util.Arrays;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DeviceSummaryTest.java, v 1.0 2024-01-04 6:27 PM Exp $
 */
@Slf4j
public class DeviceSummaryTest extends BaseTest {
    @Autowired
    private ShopMerchantDeviceStatisticsDAO shopMerchantDeviceStatisticsDAO;

    @Test
    public void deviceAllSummary(){
        DeviceSummarySearchModel model = new DeviceSummarySearchModel();
//        model.setSellerId(178L);
        model.setStartTime(new DateBuild().addToMonth(-2).toDate());
        model.setEndTime(new DateBuild().toDate());
        DeviceSummaryDTO deviceSummaryDTO = shopMerchantDeviceStatisticsDAO.deviceAllSellerSummary(model);
        log.info("{}", JsonUtil.beanToJson(deviceSummaryDTO));
    }

//    @Test
//    public void deviceSummaryByShop(){
//        DeviceSummarySearchModel model = new DeviceSummarySearchModel();
//        model.setSellerId(162L);
//        model.setStartTime(new DateBuild().addToMonth(-2).toDate());
//        model.setEndTime(new DateBuild().toDate());
//        model.setDeviceTypeIds(Arrays.asList(1L, 2L, 4L, 5L, 6L));
//        model.setBoxTypeIds(Arrays.asList(3L));
//        List<DeviceSummaryDTO> deviceSummaryDTO = shopMerchantDeviceStatisticsDAO.deviceSummaryByShop(model);
//        log.info("{}", JsonUtil.beanToJson(deviceSummaryDTO));
//    }

//    @Test
//    public void deviceSummaryBySeller(){
//        DeviceSummarySearchModel model = new DeviceSummarySearchModel();
//        model.setSellerId(162L);
//        model.setStartTime(new DateBuild().addToMonth(-2).toDate());
//        model.setEndTime(new DateBuild().toDate());
//        List<DeviceSummaryDTO> deviceSummaryDTO = shopMerchantDeviceStatisticsDAO.deviceSummaryBySeller(model);
//        log.info("{}", JsonUtil.beanToJson(deviceSummaryDTO));
//    }
}