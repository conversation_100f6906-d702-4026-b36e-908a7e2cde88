/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.statistics;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.api.request.StatisticOrderRequest;
import com.chargebolt.api.response.OrderSummaryListResponse;
import com.chargebolt.service.BusinessIndicatorsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderSummaryTest.java, v 1.0 2024-01-03 1:58 PM Exp $
 */
@Slf4j
public class OrderSummaryTest extends BaseTest {
    @Autowired
    private BusinessIndicatorsService businessIndicatorsService;
//    @Test
//    public void orderSummaryByShop(){
//        StatisticOrderRequest request= new StatisticOrderRequest();
//        request.setPageNum(1);
//        request.setPageSize(10);
//        request.setCycle(3);
//        request.setSortField(1);
//        request.setSort("desc");
//
//        OrderSummaryListResponse response= businessIndicatorsService.orderSummaryByShop(request);
//        log.info("{}", JsonUtil.beanToJson(response));
//    }
}