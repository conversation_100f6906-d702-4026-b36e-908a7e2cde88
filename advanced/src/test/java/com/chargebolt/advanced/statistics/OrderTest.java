/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.advanced.statistics;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.api.request.DataOrderDetailRequest;
import com.chargebolt.api.response.BusinessStatisticsResponse;
import com.chargebolt.api.response.OrderDetailListResponse;
import com.chargebolt.api.response.OrderStatisticsResponse;
import com.chargebolt.dao.statistics.rds.OrderBoxStatisticsDAO;
import com.chargebolt.ezreal.response.shop.ShopOrderCombinationResponse;
import com.chargebolt.service.BusinessIndicatorsService;
import com.chargebolt.service.DatailIndicatorsService;
import com.chargebolt.service.OrderDataStatisticsService;
import com.chargebolt.service.context.DataIndicatorsContext;
import com.chargebolt.service.order.OrderStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderTest.java, v 1.0 2023-12-26 6:43 PM Exp $
 */
@Slf4j
public class OrderTest extends BaseTest {
    @Autowired
    BusinessIndicatorsService businessIndicatorsService;
    @Autowired
    OrderBoxStatisticsDAO orderBoxStatisticsDAO;
    @Autowired
    DatailIndicatorsService datailIndicatorsService;
    @Autowired
    OrderDataStatisticsService orderDataStatisticsService;
    @Autowired
    OrderStatisticsService orderStatisticsService;
//    @Test
//    public void historyBusiness(){
//        DataIndicatorsContext context= new DataIndicatorsContext();
//        context.setCycle(3);
//        context.setAll(Boolean.TRUE);
//        BusinessStatisticsResponse response= businessIndicatorsService.historyBusiness(context);
//        log.info("{}", JsonUtil.beanToJson(response));
//    }

//    @Test
//    public void historyOrder(){
//        DataIndicatorsContext context= new DataIndicatorsContext();
//        context.setCycle(3);
//        context.setAll(Boolean.TRUE);
//        OrderStatisticsResponse response= businessIndicatorsService.historyOrder(context);
//        log.info("{}", JsonUtil.beanToJson(response));
//    }
    @Test
    public void deviceOrderCount(){
        Integer count= orderBoxStatisticsDAO.deviceOrderCount(null);
        log.info("{}", count);
    }
//   @Test
//    public void orderDetailList(){
//        DataOrderDetailRequest request= new DataOrderDetailRequest();
//        request.setIndicators(3);
//        request.setCycle(3);
//        request.setPageNum(1);
//        request.setPageSize(10);
//       OrderDetailListResponse response=datailIndicatorsService.orderDetailList(request);
//       log.info("{}", JsonUtil.beanToJson(response));
//    }
    @Test
    public void orderDetailList2(){
        orderDataStatisticsService.getShopIds(null, null);
//        orderDataStatisticsService.getShopIds(new DateBuild().addToDay(-3).toDate(), new DateBuild().toDate());
    }

    @Test
    public void getShopOrderTest(){

        ShopOrderCombinationResponse response =orderStatisticsService.getShopOrderCombination(1759L, new DateBuild().addToDay(-30).toDate(), new DateBuild().toDate(), 1);
        log.info("{}", JsonUtil.beanToJson(response));
    }
}