/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.tenant;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.tenant.dao.model.Protocol;
import com.chargebolt.tenant.service.ProtocolService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ProtocolTest.java, v 1.0 2024-08-30 下午3:15 Exp $
 */
@Slf4j
public class ProtocolTest extends BaseTest {

    @Autowired
    private ProtocolService protocolService;
    @Test
    public void getRecord() {
        Protocol protocol = protocolService.getRecord(new Protocol());
        log.info("protocol:{}", JsonUtil.beanToJson(protocol));
    }
}