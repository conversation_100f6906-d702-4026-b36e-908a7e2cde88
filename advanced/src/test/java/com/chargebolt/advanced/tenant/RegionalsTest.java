/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.tenant;

import com.chargebolt.advanced.BaseTest;
import com.chargebolt.dao.common.model.Regionals;
import com.chargebolt.service.area.RegionalsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.mofa3.lang.util.JsonUtil;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RegionalsTest.java, v 1.0 2024-08-30 下午5:08 Exp $
 */
@Slf4j
public class RegionalsTest extends BaseTest {
    @Autowired
    private RegionalsService regionalsService;
    @Test
    public void list(){
        List<Regionals> list= regionalsService.listRecord(new Regionals());

        List<String> priorityPhoneCodes = Arrays.asList("+86", "+85", "+84");
        sortRegionalsByPhoneCode(list, priorityPhoneCodes);
        log.info("{}", JsonUtil.beanToJson(list));
    }

    /**
     * 根据给定的 phoneCode 优先级列表对 Regionals 列表进行排序。
     *
     * @param regionals 区域列表
     * @param phoneCodes 优先级列表
     */
    public static void sortRegionalsByPhoneCode(List<Regionals> regionals, List<String> phoneCodes) {
        Comparator<Regionals> comparator = Comparator.comparingInt(regionalsItem -> {
            int index = phoneCodes.indexOf(regionalsItem.getPhoneCode());
            return index == -1 ? phoneCodes.size() : index; // 如果没有找到，则放到最后
        });
        regionals.sort(comparator);
    }
}