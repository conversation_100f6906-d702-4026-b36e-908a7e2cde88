/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.advanced.warehouse;

import com.chargebolt.advanced.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.demeter.biz.facade.WarehouseIncomeFacade;
import so.dian.demeter.client.dto.DeviceApplyDTO;
import so.dian.demeter.client.param.WarehouseIncomeApplyRemoteParam;

import java.util.Arrays;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: WareHouseImportTest.java, v 1.0 2024-05-11 上午11:11 Exp $
 */
public class WareHouseImportTest extends BaseTest {
    @Autowired
    private WarehouseIncomeFacade warehouseIncomeFacade;
    @Test
    public void importTest() {
        WarehouseIncomeApplyRemoteParam param= new WarehouseIncomeApplyRemoteParam();
        param.setCooperatorId(1L);
        param.setOperatorId(1L);

        DeviceApplyDTO deviceApplyDTO = new DeviceApplyDTO();
//        deviceApplyDTO.setDeviceType("");
//        deviceApplyDTO.setDeviceName("");
        deviceApplyDTO.setDeviceInfoId(2L);
        deviceApplyDTO.setDeviceNoList(Arrays.asList("863253861070672","863253861070680","76325386101234",
                "76325386101235","76325386101236","76325386101237","863253861070698","860182061748260"));
        param.setDeviceGroup(Arrays.asList(deviceApplyDTO));

        warehouseIncomeFacade.warehouseDeviceImport(param);
    }
}