/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.agent.controller;

import com.alibaba.fastjson.JSON;
import com.chargebolt.aeacus.annotation.Authentication;
import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.agent.response.AgentOrderDetail;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.service.authority.LoginUserDataAuthorityService;
import com.chargebolt.template.BaseChargeboltController;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.eros.common.util.LocationUtil;
import so.dian.eros.controller.hermes.BaseHermesOrderController;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.eros.manager.hermes.HermesBoxManager;
import so.dian.eros.manager.hermes.vo.BoxOrderVO;
import so.dian.eros.manager.hermes.vo.PageVO;
import so.dian.hermes.client.pojo.dto.oss.OssBoxOrderDTO;
import so.dian.hermes.client.pojo.param.BaseOperateParam;
import so.dian.hermes.client.pojo.param.oss.BoxAlterAmountParam;
import so.dian.hermes.client.pojo.param.oss.CsRefundParam;
import so.dian.hermes.client.pojo.param.oss.OssQueryOrderListParam;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentOrderController.java, v 1.0 2024-03-21 6:06 PM Exp $
 */
@Slf4j
@RestController
public class AgentOrderController extends BaseChargeboltController {

    @Resource
    private ApplicationContext ctx;
    @Resource
    private HermesBoxManager hermesBoxManager;
    @Resource
    private LoginUserDataAuthorityService loginUserDataAuthorityService;
    @Value("${hera.refundSwitch}")
    private Boolean refundSwitch;

    /**
     * 代理商分成明细
     *
     * @return
     */
    @Login
    @GetMapping(value = "/app/1.0/agent/orderList")
    public Result<PageData<AgentOrderDetail>> orderList(@RequestParam(name = "type", required = false, defaultValue = "0") Integer type,
                                                        @RequestParam(name = "content", required = false) String content,

                                                        @RequestParam(name = "startTime", required = false) Long startTime,
                                                        @RequestParam(name = "endTime", required = false) Long endTime,
                                                        @RequestParam(name = "status", required = false) Integer status,
//                                                        @RequestParam(name = "shopName", required = false) String shopName,
                                                        @RequestParam(name = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                        @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        OssQueryOrderListParam queryOrderListParam = new OssQueryOrderListParam();
        return template.execute(new ControllerCallback<PageData<AgentOrderDetail>>() {
            @Override
            public void checkParam() {
            }

            @Override
            public void buildContext() {
                // 设置订单查询条件
                if (type == 1) {
                    if (StringUtils.isNotBlank(content)) {
                        queryOrderListParam.setLoanShopName(content);
                    }
                } else if (type == 2) {
                    if (NumberUtils.isDigits(content)) {
                        try {
                            queryOrderListParam.setUserId(Long.parseLong(content));
                        } catch (Exception e) {
                            // tag 未国际化
                            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "UserId requires Digits.");
                        }
                    } else {
                        // tag 未国际化
                        throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "UserId requires Digits.");
                    }
                } else if (type == 3) {
                    queryOrderListParam.setOrderNo(content);
                } else if (type == 4) {
                    queryOrderListParam.setPowerbankNo(content);
                }

                queryOrderListParam.setPageNum(pageNum);
                queryOrderListParam.setPageSize(pageSize);
                queryOrderListParam.setCooperatorId(1L);
                // 数据权限控制
                UserDataAuthorityContext authority = loginUserDataAuthorityService.getLoginUserDataAuthority();
                // 代理商用户，查看本级用户的订单
                if (authority.getAuthorityLevel().equals(AuthorityLevelEnum.AGENT.getCode())) {
                    List<Long> userIds = authority.getQuery().getUserIds();
                    if (CollectionUtils.isEmpty(userIds)) {
                        queryOrderListParam.setSellerIdList(Lists.newArrayList(-1L));
                    } else {
                        queryOrderListParam.setSellerIdList(userIds);
                    }
                }
                // 普通用户，只能查询自己的订单
                if (authority.getAuthorityLevel().equals(AuthorityLevelEnum.USER.getCode())) {
                    queryOrderListParam.setSellerIdList(Lists.newArrayList(authority.getUserId()));
                }
                if (!Objects.isNull(startTime)) {
                    queryOrderListParam.setStartTime(new DateBuild(startTime).start().toDate());
                }
                if (!Objects.isNull(endTime)) {
                    queryOrderListParam.setEndTime(new DateBuild(endTime).end().toDate());
                }
                if (Objects.nonNull(status)) {
                    queryOrderListParam.setOrderStatus(status);
                }
            }

            @Override
            public PageData<AgentOrderDetail> execute() {
                BizResult<PageVO<BoxOrderVO>> result = hermesBoxManager.getBoxOrderPage(queryOrderListParam, ctx, LocationUtil.getLocale(ThreadLanguageHolder.getCurrentLang()));
                List<AgentOrderDetail> list = new ArrayList<>();
                if (result.isSuccess()) {
                    for (BoxOrderVO order : result.getData().getList()) {
                        AgentOrderDetail agentOrderDetail = new AgentOrderDetail();
                        agentOrderDetail.setOrderNo(order.getOrderNo());
                        agentOrderDetail.setBuyerId(order.getBuyerId());
                        agentOrderDetail.setAmountYuan(order.getCurrentAmount());
                        agentOrderDetail.setAmountYuanFormatted(order.getCurrentAmountFormatted());
                        agentOrderDetail.setCurrencyCode(order.getCurrency());
                        agentOrderDetail.setDeviceNo(order.getLoanBoxNo());
                        agentOrderDetail.setPowerBankNo(order.getPowerbankNo());
                        agentOrderDetail.setLoanShop(order.getLoanShopName());
                        agentOrderDetail.setLoanTime(order.getLoanTime());
                        agentOrderDetail.setReturnTime(order.getReturnTime());
                        agentOrderDetail.setStatus(order.getStatus());
                        agentOrderDetail.setStatusDesc(order.getStatusText());
                        agentOrderDetail.setPauseTime(order.getPauseTime());
                        list.add(agentOrderDetail);
                    }

                }
                return PageData.create(list, result.getData().getTotalCount().longValue(), pageNum.longValue(), pageSize);
            }
        });
    }

    @Data
    private static class RefundOrderParam {
        private String reason;
        private String orderNo;
        private Double refundAmount;
        private Boolean isOffRefund;

        CsRefundParam transToCsRefundParam(Long csId, String userName, String currencyCode, boolean refundSwitch) {
            if (StringUtils.isBlank(orderNo)) {
                log.error("订单客服退款入参 orderNo:不可为空");
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR);
            }
            if (StringUtils.isBlank(reason)) {
                log.warn("订单客服退款入参 reason为空或空串");
                reason = "客服未填写";
            }
            if (reason.length() > 200) {
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "原因字数过多");
            }
            CsRefundParam refundParam = new CsRefundParam();
            // TODO ZP
//            refundParam.setBizType(RefundBizTypeEnum.ORDER_CS);
            refundParam.setOrderNo(orderNo);
            refundParam.setCsId(csId);
            refundParam.setComment(reason);
            MultiCurrencyMoney refundMoney = new MultiCurrencyMoney(refundAmount, currencyCode);
            refundParam.setApplyAmount(Integer.parseInt(String.valueOf(refundMoney.getCent())));
            refundParam.setCsNick(userName);
            if (refundSwitch) {
                refundParam.setIsOffRefund(isOffRefund);
            } else {
                refundParam.setIsOffRefund(false);
            }
            return refundParam;
        }
    }


    @Data
    private static class BoxOrderOperateParam {
        private String reason;
        private String orderNo;
        private Double orderAmount;
        private String powerbankNo;

        BaseOperateParam transToBaseOperateParam(Integer csId, String csNick) {
            if (org.apache.commons.lang.StringUtils.isBlank(orderNo)) {
                log.error("订单客服操作入参 orderNo:[{}]不可为空或空字符串", orderNo);
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR);
            }
            BaseOperateParam param = new BaseOperateParam();
            param.setCsId(csId);
            param.setOrderNo(orderNo);
            param.setReason(reason);
            param.setCsNick(csNick);
            param.setPowerbankNo(powerbankNo);
            return param;
        }

        BoxAlterAmountParam transToAlterAmountParam(Integer csId, String csNick, String currencyCode) {
            if (org.apache.commons.lang.StringUtils.isBlank(orderNo)) {
                log.error("订单客服操作入参 orderNo:[{}]不可为空或空字符串", orderNo);
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR);
            }
            if (Objects.isNull(orderAmount) || orderAmount < 0) {
                log.error("订单客服操作入参 orderAmount:[{}]不可为空或小于0", orderAmount);
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR);
            }
            BoxAlterAmountParam boxAlterAmountParam = new BoxAlterAmountParam();
            boxAlterAmountParam.setCsId(csId);
            boxAlterAmountParam.setCsNick(csNick);
            boxAlterAmountParam.setOrderNo(orderNo);
            boxAlterAmountParam.setReason(reason);
            MultiCurrencyMoney orderMoney = new MultiCurrencyMoney(orderAmount, currencyCode);
            boxAlterAmountParam.setNewAmount(Integer.parseInt(String.valueOf(orderMoney.getCent())));
            boxAlterAmountParam.setPowerbankNo(powerbankNo);
            return boxAlterAmountParam;
        }
    }

    @Login
    @Authentication(permissionCode = "20004")
    @PostMapping("/app/{version}/agent/order/pause")
    public BizResult pause(@RequestBody BoxOrderOperateParam refundOrderParam) {
        OssUserDTO userDTO = getUser();
        if (Objects.isNull(userDTO) || Objects.isNull(userDTO.getUserId()) || org.apache.commons.lang.StringUtils.isBlank(userDTO.getName())) {
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.FORBIDDEN);
        }
        log.info("|{}|{}|{}|{}|{}|{}",
                DateTime.now().toString("yyyy-MM-dd HH:mm:ss"),
                refundOrderParam.getOrderNo(),
                userDTO.getUserId().intValue(),
                userDTO.getName(),
                "盒子订单暂停计费",
                JSON.toJSONString(refundOrderParam)
        );
        OssBoxOrderDTO boxOrderDTO = hermesBoxManager.getOrderDetail(refundOrderParam.getOrderNo(), userDTO.getCooperatorId());
        refundOrderParam.setPowerbankNo(boxOrderDTO.getPowerbankNo());
        return hermesBoxManager.pause(refundOrderParam.transToBaseOperateParam(getUserId().intValue(), getUser().getName()));
    }

    @Login
    @Authentication(permissionCode = "20005")
    @PostMapping("/app/{version}/agent/order/end")
    public BizResult end(@RequestBody BoxOrderOperateParam refundOrderParam) {
        OssUserDTO userDTO = getUser();
        if (Objects.isNull(userDTO) || Objects.isNull(userDTO.getUserId()) || org.apache.commons.lang.StringUtils.isBlank(userDTO.getName())) {
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.FORBIDDEN);
        }

        BaseHermesOrderController.OrderDetailParam orderDetailParam = new BaseHermesOrderController.OrderDetailParam();
        orderDetailParam.setCooperatorId(userDTO.getCooperatorId());

        OssBoxOrderDTO boxOrderDTO = hermesBoxManager.getOrderDetail(refundOrderParam.getOrderNo(), userDTO.getCooperatorId());
        BoxAlterAmountParam param = refundOrderParam.transToAlterAmountParam(userDTO.getUserId().intValue(), userDTO.getName(), boxOrderDTO.getCurrency());
        //日志格式：{基础日志}|{操作时间}|{主键编号}|{操作人id}|{操作人花名}|{操作类型}|{参数}
        log.info("|{}|{}|{}|{}|{}|{}",
                DateTime.now().toString("yyyy-MM-dd HH:mm:ss"),
                param.getOrderNo(),
                userDTO.getUserId().intValue(),
                userDTO.getName(),
                "盒子订单结束计费",
                JSON.toJSONString(param)
        );
        return hermesBoxManager.end(param);
    }

    @Login
    @Authentication(permissionCode = "20007")
    @PostMapping("/app/{version}/agent/order/modifyAmount")
    public BizResult modifyAmount(@RequestBody BoxOrderOperateParam operateParam) {
        OssUserDTO userDTO = getUser();
        if (Objects.isNull(userDTO) || Objects.isNull(userDTO.getUserId()) || org.apache.commons.lang.StringUtils.isBlank(userDTO.getName())) {
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.FORBIDDEN);
        }

        OssBoxOrderDTO boxOrderDTO = hermesBoxManager.getOrderDetail(operateParam.getOrderNo(), userDTO.getCooperatorId());
        BoxAlterAmountParam param = operateParam
                .transToAlterAmountParam(userDTO.getUserId().intValue(), userDTO.getName(), boxOrderDTO.getCurrency());
        //日志格式：{基础日志}|{操作时间}|{主键编号}|{操作人id}|{操作人花名}|{操作类型}|{参数}
        log.info("|{}|{}|{}|{}|{}|{}",
                DateTime.now().toString("yyyy-MM-dd HH:mm:ss"),
                param.getOrderNo(),
                userDTO.getUserId().intValue(),
                userDTO.getName(),
                "盒子订单改价",
                JSON.toJSONString(param)
        );
        return hermesBoxManager.alterAmount(param, userDTO);
    }

    @Login
    @Authentication(permissionCode = "20008")
    @PostMapping("/app/{version}/agent/order/refund")
    public BizResult refund(@RequestBody RefundOrderParam refundOrderParam) {
        OssUserDTO userDTO = getUser();
        if (Objects.isNull(userDTO) || Objects.isNull(userDTO.getUserId()) || StringUtils.isBlank(userDTO.getName())) {
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.FORBIDDEN);
        }
        OssBoxOrderDTO boxOrderDTO = hermesBoxManager.getOrderDetail(refundOrderParam.getOrderNo(), userDTO.getCooperatorId());
//        refundOrderParam.setRefundAmount(boxOrderDTO.getOrderAmount().doubleValue());
        if (refundOrderParam.getRefundAmount() == null) {
            MultiCurrencyMoney refundMoney = new MultiCurrencyMoney();
            refundMoney.setCent(boxOrderDTO.getPayAmount());
            refundMoney.setCurrencyCode(boxOrderDTO.getCurrency());
            refundOrderParam.setRefundAmount(refundMoney.getAmount().doubleValue());
        }
        CsRefundParam param = refundOrderParam.transToCsRefundParam(userDTO.getUserId(), userDTO.getName(), boxOrderDTO.getCurrency(), refundSwitch);
//        param.setApplyAmount((int)refundMoney.getCent());
        //日志格式：{基础日志}|{操作时间}|{主键编号}|{操作人id}|{操作人花名}|{操作类型}|{参数}
        log.info("|{}|{}|{}|{}|{}|{}",
                DateTime.now().toString("yyyy-MM-dd HH:mm:ss"),
                param.getOrderNo(),
                userDTO.getUserId().intValue(),
                userDTO.getName(),
                "盒子订单原路退款",
                JSON.toJSONString(param)
        );
        return hermesBoxManager.csRefund(param, userDTO);
    }

}