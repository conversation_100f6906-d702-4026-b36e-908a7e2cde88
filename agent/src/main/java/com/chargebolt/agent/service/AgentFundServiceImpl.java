/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.agent.service;

import com.chargebolt.agent.response.AgentFundAccountResponse;
import com.chargebolt.fund.api.response.MchFundAccountResponse;
import com.chargebolt.fund.service.FundAccountService;
import com.chargebolt.response.agent.AppAgentResponse;
import com.chargebolt.service.agent.AgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentFundServiceImpl.java, v 1.0 2024-03-22 9:32 AM Exp $
 */
@Slf4j
@Service
public class AgentFundServiceImpl implements AgentFundService {

    private final FundAccountService fundAccountService;
    private final AgentService agentService;

    public AgentFundServiceImpl(ObjectProvider<FundAccountService> fundAccountServiceProvider,
                                ObjectProvider<AgentService> agentServiceProvider) {
        this.fundAccountService= fundAccountServiceProvider.getIfUnique();
        this.agentService= agentServiceProvider.getIfUnique();
    }
    @Override
    public AgentFundAccountResponse getFundAccount(final Long agentId) {
        AgentFundAccountResponse response= new AgentFundAccountResponse();
        response.setAgentId(agentId);
        AppAgentResponse agentResponse= agentService.getAgentDetail(agentId);
        AppAgentResponse parentAgentResponse= agentService.getAgentDetail(agentResponse.getParentId());
        response.setAgentName(agentResponse.getAgentName());
        response.setSettleId(agentResponse.getParentId());
        response.setSettleName(Objects.isNull(parentAgentResponse)?"":parentAgentResponse.getAgentName());

        MchFundAccountResponse mchFundAccountResponse=fundAccountService.getAgentFundAccount(agentId);
        response.setAccountNo(mchFundAccountResponse.getAccountNo());
//        response.setCurrencyCode(mchFundAccountResponse.getCurrencyPrefix());
        response.setAmount(mchFundAccountResponse.getAmount());
        response.setAmountYuan(mchFundAccountResponse.getAmountYuan());
        response.setAmountYuanFormatted(mchFundAccountResponse.getAmountYuanFormatted());
        response.setFrozenAmount(mchFundAccountResponse.getFrozenAmount());
        response.setFrozenAmountYuan(mchFundAccountResponse.getFrozenAmountYuan());
        response.setFrozenAmountYuanFormatted(mchFundAccountResponse.getFrozenAmountYuanFormatted());
        response.setPrincipalName(agentResponse.getSellerName());
        response.setPrincipalMobile(agentResponse.getSellerMobile());
        return response;
    }
}