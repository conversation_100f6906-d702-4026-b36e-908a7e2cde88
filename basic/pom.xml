<?xml version="1.0"?>
<project
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chargebolt</groupId>
    <artifactId>ezreal</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>basic</artifactId>
  <name>basic</name>
  <version>1.0-SNAPSHOT</version>
  <packaging>jar</packaging>
  <url>http://maven.apache.org</url>


  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
  </properties>


  <dependencies>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>alibabacloud-alimt20181012</artifactId>
      <version>1.0.3</version>
    </dependency>
    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>theseus</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>pheidi-client</artifactId>
      <version>0.0.4.release</version>
    </dependency>
    <dependency>
      <groupId>cn.idev.excel</groupId>
      <artifactId>fastexcel</artifactId>
      <version>1.1.0</version>
    </dependency>
    <!-- spring 依赖 start -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-loadbalancer</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <!-- spring 依赖 end -->

    <!-- 三方包依赖 start -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.sun</groupId>
          <artifactId>tools</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.sun</groupId>
          <artifactId>jconsole</artifactId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
    </dependency>

    <!--    <dependency>-->
    <!--      <groupId>io.springfox</groupId>-->
    <!--      <artifactId>springfox-boot-starter</artifactId>-->
    <!--    </dependency>-->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-ui</artifactId>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.houbb</groupId>
      <artifactId>opencc4j</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
    </dependency>

    <!-- mq -->
    <dependency>
      <groupId>org.springframework.kafka</groupId>
      <artifactId>spring-kafka</artifactId>
    </dependency>

    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
    </dependency>

    <dependency>
      <groupId>com.aliyun.openservices</groupId>
      <artifactId>aliyun-log</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>

    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
    </dependency>
    <!-- excel -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>ooxml-schemas</artifactId>
    </dependency>


    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.github.pagehelper</groupId>
      <artifactId>pagehelper</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.rhwayfun</groupId>
      <artifactId>spring-boot-rocketmq-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-validator</groupId>
      <artifactId>commons-validator</artifactId>
    </dependency>
    <!-- 三方包依赖 end -->

    <!-- 二方包依赖 start -->
    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>commons-eden</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>poseidon-client</artifactId>
    </dependency>

    <dependency>
      <groupId>so.dian.apollo</groupId>
      <artifactId>apollo-client</artifactId>
    </dependency>

    <dependency>
      <groupId>so.dian.xdcloud</groupId>
      <artifactId>xdcloud-ble</artifactId>
    </dependency>
    <dependency>
      <groupId>so.dian.xdcloud</groupId>
      <artifactId>device-action</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>athena-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>hera-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>hades-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>kronos-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>ezreal-client</artifactId>
    </dependency>

    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-lang</artifactId>
    </dependency>

    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-template</artifactId>
    </dependency>

    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>log-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.meidalife</groupId>
      <artifactId>cybertron-client</artifactId>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.searchbox</groupId>
      <artifactId>jest</artifactId>
    </dependency>
    <!-- 二方包依赖 end -->

    <!-- 单元测试 start -->
    <dependency>
      <groupId>com.belerweb</groupId>
      <artifactId>pinyin4j</artifactId>
      <version>2.5.1</version>
      <scope>test</scope>
    </dependency>

    <!-- 单元测试 end -->
    <!-- ========  ========  ========  ========  ========  ========  ========  ========  ========  ========
    ======== -->

    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>pheidi-client-starter</artifactId>
      <version>0.0.5-SNAPSHOT</version>
    </dependency>
  </dependencies>

  <build>
    <finalName>ezreal-${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>3.4</version>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <!-- 输出路径 -->
          <outputDirectory>../target/</outputDirectory>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>build-info</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>


</project>