package com.chargebolt.aeacus.api.auth;

import com.chargebolt.aeacus.dto.OssAuthenticationDTO;
import com.chargebolt.aeacus.dto.OssMerchantUserDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.commons.eden.entity.BizResult;

public interface OssAuthenticationService {

    @RequestMapping(value = "/{version}/authentication/login", method = RequestMethod.POST)
    BizResult<OssUserDTO> checkLogin(@PathVariable(value = "version") String version, @RequestBody OssAuthenticationDTO ossAuthenticationDTO);
    @RequestMapping(value = "/{version}/authentication/permission", method = RequestMethod.POST)
    BizResult<Boolean> checkPermission(@PathVariable(value = "version") String version, @RequestBody OssAuthenticationDTO ossAuthenticationDTO);
    @RequestMapping(value = "/{version}/authentication/merchantCheckLogin", method = RequestMethod.POST)
    BizResult<OssMerchantUserDTO> merchantCheckLogin(@PathVariable(value = "version") String version, @RequestBody OssAuthenticationDTO ossAuthenticationDTO);
}
