package com.chargebolt.aeacus.api.auth;

import com.chargebolt.aeacus.dto.OssMerchantUserDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.dto.OssUserLoginDTO;
import com.chargebolt.aeacus.dto.OssUserLogoutDTO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

public interface OssLoginService {

    @RequestMapping(value = "/{version}/user/login", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult<OssUserDTO> login(@PathVariable(value = "version") String version, @RequestBody OssUserLoginDTO ossUserLoginDTO);

    @RequestMapping(value = "/{version}/user/logout", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult logout(@PathVariable(value = "version") String version, @RequestBody OssUserLogoutDTO ossUserLogoutDTO);

    @RequestMapping(value = "/{version}/user/login/sendSmsToken", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult sendSmsToken(@PathVariable(value = "version") String version, @RequestParam(value = "mobile") String mobile);

    @RequestMapping(value = "/{version}/user/login/checkSmsToken", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult checkSmsToken(@PathVariable(value = "version") String version, @RequestBody OssUserLoginDTO ossUserLoginDTO);

    @RequestMapping(value = "/{version}//merchant/login", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult<OssMerchantUserDTO> merchantLogin(@PathVariable(value = "version") String version, @RequestBody OssUserLoginDTO ossUserLoginDTO);

    @RequestMapping(value = "/{version}/merchant/logout", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult merchantLogout(@PathVariable(value = "version") String version, @RequestBody OssUserLogoutDTO ossUserLogoutDTO);
}
