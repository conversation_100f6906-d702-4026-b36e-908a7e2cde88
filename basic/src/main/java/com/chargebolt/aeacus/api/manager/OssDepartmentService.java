package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.department.OssDepartmentCreateDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentDeleteDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentUpdateDTO;
import java.io.UnsupportedEncodingException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.commons.eden.entity.BizResult;

public interface OssDepartmentService {

    @RequestMapping(value = "/{version}/department/create", method = RequestMethod.POST)
    BizResult<Boolean> create(@PathVariable(value = "version") String version, @RequestBody OssDepartmentCreateDTO createDTO);

    @RequestMapping(value = "/{version}/department/update", method = RequestMethod.POST)
    BizResult<Boolean> update(@PathVariable(value = "version") String version, @RequestBody OssDepartmentUpdateDTO updateDTO);

    @RequestMapping(value = "/{version}/department/delete", method = RequestMethod.POST)
    BizResult<Boolean> delete(@PathVariable(value = "version") String version, @RequestBody OssDepartmentDeleteDTO deleteDTO);

    @RequestMapping(value = "/{version}/department/query", method = RequestMethod.POST)
    BizResult<OssDepartmentDTO> query(@PathVariable(value = "version") String version);
}
