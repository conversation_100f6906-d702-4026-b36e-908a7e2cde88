package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.dto.group.OssGroupQueryDTO;
import java.util.List;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.commons.eden.entity.BizResult;

public interface OssGroupService {

    @RequestMapping(value = "/{version}/group/query", method = RequestMethod.POST)
    BizResult<List<OssGroupDTO>> query(@PathVariable(value = "version") String version, @RequestBody OssGroupQueryDTO ossGroupQueryDTO);
}
