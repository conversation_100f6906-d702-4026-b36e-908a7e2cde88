package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.dto.OssMobileDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface OssIconService {

    @RequestMapping(value = "/icon/getAllIcons", method = RequestMethod.GET)
    BizResult<List<OssIconDTO>> getAllIcons();

    @RequestMapping(value = "/icon/getIconsByRoleId", method = RequestMethod.GET)
    BizResult<List<OssIconDTO>> getIconsByRoleId(@RequestParam(value = "roleId") Long roleId);
}
