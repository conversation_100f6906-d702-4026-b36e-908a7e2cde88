package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.*;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface OssMerchantUserService {

    @RequestMapping(value = "/{version}/merchant/user/queryById", method = RequestMethod.GET)
    BizResult<OssMerchantUserDTO> queryById(@PathVariable(value = "version") String version, @RequestParam(value = "id") Long id);

    @RequestMapping(value = "/{version}/merchant/user/queryByMobile", method = RequestMethod.GET)
    BizResult<OssMerchantUserDTO> queryByMobile(@PathVariable(value = "version") String version, @RequestParam(value = "mobile") String mobile);

    @RequestMapping(value = "/{version}/merchant/user/create", method = RequestMethod.POST)
    BizResult<Long> create(@PathVariable(value = "version") String version, @RequestBody OssMerchantUserDTO ossMerchantUserDTO);

    @RequestMapping(value = "/{version}/merchant/user/updatePassword", method = RequestMethod.POST)
    BizResult<Boolean> updatePassword(@PathVariable(value = "version") String version, @RequestBody OssUserPasswordOpDTO passwordOpDTO);

    @RequestMapping(value = "/{version}/merchant/user/queryByIds", method = RequestMethod.POST)
    BizResult<List<OssMerchantUserDTO>> queryByIds(@PathVariable(value = "version") String version, @RequestBody List<Long> ids);

    @RequestMapping(value = "/{version}/merchant/user/queryByName", method = RequestMethod.GET)
    BizResult<OssMerchantUserDTO> queryByName(@PathVariable(value = "version") String version, @RequestParam(value = "name") String name);

    @RequestMapping(value = "/{version}/merchant/user/updateStatus", method = RequestMethod.GET)
    BizResult updateStatus(@PathVariable(value = "version") String version, @RequestParam(value = "name") String name, @RequestParam(value = "status") Integer status, @RequestParam(value = "updator") Long updator);

    @RequestMapping(value = "/{version}/merchant/user/deleteById", method = RequestMethod.DELETE)
    BizResult deleteById(@PathVariable(value = "version") String version,  @RequestParam(value = "id") Long id);
}
