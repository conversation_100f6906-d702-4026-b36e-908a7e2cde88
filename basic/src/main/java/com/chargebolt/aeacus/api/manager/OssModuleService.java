package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.OssModuleDTO;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface OssModuleService {

    @RequestMapping(value = "/{version}/module/getModulesByUser", method = RequestMethod.GET)
    BizResult<List<OssModuleDTO>> getModulesByUser(@PathVariable(value = "version") String version, @RequestParam(value = "userId") Long userId);
}
