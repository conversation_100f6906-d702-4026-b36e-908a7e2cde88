package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.dto.OssRolePermissionAddDTO;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface OssPermissionService {

    @RequestMapping(value = "/{version}/permission/getIconsByUser", method = RequestMethod.GET)
    BizResult<List<OssPermissionDTO>> getIconsByUser(@PathVariable(value = "version") String version, @RequestParam(name = "userId") Long userId);

    @RequestMapping(value = "/{version}/permission/add", method = RequestMethod.POST)
    BizResult<Boolean> add(@PathVariable(value = "version") String version, @RequestBody OssPermissionDTO ossPermissionDTO);

    @RequestMapping(value = "/{version}/permission/update", method = RequestMethod.POST)
    BizResult<Boolean> update(@PathVariable(value = "version") String version, @RequestBody OssPermissionDTO ossPermissionDTO);

    @RequestMapping(value = "/{version}/permission/addPermissionToRole", method = RequestMethod.POST)
    BizResult<Boolean> addPermissionToRole(@PathVariable(value = "version") String version, @RequestBody OssRolePermissionAddDTO ossRolePermissionAddDTO);
}
