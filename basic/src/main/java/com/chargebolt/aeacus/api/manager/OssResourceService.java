package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.dto.OssMobileDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface OssResourceService {

    @RequestMapping(value = "/resource/getIconsByUser", method = RequestMethod.GET)
    BizResult<List<OssIconDTO>> getIconsByUser(@RequestParam(value = "userId") Long userId,
            @RequestParam(value = "moduleId") Long moduleId);

    @RequestMapping(value = "/resource/getSupportMobileNations", method = RequestMethod.GET)
    BizResult<List<OssMobileDTO>> getSupportMobileNations();
}
