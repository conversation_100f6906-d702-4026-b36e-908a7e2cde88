package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.OssRoleCreateDTO;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.dto.OssRoleDeleteDTO;
import com.chargebolt.aeacus.dto.OssRoleQueryDTO;
import com.chargebolt.aeacus.dto.OssRoleUpdateDTO;
import com.chargebolt.aeacus.dto.OssRoleUserAuthDTO;
import com.chargebolt.aeacus.dto.PageData;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface OssRoleService {

    @RequestMapping(value = "/{version}/role/getByUser", method = RequestMethod.GET)
    BizResult<List<OssRoleDTO>> getRolesByUser(@PathVariable(value = "version") String version, @RequestParam(value = "userId") Long userId);

    @RequestMapping(value = "/{version}/role/add", method = RequestMethod.POST)
    BizResult<Boolean> add(@PathVariable(value = "version") String version, @RequestBody OssRoleCreateDTO ossRoleCreateDTO);

    @RequestMapping(value = "/{version}/role/update", method = RequestMethod.POST)
    BizResult<Boolean> update(@PathVariable(value = "version") String version, @RequestBody OssRoleUpdateDTO ossRoleUpdateDTO);

    @RequestMapping(value = "/{version}/role/authToUesr", method = RequestMethod.POST)
    BizResult<Boolean> authToUesr(@PathVariable(value = "version") String version, @RequestBody OssRoleUserAuthDTO ossRoleUserAuthDTO);

    @RequestMapping(value = "/{version}/role/queryByPage", method = RequestMethod.POST)
    BizResult<PageData<OssRoleDTO>> queryByPage(@PathVariable(value = "version") String version, @RequestBody OssRoleQueryDTO queryDTO);

    @RequestMapping(value = "/{version}/role/queryAll", method = RequestMethod.POST)
    BizResult<List<OssRoleDTO>> query(@PathVariable(value = "version") String version);

    @RequestMapping(value = "/{version}/role/queryById", method = RequestMethod.POST)
    BizResult<OssRoleDTO> queryById(@PathVariable(value = "version") String version, @RequestParam("roleId") Long roleId);

    @RequestMapping(value = "/{version}/role/delete", method = RequestMethod.POST)
    BizResult<Boolean> delete(@PathVariable(value = "version") String version, @RequestBody OssRoleDeleteDTO roleDeleteDTO);
}
