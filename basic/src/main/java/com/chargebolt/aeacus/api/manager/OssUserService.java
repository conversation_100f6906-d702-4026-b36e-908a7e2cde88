package com.chargebolt.aeacus.api.manager;

import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.dto.OssUserOpDTO;
import com.chargebolt.aeacus.dto.OssUserPasswordOpDTO;
import com.chargebolt.aeacus.dto.OssUserQueryDTO;
import com.chargebolt.aeacus.dto.PageData;
import java.io.UnsupportedEncodingException;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface OssUserService {

    @RequestMapping(value = "/{version}/user/queryByIds", method = RequestMethod.POST)
    BizResult<List<OssUserDTO>> queryByIds(@PathVariable(value = "version") String version, @RequestBody List<Long> ids);

    @RequestMapping(value = "/{version}/user/queryById", method = RequestMethod.GET)
    BizResult<OssUserDTO> queryById(@PathVariable(value = "version") String version, @RequestParam(value = "id") Long id);

    @RequestMapping(value = "/{version}/user/queryByName", method = RequestMethod.GET)
    BizResult<OssUserDTO> queryByName(@PathVariable(value = "version") String version, @RequestParam(value = "name") String name);

    @RequestMapping(value = "/{version}/user/queryByCooperator", method = RequestMethod.GET)
    BizResult<List<OssUserDTO>> queryByCooperator(@PathVariable(value = "version") String version, @RequestParam(value = "cooperatorId") Long cooperatorId);

    @RequestMapping(value = "/{version}/user/queryByMobile", method = RequestMethod.GET)
    BizResult<OssUserDTO> queryByMobile(@PathVariable(value = "version") String version, @RequestParam(value = "mobile") String mobile);

    @RequestMapping(value = "/{version}/user/create", method = RequestMethod.POST)
    BizResult<Long> create(@PathVariable(value = "version") String version, @RequestBody OssUserOpDTO ossUserDTO);

    @RequestMapping(value = "/{version}/user/update", method = RequestMethod.POST)
    BizResult<Boolean> update(@PathVariable(value = "version") String version, @RequestBody OssUserOpDTO ossUserDTO);

    @RequestMapping(value = "/{version}/user/update/password", method = RequestMethod.POST)
    BizResult<Boolean> updatePassword(@PathVariable(value = "version") String version, @RequestBody OssUserPasswordOpDTO passwordOpDTO);

    @RequestMapping(value = "/{version}/user/query", method = RequestMethod.POST)
    BizResult<PageData<OssUserDTO>> query(@PathVariable(value = "version") String version,
            @RequestBody OssUserQueryDTO queryDTO);

    @RequestMapping(value = "/{version}/user/queryDpmCreatorByCode", method = RequestMethod.GET)
    BizResult<List<OssUserDTO>> queryDpmCreatorByCode(@PathVariable(value = "version") String version, @RequestParam(name = "dpmCode") String dpmCode, @RequestParam(name = "dataPerType") Integer dataPerType);


}
