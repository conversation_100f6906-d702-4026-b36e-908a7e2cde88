package com.chargebolt.aeacus.cache;

import com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import so.dian.eros.cache.RedisClient;

@Component
public class DepartmentCache {

    @Resource
    private RedisClient redisClient;

    private static final String DEPARTMENTMAPKEY = "structure";

    public Integer getIncreaseLong(String key){
        return redisClient.incr(CacheEnum.DEPARTMENT.name(),key,1,null);
    }

    public void initIncreaseLong(String key,Integer initValue){
        if(!redisClient.exists(CacheEnum.DEPARTMENT.name(),key)){
            return;
        }
        int value = redisClient.get(CacheEnum.DEPARTMENT.name(),key,Integer.class);
        if(value >= initValue){
            return;
        }
        remove(key);
        redisClient.incr(CacheEnum.DEPARTMENT.name(),key,1,initValue-1);
    }

    public void remove(String key){
        redisClient.remove(CacheEnum.DEPARTMENT.name(),key);
    }

    public void addDepartment(Long departmentId,OssDepartmentDO departmentDO){
        redisClient.hashSet(CacheEnum.DEPARTMENT.name(),DEPARTMENTMAPKEY,departmentId.toString(),departmentDO);
    }

    public void addAllDepartment(Map<Long,OssDepartmentDO> map){
        redisClient.hashSetAll(CacheEnum.DEPARTMENT.name(),DEPARTMENTMAPKEY,map);
    }

    public OssDepartmentDO getDepartment(Long departmentId){
        return redisClient.hashGet(CacheEnum.DEPARTMENT.name(),DEPARTMENTMAPKEY,departmentId.toString(),OssDepartmentDO.class);
    }

    public Map<String,OssDepartmentDO> getAllDepartMent(){
        return redisClient.hashEntries(CacheEnum.DEPARTMENT.name(),DEPARTMENTMAPKEY,OssDepartmentDO.class);
    }

    public void removeHashDepartment(List<Long> departmentIds){
        departmentIds.forEach(item-> redisClient.hashDelete(CacheEnum.DEPARTMENT.name(),DEPARTMENTMAPKEY,item.toString()));
    }

    public void removeMap(){
        redisClient.remove(CacheEnum.DEPARTMENT.name(),DEPARTMENTMAPKEY);
    }

}
