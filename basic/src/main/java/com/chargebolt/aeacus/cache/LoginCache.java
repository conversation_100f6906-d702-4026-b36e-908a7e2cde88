package com.chargebolt.aeacus.cache;

import javax.annotation.Resource;
import lombok.Data;
import org.springframework.stereotype.Component;
import so.dian.eros.cache.RedisClient;

@Component
public class LoginCache {

    @Data
    public static class LoginCacheDO{
        private Integer checkTimes;

        public LoginCacheDO(){
            this.checkTimes = 1;
        }
    }

    @Resource
    private RedisClient redisClient;

    public LoginCacheDO getLoginCacheDO(String name){
        return redisClient.get(CacheEnum.LOGIN_CACHE.name(), name, LoginCacheDO.class);
    }

    public void setLoginCacheDO(String name, LoginCacheDO loginCacheDO){
        redisClient.set(CacheEnum.LOGIN_CACHE.name(), name, loginCacheDO, 30 * 60);
    }

    public void removeLoginCacheDO(String name) {
        redisClient.remove(CacheEnum.LOGIN_CACHE.name(), name);
    }
}
