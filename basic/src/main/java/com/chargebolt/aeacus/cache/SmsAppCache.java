package com.chargebolt.aeacus.cache;

import lombok.Data;
import org.springframework.stereotype.Component;
import so.dian.eros.cache.RedisClient;
import so.dian.talos.pojo.entity.MerchantDO;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class SmsAppCache {


    @Data
    public static class SmsCacheDO{

        private String token;
        private Long createTime;
        private Integer sendTimes;
        private Integer checkTimes;
        private String headerToken;
        private Long tokenCreateTime;
    }

    @Resource
    private RedisClient redisClient;

    public SmsCacheDO getSmsCacheDO(String mobile){
        return redisClient.get(CacheEnum.APP_SMS_CACHE.name(), mobile, SmsCacheDO.class);
    }

    public void setSmsCacheDO(String mobile, SmsCacheDO smsCacheDO){
        redisClient.set(CacheEnum.APP_SMS_CACHE.name(), mobile, smsCacheDO, 5 * 60);
    }

}
