package com.chargebolt.aeacus.cache;

import lombok.Data;
import org.springframework.stereotype.Component;
import so.dian.eros.cache.RedisClient;
import javax.annotation.Resource;

@Component
public class SmsCache {

    @Data
    public static class SmsCacheDO{

        private String token;
        private Long createTime;
        private Integer sendTimes;
        private Integer checkTimes;
    }

    @Resource
    private RedisClient redisClient;

    public SmsCacheDO getSmsCacheDO(String mobile){
        return redisClient.get(CacheEnum.SMS_CACHE.name(), mobile, SmsCacheDO.class);
    }

    public void setSmsCacheDO(String mobile, SmsCacheDO smsCacheDO){
        redisClient.set(CacheEnum.SMS_CACHE.name(), mobile, smsCacheDO, 30 * 60);
    }

    public void evictSmsCacheDO(String mobile){
        redisClient.remove(CacheEnum.SMS_CACHE.name(), mobile);
    }

    public void removeSmsCache(String mobile) {
        redisClient.remove(CacheEnum.SMS_CACHE.name(), mobile);
    }
}
