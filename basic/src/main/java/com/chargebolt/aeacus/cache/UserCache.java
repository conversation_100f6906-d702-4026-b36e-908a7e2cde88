package com.chargebolt.aeacus.cache;

import com.chargebolt.aeacus.common.constant.CommonConstants;
import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.eros.cache.RedisClient;
import javax.annotation.Resource;

@Component
public class UserCache {

    @Resource
    private RedisClient redisClient;
    // 30天登录有效期
    private static final Long DURATION_IN_SECOND=2592000L;

    public void setUser(OssUserDO ossUserDO){
        redisClient.set(CacheEnum.USER_LOGIN_STATUS.name(), String.valueOf(ossUserDO.getId()), ossUserDO, DURATION_IN_SECOND);
    }

    public void removeUser(Long userId){
        redisClient.remove(CacheEnum.USER_LOGIN_STATUS.name(), String.valueOf(userId));
    }

    public OssUserDO getUser(Long userId){
        OssUserDO ossUserDO = redisClient.get(CacheEnum.USER_LOGIN_STATUS.name(), String.valueOf(userId), OssUserDO.class);
        if (ossUserDO == null){
            return null;
        }
        return ossUserDO;
    }

    public void setMerchantUser(OssMerchantUserDO ossMerchantUserDO){
        redisClient.set(CacheEnum.USER_LOGIN_STATUS.name(), CommonConstants.MERCHANT + ossMerchantUserDO.getId(), ossMerchantUserDO, DURATION_IN_SECOND);
    }

    public void removeMerchantUser(Long userId){
        redisClient.remove(CacheEnum.USER_LOGIN_STATUS.name(), String.valueOf(userId));
    }

    public OssMerchantUserDO getMerchantUser(Long userId){
        return redisClient.get(CacheEnum.USER_LOGIN_STATUS.name(), CommonConstants.MERCHANT + userId, OssMerchantUserDO.class);
    }
}
