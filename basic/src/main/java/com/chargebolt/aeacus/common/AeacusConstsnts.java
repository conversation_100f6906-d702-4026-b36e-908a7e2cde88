package com.chargebolt.aeacus.common;

public class AeacusConstsnts {

    public static final Integer LOGIN_TYPE_PWD = 1;
    public static final Integer LOGIN_TYPE_SMS = 2;

    public static final Integer USER_TYPE_BD = 1;
    public static final Integer USER_TYPE_AGENT_BOSS = 2;
    public static final Integer USER_TYPE_MERCHANT = 3;

    public static final Integer USER_DENIDE_MULTILOGIN = 0;
    public static final Integer USER_ALLOW_MULTILOGIN = 1;

    public static final Integer USER_PASSWORD_RESET = 1;
    public static final Integer USER_PASSWORD_UPDATE = 2;

    public static final String USER_INIT_PASSWORD = "abc123";

    public static final String SUPER_PERMISSION_CODE = "0";
    /**
     * 管理员角色ID
     */
    public static final Long SUPER_ROLE_ID = 0L;

    public static final Integer NORMAL = 0;
    public static final Integer DELETED = 1;

    public static final Integer DEPARTMENT_MAX_LEVEL = 5;

    public static final Long DEPARTMENT_FIRST_STAGE_ID = 0L;

    // 参数常量，已存在的权限
    public static final String PARAM_RP = "removePermission";
    // 参数常量，新增的权限
    public static final String PARAM_NP = "newPermission";
    // 参数常量，已存在的icon
    public static final String PARAM_RI = "removeIcon";
    // 参数常量，新增的icon
    public static final String PARAM_NI = "newIcon";
    //0可用 1禁用
    public static final Integer STATUS_ENABLE = 0;
    public static final Integer STATUS_DISABLE = 1;
    /**
     * 用户数据权限等级
     * 1.平台用户
     */
    public static final Integer USER_DATA_AUTHORITY_LEVEL_1 = 1;
    /**
     * 用户数据权限等级
     * 2.代理商
     */
    public static final Integer USER_DATA_AUTHORITY_LEVEL_2 = 2;
    /**
     * 用户数据权限等级
     * 3.员工
     */
    public static final Integer USER_DATA_AUTHORITY_LEVEL_3 = 3;
}
