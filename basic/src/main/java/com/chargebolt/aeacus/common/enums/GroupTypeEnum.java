package com.chargebolt.aeacus.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;


@Getter
@AllArgsConstructor
public enum GroupTypeEnum implements EnumInterface<GroupTypeEnum> {
    PERMISSION(1, "权限"),
    ;
    private Integer code;
    private String desc;


    @Override
    public GroupTypeEnum getDefault() {
        return GroupTypeEnum.PERMISSION;
    }

    public static GroupTypeEnum explain(Integer code) {
        for (GroupTypeEnum groupTypeEnum : GroupTypeEnum.values()) {
            if (groupTypeEnum.getCode().equals(code)) {
                return groupTypeEnum;
            }
        }
        return null;
    }
}
