package com.chargebolt.aeacus.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;


@Getter
@AllArgsConstructor
public enum ReadonlFlagEnum implements EnumInterface<ReadonlFlagEnum> {
    ALLOW_UPDATE(0, "允许修改"),
    CANNOT_UPDATE(1, "不可修改"),
    ;
    private Integer code;
    private String desc;


    @Override
    public ReadonlFlagEnum getDefault() {
        return ReadonlFlagEnum.ALLOW_UPDATE;
    }


}
