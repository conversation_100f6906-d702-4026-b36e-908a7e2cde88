package com.chargebolt.aeacus.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;


@Getter
@AllArgsConstructor
public enum UserStatusEnum implements EnumInterface<UserStatusEnum> {
    NORMAL(0, "可用"),
    DISABLED(1, "禁用"),
    ;
    private Integer code;
    private String desc;


    @Override
    public UserStatusEnum getDefault() {
        return UserStatusEnum.NORMAL;
    }

}
