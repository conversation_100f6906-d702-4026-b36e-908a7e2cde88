package com.chargebolt.aeacus.common.exception;

import so.dian.commons.eden.enums.EnumInterface;

public enum AuthenticationExceEnum implements EnumInterface {

    INVALID_PERMISSION_CODE(120100),
    AUTHENTICATION_FORBIDDEN(120101),

    ;

    AuthenticationExceEnum(Integer code) {
        this.code = code;
    }

    private Integer code;

    @Override
    public String getDesc() {
        return String.format("${EXEC:%s}", name());
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public EnumInterface getDefault() {
        return null;
    }
}
