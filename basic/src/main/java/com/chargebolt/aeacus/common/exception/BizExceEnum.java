package com.chargebolt.aeacus.common.exception;

import so.dian.commons.eden.enums.EnumInterface;

public enum BizExceEnum implements EnumInterface {

    USER_ID_NULL(120300),
    USER_MOBILE_NULL(120301),
    USER_PASSWORD_NULL(120302),
    USER_COOPERATOR_NULL(120303),
    USER_NAME_NULL(120304),
    USER_INVALID_MOBILE(120305),
    USER_INVALID_ORIPASSWORD(120306),
    USER_DEPARTMENT_CODE_NULL(120307),
    USER_DATA_PERMISSION_NULL(120308),
    USER_CANNOT_UPDATE(120309),
    USER_NAME_LENGTH_ERROR(120310),
    USER_FULL_NAME_NULL(120311),
    USER_NAME_ALREADY_EXSIT(120312),
    USER_OPERATOR_NULL(120313),
    USER_PASSWORD_LENGTH_ERROR(120314),
    USER_PASSWORD_FORMAT_ERROR(120315),
    USER_HAD_BE_DISABLED(120316),
    USER_NOT_EXSIT(120317),
    USER_PASSWORD_SAMEWITH_ORI(120318),
    USER_NAME_FORMAT_ERROR(120319),

    ROLE_MODULE_NULL(120401),
    ROLE_ID_NULL(120402),
    ROLE_NAME_NULL(120403),
    ROLE_OPEARATOR_NULL(120404),
    ROLE_NOT_EXSIT(120405),
    ROLE_ALREADY_INUSE(120406),
    ROLE_CANNOT_OPERATOR(120407),
    ROLE_NAME_INVALID(120408),
    ROLE_NAME_ALREADY_EXSIT(120409),

    PERMISSION_CODE_NULL(120501),
    PERMISSION_TYPE_NULL(120502),
    PERMISSION_CREATOR_NULL(120503),
    PERMISSION_ID_NULL(120504),

    NO_SUCH_OBJECT(120601),
    EXISTS_OBJECT(120602),
    EXISTS_MOBILE(120603),
    EXISTS_USERNAME(120604),
    EXISTS_MERCHANT_USER_NAME(120605),

    DEPARTMENT_PARENT_ID_NULL(120701),
    DEPARTMENT_NAME_NULL(120702),
    DEPARTMENT_OPERATOR_NULL(120703),
    DEPARTMENT_PARENT_NOT_EXSIT(120704),
    DEPARTMENT_CANNOT_DEEPER(120705),
    DEPARTMENT_CANNOT_MORE(120706),
    DEPARTMENT_ID_NULL(120707),
    DEPARTMENT_ALREADY_INUSE(120708),
    DEPARTMENT_NAME_ALREADY_EXSIT(120709),
    DEPARTMENT_NAME_OVERLONG(120710),
    DEPARTMENT_NOT_EXSIT(120711),

    ICON_ID_NULL(120801),
    ;

    BizExceEnum(Integer code) {
        this.code = code;
    }

    private String desc;
    private Integer code;

    @Override
    public String getDesc() {
        return String.format("${EXEC:%s}", name());
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public EnumInterface getDefault() {
        return null;
    }
}
