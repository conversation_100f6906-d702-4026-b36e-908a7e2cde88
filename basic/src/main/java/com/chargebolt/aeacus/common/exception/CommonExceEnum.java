package com.chargebolt.aeacus.common.exception;

import so.dian.commons.eden.enums.EnumInterface;

public enum CommonExceEnum implements EnumInterface {

    INTERNAL_EXCEPTION(120000, ""),
    CHECK_NULL(120001, ""),

    ;

    CommonExceEnum(Integer code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    private String desc;
    private Integer code;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public EnumInterface getDefault() {
        return null;
    }
}
