/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.aeacus.common.exception;

import java.util.List;
import java.util.Locale;

import org.springframework.context.ApplicationContext;

import com.chargebolt.framework.ApplicationContextHolder;
import com.chargebolt.framework.i18n.I18nEnumInterface;
import com.chargebolt.framework.i18n.I18nMessageService;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.mofa3.lang.exception.UnifiedException;

/**
 * 异常 msg 需要支持多语言的时候，使用这个异常类
 *
 * <AUTHOR>
 * @version: I18nMessageException.java, v 1.0 2023-10-27 10:35 AM Exp $
 */
public class I18nMessageException extends UnifiedException {
    @Getter
    private List<String> args;

    /**
     * 自定义异常code
     *
     * @param code  code
     * @param exMsg 提示内容
     */
    public I18nMessageException(final String code, final String exMsg) {
        super(code, exMsg);
    }

    /**
     * 使用EnumInterface构造异常
     *
     * @param enumInterface 枚举接口
     */
    public I18nMessageException(EnumInterface<?> enumInterface) {
        super(enumInterface.getCode().toString(), enumInterface.getDesc());
    }

    /**
     * 使用I18nEnumInterface构造异常
     * 支持国际化枚举接口
     *
     * @param i18nEnumInterface 国际化枚举接口
     */
    public I18nMessageException(I18nEnumInterface i18nEnumInterface) {
        super(i18nEnumInterface.getI18nCode(), i18nEnumInterface.getDefaultMsg());
    }

    public I18nMessageException(EnumInterface<?> enumInterface,String msg) {
        super(enumInterface.getCode().toString(), msg);
    }

    public I18nMessageException(final String code, final String exMsg, final List<String> args) {
        super(code, exMsg);
        this.args = args;
    }

    /**
     * 自定义异常code，抛出异常栈信息
     *
     * @param code             code
     * @param exMsg            提示内容
     * @param contextThrowable 异常信息
     */
    public I18nMessageException(final String code, final String exMsg,
            final Throwable contextThrowable) {
        super(code, exMsg, contextThrowable);
    }

    @Override
    public String getLocalizedMessage() {
        return getLocalizedMessage(null);
    }

    /**
     * 获取指定语言的消息
     * 
     * @param locale 指定的语言环境，如果为null则使用当前线程的语言环境
     * @return 国际化消息
     */
    public String getLocalizedMessage(Locale locale) {
        I18nMessageService messageService = getMessageService();
        if (messageService == null) {
            return getFallbackMessage();
        }

        try {
            // 如果没有指定locale，则获取当前线程的locale
            if (locale == null) {
                locale = messageService.getCurrentLocale();
            }

            // 尝试从配置文件获取消息
            String message = messageService.getMessage(getCode(), locale, getArgs());
            if (message != null) {
                return message;
            }

            // 如果没找到，返回fallback消息
            return getFallbackMessage();

        } catch (Exception e) {
            // 如果获取消息过程中出现异常，返回fallback消息
            return getFallbackMessage();
        }
    }

    private String getFallbackMessage() {
        String message = getMessage();
        if (message != null && !message.trim().isEmpty()) {
            return message;
        }
        return "System Exception";
    }

    /**
     * 获取I18nMessageService实例
     * 从Spring上下文获取（推荐）
     */
    private I18nMessageService getMessageService() {
        try {
            ApplicationContext context = ApplicationContextHolder.getApplicationContext();
            if (context != null) {
                return context.getBean(I18nMessageService.class);
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }

}