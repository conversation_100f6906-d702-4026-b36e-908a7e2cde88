package com.chargebolt.aeacus.common.exception;

import so.dian.commons.eden.enums.EnumInterface;


public enum LoginExceEnum implements EnumInterface{

    USER_NOT_EIXTS(120101),
    INVALID_MOBILE(120102),
    INVALID_PASSWORD(120103),
    INVALID_SMS_TOKEN(120104),
    INVALID_MOBILE_NATIONCODE(120105),


    INVALID_LOGIN_TYPE(120110),

    SMS_TOKEN_SEND_FAILED(120111),
    SMS_TOKEN_SEND_TOO_MUCH_TIMES(120112),
    SMS_TOKEN_SEND_TOO_FREQUENCY(120113),
    SMS_TOKEN_CHECK_FAILED(120114),
    SMS_TOKEN_CHECK_MORE_TIMES(120115),

    PASSWORD_CHECK_MORE_TIMES(120116),


    LOGIN_EXPIRED(120120),

    LOGIN_TOKEN_INVALID(2002);

    LoginExceEnum(Integer code) {
        this.code = code;
    }

    private String desc;
    private Integer code;

    @Override
    public String getDesc() {
        return String.format("${EXEC:%s}", name());
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public EnumInterface getDefault() {
        return null;
    }
}
