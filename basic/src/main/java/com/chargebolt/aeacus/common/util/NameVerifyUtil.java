package com.chargebolt.aeacus.common.util;

public class NameVerifyUtil {
    private static String FORBID_CHARS = "~!@#$%^&*()_+【】、；‘，。、";

    public static boolean verifyForbidChars(String name){
        if(name.contains(FORBID_CHARS)){
            return false;
        }
        return true;
    }

    public static boolean verifyLetterNumber(String name){
        String numberRegex = "^[0-9]+$";
        String stringRegex = "^[a-zA-Z]+$";
        if(name.matches(numberRegex)){
            return false;
        }
        if(name.matches(stringRegex)){
            return false;
        }
        return true;
    }

    public static boolean verifyNumber(String name){
        String regex = "^[0-9]+$";
        return name.matches(regex);
    }
}
