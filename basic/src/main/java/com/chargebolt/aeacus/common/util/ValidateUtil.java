package com.chargebolt.aeacus.common.util;

import java.util.Collection;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.exception.BizException;

public class ValidateUtil {

    public  static void requiredNotNull(Object o,EnumInterface exceEnum) {
        if (o == null) {
            throw BizException.create(exceEnum);
        }
    }

    public  static void requiredNotEmpty(String o,EnumInterface exceEnum) {
        if (StringUtils.isEmpty(o)) {
            throw BizException.create(exceEnum);
        }
    }

    public  static void requiredNotEmpty(Collection o,EnumInterface exceEnum) {
        if (CollectionUtils.isEmpty(o)) {
            throw BizException.create(exceEnum);
        }
    }

    public  static void requiredNull(Object o,EnumInterface exceEnum) {
        if (o != null) {
            throw BizException.create(exceEnum);
        }
    }

    public  static void requiredTrue(boolean o,EnumInterface exceEnum) {
        if (!o) {
            throw BizException.create(exceEnum);
        }
    }

    public  static void  assertEquals(Object o,Object t,EnumInterface exceEnum) {
        if (!o.equals(t)) {
            throw BizException.create(exceEnum);
        }
    }

    public  static void  assertNotEquals(Object o,Object t,EnumInterface exceEnum) {
        if (o.equals(t)) {
            throw BizException.create(exceEnum);
        }
    }

    public static void assertNotInEquals(Object o,EnumInterface exceEnum,Object...t) {
        if (t == null || t.length == 0) {
            return;
        }
        for(Object key : t){
            if (o.equals(key)) {
                return;
            }
        }
        throw BizException.create(exceEnum);
    }

    public static boolean assertNotInEquals(Object o,Object...t) {
        if (t == null || t.length == 0) {
            return false;
        }
        for(Object key : t){
            if (o.equals(key)) {
                return true;
            }
        }
        return false;
    }
}
