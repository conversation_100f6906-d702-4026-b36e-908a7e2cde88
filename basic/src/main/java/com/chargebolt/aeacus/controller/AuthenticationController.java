package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.auth.OssAuthenticationService;
import com.chargebolt.aeacus.common.exception.AuthenticationExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.common.exception.LoginExceEnum;
import com.chargebolt.aeacus.dto.OssAuthenticationDTO;
import com.chargebolt.aeacus.dto.OssMerchantUserDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.AuthticationService;
import com.chargebolt.aeacus.service.LoginService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Resource;

@RestController
public class AuthenticationController implements OssAuthenticationService{

    @Resource
    private LoginService loginService;
    @Resource
    private AuthticationService authticationService;

    @Override
    public BizResult<OssUserDTO> checkLogin(@PathVariable(value = "version") String version, @RequestBody OssAuthenticationDTO ossAuthenticationDTO) {
        if (StringUtils.isBlank(ossAuthenticationDTO.getUserToken())){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        OssUserDO ossUserDO = loginService.checkLoginStatus(ossAuthenticationDTO.getUserToken());
        if (ossUserDO == null){
            return BizResult.error(LoginExceEnum.LOGIN_EXPIRED);
        }
        return BizResult.create(OssUserDTO.builder().userId(ossUserDO.getId()).name(ossUserDO.getName()).mobile(ossUserDO.getMobile()).cooperatorId(ossUserDO.getCooperatorId()).type(ossUserDO.getType()).build());
    }

    @Override
    public BizResult<Boolean> checkPermission(@PathVariable(value = "version") String version, @RequestBody OssAuthenticationDTO ossAuthenticationDTO) {
        if (StringUtils.isBlank(ossAuthenticationDTO.getUserToken())){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        OssUserDO ossUserDO = loginService.checkLoginStatus(ossAuthenticationDTO.getUserToken());
        if (ossUserDO == null){
            return BizResult.error(LoginExceEnum.LOGIN_EXPIRED);
        }
        if (StringUtils.isBlank(ossAuthenticationDTO.getPermissionCode())){
            return BizResult.error(AuthenticationExceEnum.INVALID_PERMISSION_CODE);
        }
        Boolean result = authticationService.checkPermission(ossUserDO.getId(), ossAuthenticationDTO.getPermissionCode());
        if (result){
            return BizResult.create(null);
        }else {
            return BizResult.error(AuthenticationExceEnum.AUTHENTICATION_FORBIDDEN);
        }
    }

    @Override
    public BizResult<OssMerchantUserDTO> merchantCheckLogin(@PathVariable(value = "version") String version, @RequestBody OssAuthenticationDTO ossAuthenticationDTO) {
        if (StringUtils.isBlank(ossAuthenticationDTO.getUserToken())){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        OssMerchantUserDO ossMerchantUserDO = loginService.merchantUserCheckLogin(ossAuthenticationDTO.getUserToken());
        if (ossMerchantUserDO == null){
            return BizResult.error(LoginExceEnum.LOGIN_EXPIRED);
        }
        return BizResult.create(OssMerchantUserDTO.builder()
                .userId(ossMerchantUserDO.getId())
                .name(ossMerchantUserDO.getName())
                .mobile(ossMerchantUserDO.getMobile())
                .cooperatorId(ossMerchantUserDO.getCooperatorId())
                .status(ossMerchantUserDO.getStatus())
                        .agentId(ossMerchantUserDO.getAgentId())
                .build());
    }
}
