package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssDepartmentService;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.dto.department.OssDepartmentCreateDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentDeleteDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentUpdateDTO;
import com.chargebolt.aeacus.service.DepartmentService;
import com.chargebolt.template.BaseChargeboltController;


import java.io.UnsupportedEncodingException;
import java.util.Objects;
import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

@RestController
@Tag(name = "DepartmentController", description = "部门API")
public class DepartmentController extends BaseChargeboltController implements OssDepartmentService {

    @Resource
    private DepartmentService departmentService;

    @Override
    @Operation(summary = "创建")
    public BizResult<Boolean> create(@PathVariable(value = "version") String version,
            @RequestBody OssDepartmentCreateDTO createDTO){
        if (createDTO == null) {
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (createDTO.getParentId() == null) {
            return BizResult.error(BizExceEnum.DEPARTMENT_PARENT_ID_NULL);
        }
        if (StringUtils.isBlank(createDTO.getName())) {
            return BizResult.error(BizExceEnum.DEPARTMENT_NAME_NULL);
        }
        try {
            if (createDTO.getName().getBytes("gbk").length > 50) {
                return BizResult.error(BizExceEnum.DEPARTMENT_NAME_OVERLONG);
            }
        } catch (UnsupportedEncodingException e) {
            return BizResult.error(BizExceEnum.DEPARTMENT_NAME_OVERLONG);
        }
        if (StringUtils.isBlank(createDTO.getOperatorName())) {
            return BizResult.error(BizExceEnum.DEPARTMENT_OPERATOR_NULL);
        }
        if(Objects.isNull(createDTO.getAgentId())){
            createDTO.setAgentId(getUser().getAgentId());
        }
        Boolean isSuccess = departmentService.create(createDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    @Operation(summary = "更新")
    public BizResult<Boolean> update(@PathVariable(value = "version") String version,
            @RequestBody OssDepartmentUpdateDTO updateDTO) {
        if (updateDTO == null) {
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (updateDTO.getId() == null) {
            return BizResult.error(BizExceEnum.DEPARTMENT_ID_NULL);
        }
        if (StringUtils.isBlank(updateDTO.getName())) {
            return BizResult.error(BizExceEnum.DEPARTMENT_NAME_NULL);
        }
        if (updateDTO.getName().length() > 50) {
            return BizResult.error(BizExceEnum.DEPARTMENT_NAME_OVERLONG);
        }
        if (StringUtils.isBlank(updateDTO.getOperatorName())) {
            return BizResult.error(BizExceEnum.DEPARTMENT_OPERATOR_NULL);
        }
        Boolean isSuccess = departmentService.update(updateDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    @Operation(summary = "删除")
    public BizResult<Boolean> delete(@PathVariable(value = "version") String version,
            @RequestBody OssDepartmentDeleteDTO deleteDTO) {
        if (deleteDTO == null) {
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (deleteDTO.getId() == null) {
            return BizResult.error(BizExceEnum.DEPARTMENT_ID_NULL);
        }
        if (StringUtils.isBlank(deleteDTO.getOperatorName())) {
            return BizResult.error(BizExceEnum.DEPARTMENT_OPERATOR_NULL);
        }
        Boolean isSuccess = departmentService.delete(deleteDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    @Operation(summary = "查询")
    public BizResult<OssDepartmentDTO> query(@PathVariable(value = "version") String version) {
        OssDepartmentDTO ossDepartmentDTO = departmentService.query();
        return BizResult.create(ossDepartmentDTO);
    }

}
