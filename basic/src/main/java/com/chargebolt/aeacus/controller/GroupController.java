package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssGroupService;
import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.dto.group.OssGroupQueryDTO;
import com.chargebolt.aeacus.service.GroupService;


import java.util.List;
import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

@RestController
@Tag(name = "GroupController", description = "组API")
public class GroupController implements OssGroupService{
    @Resource
    private GroupService groupService;

    @Override
    @Operation(summary = "查询")
    public BizResult<List<OssGroupDTO>> query(@PathVariable(value = "version") String version,
            @RequestBody OssGroupQueryDTO ossGroupQueryDTO) {
        List<OssGroupDTO> ossGroupDTOS = groupService.query(ossGroupQueryDTO);
        return BizResult.create(ossGroupDTOS);
    }
}
