package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssIconService;
import com.chargebolt.aeacus.api.manager.OssResourceService;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.dto.OssMobileDTO;
import com.chargebolt.aeacus.entity.dataobject.IconDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.service.manager.*;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@RestController
public class IconController implements OssIconService {

    @Resource
    private IconManager iconManager;
    @Resource
    private RoleIconManager roleIconManager;

    @Override
    public BizResult<List<OssIconDTO>> getAllIcons() {
        return BizResult.create(iconManager.queryAllIcons());
    }

    @Override
    public BizResult<List<OssIconDTO>> getIconsByRoleId(Long roleId) {
        List<OssIconDTO> ossIconDTOS = roleIconManager.getIconByRoleId(roleId);
        return BizResult.create(ossIconDTOS);
    }
}
