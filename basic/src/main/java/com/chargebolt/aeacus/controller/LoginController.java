package com.chargebolt.aeacus.controller;

import com.alibaba.fastjson.JSON;
import com.chargebolt.aeacus.api.auth.OssLoginService;
import com.chargebolt.aeacus.cache.UserCache;
import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.common.exception.LoginExceEnum;
import com.chargebolt.aeacus.dto.OssMerchantUserDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.dto.OssUserLoginDTO;
import com.chargebolt.aeacus.dto.OssUserLogoutDTO;
import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import lombok.extern.slf4j.Slf4j;
import com.chargebolt.aeacus.service.LoginService;
import com.chargebolt.aeacus.service.manager.MobileManager;
import com.chargebolt.aeacus.service.manager.SmsManager;
import com.chargebolt.service.messagecenter.OneSignalUserService;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Resource;

@RestController
@Slf4j
public class LoginController implements OssLoginService{

    @Resource
    private LoginService loginService;
    @Resource
    private SmsManager smsManager;
    @Resource
    private MobileManager mobileManager;
    @Resource
    private UserCache userCache;
    @Resource
    private OneSignalUserService oneSignalUserService;

    @Override
    public BizResult<OssUserDTO> login(@PathVariable(value = "version") String version, @RequestBody OssUserLoginDTO ossUserLoginDTO) {
        if (ossUserLoginDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (AeacusConstsnts.LOGIN_TYPE_PWD.equals(ossUserLoginDTO.getLoginType())){
            OssUserDO ossUserDO = loginService.loginByPassword(ossUserLoginDTO.getUsername(), ossUserLoginDTO.getPassword());
            if (ossUserDO == null){
                log.error("strange issue for login, ossUserLoginDTO=[{}], ossUserDO=[null]", ossUserLoginDTO);
                return BizResult.error(LoginExceEnum.INVALID_PASSWORD);
            }else {
                return BizResult.create(OssUserDTO
                        .builder()
                        .userId(ossUserDO.getId())
                        .name(ossUserDO.getName())
                        .cooperatorId(ossUserDO.getCooperatorId())
                        .type(ossUserDO.getType())
                        .userToken(ossUserDO.getUserToken())
                        .agentId(ossUserDO.getAgentId())
                        .build());
            }
        }else if (AeacusConstsnts.LOGIN_TYPE_SMS.equals(ossUserLoginDTO.getLoginType())){
            if (StringUtils.isEmpty(ossUserLoginDTO.getMobile())){
                return BizResult.error(LoginExceEnum.INVALID_MOBILE);
            }
            OssUserDO ossUserDO = loginService.loginBySms(mobileManager.createMobileBO(ossUserLoginDTO.getMobile()), ossUserLoginDTO.getSmsToken());
            if (ossUserDO == null){
                return BizResult.error(LoginExceEnum.INVALID_SMS_TOKEN);
            } else {
                return BizResult.create(OssUserDTO
                        .builder()
                        .userId(ossUserDO.getId())
                        .name(ossUserDO.getName())
                        .cooperatorId(ossUserDO.getCooperatorId())
                        .type(ossUserDO.getType())
                        .userToken(ossUserDO.getUserToken())
                        .agentId(ossUserDO.getAgentId())
                        .build());
            }
        } else {
            return BizResult.error(LoginExceEnum.INVALID_LOGIN_TYPE);
        }
    }

    @Override
    public BizResult logout(@PathVariable(value = "version") String version, @RequestBody OssUserLogoutDTO ossUserLogoutDTO) {
        if (ossUserLogoutDTO == null || StringUtils.isEmpty(ossUserLogoutDTO.getUserToken())){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        return loginService.logout(ossUserLogoutDTO.getUserToken());
    }

    @Override
    public BizResult<OssUserDTO> sendSmsToken(@PathVariable(value = "version") String version, @RequestParam(value = "mobile") String mobile) {
        if (StringUtils.isEmpty(mobile)){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (!mobile.startsWith("+")){
            return BizResult.error(LoginExceEnum.INVALID_MOBILE_NATIONCODE);
        }
        BizResult b = smsManager.sendSmsToken(mobileManager.createMobileBO(mobile));
        if (b.isSuccess()){
            return BizResult.create(null);
        }else {
            return b;
        }
    }

    @Override
    public BizResult checkSmsToken(@PathVariable(value = "version") String version, @RequestBody OssUserLoginDTO ossUserLoginDTO) {
        if (ossUserLoginDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (StringUtils.isEmpty(ossUserLoginDTO.getMobile()) || StringUtils.isEmpty(ossUserLoginDTO.getSmsToken())){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (!ossUserLoginDTO.getMobile().startsWith("+")){
            return BizResult.error(LoginExceEnum.INVALID_MOBILE_NATIONCODE);
        }
        return smsManager.checkSmsToken(mobileManager.createMobileBO(ossUserLoginDTO.getMobile()), ossUserLoginDTO.getSmsToken());
    }

    @Override
    public BizResult<OssMerchantUserDTO> merchantLogin(@PathVariable(value = "version") String version, @RequestBody OssUserLoginDTO ossUserLoginDTO) {
        log.info("merchantLogin:{}" + JSON.toJSONString(ossUserLoginDTO));
        if (ossUserLoginDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        OssMerchantUserDO ossUserDO = loginService.merchantUserLogin(ossUserLoginDTO.getUsername(), ossUserLoginDTO.getPassword());
        if (ossUserDO == null){
            return BizResult.error(LoginExceEnum.INVALID_PASSWORD);
        }else {
            return BizResult.create(OssMerchantUserDTO
                    .builder()
                    .userId(ossUserDO.getId())
                    .name(ossUserDO.getName())
                    .cooperatorId(ossUserDO.getCooperatorId())
                    .userToken(ossUserDO.getUserToken())
                    .build());
        }
    }

    @Override
    public BizResult merchantLogout(@PathVariable(value = "version") String version, @RequestBody OssUserLogoutDTO ossUserLogoutDTO) {
        if (ossUserLogoutDTO == null || StringUtils.isEmpty(ossUserLogoutDTO.getUserToken())){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        return loginService.merchantUserLogout(ossUserLogoutDTO.getUserToken());
    }

    @RequestMapping(value = "/{version}/user/login/getSmsToken", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_VALUE)
    public BizResult getSmsToken(@PathVariable(value = "version") String version, @RequestParam(value = "mobile") String mobile) {
        return BizResult.create(smsManager.getSmsToken(mobile));
    }

    @RequestMapping(value = "/{version}/user/login/getCacheUser", method = RequestMethod.GET)
    public BizResult getCacheUser(@RequestParam(value = "userId") Long userId){
        return BizResult.create(userCache.getUser(userId));
    }

    @RequestMapping(value = "/{version}/user/login/kick", method = RequestMethod.GET)
    public BizResult kick(@RequestParam(value = "userId") Long userId){
        userCache.removeUser(userId);
        return BizResult.create(null);
    }
}
