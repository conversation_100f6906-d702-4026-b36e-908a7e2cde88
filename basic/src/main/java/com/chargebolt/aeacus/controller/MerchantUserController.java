package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssMerchantUserService;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.dto.*;
import com.chargebolt.aeacus.service.MerchantUserService;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
@Tag(name = "MerchantUserController", description = "商户账户API")
public class MerchantUserController implements OssMerchantUserService {
    @Resource
    private MerchantUserService merchantUserService;

    @Override
    @Operation(summary = "创建")
    public BizResult<Long> create(@PathVariable(value = "version") String version, @RequestBody OssMerchantUserDTO ossMerchantUserDTO) {
        if (ossMerchantUserDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (StringUtils.isEmpty(ossMerchantUserDTO.getMobile())){
            return BizResult.error(BizExceEnum.USER_MOBILE_NULL);
        }
        if (ossMerchantUserDTO.getCooperatorId() == null){
            return BizResult.error(BizExceEnum.USER_COOPERATOR_NULL);
        }
        log.info("用户中心-MerchantUserController create merchant user, info:{}", ossMerchantUserDTO);
        Long id = merchantUserService.create(ossMerchantUserDTO);
        return BizResult.create(id);
    }

    @Override
    @Operation(summary = "修改密码")
    public BizResult<Boolean> updatePassword(@PathVariable(value = "version") String version, @RequestBody OssUserPasswordOpDTO passwordOpDTO) {
        if (passwordOpDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        // 校验修改者是否为null
        if (passwordOpDTO.getOperatorId() == null){
            return BizResult.error(BizExceEnum.USER_ID_NULL);
        }

        log.info("用户中心-MerchantUserController update_merchant_user_password, passwordDTO:{}", passwordOpDTO);
        Boolean isSuccess = merchantUserService.updatePassword(passwordOpDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    @Operation(summary = "根据ids查询")
    public BizResult<List<OssMerchantUserDTO>> queryByIds(@PathVariable(value = "version") String version, @RequestBody List<Long> ids) {
        log.info("用户中心-MerchantUserController queryByIds,ids:{}",ids);
        List<OssMerchantUserDTO> userDTOS = merchantUserService.queryByIds(ids);
        return BizResult.create(userDTOS);
    }

    @Override
    @Operation(summary = "根据账户名查询")
    public BizResult<OssMerchantUserDTO> queryByName(@PathVariable(value = "version") String version, @RequestParam(value = "name") String name) {
        log.info("用户中心-MerchantUserController queryByName,name:{}", name);
        OssMerchantUserDTO ossMerchantUserDTO = merchantUserService.queryByName(name);
        return BizResult.create(ossMerchantUserDTO);
    }

    @Override
    public BizResult updateStatus(@PathVariable(value = "version") String version, @RequestParam(value = "name") String name, @RequestParam(value = "status") Integer status, @RequestParam(value = "updator") Long updator) {
        log.info("用户中心-MerchantUserController updateStatus, name:{}, name:{}, name:{}", name, status, updator);
        merchantUserService.updateStatus(name, status, updator);
        return BizResult.create(null);
    }

    @Override
    public BizResult deleteById(@PathVariable(value = "version") String version,  @RequestParam(value = "id") Long id) {
        log.info("用户中心-MerchantUserController deleteById, id:{}", id);
        merchantUserService.deleteById(id);
        return BizResult.create(null);
    }

    @Override
    @Operation(summary = "根据手机号查询")
    public BizResult<OssMerchantUserDTO> queryByMobile(@PathVariable(value = "version") String version, @RequestParam(value = "mobile") String mobile) {
        log.info("用户中心-MerchantUserController queryByMobile,mobileNo:{}",mobile);
        OssMerchantUserDTO ossMerchantUserDTO = merchantUserService.queryByMobile(mobile);
        return BizResult.create(ossMerchantUserDTO);
    }

    @Override
    @Operation(summary = "根据id查询")
    public BizResult<OssMerchantUserDTO> queryById(@PathVariable(value = "version") String version, @RequestParam(value = "id") Long id) {
        log.info("用户中心-MerchantUserController queryById,id:{}",id);
        OssMerchantUserDTO ossMerchantUserDTO = merchantUserService.queryById(id);
        return BizResult.create(ossMerchantUserDTO);
    }
}
