package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssModuleService;
import com.chargebolt.aeacus.dto.OssModuleDTO;
import com.chargebolt.aeacus.entity.dataobject.ModuleDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.service.manager.ModuleManager;
import com.chargebolt.aeacus.service.manager.RoleManager;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.List;

@RestController
public class ModuleController implements OssModuleService{

    @Resource
    private RoleManager roleManager;
    @Resource
    private ModuleManager moduleManager;

    @Override
    public BizResult<List<OssModuleDTO>> getModulesByUser(String version, @RequestParam(value = "userId") Long userId) {

        List<RoleDO> roleDOS = roleManager.queryByUser(userId);
        List<Long> moduleIds = Lists.transform(roleDOS, new Function<RoleDO, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable RoleDO roleDO) {
                return roleDO.getModuleId();
            }
        });

        List<ModuleDO> moduleDOS = moduleManager.queryAllModules();
        List<OssModuleDTO> ossModuleDTOS = Lists.newLinkedList();
        for (ModuleDO moduleDO : moduleDOS){
            if (moduleIds.contains(moduleDO.getId())){
                ossModuleDTOS.add(OssModuleDTO.builder().id(moduleDO.getId()).name(moduleDO.getName()).url(moduleDO.getUrl()).build());
            }
        }
        return BizResult.create(ossModuleDTOS);
    }
}
