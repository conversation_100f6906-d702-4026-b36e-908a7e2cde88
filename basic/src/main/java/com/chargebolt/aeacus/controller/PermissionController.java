package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssPermissionService;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.dto.OssRolePermissionAddDTO;
import com.chargebolt.aeacus.entity.dataobject.PermissionDO;
import com.chargebolt.aeacus.entity.dataobject.RolePermissionMappingDO;
import com.chargebolt.aeacus.service.manager.PermissionManager;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class PermissionController implements OssPermissionService{

    @Resource
    private PermissionManager permissionManager;

    @Override
    public BizResult<List<OssPermissionDTO>> getIconsByUser(@PathVariable(value = "version") String version, @RequestParam Long userId) {
        List<OssPermissionDTO> ossPermissionDTOS = Lists.newLinkedList();
        List<PermissionDO> permissionDOS = permissionManager.getPermissionByUserId(userId);

        if (!CollectionUtils.isEmpty(permissionDOS)) {
            for (PermissionDO permissionDO : permissionDOS) {
                ossPermissionDTOS.add(OssPermissionDTO
                        .builder()
                        .id(permissionDO.getId())
                        .url(permissionDO.getUrl())
                        .img(permissionDO.getImg())
                        .code(permissionDO.getCode())
                        .build());
            }
        }
        return BizResult.create(ossPermissionDTOS);
    }

    @Override
    public BizResult<Boolean> update(String version, @RequestBody OssPermissionDTO ossPermissionDTO) {
        if (ossPermissionDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if(StringUtils.isEmpty(ossPermissionDTO.getCode())){
            return BizResult.error(BizExceEnum.PERMISSION_CODE_NULL);
        }
        if(ossPermissionDTO.getType() == null){
            return BizResult.error(BizExceEnum.PERMISSION_TYPE_NULL);
        }
        if(ossPermissionDTO.getCreator() == null){
            return BizResult.error(BizExceEnum.PERMISSION_CREATOR_NULL);
        }
        PermissionDO permissionDO = permissionManager.getByCode(ossPermissionDTO.getCode());
        if (permissionDO == null){
            return BizResult.error(BizExceEnum.NO_SUCH_OBJECT);
        }
        permissionDO.setUrl(ossPermissionDTO.getUrl());
        permissionDO.setCode(ossPermissionDTO.getCode());
        permissionDO.setType(ossPermissionDTO.getType());
        permissionDO.setImg(ossPermissionDTO.getImg());
        permissionDO.setCreator(ossPermissionDTO.getCreator());
        permissionManager.update(permissionDO);
        return BizResult.create(null);

    }

    @Override
    public BizResult<Boolean> addPermissionToRole(String version, @RequestBody OssRolePermissionAddDTO ossRolePermissionAddDTO) {
        if (ossRolePermissionAddDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (ossRolePermissionAddDTO.getRoleId() == null){
            return BizResult.error(BizExceEnum.ROLE_ID_NULL);
        }
        if (CollectionUtils.isEmpty(ossRolePermissionAddDTO.getPermissionIds())){
            return BizResult.error(BizExceEnum.PERMISSION_ID_NULL);
        }
        List<RolePermissionMappingDO> rolePermissionMappingDOS = Lists.newLinkedList();
        for (Long permissionId : ossRolePermissionAddDTO.getPermissionIds()){
            RolePermissionMappingDO rolePermissionMappingDO = new RolePermissionMappingDO();
            rolePermissionMappingDO.setRoleId(ossRolePermissionAddDTO.getRoleId());
            rolePermissionMappingDO.setPermissionId(permissionId);
            rolePermissionMappingDOS.add(rolePermissionMappingDO);
        }
        permissionManager.addRolePermissionMapping(rolePermissionMappingDOS);
        return BizResult.create(null);
    }

    @Override
    public BizResult<Boolean> add(String version, @RequestBody OssPermissionDTO ossPermissionDTO){
        if (ossPermissionDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if(StringUtils.isEmpty(ossPermissionDTO.getCode())){
            return BizResult.error(BizExceEnum.PERMISSION_CODE_NULL);
        }
        if(ossPermissionDTO.getType() == null){
            return BizResult.error(BizExceEnum.PERMISSION_TYPE_NULL);
        }
        if(ossPermissionDTO.getCreator() == null){
            return BizResult.error(BizExceEnum.PERMISSION_CREATOR_NULL);
        }

        PermissionDO permissionDO = new PermissionDO();
        permissionDO.setUrl(ossPermissionDTO.getUrl());
        permissionDO.setCode(ossPermissionDTO.getCode());
        permissionDO.setType(ossPermissionDTO.getType());
        permissionDO.setImg(ossPermissionDTO.getImg());
        permissionDO.setCreator(ossPermissionDTO.getCreator());
        permissionDO.setGroupId(ossPermissionDTO.getGroupId());
        permissionDO.setRelyIds(ossPermissionDTO.getRelyIds());
        Long id = permissionManager.insert(permissionDO);
        if (id > 0){
            return BizResult.create(null);
        }else {
            return BizResult.error(CommonExceEnum.INTERNAL_EXCEPTION);
        }

    }
}
