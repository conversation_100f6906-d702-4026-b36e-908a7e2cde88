package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssResourceService;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.dto.OssMobileDTO;
import com.chargebolt.aeacus.entity.dataobject.IconDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.entity.dataobject.RoleIconMappingDO;
import com.chargebolt.aeacus.service.manager.*;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;

@RestController
public class ResourceController implements OssResourceService{

    @Resource
    private UserManager userManager;
    @Resource
    private RoleManager roleManager;
    @Resource
    private IconManager iconManager;
    @Resource
    private MobileManager mobileManager;
    @Resource
    private RoleIconManager roleIconManager;

    @Override
    public BizResult<List<OssIconDTO>> getIconsByUser(@RequestParam(value = "userId") Long userId, @RequestParam(value = "moduleId") Long moduleId) {
        if (userId == null || moduleId == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        OssUserDO ossUserDO = userManager.getById(userId);
        if (ossUserDO == null){
            return BizResult.error(BizExceEnum.NO_SUCH_OBJECT);
        }
        List<RoleDO> roleDOS = roleManager.queryByUser(ossUserDO.getId());
        if (CollectionUtils.isEmpty(roleDOS)){
            return BizResult.create(Collections.emptyList());
        }
        // 去除 icon 所属moudle校验
        /*List<RoleDO> filterRoleDOS = Lists.newLinkedList();
        for (RoleDO roleDO : roleDOS){
            if (roleDO.getModuleId().equals(moduleId)){
                filterRoleDOS.add(roleDO);
            }
        }
        if (CollectionUtils.isEmpty(filterRoleDOS)){
            return BizResult.create(Collections.emptyList());
        }*/
        Set<Long> iconIds = new HashSet<>();
        for (RoleDO roleDO : roleDOS){
            List<RoleIconMappingDO> roleIconMappingDOS = roleIconManager.getMappingsByRoleId(roleDO.getId());
            if (!CollectionUtils.isEmpty(roleIconMappingDOS)) {
                for (RoleIconMappingDO roleIconMappingDO : roleIconMappingDOS) {
                    iconIds.add(roleIconMappingDO.getIconId());
                }
            }
        }
        if (!CollectionUtils.isEmpty(iconIds)) {
            List list = new ArrayList(iconIds);
            List<OssIconDTO> ossIconDTOS = iconManager.queryIconsByIds(list);
            if (CollectionUtils.isEmpty(ossIconDTOS)){
                return BizResult.create(Collections.emptyList());
            }
            return BizResult.create(ossIconDTOS);
        }
        return BizResult.create(Collections.emptyList());
    }

    @Override
    public BizResult<List<OssMobileDTO>> getSupportMobileNations() {
        return BizResult.create(Lists.transform(mobileManager.getSupportNations(), new Function<MobileManager.SupportNations, OssMobileDTO>() {
            @Nullable
            @Override
            public OssMobileDTO apply(@Nullable MobileManager.SupportNations supportNations) {
                return OssMobileDTO
                        .builder()
                        .nationCode(supportNations.getNationCode())
                        .nationName(supportNations.getNationName())
                        .build();
            }
        }));
    }

    private List<IconDO> removeSameAndSort(List<IconDO> iconDOList) {
        List<IconDO> result = Lists.newArrayList();
        List<Long> existedIconIdList = Lists.newArrayList();
        for (IconDO iconDO : iconDOList) {
            if(existedIconIdList.contains(iconDO.getId())) {
                continue;
            }
            result.add(iconDO);
            existedIconIdList.add(iconDO.getId());
        }
        Collections.sort(result, (o1,o2) -> o1.getOrder().compareTo(o2.getOrder()));
        return result;
    }
}
