package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssRoleService;
import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.common.util.NameVerifyUtil;
import com.chargebolt.aeacus.dto.OssRoleCreateDTO;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.dto.OssRoleDeleteDTO;
import com.chargebolt.aeacus.dto.OssRoleQueryDTO;
import com.chargebolt.aeacus.dto.OssRoleUpdateDTO;
import com.chargebolt.aeacus.dto.OssRoleUserAuthDTO;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.service.RoleService;
import com.chargebolt.aeacus.service.manager.RoleManager;
import com.chargebolt.template.BaseChargeboltController;
import com.google.common.base.Function;
import com.google.common.collect.Lists;


import java.util.Objects;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@RestController
@Tag(name = "RoleController", description = "角色API")
public class RoleController extends BaseChargeboltController implements OssRoleService{

    @Resource
    private RoleManager roleManager;
    @Resource
    private RoleService roleService;

    @Override
    public BizResult<List<OssRoleDTO>> getRolesByUser(@PathVariable(value = "version") String version, @RequestBody Long userId) {
        if (userId == null){
            return BizResult.create(Collections.emptyList());
        }
        List<RoleDO> roleDOS = roleManager.queryByUser(userId);
        if (CollectionUtils.isEmpty(roleDOS)){
            return BizResult.create(Collections.emptyList());
        }
        return BizResult.create(Lists.transform(roleDOS, new Function<RoleDO, OssRoleDTO>() {
            @Nullable
            @Override
            public OssRoleDTO apply(@Nullable RoleDO roleDO) {
                return OssRoleDTO
                        .builder()
                        .id(roleDO.getId())
                        .name(roleDO.getName())
                        .build();
            }
        }));
    }

    @Override
    @Operation(summary = "创建")
    public BizResult<Boolean> add(String version, @RequestBody OssRoleCreateDTO ossRoleCreateDTO) {
        if (ossRoleCreateDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (StringUtils.isEmpty(ossRoleCreateDTO.getName())){
            return BizResult.error(BizExceEnum.ROLE_NAME_NULL);
        }
        if (StringUtils.isEmpty(ossRoleCreateDTO.getCreator())){
            return BizResult.error(BizExceEnum.ROLE_OPEARATOR_NULL);
        }
        if(CollectionUtils.isEmpty(ossRoleCreateDTO.getPermissionIds()) && CollectionUtils.isEmpty(ossRoleCreateDTO.getIconIds())){
            if (CollectionUtils.isEmpty(ossRoleCreateDTO.getIconIds())) {
                return BizResult.error(BizExceEnum.ICON_ID_NULL);
            } else if (CollectionUtils.isEmpty(ossRoleCreateDTO.getPermissionIds())) {
                return BizResult.error(BizExceEnum.PERMISSION_ID_NULL);
            }
        }
        if(ossRoleCreateDTO.getName().length() > 50 || !NameVerifyUtil.verifyForbidChars(ossRoleCreateDTO.getName())){
            return BizResult.error(BizExceEnum.ROLE_NAME_INVALID);
        }
        if (ossRoleCreateDTO.getModuleId() == null){
            ossRoleCreateDTO.setModuleId(0L);
        }
        if(Objects.isNull(ossRoleCreateDTO.getAgentId())){
            ossRoleCreateDTO.setAgentId(getUser().getAgentId());
        }
        Boolean isSuccess = roleService.create(ossRoleCreateDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    @Operation(summary = "更新")
    public BizResult<Boolean> update(String version, @RequestBody OssRoleUpdateDTO ossRoleDTO) {
        if (ossRoleDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (ossRoleDTO.getId() == null){
            return BizResult.error(BizExceEnum.ROLE_ID_NULL);
        }
        if (StringUtils.isEmpty(ossRoleDTO.getName())){
            return BizResult.error(BizExceEnum.ROLE_NAME_NULL);
        }
        if(Objects.equals(ossRoleDTO.getId(), AeacusConstsnts.SUPER_ROLE_ID)&& Objects.equals(version, "1.0")){
            return BizResult.error(BizExceEnum.ROLE_CANNOT_OPERATOR);
        }
        if (StringUtils.isEmpty(ossRoleDTO.getUpdator())){
            return BizResult.error(BizExceEnum.ROLE_OPEARATOR_NULL);
        }
        if(CollectionUtils.isEmpty(ossRoleDTO.getPermissionIds()) && CollectionUtils.isEmpty(ossRoleDTO.getIconIds())){
            if (CollectionUtils.isEmpty(ossRoleDTO.getIconIds())) {
                return BizResult.error(BizExceEnum.ICON_ID_NULL);
            } else if (CollectionUtils.isEmpty(ossRoleDTO.getPermissionIds())) {
                return BizResult.error(BizExceEnum.PERMISSION_ID_NULL);
            }
        }
        Boolean isSuccess = roleService.update(ossRoleDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    public BizResult<Boolean> authToUesr(String version, @RequestBody OssRoleUserAuthDTO ossRoleUserAuthDTO) {
        if (ossRoleUserAuthDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }if (ossRoleUserAuthDTO.getRoleId() == null){
            return BizResult.error(BizExceEnum.ROLE_ID_NULL);
        }if (ossRoleUserAuthDTO.getUserId() == null){
            return BizResult.error(BizExceEnum.USER_ID_NULL);
        }
        OssUserRoleMappingDO ossUserRoleMappingDO = new OssUserRoleMappingDO();
        ossUserRoleMappingDO.setRoleId(ossRoleUserAuthDTO.getRoleId());
        ossUserRoleMappingDO.setUserId(ossRoleUserAuthDTO.getUserId());
        ossUserRoleMappingDO.setExpireTime(ossRoleUserAuthDTO.getExpireTime());
        roleManager.addUserRoleMapping(ossUserRoleMappingDO);
        return BizResult.create(null);
    }

    @Override
    @Operation(summary = "分页查询")
    public BizResult<PageData<OssRoleDTO>> queryByPage(@PathVariable(value = "version") String version,
            @RequestBody OssRoleQueryDTO queryDTO) {
        if(Objects.isNull(queryDTO.getAgentId())){
            queryDTO.setAgentId(getUser().getAgentId());
        }
        return BizResult.create(roleService.queryByPage(queryDTO));
    }

    @Override
    @Operation(summary = "全部查询")
    public BizResult<List<OssRoleDTO>> query(@PathVariable(value = "version") String version){
        return BizResult.create(roleService.queryAllRoles());
    }

    @Override
    @Operation(summary = "根据id查询")
    public BizResult<OssRoleDTO> queryById(@PathVariable(value = "version") String version,
            @RequestParam("roleId") Long roleId) {
        OssRoleDTO ossRoleDTOS = roleService.queryById(roleId);
        return BizResult.create(ossRoleDTOS);
    }

    @Override
    @Operation(summary = "删除")
    public BizResult<Boolean> delete(@PathVariable(value = "version") String version, @RequestBody OssRoleDeleteDTO ossRoleDeleteDTO) {
        if (ossRoleDeleteDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (ossRoleDeleteDTO.getId() == null){
            return BizResult.error(BizExceEnum.ROLE_ID_NULL);
        }
        if(Objects.equals(ossRoleDeleteDTO.getId(), AeacusConstsnts.SUPER_ROLE_ID)){
            return BizResult.error(BizExceEnum.ROLE_CANNOT_OPERATOR);
        }
        if (StringUtils.isEmpty(ossRoleDeleteDTO.getUpdator())){
            return BizResult.error(BizExceEnum.ROLE_OPEARATOR_NULL);
        }
        Boolean isSuccess = roleService.delete(ossRoleDeleteDTO);
        return BizResult.create(isSuccess);
    }

}
