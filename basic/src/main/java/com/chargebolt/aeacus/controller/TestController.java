package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.cache.LoginCache;
import com.chargebolt.aeacus.cache.SmsCache;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

@RestController
public class TestController {

    @Resource
    private SmsCache smsCache;
    @Resource
    private LoginCache loginCache;

    @GetMapping(value = "/user/removeCache")
    public BizResult<Boolean> login(@RequestParam("mobile") String mobile) {
        smsCache.removeSmsCache(mobile);
        return BizResult.create(true);
    }

    @GetMapping(value = "/user/removeLoginCache")
    public BizResult<Boolean> removeLoginCache(@RequestParam("name") String name) {
        loginCache.removeLoginCacheDO(name);
        return BizResult.create(true);
    }
}
