package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.api.manager.OssUserService;
import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.CommonExceEnum;
import com.chargebolt.aeacus.common.exception.LoginExceEnum;
import com.chargebolt.aeacus.common.util.NameVerifyUtil;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.dto.OssUserOpDTO;
import com.chargebolt.aeacus.dto.OssUserPasswordOpDTO;
import com.chargebolt.aeacus.dto.OssUserQueryDTO;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.aeacus.service.AeacusUserService;
import com.chargebolt.template.BaseChargeboltController;


import java.io.UnsupportedEncodingException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@RestController
@Slf4j
@Tag(name = "UserController", description = "账户API")
@Component
public class UserController extends BaseChargeboltController implements OssUserService{
    @Resource
    private AeacusUserService aeacusUserService;

    @Override
    @Operation(summary = "创建")
    public BizResult<Long> create(@PathVariable(value = "version") String version, @RequestBody OssUserOpDTO ossUserOpDTO) {
        if (ossUserOpDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (StringUtils.isBlank(ossUserOpDTO.getNationCode())){
            return BizResult.error(LoginExceEnum.INVALID_MOBILE_NATIONCODE);
        }
        if (StringUtils.isBlank(ossUserOpDTO.getMobile())){
            return BizResult.error(BizExceEnum.USER_MOBILE_NULL);
        }
        if (StringUtils.isBlank(ossUserOpDTO.getName())){
            return BizResult.error(BizExceEnum.USER_NAME_NULL);
        }
        if (StringUtils.isBlank(ossUserOpDTO.getFullName())){
            return BizResult.error(BizExceEnum.USER_FULL_NAME_NULL);
        }
        if (StringUtils.isBlank(ossUserOpDTO.getDepartmentCode())){
            return BizResult.error(BizExceEnum.USER_DEPARTMENT_CODE_NULL);
        }
        if (ossUserOpDTO.getDataPerType() == null){
            return BizResult.error(BizExceEnum.USER_DATA_PERMISSION_NULL);
        }
        if (CollectionUtils.isEmpty(ossUserOpDTO.getRoleIds())){
            return BizResult.error(BizExceEnum.ROLE_ID_NULL);
        }
        Integer length;
        try {
            length = ossUserOpDTO.getName().getBytes("gbk").length;
        } catch (UnsupportedEncodingException e) {
            return BizResult.error(BizExceEnum.USER_NAME_LENGTH_ERROR);
        }
        if (length > 20 || length < 1){
            return BizResult.error(BizExceEnum.USER_NAME_LENGTH_ERROR);
        }
        if(NameVerifyUtil.verifyNumber(ossUserOpDTO.getName())){
            return BizResult.error(BizExceEnum.USER_NAME_FORMAT_ERROR);
        }
        if (ossUserOpDTO.getCooperatorId() == null){
            return BizResult.error(BizExceEnum.USER_COOPERATOR_NULL);
        }
        if(Objects.isNull(ossUserOpDTO.getAgentId())){
            ossUserOpDTO.setAgentId(getUser().getAgentId());
        }
        log.info("用户中心-create,userInfo:{}",ossUserOpDTO);
        Long id = aeacusUserService.create(ossUserOpDTO);
        return BizResult.create(id);
    }

    @Override
    @Operation(summary = "更新")
    public BizResult<Boolean> update(@PathVariable(value = "version") String version, @RequestBody OssUserOpDTO ossUserOpDTO) {
        if (ossUserOpDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (ossUserOpDTO.getId() == null){
            return BizResult.error(BizExceEnum.USER_ID_NULL);
        }
        if (StringUtils.isEmpty(ossUserOpDTO.getOperatorName())){
            return BizResult.error(BizExceEnum.USER_OPERATOR_NULL);
        }
        log.info("用户中心-update,userInfo:{}",ossUserOpDTO);
        Boolean isSuccess = aeacusUserService.update(ossUserOpDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    @Operation(summary = "修改密码")
    public BizResult<Boolean> updatePassword(@PathVariable(value = "version") String version, @RequestBody OssUserPasswordOpDTO passwordOpDTO) {
        if (passwordOpDTO == null){
            return BizResult.error(CommonExceEnum.CHECK_NULL);
        }
        if (passwordOpDTO.getId() == null){
            return BizResult.error(BizExceEnum.USER_ID_NULL);
        }
        if (StringUtils.isEmpty(passwordOpDTO.getOperatorName())){
            return BizResult.error(BizExceEnum.USER_OPERATOR_NULL);
        }
        if(AeacusConstsnts.USER_PASSWORD_UPDATE.equals(passwordOpDTO.getType())){
            if(StringUtils.isEmpty(passwordOpDTO.getPassword()) || StringUtils.isEmpty(passwordOpDTO.getOriPassword())){
                return BizResult.error(BizExceEnum.USER_PASSWORD_NULL);
            }
            if(passwordOpDTO.getPassword().length() < 6 || passwordOpDTO.getPassword().length() > 18){
                return BizResult.error(BizExceEnum.USER_PASSWORD_LENGTH_ERROR);
            }
            if(!NameVerifyUtil.verifyLetterNumber(passwordOpDTO.getPassword())){
                return BizResult.error(BizExceEnum.USER_PASSWORD_FORMAT_ERROR);
            }
        }
        log.info("用户中心-update_password,passwordDTO:{}",passwordOpDTO);
        Boolean isSuccess = aeacusUserService.updatePassword(passwordOpDTO);
        return BizResult.create(isSuccess);
    }

    @Override
    @Operation(summary = "根据ids查询")
    public BizResult<List<OssUserDTO>> queryByIds(@PathVariable(value = "version") String version, @RequestBody List<Long> ids) {
        log.info("用户中心-queryByIds,ids:{}",ids);
        List<OssUserDTO> userDTOS = aeacusUserService.queryByIds(ids);
        return BizResult.create(userDTOS);
    }

    @Override
    @Operation(summary = "根据名称查询")
    public BizResult<OssUserDTO> queryByName(String version, @RequestParam(name = "name") String name) {
        log.info("用户中心-queryByName,name:{}", name);
        OssUserDTO ossUserDTO = aeacusUserService.queryByName(name);
        return BizResult.create(ossUserDTO);
    }

    @Override
    public BizResult<List<OssUserDTO>> queryByCooperator(String version, @RequestParam(name = "cooperatorId") Long cooperatorId) {
        log.info("用户中心-queryByCooperator,cooperatorId:{}",cooperatorId);
        List<OssUserDTO> userDTOS = aeacusUserService.queryByCooperator(cooperatorId);
        return BizResult.create(userDTOS);
    }

    @Override
    public BizResult<OssUserDTO> queryByMobile(@PathVariable(value = "version") String version, @RequestParam(value = "mobile") String mobile) {
        log.info("用户中心-queryByMobile,mobileNo:{}",mobile);
        OssUserDTO ossUserDTO = aeacusUserService.queryByMobile(mobile);
        return BizResult.create(ossUserDTO);
    }

    @Override
    @Operation(summary = "根据id查询")
    public BizResult<OssUserDTO> queryById(@PathVariable(value = "version") String version, @RequestParam(value = "id") Long id) {
        log.info("用户中心-queryById,id:{}",id);
        OssUserDTO userDTOS = aeacusUserService.queryById(id);
        return BizResult.create(userDTOS);
    }

    @Override
    @Operation(summary = "分页查询")
    public BizResult<PageData<OssUserDTO>> query(@PathVariable(value = "version") String version,
            @RequestBody OssUserQueryDTO queryDTO) {
        log.info("用户中心-query,queryDTO:{}",queryDTO);
        PageData<OssUserDTO> userDTOS = aeacusUserService.queryByQueryDTO(queryDTO);
        return BizResult.create(userDTOS);
    }

    @Override
    @Operation(summary = "根据部门code查询部门创建者信息")
    public BizResult<List<OssUserDTO>> queryDpmCreatorByCode(@PathVariable(value = "version") String version, @RequestParam(name = "dpmCode") String dpmCode, @RequestParam(name = "dataPerType") Integer dataPerType) {
        return BizResult.create(aeacusUserService.queryDpmCreatorByCode(dpmCode, dataPerType));
    }



}
