/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.aeacus.controller;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.api.manager.OssUserService;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.AeacusUserService;
import com.chargebolt.aeacus.service.RoleService;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.basic.response.user.UserInfoResponse;
import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.demeter.response.DeviceStatisticResponse;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.template.controller.ControllerCallback;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: UserNewController.java, v 1.0 2023-12-05 4:09 PM Exp $
 */
@Slf4j
@RestController
public class UserNewController extends BaseChargeboltController {
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserManager userManager;

    /**
     * 获取登录用户的权限 code集合
     *
     * @return
     */
    @Login
    @GetMapping(value = "/2.0/user/getPermission")
    public Result<List<OssPermissionDTO>> getPermission() {
        return template.execute(new ControllerCallback<List<OssPermissionDTO>>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public List<OssPermissionDTO> execute() {
                return roleService.getPermission(getUser().getUserId());
            }
        });
    }

    @Login
    @GetMapping(value = "/2.0/user/getUserInfo")
    public Result<UserInfoResponse> getUserInfo() {
        return template.execute(new ControllerCallback<UserInfoResponse>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public UserInfoResponse execute() {
                OssUserDO userDO= userManager.getById(getUser().getUserId());
                UserInfoResponse response= new UserInfoResponse();
                response.setFullName(userDO.getName());
                response.setJoinTime(userDO.getCreateTime().getTime());
                return response;
            }
        });
    }
}