package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OssDepartmentDAO {
    int insert(OssDepartmentDO record);

    int updateById(OssDepartmentDO record);

    int deleteByIds(@Param("ids") List<Long> ids,@Param("deletor") String deletor);

    OssDepartmentDO selectById(@Param("id") Long id);

    List<OssDepartmentDO> selectAll();

    OssDepartmentDO selectByName(@Param("name") String name, @Param("agentId") Long agentId);

    List<OssDepartmentDO> selectLikeCode(@Param("code") String code);

    OssDepartmentDO selectByCode(@Param("code") String code);

    List<OssDepartmentDO> selectByCodeList(@Param("codeList") List<String> codeList);
}