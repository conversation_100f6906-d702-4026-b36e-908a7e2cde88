package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OssMerchantUserDAO {

    /**
     * 根据手机号查询商户账号信息
     * @param mobile String
     * @return OssMerchantUserDO
     */
    OssMerchantUserDO getByMobile(@Param("mobile") String mobile);

    /**
     * 根据账户名称查询账号信息
     * @param name 账户名称
     * @return OssMerchantUserDO
     */
    OssMerchantUserDO queryByName(@Param("name") String name);

    /**
     * 根据ID集合查询商户账号信息
     * @param id Long
     * @return OssMerchantUserDO
     */
    List<OssMerchantUserDO> getByIds(@Param("ids") List<Long> id);

    /**
     * 根据ID查询商户账号信息
     * @param id Long
     * @return OssMerchantUserDO
     */
    OssMerchantUserDO getById(@Param("id") Long id);

    /**
     * 插入商户账号信息
     * @param ossMerchantUserDO OssMerchantUserDO
     * @return Long
     */
    Long insert(OssMerchantUserDO ossMerchantUserDO);

    /**
     * 更新密码
     * @param ossMerchantUserDO OssMerchantUserDO
     * @return Integer
     */
    Integer updatePassword(OssMerchantUserDO ossMerchantUserDO);

    /**
     * 更新token
     * @param ossMerchantUserDO OssMerchantUserDO
     * @return Integer
     */
    Integer updateToken(OssMerchantUserDO ossMerchantUserDO);

    /**
     * 更新用户状态
     * @param name String
     * @param status Integer
     * @return Integer
     */
    Integer updateStatus (@Param("name") String name, @Param("status") Integer status, @Param("updator") Long updator);

    /**
     *
     * @param id Long
     * @return Integer
     */
    Integer deleteById (@Param("id") Long id);
}
