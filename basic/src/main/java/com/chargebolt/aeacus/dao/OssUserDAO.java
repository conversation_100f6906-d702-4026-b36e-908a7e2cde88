package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import org.apache.ibatis.annotations.Param;
import so.dian.eros.pojo.dto.AllocateUserDTO;

import java.util.List;

public interface OssUserDAO {

    OssUserDO getByMobile(@Param("mobile") String mobile);

    OssUserDO getById(@Param("id") Long id);

    OssUserDO queryByName(@Param("name") String name);

    Long insert(OssUserDO ossUserDO);

    Integer updateToken(OssUserDO ossUserDO);

    Integer update(OssUserDO ossUserDO);

    List<OssUserDO> queryByPage(@Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    List<OssUserDO> queryByIds(@Param("ids") List<Long> ids);

    List<OssUserDO> queryByCooperatorId(@Param("cooperatorId") Long cooperatorId);


    List<OssUserDO> query(@Param("userDO")OssUserDO ossUserDO,@Param("ids") List<Long> userIds);

//    List<OssUserDO> queryByCooperator(@Param("cooperatorId") Long cooperatorId);

//    List<OssUserDO> queryByNames(@Param("names") List<String> names);

    List<OssUserDO> queryByDpmCode(@Param("dpmCodes") List<String> dpmCodes);

    List<AllocateUserDTO> queryAllocateUsersByName(@Param("name") String principalName, @Param("agentIds") List<Long> agentIds);

    List<AllocateUserDTO> queryAllByNameAndId(@Param("principalName") String principalName, @Param("currentPrincipalId") Long currentPrincipalId);

    /**
     * 获取代理商老板登录账号
     *
     * @param agentId
     * @return
     */
    List<OssUserDO> getByAgentId(@Param("agentId") Long agentId);
}
