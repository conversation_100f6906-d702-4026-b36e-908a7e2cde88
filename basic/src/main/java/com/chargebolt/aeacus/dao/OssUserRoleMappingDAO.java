package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OssUserRoleMappingDAO {

    List<OssUserRoleMappingDO> queryByUser(@Param("userId") Long userId);

    List<OssUserRoleMappingDO> queryByRole(@Param("roleId") Long roleId);

    void insert(OssUserRoleMappingDO ossUserRoleMappingDO);

    List<OssUserRoleMappingDO> selectByRoleIds(@Param("roleIds") List<Long> roleIds);

    List<OssUserRoleMappingDO> selectByUserIds(@Param("userIds") List<Long> userIds);

    void deleteByUserId(@Param("userId") Long userId);
}
