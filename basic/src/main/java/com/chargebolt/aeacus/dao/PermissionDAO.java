package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.PermissionDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PermissionDAO {

    List<PermissionDO> queryByPage(@Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    List<PermissionDO> queryByIds(@Param("ids") List<Long> permissionIds);

    PermissionDO getByCode(@Param("code") String code);

    Long insert(PermissionDO permissionDO);

    Integer update(PermissionDO permissionDO);

    List<PermissionDO> selectByGroupIds(@Param("groupIds") List<Long> groupIds);
}
