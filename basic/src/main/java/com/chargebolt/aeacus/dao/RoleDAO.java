package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.component.annotation.DataAuthoritySql;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RoleDAO {

    List<RoleDO> queryByIds(@Param("ids") List<Long> ids);
    RoleDO queryById(@Param("id") Long id);

    List<RoleDO> selectAll();

    @DataAuthoritySql
    List<RoleDO> select();
    Long insert(RoleDO roleDO);

    Integer update(RoleDO roleDO);

    @DataAuthoritySql
    List<RoleDO> selectByPage(@Param("name") String name, @Param("agentId") Long agentId);

    RoleDO selectByName(@Param("name") String name);


    Integer removeById(@Param("id") Long id);

    List<RoleDO> selectByAgentId(@Param("agentId") Long agentId);
}
