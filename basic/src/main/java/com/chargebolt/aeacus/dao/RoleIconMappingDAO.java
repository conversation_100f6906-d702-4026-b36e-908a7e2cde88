package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.RoleIconMappingDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RoleIconMappingDAO {
    List<RoleIconMappingDO> queryAllRoleIcons();

    /**
     * 批量插入角色和icon关系
     * @param list List<RoleIconMappingDO>
     */
    void batchInsert(List<RoleIconMappingDO> list);

    /**
     * 通过roleId查询对应的角色和icon关系
     * @param roleId Long
     * @return List<RoleIconMappingDO>
     */
    List<RoleIconMappingDO> queryMappingsByRoleId(Long roleId);

    /**
     * 根据Id删除角色和icon关系
     * @param ids List<Long>
     */
    void deleteByIds(@Param("ids") List<Long> ids);
}
