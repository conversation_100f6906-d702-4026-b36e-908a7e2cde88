package com.chargebolt.aeacus.dao;

import com.chargebolt.aeacus.entity.dataobject.RolePermissionMappingDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RolePermissionMappingDAO {

    List<RolePermissionMappingDO> queryByRole(@Param("roleId") Long roleId);

    List<RolePermissionMappingDO> queryByRoleIds(@Param("ids") List<Long> ids);

    void insert(RolePermissionMappingDO rolePermissionMappingDO);

    void deleteByIds(@Param("ids") List<Long> ids);
}
