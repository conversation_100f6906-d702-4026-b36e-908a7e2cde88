package com.chargebolt.aeacus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssMerchantUserDTO {

    private Long userId;
    // 账户名称
    private String name;
    // 手机号
    private String mobile;
    // 密码
    private String password;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 未加密
    private String accessToken;
    // 代理商ID
    private Long cooperatorId;
    // accessToken加密之后
    private String userToken;
    //0可用 1禁用
    private Integer status;
    // 创建者
    private Long creator;
    // 修改者
    private Long updator;
    // 登陆次数
    private Date loginTime;
    private Long agentId;
}
