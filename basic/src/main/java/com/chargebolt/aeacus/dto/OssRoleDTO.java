package com.chargebolt.aeacus.dto;

import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssRoleDTO {
    private Long id;
    private String name;
    private Long moduleId;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updator;
    private Long agentId;

    private List<OssGroupDTO> permissionGroup;
    private List<OssIconDTO> iconDTOList;
}
