package com.chargebolt.aeacus.dto;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssUserDTO {

    private Long userId;
    private String name;
    private String fullName;
    private String nickName;
    private String nationCode;
    private String mobile;
    private String userToken;
    private Long cooperatorId;
    private Integer type;
    private String departmentCode;
    private String departmentName;
    private Integer dataPerType;
    private Integer status;

    private String creator;
    private Date create_time;

    private String updator;
    private Date update_time;
    private Integer readonlFlag;
    private Long agentId;
    private String agentName;

    private List<OssRoleDTO> roleDtos;

}
