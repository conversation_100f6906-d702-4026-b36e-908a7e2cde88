package com.chargebolt.aeacus.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OssUserOpDTO {

    private Long id;
    private String name;
    private String fullName;
    private String nickName;
    private String nationCode;
    private String mobile;
    private String departmentCode;
    private Integer dataPerType;
    private Integer type;
    private Long cooperatorId;
    private Integer status;

    private Long operatorId;
    private String operatorName;
    private Long agentId;
    private List<Long> roleIds;
}
