package com.chargebolt.aeacus.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssUserQueryDTO extends BasePageParam {
    private String fullName;
    private String nickName;
    private String departmentCode;
    private List<Long> roleIds;
    private Integer status;
    private Long agentId;
    private List<Long> userIds;
}
