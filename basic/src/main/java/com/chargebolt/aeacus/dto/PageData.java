package com.chargebolt.aeacus.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 分页返回
 *
 * <AUTHOR>
 * @date 2018/3/27 下午8:17
 * @Copyright 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "of", access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageData<T> implements Serializable {

    private static final long serialVersionUID = -4257092979237049883L;
    /**
     * 列表数据
     */
    private List<T> list;
    private Long totalCount;
    private Long pageNo;
    private Integer pageSize;
    /**
     * 列表扩展信息
     */
    private Map<String, Object> extra;

    /**
     * 正常返回
     *
     * @param data 数据内容
     * @param <T> 返回数据的类型
     * @return 包装后的返回
     */
    public static <T> PageData<T> create(List<T> data) {
        return create(data, null);
    }

    public static <T> PageData<T> create(List<T> data, Long totalCount) {
        return create(data, totalCount, null, null);
    }

    public static <T> PageData<T> create(List<T> data, Long totalCount, Long pageNo, Integer pageSize) {
        return create(data, totalCount, pageNo, pageSize, null);
    }

    public static <T> PageData<T> create(List<T> data, Long totalCount, Long pageNo, Integer pageSize,
            Map extra) {
        return PageData.<T>of().setList(data).setTotalCount(totalCount).setPageNo(pageNo).setPageSize(pageSize)
                .setExtra(extra);
    }
}