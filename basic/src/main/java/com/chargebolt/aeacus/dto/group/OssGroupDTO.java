package com.chargebolt.aeacus.dto.group;

import com.chargebolt.aeacus.dto.OssPermissionDTO;
import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OssGroupDTO {
    private Long id;

    private String name;

    private Integer type;

    private String creator;

    private Date createTime;

    private String updator;

    private Date updateTime;

    private Integer deleted;

    private String remark;

    private List<OssPermissionDTO> permissionDTOList;
}
