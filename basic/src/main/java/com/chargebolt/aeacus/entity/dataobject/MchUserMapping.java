/*
 * Dian.so Inc.
 * Copyright (c) 2016-2025 All Rights Reserved.
 */
package com.chargebolt.aeacus.entity.dataobject;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * mch_user_mapping
 *
 * <AUTHOR>
 * @version $Id: MchUserMapping.java, v 0.1 2025-05-09 11:11:48 Exp $
 */
@Data
public class MchUserMapping implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 174887641519373210L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long mchId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}