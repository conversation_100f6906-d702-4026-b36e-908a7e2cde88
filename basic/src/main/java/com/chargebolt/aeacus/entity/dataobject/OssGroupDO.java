package com.chargebolt.aeacus.entity.dataobject;

import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class OssGroupDO {

    public OssGroupDO(Long id,String name,Integer type){
        this.id = id;
        this.name = name;
        this.type = type;
    }

    private Long id;

    private String name;

    private Integer type;

    private String creator;

    private Date createTime;

    private String updator;

    private Date updateTime;

    private Integer deleted;

    private String remark;

}