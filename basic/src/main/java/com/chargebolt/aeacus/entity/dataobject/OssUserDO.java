package com.chargebolt.aeacus.entity.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class OssUserDO {

    private Long id;
    private String name;
    private String nickName;
    private String fullName;
    private String mobile;
    private String password;
    private Date createTime;
    private Date updateTime;
    /**
     * 1.BD
     * 2.代理商老板
     */
    private Integer type;
    private String accessToken;
    private Long cooperatorId;
    private String userToken;
    private Integer multiLogin;

    private String departmentCode;
    //数据权限类型：0全部1本人2本部门3本部门及以下
    private Integer dataPerType;
    //0可修改 1不可修改
    private Integer readonlFlag;
    //0可用 1禁用
    private Integer status;

    private String creator;
    private String updator;
    private Date loginTime;
    private Long agentId;
    /**
     * 逻辑删除标识
     * 0.正常
     * 1.逻辑删除
     */
    private Integer deleted;
    public String getNickName(){
        return nickName == null ? "":nickName;
    }

    public String getNickNameOrName() {
        return nickName == null ? name:nickName;
    }
}
