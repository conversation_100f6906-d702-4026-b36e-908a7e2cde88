package com.chargebolt.aeacus.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.enums.ReadonlFlagEnum;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.common.util.ValidateUtil;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.dto.OssUserOpDTO;
import com.chargebolt.aeacus.dto.OssUserPasswordOpDTO;
import com.chargebolt.aeacus.dto.OssUserQueryDTO;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.DepartmentManager;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.aeacus.service.manager.UserRoleManager;
import com.chargebolt.aeacus.service.support.UserSupport;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.enums.DataPerTypeEnum;
import com.chargebolt.ezreal.utils.MobileUtil;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.service.authority.LoginUserDataAuthorityService;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.codec.MD5Utils;

@Service
@Slf4j
public class AeacusUserService {
    @Resource
    private UserManager userManager;
    @Resource
    private UserRoleManager userRoleManager;
    @Resource
    private UserSupport userSupport;
    @Resource
    private RoleService roleService;
    @Resource
    private DepartmentManager departmentManager;
    @Resource
    private LoginUserDataAuthorityService loginUserDataAuthorityService;
    @Resource
    private AgentService agentService;

    //创建
    public Long create(OssUserOpDTO ossUserOpDTO){
        OssUserDO ossUserDO = userManager.queryByName(ossUserOpDTO.getName());
        if(ossUserDO != null){
            throw new I18nMessageException(BizExceEnum.USER_NAME_ALREADY_EXSIT);
        }
        // 手机号码是否已存在
        if(userManager.getByMobile(MobileUtil.getNationMobile(ossUserOpDTO.getNationCode(),ossUserOpDTO.getMobile())) != null){
            throw new I18nMessageException(BizExceEnum.EXISTS_MOBILE);
        }
        ossUserDO = userSupport.convertAgent2OssUserDO(ossUserOpDTO);
        userRoleManager.insertWithRole(ossUserDO,ossUserOpDTO.getRoleIds());
        return ossUserDO.getId();
    }

    //修改
    public Boolean update(OssUserOpDTO ossUserOpDTO) {
        OssUserDO ossUserDO = getById(ossUserOpDTO.getId());
        if(ReadonlFlagEnum.CANNOT_UPDATE.getCode().equals(ossUserDO.getReadonlFlag())){
            throw new I18nMessageException(BizExceEnum.USER_CANNOT_UPDATE);
        }
        if(!ossUserDO.getMobile().equals(ossUserOpDTO.getMobile())){
            log.warn("user update mobile,id:{},oriMobile:{},newMobile:{}",ossUserDO.getId(),ossUserDO.getMobile(),ossUserOpDTO.getMobile());
        }
        userSupport.operateOssUserDO(ossUserDO,ossUserOpDTO);
        userRoleManager.updateWithRole(ossUserDO,ossUserOpDTO.getRoleIds());
        return true;
    }

    //修改密码
    public Boolean updatePassword(OssUserPasswordOpDTO passwordOpDTO) {
        OssUserDO ossUserDO = getById(passwordOpDTO.getId());
        ossUserDO.setUpdator(passwordOpDTO.getOperatorName());
        if (passwordOpDTO.getType() == null || AeacusConstsnts.USER_PASSWORD_RESET.equals(passwordOpDTO.getType())){
            ossUserDO.setPassword(generateEncryptPassword(AeacusConstsnts.USER_INIT_PASSWORD));
        }else {
//            ValidateUtil.assertEquals(ossUserDO.getPassword(), generateEncryptPassword(passwordOpDTO.getOriPassword()),BizExceEnum.USER_INVALID_ORIPASSWORD);
//            ValidateUtil.assertNotEquals(passwordOpDTO.getPassword(), passwordOpDTO.getOriPassword(),BizExceEnum.USER_PASSWORD_SAMEWITH_ORI);
            ossUserDO.setPassword(generateEncryptPassword(passwordOpDTO.getPassword()));
        }
        userManager.update(ossUserDO);
        return true;
    }

    //根据id列表查询用户
    public List<OssUserDTO> queryByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<OssUserDO> ossUserDOS = userManager.queryByIds(ids);
        Map<Long, Agent> agentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(ossUserDOS)){
            List<Agent> agentList = agentService.queryListByIds(ossUserDOS.stream().map(OssUserDO::getAgentId).collect(Collectors.toList()));
            agentMap = agentList.stream().collect(Collectors.toMap(Agent::getId, agent -> agent));
        }
        return userSupport.convert2OssUserDTOWithAgent(ossUserDOS,agentMap);
    }

    //根据用户名查询
    public OssUserDTO queryByName(String name) {
        OssUserDO ossUserDO = userManager.queryByName(name);
        return userSupport.convert2OssUserDTO(ossUserDO);
    }

    //根据运营商id查询
    public List<OssUserDTO> queryByCooperator(Long cooperatorId) {
        if (cooperatorId == null){
            return Collections.emptyList();
        }
        List<OssUserDO> ossUserDOS = userManager.queryByCooperator(cooperatorId);
        return userSupport.convert2OssUserDTO(ossUserDOS);
    }

    //根据手机号查询
    public OssUserDTO queryByMobile(String mobile) {
        if (StringUtils.isBlank(mobile)){
            return null;
        }
        OssUserDO ossUserDO = userManager.getByMobile(mobile);
        return userSupport.convert2OssUserDTO(ossUserDO);
    }

    //根据id查看用户详情
    public OssUserDTO queryById(Long id) {
        ValidateUtil.requiredNotNull(id,BizExceEnum.USER_ID_NULL);
        OssUserDO ossUserDO = userManager.getById(id);
        if(ossUserDO == null){
            return null;
        }
        List<OssRoleDTO> ossRoleDTOS = roleService.queryByUserId(ossUserDO.getId());
        return userSupport.convert2OssUserDTOWithRole(ossUserDO,ossRoleDTOS);
    }

    //分页查询&模糊搜索
    public PageData<OssUserDTO> queryByQueryDTO(OssUserQueryDTO queryDTO) {
        if(CollectionUtils.isEmpty(queryDTO.getRoleIds())){
            return queryByPage(queryDTO, queryDTO.getUserIds());
        }else {
            List<Long> userIds = userRoleManager.getUserIdsByRoleIds(queryDTO.getRoleIds());
            if(CollectionUtils.isEmpty(userIds)){
                return PageData.create(null);
            }
            return queryByPage(queryDTO,userIds);
        }
    }

    public PageData<OssUserDTO> queryByPage(OssUserQueryDTO queryDTO,List<Long> userIds){
        OssUserDO ossUserDO = new OssUserDO();
        ossUserDO.setNickName(queryDTO.getNickName());
        ossUserDO.setDepartmentCode(queryDTO.getDepartmentCode());
        ossUserDO.setFullName(queryDTO.getFullName());
        ossUserDO.setType(AeacusConstsnts.USER_TYPE_BD);
        ossUserDO.setAgentId(queryDTO.getAgentId());
        // 用户状态筛选；0可用，1禁用，null查询全部
        ossUserDO.setStatus(queryDTO.getStatus());
        Page<OssUserDO> ossUserDOS = PageHelper.startPage(queryDTO.getPageNo(), queryDTO.getPageSize(), Boolean.TRUE)
                .setOrderBy(" id DESC")
                .doSelectPage(new ISelect() {
            @Override
            public void doSelect() {
                userManager.queryByPage(ossUserDO,userIds);
            }
        });
        if(CollectionUtils.isEmpty(ossUserDOS)){
            return PageData.create(null);
        }
        Map<Long,List<OssRoleDTO>> roleDTOS = roleService.queryByUserIds(ossUserDOS.getResult().stream().map(OssUserDO::getId).collect(Collectors.toList()));
        List<OssUserDTO> ossUserDTOS = userSupport.convert2OssUserDTO(ossUserDOS.getResult(),roleDTOS);
        return PageData.create(ossUserDTOS,ossUserDOS.getTotal(), (long) ossUserDOS.getPageNum(),ossUserDOS.getPageSize());
    }

    private OssUserDO getById(Long userId){
        OssUserDO ossUserDO = userManager.getById(userId);
        if (ossUserDO == null){
            throw new I18nMessageException(BizExceEnum.NO_SUCH_OBJECT);
        }
        return ossUserDO;
    }

    private String generateEncryptPassword(String password){
        return MD5Utils.encrypt(password + UserManager.PASSWORD_SALT).toUpperCase();
    }



    /**
     * 根据部门code查询部门创建人
     *
     * @param dpmCode 部门ID
     * @param dataPerType 数据类型 {@link DataPerTypeEnum}
     * @return
     */
    public List<OssUserDTO> queryDpmCreatorByCode(String dpmCode, Integer dataPerType) {

        List<OssUserDTO> ossUserDTOS = new ArrayList<>();
        OssDepartmentDO departmentDO = departmentManager.getByCode(dpmCode);
        if (Objects.nonNull(departmentDO)) {

            if (DataPerTypeEnum.DEPARTMENT.getValue().equals(dataPerType)) {
                // 根据code查询部门当前部门的所有用户
                List<String> dpmCodes = new ArrayList<>();
                dpmCodes.add(dpmCode);
                List<OssUserDO> ossUserDOS = userManager.getByDpmCode(dpmCodes);
                ossUserDTOS = userSupport.convert2OssUserDTO(ossUserDOS);
            }
            else if (DataPerTypeEnum.DEPARTMENT_AND_BELOW.getValue().equals(dataPerType)) {
                // 根据部门code查询本部门及下属部门的部门信息
                List<OssDepartmentDO> ossDepartmentDOS = departmentManager.getLikeCode(dpmCode);
                List<String> dpmCodes = ossDepartmentDOS.stream().map(OssDepartmentDO::getCode)
                        .collect(Collectors.toList());
                // 根据部门code列表查询相应的用户信息列表
                List<OssUserDO> ossUserDOS = userManager.getByDpmCode(dpmCodes);
                ossUserDTOS = userSupport.convert2OssUserDTO(ossUserDOS);
            }
        }
        return ossUserDTOS;
    }


    /**
     * 获取用户部门和数据权限获取员工列表
     *
     * @return
     */
    public List<OssUserDTO> getUserList(final OssUserDTO userInfo, String sellerName) {

        List<OssUserDTO> ossUserDTOS = new ArrayList<>();
        UserDataAuthorityContext authority = loginUserDataAuthorityService.getLoginUserDataAuthority();
        if (AuthorityLevelEnum.ALL.getCode().equals(authority.getAuthorityLevel())){
            List<OssUserDO> ossUserDOS = userManager.queryByPage(new OssUserDO(), null);
            ossUserDTOS = userSupport.convert2OssUserDTO(ossUserDOS);
        }
        if (AuthorityLevelEnum.AGENT.getCode().equals(authority.getAuthorityLevel())) {
            List<OssUserDO> ossUserDOS = userManager.getByAgentId(userInfo.getAgentId());
            ossUserDTOS = userSupport.convert2OssUserDTO(ossUserDOS);
        }
        if (AuthorityLevelEnum.USER.getCode().equals(authority.getAuthorityLevel())) {
            OssUserDO ossUserDO = userManager.getById(userInfo.getUserId());
            ossUserDTOS = userSupport.convert2OssUserDTO(Arrays.asList(ossUserDO));
        }
        // 模糊匹配shellName
        if (StringUtils.isNotBlank(sellerName)) {
            ossUserDTOS = ossUserDTOS.stream().filter(ossUserDO -> ossUserDO.getName().contains(sellerName))
                    .collect(Collectors.toList());
        }
        return ossUserDTOS;
    }

    /**
     * 区号+手机号码获取用户信息
     *
     * @param mobile
     * @return
     */
    public OssUserDTO getByNationMobile(String mobile) {
        if (StringUtils.isBlank(mobile)){
            return null;
        }
        OssUserDO ossUserDO = userManager.getByMobile(mobile);
        return userSupport.convert2OssUserDTO(ossUserDO);
    }

}

