package com.chargebolt.aeacus.service;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.entity.dataobject.*;
import com.chargebolt.aeacus.service.manager.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.talos.pojo.entity.MerchantDO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j(topic = "biz")
public class AuthticationService {
    private static final String USER_PERMISSION_CODE_LIST="__CACHE_USER_PERMISSION_CODE_LIST_";
    @Resource
    private PermissionManager permissionManager;
    @Resource
    private RoleManager roleManager;
    @Resource
    private RoleIconManager roleIconManager;
    @Resource
    private RedissonClient redissonClient;

    public Boolean checkPermission(Long userId, String permissionCode){
        PermissionDO permissionDO = permissionManager.getByCode(permissionCode);
        if (permissionDO == null){
            return false;
        }
        RBucket<List<String>> bucket=redissonClient.getBucket("USER_PERMISSION_CODE_LIST");
        List<String> userPermissionList= new ArrayList<>();
        userPermissionList= bucket.get();
        if(CollectionUtils.isEmpty(userPermissionList)){
            List<PermissionDO> permissionDOS = permissionManager.getPermissionByUserId(userId);
            userPermissionList = permissionDOS.stream()
                    .map(PermissionDO::getCode)
                    .collect(Collectors.toList());
            bucket.set(userPermissionList, 10, TimeUnit.MINUTES);
        }

        if (userPermissionList.contains(AeacusConstsnts.SUPER_PERMISSION_CODE) || userPermissionList.contains(permissionCode)){
            return true;
        }
        // 校验icon权限
        List<RoleDO> roleDOS = roleManager.queryByUser(userId);
        for (RoleDO roleDO : roleDOS) {
            List<OssIconDTO> iconDTOS = roleIconManager.getIconByRoleId(roleDO.getId());
            if (!CollectionUtils.isEmpty(iconDTOS)) {
                return true;
            }
        }

        return false;
    }
}
