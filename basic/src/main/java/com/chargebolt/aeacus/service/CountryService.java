package com.chargebolt.aeacus.service;

import com.chargebolt.aeacus.entity.dataobject.CountryDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;


import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Service
public class CountryService {

    private final ResourceLoader resourceLoader;
    private static final String countriesJsonPath = "classpath:countries.json";

    @Autowired
    public CountryService(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    public List<CountryDO> parseJsonFile() throws IOException {
        Resource resource = resourceLoader.getResource(countriesJsonPath);
        InputStream inputStream = resource.getInputStream();
        ObjectMapper objectMapper = new ObjectMapper();
        List<CountryDO> countries = objectMapper.readValue(inputStream, new TypeReference<List<CountryDO>>() {
        });
        // 排重
        List<CountryDO> distinctCountries = countries.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CountryDO::getPhoneCode))),
                        ArrayList::new
                ));
        return distinctCountries;
    }
}
