package com.chargebolt.aeacus.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.department.OssDepartmentCreateDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentDeleteDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentUpdateDTO;
import com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.DepartmentManager;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.aeacus.service.support.DepartmentSupport;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DepartmentService {
    @Resource
    private DepartmentManager departmentManager;
    @Resource
    private DepartmentSupport departmentSupport;
    @Resource
    private UserManager userManager;

    public Boolean create(OssDepartmentCreateDTO createDTO){
        OssDepartmentDO parentDO = departmentManager.getById(createDTO.getParentId());
        if(parentDO == null){
            throw new I18nMessageException(BizExceEnum.DEPARTMENT_PARENT_NOT_EXSIT);
        }
        if(parentDO.getLevel() >= AeacusConstsnts.DEPARTMENT_MAX_LEVEL){
            throw new I18nMessageException(BizExceEnum.DEPARTMENT_CANNOT_DEEPER);
        }
        OssDepartmentDO departmentDO = departmentManager.getByName(createDTO.getName(),createDTO.getAgentId());
        if(departmentDO != null){
            throw new I18nMessageException(BizExceEnum.DEPARTMENT_NAME_ALREADY_EXSIT);
        }
        departmentDO = departmentSupport.convert2DepartmentDO(createDTO,parentDO);
        departmentManager.insert(departmentDO);
        return true;
    }

    public Boolean update(OssDepartmentUpdateDTO updateDTO) {
        OssDepartmentDO ossDepartmentDO = departmentManager.getById(updateDTO.getId());
        if(ossDepartmentDO == null){
            throw new I18nMessageException(BizExceEnum.DEPARTMENT_PARENT_NOT_EXSIT);
        }
        ossDepartmentDO.setName(updateDTO.getName());
        ossDepartmentDO.setUpdator(updateDTO.getOperatorName());
        departmentManager.updateById(ossDepartmentDO);
        return true;
    }

    public Boolean delete(OssDepartmentDeleteDTO deleteDTO) {
        OssDepartmentDO ossDepartmentDO = departmentManager.getById(deleteDTO.getId());
        if(ossDepartmentDO == null){
            throw new I18nMessageException(BizExceEnum.DEPARTMENT_NOT_EXSIT);
        }
        if(Objects.equals(ossDepartmentDO.getDeleted(), AeacusConstsnts.DELETED)){
            return true;
        }
        List<OssUserDO> ossUserDOS = userManager.queryByDepartmentCode(ossDepartmentDO.getCode());
        if(!CollectionUtils.isEmpty(ossUserDOS)){
            throw new I18nMessageException(BizExceEnum.DEPARTMENT_ALREADY_INUSE);
        }
        List<OssDepartmentDO> ossDepartmentDOS = departmentManager.getLikeCode(ossDepartmentDO.getCode());
        if(CollectionUtils.isEmpty(ossDepartmentDOS)){
            return true;
        }
        departmentManager.deleteByIds(ossDepartmentDOS.stream().map(OssDepartmentDO::getId).collect(Collectors.toList()),deleteDTO.getOperatorName());
        return true;
    }

    public OssDepartmentDTO query() {
        List<OssDepartmentDO> ossDepartmentDOS = departmentManager.getDepartmentCache();
        if(CollectionUtils.isEmpty(ossDepartmentDOS)){
            return null;
        }
        Map<Long,OssDepartmentDTO> dtos = departmentSupport.convert2DepartmentDTOMap(ossDepartmentDOS);
        OssDepartmentDTO orignDto =  dtos.get(AeacusConstsnts.DEPARTMENT_FIRST_STAGE_ID);
        for (Long id : dtos.keySet()) {
            OssDepartmentDTO dto = dtos.get(id);
            if(dto.getParentId() != null){
                OssDepartmentDTO parentDto = dtos.get(dto.getParentId());
                parentDto.getChildNodes().add(dto);
            }
        }
        return orignDto;
    }
}
