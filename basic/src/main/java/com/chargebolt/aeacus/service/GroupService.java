package com.chargebolt.aeacus.service;

import com.chargebolt.aeacus.common.enums.GroupTypeEnum;
import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.dto.group.OssGroupQueryDTO;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.entity.dataobject.OssGroupDO;
import com.chargebolt.aeacus.entity.dataobject.PermissionDO;
import com.chargebolt.aeacus.service.manager.GroupManager;
import com.chargebolt.aeacus.service.manager.PermissionManager;
import com.chargebolt.aeacus.service.support.GroupSupport;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class GroupService {
    @Resource
    private PermissionManager permissionManager;
    @Resource
    private GroupManager groupManager;
    @Resource
    private GroupSupport groupSupport;

    public List<OssGroupDTO> query(OssGroupQueryDTO ossGroupQueryDTO){
        //1.获取group信息
        List<OssGroupDO> ossGroupDOS = groupManager.query(new OssGroupDO(ossGroupQueryDTO.getId(),ossGroupQueryDTO.getName(),ossGroupQueryDTO.getType()));
        if(CollectionUtils.isEmpty(ossGroupDOS)){
            return Collections.emptyList();
        }
        //2.按照type分组
        Map<Integer,List<Long>> groupTypeMap = new HashMap<>();
        ossGroupDOS.forEach(item->{
            List<Long> groupIds = groupTypeMap.get(item.getType());
            if(CollectionUtils.isEmpty(groupIds)){
                groupIds = new ArrayList<>();
                groupTypeMap.put(item.getType(),groupIds);
            }
            groupIds.add(item.getId());
        });
        List<OssGroupDTO> ossGroupDTOS = null;
        //3.获取相应组对象
        for (Integer type : groupTypeMap.keySet()) {
            GroupTypeEnum groupTypeEnum = GroupTypeEnum.explain(type);
            if(groupTypeEnum == null){
                continue;
            }
            switch (groupTypeEnum){
                case PERMISSION:
                    Map<Long,List<OssPermissionDTO>> map = groupingPermission(groupTypeMap.get(type));
                    ossGroupDTOS = groupSupport.convert2OssGroupDTOs(ossGroupDOS,map);
                    break;
                default:
                    break;
            }
        }
        return ossGroupDTOS;
    }

    private Map<Long,List<OssPermissionDTO>> groupingPermission(List<Long> groupIds){
        List<PermissionDO> permissionDOS = permissionManager.getByGroupIds(groupIds);
        if(CollectionUtils.isEmpty(permissionDOS)){
            return new HashMap<>();
        }
        Map<Long,List<OssPermissionDTO>> map = new HashMap<>();
        permissionDOS.forEach(item->{
            List<OssPermissionDTO> dtoList = map.get(item.getGroupId());
            if(CollectionUtils.isEmpty(dtoList)){
                dtoList = new ArrayList<>();
                map.put(item.getGroupId(),dtoList);
            }
            dtoList.add(OssPermissionDTO.builder().id(item.getId()).url(item.getUrl()).img(item.getImg()).description(item.getDescription()).code(item.getCode()).groupId(item.getGroupId()).relyIds(item.getRelyIds()).build());
        });
        return map;
    }

    public List<OssGroupDTO> query(List<OssPermissionDTO> permissionDOS){
        if(CollectionUtils.isEmpty(permissionDOS)){
            return Collections.emptyList();
        }
        Set<Long> groupIds = new HashSet<>();
        Map<Long,List<OssPermissionDTO>> permissionMap = new HashMap<>();
        permissionDOS.forEach(item->{
            List<OssPermissionDTO> permissionDTOS = permissionMap.get(item.getGroupId());
            if(CollectionUtils.isEmpty(permissionDTOS)){
                permissionDTOS = new ArrayList<>();
                permissionMap.put(item.getGroupId(),permissionDTOS);
            }
            groupIds.add(item.getGroupId());
            permissionDTOS.add(item);
        });
        List<OssGroupDO> ossGroupDOS = groupManager.getByIds(new ArrayList<>(groupIds));
        if(CollectionUtils.isEmpty(ossGroupDOS)){
            return Collections.emptyList();
        }
        return groupSupport.convert2OssGroupDTOs(ossGroupDOS,permissionMap);

    }

}
