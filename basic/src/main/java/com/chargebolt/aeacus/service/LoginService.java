package com.chargebolt.aeacus.service;

import java.util.Date;
import java.util.List;
import java.util.Random;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.chargebolt.aeacus.cache.LoginCache;
import com.chargebolt.aeacus.cache.LoginCache.LoginCacheDO;
import com.chargebolt.aeacus.cache.UserCache;
import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.constant.CommonConstants;
import com.chargebolt.aeacus.common.enums.UserStatusEnum;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.common.exception.LoginExceEnum;
import com.chargebolt.aeacus.common.util.AESUtils;
import com.chargebolt.aeacus.entity.bizobject.MobileBO;
import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.MerchantUserManager;
import com.chargebolt.aeacus.service.manager.SmsManager;
import com.chargebolt.aeacus.service.manager.UserManager;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.codec.MD5Utils;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;

@Component
@Slf4j
public class LoginService {

    private final static String AES_SECRET = "qo5q2rEGlbmefKfU";
    @Resource
    private MerchantUserManager merchantUserManager;
    @Resource
    private UserManager userManager;
    @Resource
    private LoginCache loginCache;
    @Resource
    private SmsManager smsManager;
    @Resource
    private UserCache userCache;


    public OssUserDO loginByPassword(String name, String password) {
        OssUserDO ossUserDO = userManager.queryByName(name);
        if (ossUserDO == null) {
            throw new I18nMessageException(BizExceEnum.USER_NOT_EXSIT);
        }
        if (StringUtils.isEmpty(ossUserDO.getPassword())) {
            log.error("strange issue for login, name=[{}], password=[{}], ossUserDO=[{}]", name, password, ossUserDO);
            return null;
        }
        if (UserStatusEnum.DISABLED.getCode().equals(ossUserDO.getStatus())) {
            throw new I18nMessageException(BizExceEnum.USER_HAD_BE_DISABLED);
        }
        LoginCacheDO loginCacheDO = loginCache.getLoginCacheDO(name);
        if (loginCacheDO != null && loginCacheDO.getCheckTimes() > 10) {
            // tag 未国际化
            throw BizException.create(LoginExceEnum.PASSWORD_CHECK_MORE_TIMES, "密码检查超过最大次数");
        }
        if (!ossUserDO.getPassword().equals(MD5Utils.encrypt(password + UserManager.PASSWORD_SALT).toUpperCase())) {
            if (loginCacheDO == null) {
                loginCacheDO = new LoginCacheDO();
            }
            loginCacheDO.setCheckTimes(loginCacheDO.getCheckTimes() + 1);
            loginCache.setLoginCacheDO(name, loginCacheDO);
            log.error("strange issue for login, name=[{}], password=[{}], ossUserDO=[{}]", name, password, ossUserDO);
            return null;
        }
        return postLoginCheck(ossUserDO);
    }

    public OssUserDO loginBySms(MobileBO mobileBO, String smsToken) {
        OssUserDO ossUserDO = userManager.getByMobile(mobileBO.getMobile());
        if (ossUserDO == null) {
            return null;
        }
        BizResult bizResult = smsManager.checkSmsToken(mobileBO, smsToken);
        if (!bizResult.isSuccess()) {
            return null;
        }
        ossUserDO.setLoginTime(new Date());
        return postLoginCheck(ossUserDO);
    }

    public BizResult logout(String token) {
        OssUserDO ossUserDO = checkLoginStatus(token);
        if (ossUserDO == null) {
            return null;
        }
        userCache.removeUser(ossUserDO.getId());
        return BizResult.create(null);
    }

    private OssUserDO postLoginCheck(OssUserDO ossUserDO) {
        if (AeacusConstsnts.USER_ALLOW_MULTILOGIN.equals(ossUserDO.getMultiLogin())) {
            if (userCache.getUser(ossUserDO.getId()) == null) {
                // 失效后第一次登录，刷新token
                String newAccessToken = newAccessToken();
                ossUserDO.setAccessToken(newAccessToken);
                userManager.updateToken(ossUserDO);
                String tokenSrc = newAccessToken + "|" + ossUserDO.getId();
                String token = aesEncrypt(tokenSrc, AES_SECRET);
                ossUserDO.setUserToken(token);
                userCache.setUser(ossUserDO);
            } else {
                // 当前已经有登录，沿用当前token
                String tokenSrc = ossUserDO.getAccessToken() + "|" + ossUserDO.getId();
                String token = aesEncrypt(tokenSrc, AES_SECRET);
                ossUserDO.setUserToken(token);
            }
        } else {
            String newAccessToken = newAccessToken();
            ossUserDO.setAccessToken(newAccessToken);
            userManager.updateToken(ossUserDO);
            String tokenSrc = newAccessToken + "|" + ossUserDO.getId();
            String token = aesEncrypt(tokenSrc, AES_SECRET);
            userCache.setUser(ossUserDO);
            ossUserDO.setUserToken(token);
        }
        return ossUserDO;
    }

    public OssUserDO checkLoginStatus(String userToken) {
        if (StringUtils.isEmpty(userToken)) {
            return null;
        }
        String userIdStr = aesDecrypt(userToken, AES_SECRET);
        String[] splited = userIdStr.split("\\|");
        if (splited.length == 2) {
            Long userId = Long.parseLong(splited[1]);
            OssUserDO ossUserDO = userCache.getUser(userId);
            if (ossUserDO != null && ossUserDO.getAccessToken().equals(splited[0])) {
                return ossUserDO;
            }
        }
        return null;
    }

    /**
     * 用户登录token获取用户ID
     *
     * @param userToken
     * @return
     */
    public Long getUserIdByUserToken(String userToken) {
        String userIdStr = aesDecrypt(userToken, AES_SECRET);
        String[] splited = userIdStr.split("\\|");
        if (splited.length == 2) {
            return Long.parseLong(splited[1]);
        }
        return null;
    }

    /**
     * 校验商户账号是否登陆
     *
     * @param userToken String
     * @return OssMerchantUserDO
     */
    public OssMerchantUserDO merchantUserCheckLogin(String userToken) {
        String userIdStr = aesDecrypt(userToken, AES_SECRET);
        if (StringUtils.isEmpty(userIdStr)) {
            return null;
        }
        String[] splitStrArray = userIdStr.split("\\|");
        if (splitStrArray.length == CommonConstants.INTEGER_TWO) {
            Long userId = Long.parseLong(splitStrArray[CommonConstants.INTEGER_ONE]);
            OssMerchantUserDO ossUserDO = userCache.getMerchantUser(userId);
            if (ossUserDO != null && ossUserDO.getAccessToken().equals(splitStrArray[CommonConstants.INTEGER_ZERO])) {
                return ossUserDO;
            }
        }
        return null;
    }

    /**
     * 商户账号登陆
     *
     * @param name     账号名称
     * @param password 密码
     * @return ossMerchantUserDO
     */
    public OssMerchantUserDO merchantUserLogin(String name, String password) {
        OssMerchantUserDO ossMerchantUserDO = merchantUserManager.queryByName(name);
        if (null == ossMerchantUserDO) {
            throw new I18nMessageException(BizExceEnum.USER_NOT_EXSIT);
        }
        // 判断用户状态
        if (UserStatusEnum.DISABLED.getCode().equals(ossMerchantUserDO.getStatus())) {
            throw new I18nMessageException(BizExceEnum.USER_HAD_BE_DISABLED);
        }
        // 密码为null直接返回
        if (StringUtils.isEmpty(ossMerchantUserDO.getPassword())) {
            return null;
        }

        // 判断登陆次数是否大于三次，是则半小时后才能重试
        LoginCacheDO loginCacheDO = loginCache.getLoginCacheDO(CommonConstants.MERCHANT + name);
        if (loginCacheDO != null && loginCacheDO.getCheckTimes() > 10) {
            // tag 未国际化
            throw BizException.create(LoginExceEnum.PASSWORD_CHECK_MORE_TIMES, "密码检查超过最大次数");
        }
        // MD5加密
        String passwordMD5Str = MD5Utils.encrypt(password + UserManager.PASSWORD_SALT).toUpperCase();
        // 如果密码不相等，则增加一次校验次数
        if (!passwordMD5Str.equals(ossMerchantUserDO.getPassword())) {
            if (null == loginCacheDO) {
                loginCacheDO = new LoginCacheDO();
            } else {
                loginCacheDO.setCheckTimes(loginCacheDO.getCheckTimes() + 1);
            }
            loginCache.setLoginCacheDO(CommonConstants.MERCHANT + name, loginCacheDO);
            return null;
        }
        // 生产新的token
        String newAccessToken = newAccessToken();
        ossMerchantUserDO.setAccessToken(newAccessToken);
        // 更新token
        merchantUserManager.updateToken(ossMerchantUserDO);
        String tokenSrc = newAccessToken + "|" + ossMerchantUserDO.getId();
        String token = aesEncrypt(tokenSrc, AES_SECRET);
        userCache.setMerchantUser(ossMerchantUserDO);
        ossMerchantUserDO.setUserToken(token);
        return ossMerchantUserDO;
    }

    /**
     * 商户账号退出登陆
     *
     * @param token String
     * @return BizResult
     */
    public BizResult merchantUserLogout(String token) {
        OssMerchantUserDO ossUserDO = merchantUserCheckLogin(token);
        if (ossUserDO == null) {
            return null;
        }
        userCache.removeMerchantUser(ossUserDO.getId());
        return BizResult.create(null);
    }

    /**
     * 代理商下所有账号登出
     *
     * @param agentId
     */
    public void agentLogout(Long agentId) {
        OssUserDO ossUserDO= new OssUserDO();
        ossUserDO.setAgentId(agentId);
        List<OssUserDO> userDOList= userManager.queryByPage(ossUserDO, null);
        if(CollectionUtils.isNotEmpty(userDOList)){
            for(OssUserDO userDO: userDOList){
                loginCache.removeLoginCacheDO(userDO.getName());
                userCache.removeMerchantUser(userDO.getId());
            }
        }
    }

    private static String newAccessToken() {
        return String.format("%06d", new Random().nextInt(1000000));
    }

    private static String aesEncrypt(String src, String secret) {
        try {
            String aesCode = AESUtils.encrypt(src, secret);
            return aesCode;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String aesDecrypt(String src, String secret) {
        try {
            return AESUtils.decrypt(src, secret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
