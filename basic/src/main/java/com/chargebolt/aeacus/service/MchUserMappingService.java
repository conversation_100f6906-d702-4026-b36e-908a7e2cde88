/*
 * Dian.so Inc.
 * Copyright (c) 2016-2025 All Rights Reserved.
 */
package com.chargebolt.aeacus.service;

import com.chargebolt.aeacus.entity.dataobject.MchUserMapping;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: MchUserMappingService.java, v 0.1 2025-05-09 11:11:48 Exp $
 */

public interface MchUserMappingService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<MchUserMapping>     返回结果
     */
    List<MchUserMapping> listRecord(MchUserMapping model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return MchUserMapping     返回结果
     */
    MchUserMapping getRecord(MchUserMapping model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(MchUserMapping model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(MchUserMapping model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(MchUserMapping model);

    /**
     * 保存商户用户关联关系
     *
     * @param userId
     * @param mchId
     * @return
     */
    int saveMchUserRelated(Long userId, Long mchId);
}