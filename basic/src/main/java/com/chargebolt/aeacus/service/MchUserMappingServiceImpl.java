/*
 * Dian.so Inc.
 * Copyright (c) 2016-2025 All Rights Reserved.
 */
package com.chargebolt.aeacus.service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.chargebolt.aeacus.dao.MchUserMappingDAO;
import com.chargebolt.aeacus.entity.dataobject.MchUserMapping;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: MchUserMappingServiceImpl.java, v 0.1 2025-05-09 11:11:48 Exp $
 */
@Service
public class MchUserMappingServiceImpl implements MchUserMappingService {
    @Override
    public List<MchUserMapping> listRecord(MchUserMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return mchUserMappingDAO.listRecord(model);
    }

    @Override
    public MchUserMapping getRecord(MchUserMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return mchUserMappingDAO.getRecord(model);
    }

    @Override
    public int saveRecord(MchUserMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return mchUserMappingDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(MchUserMapping model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return mchUserMappingDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(MchUserMapping model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return mchUserMappingDAO.updateRecord(model);
    }

    @Override
    public int saveMchUserRelated(final Long userId, final Long mchId) {
        MchUserMapping query= new MchUserMapping();
        query.setUserId(userId);
        query.setMchId(mchId);
        query= getRecord(query);
        if(Objects.nonNull(query)){
            return -1;
        }
        MchUserMapping model= new MchUserMapping();
        model.setUserId(userId);
        model.setMchId(mchId);
        return saveRecord(model);
    }


    /**
     * 推荐使用构造器注入
     */
    private final MchUserMappingDAO mchUserMappingDAO;
    public MchUserMappingServiceImpl(ObjectProvider<MchUserMappingDAO> mchUserMappingProvider) {
        this.mchUserMappingDAO= mchUserMappingProvider.getIfUnique();
    }
}