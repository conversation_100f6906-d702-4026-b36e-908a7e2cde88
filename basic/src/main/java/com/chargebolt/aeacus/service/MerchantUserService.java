package com.chargebolt.aeacus.service;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.common.util.ValidateUtil;
import com.chargebolt.aeacus.dto.OssMerchantUserDTO;
import com.chargebolt.aeacus.dto.OssUserPasswordOpDTO;
import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;
import com.chargebolt.aeacus.service.manager.MerchantUserManager;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.aeacus.service.support.UserSupport;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.codec.MD5Utils;

@Service
@Slf4j
public class MerchantUserService {
    @Resource
    private MerchantUserManager merchantUserManager;
    @Resource
    private UserSupport userSupport;

    //创建
    public Long create(OssMerchantUserDTO ossMerchantUserDTO){
        OssMerchantUserDO ossMerchantUserDO = userSupport.convert2OssMerchantUserDO(ossMerchantUserDTO);
        return merchantUserManager.insert(ossMerchantUserDO);
    }

    //修改密码
    public Boolean updatePassword(OssUserPasswordOpDTO passwordOpDTO) {
        OssMerchantUserDO ossMerchantUserDO = merchantUserManager.getById(passwordOpDTO.getId());
        if (null == ossMerchantUserDO) {
            throw new I18nMessageException(BizExceEnum.USER_ID_NULL);
        }
        ossMerchantUserDO.setUpdator(passwordOpDTO.getOperatorId());
        // 1. 校验原密码是否正确
        ValidateUtil.assertEquals(ossMerchantUserDO.getPassword(), generateEncryptPassword(passwordOpDTO.getOriPassword()), BizExceEnum.USER_INVALID_ORIPASSWORD);
        // 2. 校验新密码和原密码是否重复
        ValidateUtil.assertNotEquals(passwordOpDTO.getPassword(), passwordOpDTO.getOriPassword(), BizExceEnum.USER_PASSWORD_SAMEWITH_ORI);
        // 3. 校验通过，入库
        ossMerchantUserDO.setPassword(generateEncryptPassword(passwordOpDTO.getPassword()));
        merchantUserManager.updatePassword(ossMerchantUserDO);
        return true;
    }

    //根据手机号查询
    public OssMerchantUserDTO queryByMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)){
            return null;
        }
        OssMerchantUserDO ossMerchantUserDO = merchantUserManager.getByMobile(mobile);
        return userSupport.convert2OssMerchantUserDTO(ossMerchantUserDO);
    }

    //根据id列表查询用户
    public List<OssMerchantUserDTO> queryByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<OssMerchantUserDO> ossMerchantUserDOS = merchantUserManager.queryByIds(ids);
        return userSupport.convert2OssMerchantUserDTOList(ossMerchantUserDOS);
    }

    //根据用户名查询
    public OssMerchantUserDTO queryByName(String name) {
        OssMerchantUserDO ossMerchantUserDO = merchantUserManager.queryByName(name);
        return userSupport.convert2OssMerchantUserDTO(ossMerchantUserDO);
    }

    //根据id查看用户详情
    public OssMerchantUserDTO queryById(Long id) {
        ValidateUtil.requiredNotNull(id, BizExceEnum.USER_ID_NULL);
        OssMerchantUserDO ossMerchantUserDO = merchantUserManager.getById(id);
        if(ossMerchantUserDO == null){
            return null;
        }
        return userSupport.convert2OssMerchantUserDTO(ossMerchantUserDO);
    }

    //修改状态
    public void updateStatus(String name, Integer status, Long updator) {
        merchantUserManager.updateStatus(name, status, updator);
    }

    //删除
    public void deleteById(Long id) {
        merchantUserManager.deleteById(id);
    }

    private static String generateEncryptPassword(String password){
        return MD5Utils.encrypt(password + UserManager.PASSWORD_SALT).toUpperCase();
    }
}

