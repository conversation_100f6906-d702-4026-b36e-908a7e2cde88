package com.chargebolt.aeacus.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.dto.OssRoleCreateDTO;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.dto.OssRoleDeleteDTO;
import com.chargebolt.aeacus.dto.OssRoleQueryDTO;
import com.chargebolt.aeacus.dto.OssRoleUpdateDTO;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.entity.dataobject.RoleIconMappingDO;
import com.chargebolt.aeacus.entity.dataobject.RolePermissionMappingDO;
import com.chargebolt.aeacus.service.manager.IconManager;
import com.chargebolt.aeacus.service.manager.RoleIconManager;
import com.chargebolt.aeacus.service.manager.RoleManager;
import com.chargebolt.aeacus.service.manager.RolePermissionManager;
import com.chargebolt.aeacus.service.support.RoleSupport;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RoleService {
    @Resource
    private RoleSupport roleSupport;
    @Resource
    private RoleManager roleManager;
    @Resource
    private RolePermissionManager rolePermissionManager;
    @Resource
    private GroupService groupService;
    @Resource
    private RoleIconManager roleIconManager;
    @Resource
    private IconManager iconManager;

    public Boolean create(OssRoleCreateDTO ossRoleCreateDTO) {
        RoleDO roleDO = roleManager.queryByName(ossRoleCreateDTO.getName());
        if(roleDO != null){
            throw new I18nMessageException(BizExceEnum.ROLE_NAME_ALREADY_EXSIT);
        }
        roleDO = roleSupport.convert2RoleDTO(ossRoleCreateDTO);
        rolePermissionManager.createRole(roleDO, ossRoleCreateDTO);
        return true;
    }

    public Boolean update(OssRoleUpdateDTO updateDTO){
        RoleDO roleDO = roleManager.getById(updateDTO.getId());
        if (roleDO == null){
            throw new I18nMessageException(BizExceEnum.NO_SUCH_OBJECT);
        }
        roleDO.setName(updateDTO.getName());
        roleDO.setUpdator(updateDTO.getUpdator());
        List<Long> removePermission = null;
        if(!CollectionUtils.isEmpty(updateDTO.getPermissionIds())){
            List<RolePermissionMappingDO> permissionMappingDOS = rolePermissionManager.getPermissionMappings(updateDTO.getId());
            if (!CollectionUtils.isEmpty(permissionMappingDOS)) {
                removePermission = permissionMappingDOS.stream().map(RolePermissionMappingDO::getId).collect(Collectors.toList());
            }
        }
        // 获取原来的角色和icon关系
        List<Long> removeIcon = null;
        if(!CollectionUtils.isEmpty(updateDTO.getIconIds())){
            List<RoleIconMappingDO> roleIconMappingDOS = roleIconManager.getMappingsByRoleId(updateDTO.getId());
            if (!CollectionUtils.isEmpty(roleIconMappingDOS)) {
                removeIcon = roleIconMappingDOS.stream().map(RoleIconMappingDO::getId).collect(Collectors.toList());
            }
        }

        // 参数封装
        Map<String, List<Long>> paramMap = new HashMap<>();
        paramMap.put(AeacusConstsnts.PARAM_NP, updateDTO.getPermissionIds());
        paramMap.put(AeacusConstsnts.PARAM_NI, updateDTO.getIconIds());
        paramMap.put(AeacusConstsnts.PARAM_RP, removePermission);
        paramMap.put(AeacusConstsnts.PARAM_RI, removeIcon);
        rolePermissionManager.updateRole(roleDO, paramMap);
        return true;
    }

    public List<OssRoleDTO> queryByUserId(Long userId){
        List<RoleDO> roleDOS = roleManager.queryByUser(userId);
        return roleSupport.convert2RoleDTO(roleDOS);
    }

    public Map<Long,List<OssRoleDTO>> queryByUserIds(List<Long> userIds){
        List<OssUserRoleMappingDO> ossUserRoleMappingDOS = roleManager.queryByUserIds(userIds);
        if(CollectionUtils.isEmpty(ossUserRoleMappingDOS)){
            return new HashMap<>();
        }
        Map<Long,List<OssRoleDTO>> userRoleMap = new HashMap<>();
        ossUserRoleMappingDOS.forEach(item->{
            RoleDO roleDO = roleManager.queryCacheById(item.getRoleId());
            if(roleDO == null || (item.getExpireTime() != null && item.getExpireTime().before(new Date()))){
                return;
            }
            List<OssRoleDTO> roleDTOS = userRoleMap.get(item.getUserId());
            if(CollectionUtils.isEmpty(roleDTOS)){
                roleDTOS = new ArrayList<>();
                userRoleMap.put(item.getUserId(),roleDTOS);
            }
            roleDTOS.add(roleSupport.convert2RoleDTO(roleDO,null));
        });
        return userRoleMap;
    }

    public PageData<OssRoleDTO> queryByPage(OssRoleQueryDTO queryDTO) {
        Page<RoleDO> roleDOS = PageHelper.startPage(queryDTO.getPageNo(), queryDTO.getPageSize(), Boolean.TRUE).doSelectPage(new ISelect() {
            @Override
            public void doSelect() {
                roleManager.queryByPage(queryDTO.getName(), queryDTO.getAgentId());
            }
        });
        List<OssRoleDTO> ossRoleDTOS = roleSupport.convert2RoleDTO(roleDOS.getResult());
        return PageData.create(ossRoleDTOS,roleDOS.getTotal(), (long) roleDOS.getPageNum(),roleDOS.getPageSize());
    }

    public OssRoleDTO queryById(Long roleId){
        RoleDO roleDO = roleManager.getById(roleId);
        if(roleDO == null){
            return null;
        }
        List<OssPermissionDTO> permissionDOS = rolePermissionManager.getPermissionByRoleId(roleId);
        List<OssGroupDTO> ossGroupDTOS = groupService.query(permissionDOS);
        OssRoleDTO ossRoleDTO = roleSupport.convert2RoleDTO(roleDO,ossGroupDTOS);
        // 根据roleId查询角色和icon关系
        List<RoleIconMappingDO> roleIconMappingDOS = roleIconManager.getMappingsByRoleId(roleId);
        List<Long> iconIds = roleIconMappingDOS.stream().map(RoleIconMappingDO::getIconId).collect(Collectors.toList());
        // 根据iconId查询icon信息
        if (CollectionUtils.isEmpty(iconIds)) {
            return ossRoleDTO;
        }
        List<OssIconDTO> iconDTOS = iconManager.queryIconsByIds(iconIds);
        ossRoleDTO.setIconDTOList(iconDTOS);
        return ossRoleDTO;
    }

    public List<OssPermissionDTO> getPermission(Long userId){
        List<RoleDO> roleDOS = roleManager.queryByUser(userId);
        List<OssPermissionDTO> permissionDTOList= new ArrayList<>();
        for(RoleDO roleDO: roleDOS){
            List<OssPermissionDTO> permissionDOS = rolePermissionManager.getPermissionByRoleId(roleDO.getId());
            permissionDTOList.addAll(permissionDOS);
        }
        return permissionDTOList.stream()
                .filter(Objects::nonNull)
                .filter(distinctByKey(OssPermissionDTO::getCode))
                .collect(Collectors.toList());
    }
    public static <T> java.util.function.Predicate<T> distinctByKey(java.util.function.Function<? super T, Object> keyExtractor) {
        java.util.Set<Object> seen = java.util.Collections.newSetFromMap(new java.util.concurrent.ConcurrentHashMap<>());
        return t -> seen.add(keyExtractor.apply(t));
    }
    public Boolean delete(OssRoleDeleteDTO deleteDTO) {
        RoleDO roleDO = roleManager.getById(deleteDTO.getId());
        if(roleDO == null || Objects.equals(roleDO.getDeleted(), AeacusConstsnts.DELETED)){
            throw new I18nMessageException(BizExceEnum.ROLE_NOT_EXSIT);
        }
        List<OssUserRoleMappingDO> roleMappingDO = roleManager.getUserByRoleId(roleDO.getId());
        if(!CollectionUtils.isEmpty(roleMappingDO)){
            throw new I18nMessageException(BizExceEnum.ROLE_ALREADY_INUSE);
        }
        roleDO.setUpdator(deleteDTO.getUpdator());
        roleDO.setDeleted(AeacusConstsnts.DELETED);
        roleManager.update(roleDO);
        return true;
    }

    public List<OssRoleDTO> queryAllRoles() {
        List<RoleDO> roleDOS = roleManager.getAll();
        return roleSupport.convert2RoleDTO(roleDOS);
    }
}

