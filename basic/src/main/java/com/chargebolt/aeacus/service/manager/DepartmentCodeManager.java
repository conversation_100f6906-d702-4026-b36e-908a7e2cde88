package com.chargebolt.aeacus.service.manager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.chargebolt.aeacus.cache.DepartmentCache;
import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Component
public class DepartmentCodeManager {

    @Resource
    private DepartmentManager departmentManager;
    @Resource
    private DepartmentCache departmentCache;

    private static final Integer CODE_LENGTH = 3;
    private volatile Boolean RUNNING_FLAG= Boolean.TRUE;
    // 实现逻辑有隐患，需要改进
//    fixme 耗时测试
    @PostConstruct
    @Scheduled(cron = "0 30 * * * ?")
    public void init() {
        if(Boolean.FALSE.equals(RUNNING_FLAG)){
            return;
        }
        RUNNING_FLAG= Boolean.FALSE;
        log.info("OssDepartment init ....");
        List<OssDepartmentDO> departmentDOS = departmentManager.getAll();
        if (CollectionUtils.isEmpty(departmentDOS)) {
            RUNNING_FLAG= Boolean.TRUE;
            return;
        }
        Map<Long,OssDepartmentDO> departmentMap = departmentDOS.stream().collect(Collectors.toMap(OssDepartmentDO::getId,item->item));
        Map<String,Integer> codeMap = new HashMap<>();
        for (Long departmentId : departmentMap.keySet()) {
            // 根据部门id查询当前部门信息
            OssDepartmentDO departmentDO = departmentMap.get(departmentId);
            // 部门id为0的不处理
            if(Objects.equals(departmentDO.getId(), AeacusConstsnts.DEPARTMENT_FIRST_STAGE_ID)){
                continue;
            }
            // 查找父部门信息
            OssDepartmentDO parentDO = departmentMap.get(departmentDO.getParentId());
            // 截取当前部门信息的code
            String code = departmentDO.getCode().substring(departmentDO.getCode().length() - CODE_LENGTH);
            Integer codeInt = Integer.parseInt(code, 16);
            // 判断父部门code是否存在map中
            if(!codeMap.containsKey(parentDO.getCode())){
                codeMap.put(parentDO.getCode(),codeInt);
                continue;
            }
            Integer codeOriInt = codeMap.get(parentDO.getCode());
            if(codeInt > codeOriInt){
                codeMap.put(parentDO.getCode(),codeInt);
            }
        }
        codeMap.forEach((code,hexInt)-> departmentCache.initIncreaseLong(code, hexInt));
        departmentCache.removeMap();
        departmentCache.addAllDepartment(departmentMap);
        RUNNING_FLAG= Boolean.TRUE;
        log.info("OssDepartment init finished....");
    }

    public String generatorCode(String parentCode) {
        int code = departmentCache.getIncreaseLong(parentCode);
        if (code > 4095) {
            throw new I18nMessageException(BizExceEnum.DEPARTMENT_CANNOT_MORE);
        }
        String codeStr = Integer.toHexString(code);
        if (codeStr.length() == 1) {
            return "00" + codeStr;
        }
        if (codeStr.length() == 2) {
            return "0" + codeStr;
        }
        return codeStr;
    }

    public String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        OssDepartmentDO departmentDO = departmentManager.getByCode(code);
        if (departmentDO == null) {
            return null;
        }
        if(Objects.equals(departmentDO.getId(), AeacusConstsnts.DEPARTMENT_FIRST_STAGE_ID)){
            return departmentDO.getName();
        }
        return getName(departmentDO.getParentId(),departmentDO.getName());
    }

    public String getName(Long parentId,String name){
        OssDepartmentDO parentDO = departmentCache.getDepartment(parentId);
        name = parentDO.getName() + "/" + name;
        if(Objects.equals(parentDO.getId(), AeacusConstsnts.DEPARTMENT_FIRST_STAGE_ID)){
            return name;
        }
        return getName(parentDO.getParentId(),name);
    }
}
