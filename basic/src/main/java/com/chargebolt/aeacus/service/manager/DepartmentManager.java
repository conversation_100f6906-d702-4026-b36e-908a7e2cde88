package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.cache.DepartmentCache;
import com.chargebolt.aeacus.dao.OssDepartmentDAO;
import com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class DepartmentManager {
    @Resource
    private OssDepartmentDAO ossDepartmentDAO;
    @Resource
    private DepartmentCache departmentCache;

    public void insert(OssDepartmentDO ossDepartmentDO){
        ossDepartmentDAO.insert(ossDepartmentDO);
        departmentCache.addDepartment(ossDepartmentDO.getId(),ossDepartmentDO);
    }

    public void updateById(OssDepartmentDO ossDepartmentDO){
        ossDepartmentDAO.updateById(ossDepartmentDO);
        departmentCache.addDepartment(ossDepartmentDO.getId(),ossDepartmentDO);
    }

    public void deleteByIds(List<Long> ids,String deletor){
        ossDepartmentDAO.deleteByIds(ids,deletor);
        departmentCache.removeHashDepartment(ids);
    }

    public OssDepartmentDO getById(Long id){
        return departmentCache.getDepartment(id);
    }

    public OssDepartmentDO getByName(String name, Long agentId){
        return ossDepartmentDAO.selectByName(name, agentId);
    }

    public List<OssDepartmentDO> getAll(){
        return ossDepartmentDAO.selectAll();
    }

    public List<OssDepartmentDO> getDepartmentCache(){
        return new ArrayList<>(departmentCache.getAllDepartMent().values());
    }

    public List<OssDepartmentDO> getLikeCode(String code){
        return ossDepartmentDAO.selectLikeCode(code);
    }

    public OssDepartmentDO getByCode(String code){
        return ossDepartmentDAO.selectByCode(code);
    }
}
