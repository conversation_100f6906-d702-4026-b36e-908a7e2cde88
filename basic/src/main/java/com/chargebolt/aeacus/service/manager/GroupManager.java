package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.dao.OssGroupDAO;
import com.chargebolt.aeacus.entity.dataobject.OssGroupDO;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class GroupManager {

    @Resource
    private OssGroupDAO ossGroupDAO;

    public OssGroupDO getById(Long id){
        return ossGroupDAO.selectById(id);
    }

    public List<OssGroupDO> getByIds(List<Long> groupIds){
        return ossGroupDAO.selectByIds(groupIds);
    }

    public List<OssGroupDO> query(OssGroupDO ossGroupDO){
        return ossGroupDAO.selectList(ossGroupDO);
    }

    public void insert(OssGroupDO ossGroupDO){
        ossGroupDAO.insert(ossGroupDO);
    }
}
