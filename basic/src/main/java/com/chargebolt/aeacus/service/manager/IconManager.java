package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.dao.IconDAO;
import com.chargebolt.aeacus.dao.RoleIconMappingDAO;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.entity.dataobject.IconDO;
import com.chargebolt.aeacus.entity.dataobject.RoleIconMappingDO;
import com.chargebolt.aeacus.service.support.IconSupport;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Objects;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.util.CollectionUtils;
import so.dian.commons.eden.util.LocalMapUtils;
@Slf4j
@Component
public class IconManager {

    @Resource
    private IconDAO iconDAO;

    @Resource
    private IconSupport iconSupport;

    @Resource
    private RoleIconMappingDAO roleIconMappingDAO;

    /**
     * 获取所有icon信息
     * @return List<OssIconDTO>
     */
    public List<OssIconDTO> queryAllIcons(){
        return iconSupport.convert2IconDTO(iconDAO.queryAllIcons());
    }

    /**
     * 根据id查询icon信息
     * @param ids List<Long>
     * @return List<OssIconDTO>
     */
    public List<OssIconDTO> queryIconsByIds(List<Long> ids){
        return iconSupport.convert2IconDTO(iconDAO.queryIconsByIds(ids));
    }

    /**
     * 根据id查询icon信息
     * @param ids List<Long>
     * @return List<OssIconDTO>
     */
    public List<OssIconDTO> queryIconsBy(List<Long> ids){
        return iconSupport.convert2IconDTO(iconDAO.queryIconsByIds(ids));
    }

}
