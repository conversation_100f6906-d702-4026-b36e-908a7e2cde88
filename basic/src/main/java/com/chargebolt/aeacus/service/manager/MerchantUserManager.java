package com.chargebolt.aeacus.service.manager;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dao.OssMerchantUserDAO;
import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.codec.MD5Utils;

@Component
@Slf4j
public class MerchantUserManager {

    @Resource
    private OssMerchantUserDAO ossMerchantUserDAO;

    /**
     * 根据手机号查询商户账号信息
     * @param mobile 手机号
     * @return OssMerchantUserDO
     */
    public OssMerchantUserDO getByMobile(String mobile){
        OssMerchantUserDO ossMerchantUserDO = null;
        try {
            ossMerchantUserDO = ossMerchantUserDAO.getByMobile(mobile);
        } catch (Exception e) {
            log.error("MerchantUserManager getByMobile, info:{}", e.getMessage());
        }
        return ossMerchantUserDO;
    }

    /**
     * 根据账号ID集合查询账号信息
     * @param ids List<Long>
     * @return List<OssMerchantUserDO>
     */
    public List<OssMerchantUserDO> queryByIds(List<Long> ids) {
        List<OssMerchantUserDO> ossMerchantUserDOS = new ArrayList<>();
        try {
            ossMerchantUserDOS = ossMerchantUserDAO.getByIds(ids);
        } catch (Exception e) {
            log.error("MerchantUserManager queryByIds, info:{}", e.getMessage());
        }
        return ossMerchantUserDOS;
    }

    /**
     * 根据账户名称查询账号信息
     * @param name 账户名称
     * @return OssMerchantUserDO
     */
    public OssMerchantUserDO queryByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        OssMerchantUserDO ossMerchantUserDO = null;
        try {
            ossMerchantUserDO = ossMerchantUserDAO.queryByName(name);
        } catch (Exception e) {
            log.error("MerchantUserManager queryByName, info:{}", e.getMessage());
        }
        return ossMerchantUserDO;
    }

    /**
     * 根据ID查询商户账号信息
     * @param id 用户ID
     * @return OssMerchantUserDO
     */
    public OssMerchantUserDO getById(Long id){
        OssMerchantUserDO ossMerchantUserDO = null;
        try {
            ossMerchantUserDO = ossMerchantUserDAO.getById(id);
        } catch (Exception e) {
            log.error("MerchantUserManager getById, info:{}", e.getMessage());
        }
        return ossMerchantUserDO;
    }

    /**
     * 更新token
     * @param ossMerchantUserDO OssMerchantUserDO
     */
    public void updateToken(OssMerchantUserDO ossMerchantUserDO){
        try {
            ossMerchantUserDAO.updateToken(ossMerchantUserDO);
        } catch (Exception e) {
            log.error("MerchantUserManager updateToken, info:{}", e.getMessage());
        }
    }

    /**
     * 插入商户账号信息
     * @param ossMerchantUserDO OssMerchantUserDO
     */
    public Long insert(OssMerchantUserDO ossMerchantUserDO){
        String originPassword = ossMerchantUserDO.getPassword();
        ossMerchantUserDO.setPassword(MD5Utils.encrypt(originPassword + UserManager.PASSWORD_SALT).toUpperCase());
        try {
            ossMerchantUserDAO.insert(ossMerchantUserDO);
            return ossMerchantUserDO.getId();
        } catch (DuplicateKeyException duplicateKeyException) {
            // 名称重复
            throw new I18nMessageException(BizExceEnum.EXISTS_MERCHANT_USER_NAME);
        } catch (Exception e) {
            log.error("MerchantUserManager insert exception, info:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 更新密码
     * @param ossMerchantUserDO OssMerchantUserDO
     */
    public void updatePassword(OssMerchantUserDO ossMerchantUserDO){
        try {
            ossMerchantUserDAO.updatePassword(ossMerchantUserDO);
        } catch (Exception e) {
            log.error("MerchantUserManager updatePassword, info:{}", e.getMessage());
        }
    }

    /**
     * 更新用户状态
     * @param name String
     * @param status String
     */
    public void updateStatus (String name, Integer status, Long updator) {
        try {
            ossMerchantUserDAO.updateStatus(name, status, updator);
        } catch (Exception e) {
            log.error("MerchantUserManager updateStatus, info:{}", e.getMessage());
        }
    }

    /**
     * @param id Long
     */
    public void deleteById (Long id) {
        if (null == id) {
            return;
        }
        try {
            ossMerchantUserDAO.deleteById(id);
        } catch (Exception e) {
            log.error("MerchantUserManager deleteById, info:{}", e.getMessage());
        }
    }

}
