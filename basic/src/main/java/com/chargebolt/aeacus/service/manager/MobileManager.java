package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.common.exception.LoginExceEnum;
import com.chargebolt.aeacus.entity.bizobject.MobileBO;
import com.chargebolt.aeacus.entity.dataobject.CountryDO;
import com.chargebolt.aeacus.service.CountryService;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.exception.BizException;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class MobileManager {

    public MobileManager() throws IOException {
    }

    @Data
    @Builder
    public static class SupportNations{
        private String nationName;
        private String nationCode;
    }
    @Resource
    private CountryService countryService;

    public MobileBO createMobileBO(String mobile){
        List<SupportNations> supportNations1 = getSupportNations();
        for (SupportNations supportNations : supportNations1){
            if (mobile.startsWith(supportNations.nationCode)){
                return MobileBO.builder().nationCode(supportNations.nationCode).mobile(mobile).build();
            }
        }
        // tag 未国际化
        throw BizException.create(LoginExceEnum.INVALID_MOBILE, mobile);
    }
    public List<SupportNations> getSupportNations() {
        try {
            List<CountryDO> countryDOList = countryService.parseJsonFile();
            return countryDOList.stream()
                    .map(countryDO -> SupportNations.builder()
                            .nationCode(countryDO.getPhoneCode())
                            .nationName(countryDO.getCountryCode())
                            .build())
                    .collect(Collectors.toList());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
