package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.dao.ModuleDAO;
import com.chargebolt.aeacus.entity.dataobject.ModuleDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ModuleManager {

    @Resource
    private ModuleDAO moduleDAO;

    public List<ModuleDO> queryAllModules(){
        return moduleDAO.queryAllModules();
    }
}
