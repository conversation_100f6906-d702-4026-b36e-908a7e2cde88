package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.dao.PermissionDAO;
import com.chargebolt.aeacus.dao.RolePermissionMappingDAO;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.entity.dataobject.PermissionDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.entity.dataobject.RolePermissionMappingDO;
import com.google.common.collect.Lists;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.commons.eden.exception.BizException;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Component
public class PermissionManager {

    private static final Integer PAGE_SIZE = 50;

    @Resource
    private PermissionDAO permissionDAO;
    @Resource
    private RolePermissionMappingDAO rolePermissionMappingDAO;
    @Resource
    private RoleManager roleManager;

    public List<PermissionDO> queryByPage(Integer page){
        return permissionDAO.queryByPage((page - 1) * PAGE_SIZE, PAGE_SIZE);
    }

    public List<RolePermissionMappingDO> queryMappingByRole(Long roleId) {
        return rolePermissionMappingDAO.queryByRole(roleId);
    }

    public List<RolePermissionMappingDO> queryMappingByRoleIds(List<Long> ids) {
        return rolePermissionMappingDAO.queryByRoleIds(ids);
    }

    public List<PermissionDO> queryByRole(Long roleId) {
        List<RolePermissionMappingDO> rolePermissionMappingDOS = queryMappingByRole(roleId);
        List<Long> permissionIds = Lists.newLinkedList();
        for (RolePermissionMappingDO rolePermissionMappingDO : rolePermissionMappingDOS){
            permissionIds.add(rolePermissionMappingDO.getPermissionId());
        }
        if (CollectionUtils.isEmpty(permissionIds)){
            return Collections.emptyList();
        }
        List<PermissionDO> permissionDOS = permissionDAO.queryByIds(permissionIds);
        return permissionDOS;
    }

    public PermissionDO getByCode(String code){
        return permissionDAO.getByCode(code);
    }

    public PermissionDO getById(Long id){
        List<PermissionDO> permissionDOS = permissionDAO.queryByIds(Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(permissionDOS)){
            return null;
        }else {
            return permissionDOS.get(0);
        }
    }

    public Long insert(PermissionDO permissionDO) {
        if (permissionDAO.getByCode(permissionDO.getCode()) != null){
            // tag 未国际化
            throw BizException.create(BizExceEnum.EXISTS_OBJECT, permissionDO.getCode());
        }
        return permissionDAO.insert(permissionDO);
    }

    public Integer update(PermissionDO permissionDO) {
        return permissionDAO.update(permissionDO);
    }

    @Transactional
    public void addRolePermissionMapping(List<RolePermissionMappingDO> rolePermissionMappingDOs){
        if(CollectionUtils.isEmpty(rolePermissionMappingDOs)){
            return;
        }
        for (RolePermissionMappingDO rolePermissionMappingDO : rolePermissionMappingDOs){
            rolePermissionMappingDAO.insert(rolePermissionMappingDO);
        }
    }

    public List<PermissionDO> getByGroupIds(List<Long> groupIds){
        if(CollectionUtils.isEmpty(groupIds)){
            return null;
        }
        groupIds = groupIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return permissionDAO.selectByGroupIds(groupIds);
    }

    public void deleteByIds(List<Long> ids){
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        rolePermissionMappingDAO.deleteByIds(ids);
    }

    public List<PermissionDO> getPermissionByUserId(Long userId) {
        List<RoleDO> userRoleDOS = roleManager.queryByUser(userId);
        List<Long> roleIds = userRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
        List<RolePermissionMappingDO> rolePermissionMappingDOS = rolePermissionMappingDAO.queryByRoleIds(roleIds);
        List<Long> permissionIds = rolePermissionMappingDOS.stream()
                .map(RolePermissionMappingDO::getPermissionId)
                .distinct()
                .collect(Collectors.toList());
        return permissionDAO.queryByIds(permissionIds);

    }
}
