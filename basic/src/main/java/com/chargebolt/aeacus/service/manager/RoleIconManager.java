package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.dao.RoleIconMappingDAO;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.entity.dataobject.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RoleIconManager {
    @Resource
    private RoleIconMappingDAO roleIconMappingDAO;

    @Resource
    private IconManager iconManager;

    /**
     * 批量添加绑定角色和icon关系
     * @param roleDO RoleDO
     * @param iconIds List<Long>
     */
    void batchBindIconToRole(RoleDO roleDO, List<Long> iconIds){
        if (CollectionUtils.isEmpty(iconIds)) {
            return;
        }
        List<RoleIconMappingDO> roleIconMappingDOS = Lists.newLinkedList();
        for (Long iconId : new HashSet<>(iconIds)){
            RoleIconMappingDO roleIconMappingDO = new RoleIconMappingDO();
            roleIconMappingDO.setRoleId(roleDO.getId());
            roleIconMappingDO.setIconId(iconId);
            roleIconMappingDOS.add(roleIconMappingDO);
        }
        roleIconMappingDAO.batchInsert(roleIconMappingDOS);
    }

    public List<OssIconDTO> getIconByRoleId(Long roleId){
        /*// 1.先根据角色ID在内存中查询符合的icon信息
        List<IconDO> iconDOS = iconManager.queryIconsByRole(roleId);
        // 1.1 如果内存中存在，则返回
        if (!CollectionUtils.isEmpty(iconDOS)) {
            return iconDOS.stream().map(item-> OssIconDTO.builder()
                    .id(item.getId())
                    .url(item.getUrl())
                    .type(item.getType())
                    .title(item.getTitle())
                    .img(item.getImg())
                    .build())
                    .collect(Collectors.toList());
        }*/
        // 2. 查库
        List<RoleIconMappingDO> roleIconMappingDOS = this.getMappingsByRoleId(roleId);
        if (CollectionUtils.isEmpty(roleIconMappingDOS)) {
            return null;
        }
        List<Long> iconIds = roleIconMappingDOS.stream().map(RoleIconMappingDO::getIconId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(iconIds)) {
            return null;
        }
        return iconManager.queryIconsByIds(iconIds);
    }

    /**
     * 根据roleId查询对应的角色和icon关系
     * @param roleId Long
     * @return List<RoleIconMappingDO>
     */
    public List<RoleIconMappingDO> getMappingsByRoleId(Long roleId){
        return roleIconMappingDAO.queryMappingsByRoleId(roleId);
    }

    /**
     * 根据Id删除角色和icon关系
     * @param ids List<Long>
     */
    public void deleteByIds(List<Long> ids){
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        roleIconMappingDAO.deleteByIds(ids);
    }
}
