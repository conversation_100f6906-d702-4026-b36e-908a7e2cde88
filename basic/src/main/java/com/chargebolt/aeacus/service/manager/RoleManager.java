package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.dao.OssUserRoleMappingDAO;
import com.chargebolt.aeacus.dao.RoleDAO;
import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
@Slf4j
@Component
public class RoleManager {

    @Resource
    private RoleDAO roleDAO;
    @Resource
    private OssUserRoleMappingDAO ossUserRoleMappingDAO;

    public RoleDO queryCacheById(Long roleId){
        return roleDAO.queryById(roleId);
    }

    public List<RoleDO> getAll(){
        return roleDAO.select();
    }

    public List<RoleDO> queryByUser(Long userId){
        List<OssUserRoleMappingDO> ossUserRoleMappingDAOS = ossUserRoleMappingDAO.queryByUser(userId);
        List<Long> roleIds = Lists.newLinkedList();
        Date now = new Date();
        for (OssUserRoleMappingDO ossUserRoleMappingDO : ossUserRoleMappingDAOS){
            if (ossUserRoleMappingDO.getExpireTime() == null){
                roleIds.add(ossUserRoleMappingDO.getRoleId());
            }
            if (ossUserRoleMappingDO.getExpireTime() != null && ossUserRoleMappingDO.getExpireTime().after(now)){
                roleIds.add(ossUserRoleMappingDO.getRoleId());
            }
        }
        if (CollectionUtils.isEmpty(roleIds)){
            return Collections.emptyList();
        }
        List<RoleDO> roleDOS = roleDAO.queryByIds(roleIds);
        return roleDOS;
    }

    public List<OssUserRoleMappingDO> queryByUserIds(List<Long> userIds){
        return ossUserRoleMappingDAO.selectByUserIds(userIds);
    }

    public RoleDO getById(Long id){
        List<RoleDO> roleDOS = roleDAO.queryByIds(Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(roleDOS)){
            return null;
        }else {
            return roleDOS.get(0);
        }
    }

    public Long insert(RoleDO roleDO){
        return roleDAO.insert(roleDO);
    }

    public Integer update(RoleDO roleDO){
        return roleDAO.update(roleDO);
    }

    public void addUserRoleMapping(OssUserRoleMappingDO ossUserRoleMappingDO){
        ossUserRoleMappingDAO.insert(ossUserRoleMappingDO);
    }

    public void deleteByUserId(Long userId){
        if(userId == null){
            return;
        }
        ossUserRoleMappingDAO.deleteByUserId(userId);
    }

    public List<OssUserRoleMappingDO> getUserByRoleIds(List<Long> roleIds){
        if(CollectionUtils.isEmpty(roleIds)){
            return null;
        }
        roleIds = roleIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(roleIds)){
            return null;
        }
        return ossUserRoleMappingDAO.selectByRoleIds(roleIds);
    }

    public List<OssUserRoleMappingDO> getUserByRoleId(Long roleId){
        return ossUserRoleMappingDAO.queryByRole(roleId);
    }

    public List<RoleDO> queryByPage(String name, Long agnetId){
        return roleDAO.selectByPage(name, agnetId);
    }

    public RoleDO queryByName(String name){
        return roleDAO.selectByName(name);
    }

    public List<RoleDO> selectByAgentId(Long agentId){
        return roleDAO.selectByAgentId(agentId);
    }
}
