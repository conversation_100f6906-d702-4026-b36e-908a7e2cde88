package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.dto.OssRoleCreateDTO;
import com.chargebolt.aeacus.entity.dataobject.PermissionDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.entity.dataobject.RolePermissionMappingDO;
import com.google.common.collect.Lists;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class RolePermissionManager {
    @Resource
    private RoleManager roleManager;
    @Resource
    private PermissionManager permissionManager;
    @Resource
    private RoleIconManager roleIconManager;

    @Transactional
    public void createRole(RoleDO roleDO, OssRoleCreateDTO ossRoleCreateDTO){
        roleManager.insert(roleDO);
        addPermissionToRole(roleDO, ossRoleCreateDTO.getPermissionIds());
        // 新增角色和icon关系
        roleIconManager.batchBindIconToRole(roleDO, ossRoleCreateDTO.getIconIds());
    }

    public void addPermissionToRole(RoleDO roleDO,List<Long> permissionIds){
        List<RolePermissionMappingDO> rolePermissionMappingDOS = Lists.newLinkedList();
        for (Long permissionId : new HashSet<>(permissionIds)){
            RolePermissionMappingDO rolePermissionMappingDO = new RolePermissionMappingDO();
            rolePermissionMappingDO.setRoleId(roleDO.getId());
            rolePermissionMappingDO.setPermissionId(permissionId);
            rolePermissionMappingDOS.add(rolePermissionMappingDO);
        }
        permissionManager.addRolePermissionMapping(rolePermissionMappingDOS);
    }

    public List<OssPermissionDTO> getPermissionByRoleId(Long roleId){
        List<PermissionDO> permissionDOS = getPermissionDOs(roleId);
        return permissionDOS.stream().map(item-> OssPermissionDTO.builder().id(item.getId()).url(item.getUrl()).img(item.getImg()).description(item.getDescription()).code(item.getCode()).groupId(item.getGroupId()).relyIds(item.getRelyIds()).build()).collect(
                Collectors.toList());
    }

    public List<PermissionDO> getPermissionDOs(Long roleId){
        return permissionManager.queryByRole(roleId);
    }

    public List<RolePermissionMappingDO> getPermissionMappings(Long roleId){
        return permissionManager.queryMappingByRole(roleId);
    }


    @Transactional
    public void updateRole(RoleDO roleDO, Map<String, List<Long>> paramMap){
        // 1. 删除已存在的角色和权限关系
        if(!CollectionUtils.isEmpty(paramMap.get(AeacusConstsnts.PARAM_RP))){
            permissionManager.deleteByIds(paramMap.get(AeacusConstsnts.PARAM_RP));
        }
        // 2. 新增角色和权限关系
        if(!CollectionUtils.isEmpty(paramMap.get(AeacusConstsnts.PARAM_NP))){
            addPermissionToRole(roleDO, paramMap.get(AeacusConstsnts.PARAM_NP));
        }
        // 3. 删除已存在的角色和icon关系
        if(!CollectionUtils.isEmpty(paramMap.get(AeacusConstsnts.PARAM_RI))){
            roleIconManager.deleteByIds(paramMap.get(AeacusConstsnts.PARAM_RI));
        }
        // 4. 新增角色和icon关系
        if(!CollectionUtils.isEmpty(paramMap.get(AeacusConstsnts.PARAM_NI))){
            roleIconManager.batchBindIconToRole(roleDO, paramMap.get(AeacusConstsnts.PARAM_NI));
        }
        // 5. 更新角色信息
        roleManager.update(roleDO);
    }
}
