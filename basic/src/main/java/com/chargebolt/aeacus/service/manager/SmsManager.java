package com.chargebolt.aeacus.service.manager;

import com.alibaba.fastjson.JSON;
import com.chargebolt.aeacus.cache.SmsCache;
import com.chargebolt.aeacus.common.exception.LoginExceEnum;
import com.chargebolt.aeacus.entity.bizobject.MobileBO;
import com.chargebolt.athena.param.sms.SmsMsgParam;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.util.EnvUtil;
import so.dian.eros.remote.athena.SmsMessageClient;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Random;

@Component
@Slf4j(topic = "biz")
public class SmsManager {

    @Resource
    private SmsCache smsCache;
    @Resource
    private SmsMessageClient smsMessageClient;

    // [%s] Verification code: %s
    public BizResult sendSmsToken(MobileBO mobileBO) {
        SmsCache.SmsCacheDO smsCacheDO = smsCache.getSmsCacheDO(mobileBO.getMobile());
        if (smsCacheDO == null) {
            String token = getRandomToken();
            smsCacheDO = new SmsCache.SmsCacheDO();
            smsCacheDO.setToken(token);
            smsCacheDO.setCreateTime(System.currentTimeMillis());
            smsCacheDO.setCheckTimes(0);
            smsCacheDO.setSendTimes(1);
            boolean success = send(mobileBO, token);
            if (!success) {
                return BizResult.error(LoginExceEnum.SMS_TOKEN_SEND_FAILED);
            }
            smsCache.setSmsCacheDO(mobileBO.getMobile(), smsCacheDO);
        } else {
            String token = smsCacheDO.getToken();
            if (smsCacheDO.getSendTimes() >= 3) {
                return BizResult.error(LoginExceEnum.SMS_TOKEN_SEND_TOO_MUCH_TIMES);
            }
            if (System.currentTimeMillis() - smsCacheDO.getCreateTime() < 60 * 1000) {
                return BizResult.error(LoginExceEnum.SMS_TOKEN_SEND_TOO_FREQUENCY);
            }
            smsCacheDO.setToken(token);
            smsCacheDO.setCreateTime(System.currentTimeMillis());
            smsCacheDO.setSendTimes(smsCacheDO.getSendTimes() + 1);
            boolean success = send(mobileBO, token);
            if (!success) {
                return BizResult.error(LoginExceEnum.SMS_TOKEN_SEND_FAILED);
            }
            smsCache.setSmsCacheDO(mobileBO.getMobile(), smsCacheDO);
        }
        return BizResult.create(null);
    }

    /**
     * 没有使用，可以删除
     * @deprecated
     * @param mobileBO
     * @param smsToken
     * @return
     */
    @Deprecated
    public BizResult checkSmsToken(MobileBO mobileBO, String smsToken) {
        log.info(String.format("mobileBO: %s, token: %s", JSON.toJSONString(mobileBO), smsToken));
        if ("31415926".equals(smsToken)) {
            return BizResult.create(null);
        }

        SmsCache.SmsCacheDO smsCacheDO = smsCache.getSmsCacheDO(mobileBO.getMobile());
        if (smsCacheDO == null) {
            return BizResult.error(LoginExceEnum.SMS_TOKEN_CHECK_FAILED);
        }
        if (smsCacheDO.getToken().equals(smsToken)) {
            //暂时不清code
//            smsCache.evictSmsCacheDO(mobileBO.getMobile());
            return BizResult.create(null);
        } else {
            if (smsCacheDO.getCheckTimes() >= 3) {
                return BizResult.error(LoginExceEnum.SMS_TOKEN_CHECK_MORE_TIMES);
            }
            smsCacheDO.setCheckTimes(smsCacheDO.getCheckTimes() + 1);
            smsCache.setSmsCacheDO(mobileBO.getMobile(), smsCacheDO);
            return BizResult.error(LoginExceEnum.SMS_TOKEN_CHECK_FAILED);
        }
    }

    public String getSmsToken(String mobile) {
        SmsCache.SmsCacheDO smsCacheDO = smsCache.getSmsCacheDO(mobile);
        if (smsCacheDO != null) {
            return smsCacheDO.getToken();
        }
        return null;
    }

    private String getRandomToken() {
        Random rand = new Random();
        return String.valueOf(rand.nextInt(8999) + 1000);
    }

    private boolean send(MobileBO mobileBO, String token) {
        SmsMsgParam smsMsgParam = new SmsMsgParam();
        smsMsgParam.setMobile(Long.valueOf(mobileBO.getMobile()).toString());
        smsMsgParam.setNationCode(mobileBO.getNationCode());
        Map<String, String> data = Maps.newHashMap();
        data.put("code", token);
        smsMsgParam.setData(data);
        log.info("短信发送：{}", JsonUtil.beanToJson(smsMsgParam));
        BizResult<Boolean> booleanBizResult = smsMessageClient.sendMsg(smsMsgParam);
        return booleanBizResult.getData();
    }
}
