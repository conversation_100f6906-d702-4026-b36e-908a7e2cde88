package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.common.exception.BizExceEnum;
import com.chargebolt.aeacus.dao.OssUserDAO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.component.annotation.DataAuthoritySql;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.commons.eden.codec.MD5Utils;
import so.dian.commons.eden.exception.BizException;
import so.dian.eros.pojo.dto.AllocateUserDTO;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
@Slf4j
@Component
public class UserManager {

    public final static String PASSWORD_SALT = "vmr0%Gth35_t";


    private static final Integer PAGE_SIZE = 50;

    @Resource
    private OssUserDAO ossUserDAO;

    public List<OssUserDO> queryByPage(Integer page){
        return ossUserDAO.queryByPage((page - 1) * PAGE_SIZE, PAGE_SIZE);
    }

    public List<OssUserDO> queryByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return null;
        }
        return ossUserDAO.queryByIds(ids);
    }

    /**
     * 通过手机号码查询用户
     *
     * @param mobile
     * @return
     */
    public OssUserDO getByMobile(String mobile){
        return ossUserDAO.getByMobile(mobile);
    }

    public OssUserDO getById(Long id){
        return ossUserDAO.getById(id);
    }

    @DataAuthoritySql
    public OssUserDO queryByName(String name) {
        return ossUserDAO.queryByName(name);
    }

    public List<OssUserDO> queryByPage(OssUserDO ossUserDO,List<Long> userIds) {
        if(!CollectionUtils.isEmpty(userIds)){
            userIds = userIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(userIds)){
                userIds = null;
            }
        }
        return ossUserDAO.query(ossUserDO,userIds);
    }


    public List<OssUserDO> queryByCooperator(Long cooperatorId) {
        return ossUserDAO.queryByCooperatorId(cooperatorId);
    }

    public Integer updateToken(OssUserDO ossUserDO){
        return ossUserDAO.updateToken(ossUserDO);
    }

    public OssUserDO insert(OssUserDO ossUserDO){
        if (ossUserDAO.queryByName(ossUserDO.getName()) != null){
            // tag 未国际化
            throw BizException.create(BizExceEnum.EXISTS_USERNAME, ossUserDO.getName());
        }
        if (ossUserDAO.getByMobile(ossUserDO.getMobile()) != null){
            throw BizException.create(BizExceEnum.EXISTS_MOBILE, ossUserDO.getName());
        }
        String originPassword = ossUserDO.getPassword();
        ossUserDO.setPassword(MD5Utils.encrypt(originPassword + PASSWORD_SALT).toUpperCase());
        ossUserDO.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        ossUserDAO.insert(ossUserDO);
        return ossUserDO;
    }

    public void update(OssUserDO ossUserDO){
        ossUserDO.setUpdateTime(new Date());
        ossUserDAO.update(ossUserDO);
    }

    public List<OssUserDO> queryByDepartmentCode(String code){
        OssUserDO ossUserDO = new OssUserDO();
        ossUserDO.setDepartmentCode(code);
        return ossUserDAO.query(ossUserDO,null);
    }

//    /**
//     * 根据用户名字获取用户信息
//     * @param name String
//     * @return OssUserDO
//     */
//    public OssUserDO getByName (String name) {
//        return ossUserDAO.queryByName(name);
//    }
//
//    /**
//     * 根据用户名字集合获取用户信息集合
//     * @param names List<String>
//     * @return List<OssUserDO>
//     */
//    public List<OssUserDO> getByNames (List<String> names) {
//        return ossUserDAO.queryByNames(names);
//    }

    /**
     * 根据部门code集合获取用户信息集合
     * @param dpmCodes List<String>
     * @return List<OssUserDO>
     */
    public List<OssUserDO> getByDpmCode (List<String> dpmCodes) {
        return ossUserDAO.queryByDpmCode(dpmCodes);
    }


    public List<AllocateUserDTO> queryAllocateUsersByName(String principalName, List<Long> agentIds) {
        return ossUserDAO.queryAllocateUsersByName(principalName, agentIds);
    }

    public List<AllocateUserDTO> queryAllByNameAndId(String principalName, Long currentPrincipalId) {
        return ossUserDAO.queryAllByNameAndId(principalName, currentPrincipalId);
    }

    /**
     * 获取代理商老板账号
     *
     * @param agentId
     * @return
     */
    public List<OssUserDO> getByAgentId(Long agentId){
        return ossUserDAO.getByAgentId(agentId);
    }

    /**
     * 用户逻辑删除
     * @param userId
     * @return
     */
    public Integer deletedUser(Long userId){
        OssUserDO ossUserDO = new OssUserDO();
        ossUserDO.setId(userId);
        ossUserDO.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        ossUserDO.setUpdateTime(new Date());
        return ossUserDAO.update(ossUserDO);
    }
}
