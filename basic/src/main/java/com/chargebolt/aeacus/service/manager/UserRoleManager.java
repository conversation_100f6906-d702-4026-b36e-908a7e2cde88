package com.chargebolt.aeacus.service.manager;

import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Component
public class UserRoleManager {

    @Resource
    private UserManager userManager;
    @Resource
    private RoleManager roleManager;

    @Transactional
    public void updateWithRole(OssUserDO ossUserDO, List<Long> roleIds){
        userManager.update(ossUserDO);
        if(CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        updateRoleMapping(roleIds,ossUserDO.getId());
    }

    @Transactional
    public void insertWithRole(OssUserDO ossUserDO, List<Long> roleIds){
        userManager.insert(ossUserDO);
        if(CollectionUtils.isEmpty(roleIds)){
            return;
        }
        addRoleMapping(new HashSet<>(),roleIds,ossUserDO.getId());
    }

    public List<Long> getUserIdsByRoleIds(List<Long> roleIds){
        List<OssUserRoleMappingDO> ossUserRoleMappingDOS = roleManager.getUserByRoleIds(roleIds);
        if(CollectionUtils.isEmpty(ossUserRoleMappingDOS)){
            return null;
        }
        return ossUserRoleMappingDOS.stream().map(OssUserRoleMappingDO::getUserId).collect(Collectors.toList());
    }

    private void updateRoleMapping(List<Long> roleIds,Long userId){
        roleManager.deleteByUserId(userId);
        addRoleMapping(new HashSet<>(),roleIds,userId);
    }

    private void addRoleMapping(HashSet<Long> oriRoleSet,List<Long> roleIds,Long userId){
        roleIds.forEach(roleId->{
            if(oriRoleSet.contains(roleId)){
                return;
            }
            oriRoleSet.add(roleId);
            OssUserRoleMappingDO ossUserRoleMappingDO = new OssUserRoleMappingDO();
            ossUserRoleMappingDO.setRoleId(roleId);
            ossUserRoleMappingDO.setUserId(userId);
            roleManager.addUserRoleMapping(ossUserRoleMappingDO);
        });
    }

}
