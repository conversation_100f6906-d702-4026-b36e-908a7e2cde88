package com.chargebolt.aeacus.service.support;

import com.chargebolt.aeacus.dto.department.OssDepartmentCreateDTO;
import com.chargebolt.aeacus.dto.department.OssDepartmentDTO;
import com.chargebolt.aeacus.entity.dataobject.OssDepartmentDO;
import com.chargebolt.aeacus.service.manager.DepartmentCodeManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class DepartmentSupport {

    @Resource
    private DepartmentCodeManager codeManager;

    public OssDepartmentDO convert2DepartmentDO(OssDepartmentCreateDTO createDTO,OssDepartmentDO parentDO){
        OssDepartmentDO ossDepartmentDO = new OssDepartmentDO();
        ossDepartmentDO.setName(createDTO.getName());
        ossDepartmentDO.setLevel(parentDO.getLevel() + 1);
        ossDepartmentDO.setCode(parentDO.getCode() + codeManager.generatorCode(parentDO.getCode()));
        ossDepartmentDO.setParentId(parentDO.getId());
        ossDepartmentDO.setCreator(createDTO.getOperatorName());
        ossDepartmentDO.setAgentId(createDTO.getAgentId());
        return ossDepartmentDO;
    }

    public Map<Long,OssDepartmentDTO> convert2DepartmentDTOMap(List<OssDepartmentDO> ossDepartmentDOS) {
        return ossDepartmentDOS.stream().collect(Collectors.toMap(OssDepartmentDO::getId, this::convert2DepartmentDTO));
    }

    public OssDepartmentDTO convert2DepartmentDTO(OssDepartmentDO ossDepartmentDO){
        return OssDepartmentDTO.builder()
                .id(ossDepartmentDO.getId())
                .code(ossDepartmentDO.getCode())
                .name(ossDepartmentDO.getName())
                .level(ossDepartmentDO.getLevel())
                .parentId(ossDepartmentDO.getParentId())
                .childNodes(new ArrayList<>())
                .build();
    }
}
