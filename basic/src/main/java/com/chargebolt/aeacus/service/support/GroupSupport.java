package com.chargebolt.aeacus.service.support;

import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.entity.dataobject.OssGroupDO;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class GroupSupport {

    public List<OssGroupDTO> convert2OssGroupDTOs(List<OssGroupDO> ossGroupDOS, Map<Long, List<OssPermissionDTO>> map) {
        return ossGroupDOS.stream().map(item->convert2OssGroupDTO(item,map)).collect(Collectors.toList());
    }

    public OssGroupDTO convert2OssGroupDTO(OssGroupDO ossGroupDO,Map<Long, List<OssPermissionDTO>> map){
        return OssGroupDTO.builder()
                .id(ossGroupDO.getId())
                .name(ossGroupDO.getName())
                .type(ossGroupDO.getType())
                .permissionDTOList(map.get(ossGroupDO.getId()))
                .createTime(ossGroupDO.getCreateTime())
                .creator(ossGroupDO.getCreator())
                .deleted(ossGroupDO.getDeleted())
                .remark(ossGroupDO.getRemark())
                .updateTime(ossGroupDO.getUpdateTime())
                .updator(ossGroupDO.getUpdator())
                .build();
    }
}
