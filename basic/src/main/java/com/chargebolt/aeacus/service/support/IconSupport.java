package com.chargebolt.aeacus.service.support;

import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.entity.dataobject.IconDO;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class IconSupport {

    public List<OssIconDTO> convert2IconDTO(List<IconDO> iconDOS) {
        if(CollectionUtils.isEmpty(iconDOS)){
            return Collections.emptyList();
        }
        return iconDOS.stream().map(item->convert2IconDTO(item)).collect(Collectors.toList());
    }

    public OssIconDTO convert2IconDTO(IconDO iconDO) {
        return OssIconDTO.builder()
                .id(iconDO.getId())
                .img(iconDO.getImg())
                .title(iconDO.getTitle())
                .type(iconDO.getType())
                .url(iconDO.getUrl())
                .code(iconDO.getAction())
                .build();
    }

    public IconDO convert2IconDO(OssIconDTO ossIconDTO) {
        IconDO iconDO = new IconDO();
        return null;
    }
}
