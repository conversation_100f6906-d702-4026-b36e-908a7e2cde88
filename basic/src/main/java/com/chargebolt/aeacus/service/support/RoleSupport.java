package com.chargebolt.aeacus.service.support;

import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.dto.OssRoleCreateDTO;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.chargebolt.response.aeacus.RoleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class RoleSupport {

    public List<OssRoleDTO> convert2RoleDTO(List<RoleDO> roleDOS) {
        if(CollectionUtils.isEmpty(roleDOS)){
            return Collections.emptyList();
        }
        return roleDOS.stream().map(item->convert2RoleDTO(item,null)).collect(Collectors.toList());
    }

    public OssRoleDTO convert2RoleDTO(RoleDO roleDO,List<OssGroupDTO> ossGroupDTOS) {
        return OssRoleDTO.builder()
                .id(roleDO.getId())
                .name(roleDO.getName())
                .createTime(roleDO.getCreateTime())
                .creator(roleDO.getCreator())
                .updateTime(roleDO.getUpdateTime())
                .updator(roleDO.getUpdator())
                .permissionGroup(ossGroupDTOS)
                .build();
    }

    public RoleDO convert2RoleDTO(OssRoleCreateDTO ossRoleCreateDTO) {
        RoleDO roleDO = new RoleDO();
        roleDO.setName(ossRoleCreateDTO.getName());
        roleDO.setModuleId(ossRoleCreateDTO.getModuleId());
        roleDO.setDeleted(0);
        roleDO.setCreator(ossRoleCreateDTO.getCreator());
        return roleDO;
    }

    public RoleResponse convert2Response(RoleDO roleDO, Map<Long, String> agentMap) {

        return RoleResponse.builder()
                .id(roleDO.getId())
                .roleId(roleDO.getId())
                .roleName(roleDO.getName())
                .agentId(roleDO.getAgentId())
                .agentName(agentMap.get(roleDO.getAgentId()))
                .updater(roleDO.getUpdator())
                .creator(roleDO.getCreator())
                .updateTime(roleDO.getCreateTime().getTime())
                .createTime(roleDO.getCreateTime().getTime())
                .build();
    }
}
