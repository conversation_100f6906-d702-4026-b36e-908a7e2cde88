package com.chargebolt.aeacus.service.support;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.constant.CommonConstants;
import com.chargebolt.aeacus.dto.OssMerchantUserDTO;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.dto.OssUserOpDTO;
import com.chargebolt.aeacus.entity.dataobject.OssMerchantUserDO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.DepartmentCodeManager;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.chargebolt.commons.constant.BizConstant;
import com.chargebolt.ezreal.utils.MobileUtil;
import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.response.agent.AppAgentResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.mofa3.lang.util.RandomCodeUtil;
import so.dian.talos.pojo.entity.MerchantDO;

import static com.chargebolt.aeacus.common.AeacusConstsnts.USER_TYPE_AGENT_BOSS;
import static com.chargebolt.aeacus.common.AeacusConstsnts.USER_TYPE_MERCHANT;

@Service
@Slf4j
public class UserSupport {
    @Resource
    private DepartmentCodeManager codeManager;


    public OssUserDO convertAgent2OssUserDO(OssUserOpDTO ossUserOpDTO) {
        OssUserDO ossUserDO = new OssUserDO();
        if(ossUserOpDTO.getMobile().startsWith("+")){
            ossUserDO.setMobile(ossUserOpDTO.getMobile());
        }else{
            ossUserDO.setMobile(MobileUtil.getNationMobile(ossUserOpDTO.getNationCode(),ossUserOpDTO.getMobile()));
        }
        ossUserDO.setType(ossUserOpDTO.getType());
        ossUserDO.setDepartmentCode(ossUserOpDTO.getDepartmentCode());
        ossUserDO.setDataPerType(ossUserOpDTO.getDataPerType());
        ossUserDO.setNickName(ossUserOpDTO.getNickName());
        ossUserDO.setFullName(ossUserOpDTO.getFullName());
        ossUserDO.setCreator(ossUserOpDTO.getOperatorName());

        ossUserDO.setName(ossUserOpDTO.getName());
        ossUserDO.setPassword(AeacusConstsnts.USER_INIT_PASSWORD);
        ossUserDO.setCooperatorId(ossUserOpDTO.getCooperatorId());
        ossUserDO.setMultiLogin(AeacusConstsnts.USER_DENIDE_MULTILOGIN);
        ossUserDO.setAgentId(ossUserOpDTO.getAgentId());
        return ossUserDO;
    }

    public void operateOssUserDO(OssUserDO ossUserDO,OssUserOpDTO ossUserOpDTO){
        ossUserDO.setMobile(MobileUtil.getNationMobile(ossUserOpDTO.getNationCode(),ossUserOpDTO.getMobile()));
        ossUserDO.setType(ossUserOpDTO.getType());
        ossUserDO.setDepartmentCode(ossUserOpDTO.getDepartmentCode());
        ossUserDO.setDataPerType(ossUserOpDTO.getDataPerType());
        ossUserDO.setFullName(ossUserOpDTO.getFullName());
        ossUserDO.setNickName(ossUserOpDTO.getNickName());
        ossUserDO.setStatus(ossUserOpDTO.getStatus());
        ossUserDO.setUpdator(ossUserOpDTO.getOperatorName());
    }

    public List<OssUserDTO> convert2OssUserDTO(List<OssUserDO> ossUserDOS) {
        if(CollectionUtils.isEmpty(ossUserDOS)){
            return Collections.emptyList();
        }
        return ossUserDOS.stream().map(this::convert2OssUserDTO).collect(Collectors.toList());
    }

    /**
     * 转换OssUserDO为OssUserDTO，同时设置agentName
     * @param ossUserDOS 用户列表
     * @param agentMap 公司信息
     * @return
     */
    public List<OssUserDTO> convert2OssUserDTOWithAgent(List<OssUserDO> ossUserDOS,Map<Long, Agent> agentMap) {
        if(CollectionUtils.isEmpty(ossUserDOS)){
            return Collections.emptyList();
        }
        return ossUserDOS.stream()
                .map(item -> {
                    OssUserDTO ossUserDTO = convert2OssUserDTO(item);
                    Optional.ofNullable(agentMap.get(item.getAgentId()))
                            .ifPresent(agent -> ossUserDTO.setAgentName(agent.getAgentName()));
                    return ossUserDTO;
                })
                .collect(Collectors.toList());
    }

    public List<OssUserDTO> convert2OssUserDTO(List<OssUserDO> ossUserDOS,Map<Long,List<OssRoleDTO>> roleDTOMap) {
        if(CollectionUtils.isEmpty(ossUserDOS)){
            return Collections.emptyList();
        }
        return ossUserDOS.stream().map(item->convert2OssUserDTOWithRole(item,roleDTOMap.get(item.getId()))).collect(Collectors.toList());
    }

    public OssUserDTO convert2OssUserDTOWithRole(OssUserDO ossUserDO,List<OssRoleDTO> ossRoleDTOS){
        OssUserDTO ossUserDTO = convert2OssUserDTO(ossUserDO);
        ossUserDTO.setRoleDtos(ossRoleDTOS);
        return ossUserDTO;
    }

    public OssUserDTO convert2OssUserDTO(OssUserDO ossUserDO){
        if(ossUserDO == null){
            return null;
        }
        if(ossUserDO.getMobile().startsWith("+")|| ossUserDO.getMobile().contains("-")){
            return OssUserDTO.builder()
                    .userId(ossUserDO.getId())
                    .nationCode(MobileUtil.analysis(ossUserDO.getMobile()).getNationCode())
                    .mobile(MobileUtil.analysis(ossUserDO.getMobile()).getMobile())
                    .name(ossUserDO.getName())
                    .fullName(ossUserDO.getFullName())
                    .nickName(ossUserDO.getNickName())
                    .cooperatorId(ossUserDO.getCooperatorId())
                    .dataPerType(ossUserDO.getDataPerType())
                    .departmentCode(ossUserDO.getDepartmentCode())
                    .departmentName(codeManager.getNameByCode(ossUserDO.getDepartmentCode()))
                    .type(ossUserDO.getType())
                    .create_time(ossUserDO.getCreateTime())
                    .creator(ossUserDO.getCreator())
                    .update_time(ossUserDO.getUpdateTime())
                    .updator(ossUserDO.getUpdator())
                    .status(ossUserDO.getStatus())
                    .readonlFlag(ossUserDO.getReadonlFlag())
                    .agentId(ossUserDO.getAgentId())
                    .build();
        }
        return OssUserDTO.builder()
                .userId(ossUserDO.getId())
                .mobile(ossUserDO.getMobile())
                .name(ossUserDO.getName())
                .fullName(ossUserDO.getFullName())
                .nickName(ossUserDO.getNickName())
                .cooperatorId(ossUserDO.getCooperatorId())
                .dataPerType(ossUserDO.getDataPerType())
                .departmentCode(ossUserDO.getDepartmentCode())
                .departmentName(codeManager.getNameByCode(ossUserDO.getDepartmentCode()))
                .type(ossUserDO.getType())
                .create_time(ossUserDO.getCreateTime())
                .creator(ossUserDO.getCreator())
                .update_time(ossUserDO.getUpdateTime())
                .updator(ossUserDO.getUpdator())
                .status(ossUserDO.getStatus())
                .readonlFlag(ossUserDO.getReadonlFlag())
                .agentId(ossUserDO.getAgentId())
                .build();
    }

    public OssMerchantUserDTO convert2OssMerchantUserDTO(OssMerchantUserDO ossUserDO){
        if(ossUserDO == null){
            return null;
        }
        return OssMerchantUserDTO.builder()
                .userId(ossUserDO.getId())
                .mobile(ossUserDO.getMobile())
                .name(ossUserDO.getName())
                .cooperatorId(ossUserDO.getCooperatorId())
                .createTime(ossUserDO.getCreateTime())
                .creator(ossUserDO.getCreator())
                .updateTime(ossUserDO.getUpdateTime())
                .updator(ossUserDO.getUpdator())
                .status(ossUserDO.getStatus())
                .userToken(ossUserDO.getUserToken())
                .build();
    }

    public List<OssMerchantUserDTO> convert2OssMerchantUserDTOList(List<OssMerchantUserDO> ossUserDOs){
        if(CollectionUtils.isEmpty(ossUserDOs)){
            return null;
        }
        List<OssMerchantUserDTO> ossMerchantUserDTOS = new ArrayList<>();
        for (OssMerchantUserDO userDO : ossUserDOs) {
            ossMerchantUserDTOS.add(convert2OssMerchantUserDTO(userDO));
        }
        return ossMerchantUserDTOS;
    }

    public OssMerchantUserDO convert2OssMerchantUserDO(OssMerchantUserDTO ossMerchantUserDTO) {
        OssMerchantUserDO ossMerchantUserDO = new OssMerchantUserDO();
        ossMerchantUserDO.setMobile(ossMerchantUserDTO.getMobile());
        ossMerchantUserDO.setName(ossMerchantUserDTO.getName());
        ossMerchantUserDO.setPassword(AeacusConstsnts.USER_INIT_PASSWORD);
        ossMerchantUserDO.setCooperatorId(ossMerchantUserDTO.getCooperatorId());
        ossMerchantUserDO.setCreator(ossMerchantUserDTO.getCreator());
        ossMerchantUserDO.setUpdator(ossMerchantUserDTO.getUpdator());
        ossMerchantUserDO.setStatus(CommonConstants.INTEGER_ZERO);
        return ossMerchantUserDO;
    }

    /**
     * 转换agentUserResponse为OssUserDO
     * @param agentResponse 公司信息
     * @param currentUser 当前用户
     * @return OssUserDO
     */
    public OssUserDO convertAgent2OssUserDO(AppAgentResponse agentResponse, OssUserDTO currentUser) {
        OssUserDO ossUserDO = new OssUserDO();
        ossUserDO.setMobile(MobileUtil.getNationMobile(agentResponse.getRegionalCode(), agentResponse.getContractMobile()));
        ossUserDO.setName(agentResponse.getRegionalCode()+ RandomCodeUtil.generateTextCode(RandomCodeUtil.TYPE_NUM_ONLY, 6, null));
        ossUserDO.setNickName(agentResponse.getContractName());
        ossUserDO.setFullName(agentResponse.getContractName());
        ossUserDO.setPassword(BizConstant.BD_DEFAULT_PWD);
        ossUserDO.setCooperatorId(1L);
        ossUserDO.setAgentId(agentResponse.getId());
        // 数据权限类型：0全部1本人2本部门3本部门及以下'
        ossUserDO.setDataPerType(0);
        // 合资公司类型
        ossUserDO.setType(USER_TYPE_AGENT_BOSS);
        // 用户根节点默认为000
        ossUserDO.setDepartmentCode("000");
        ossUserDO.setCreator(currentUser.getName());
        ossUserDO.setStatus(CommonConstants.INTEGER_ZERO);
        return ossUserDO;
    }

    /**
     * 转换agentUserResponse为OssUserDO
     * @param merchantDO 商户信息
     * @return OssUserDO
     */
    public OssUserDO convertMch2OssUserDO(MerchantDO merchantDO) {
        OssUserDO ossUserDO = new OssUserDO();
        ossUserDO.setMobile(MobileUtil.getNationMobile(merchantDO.getNationCode(),merchantDO.getMobile()));
        String name= "mch"+merchantDO.getId()+ RandomCodeUtil.generateTextCode(RandomCodeUtil.TYPE_NUM_ONLY, 3, null);
        ossUserDO.setName(name);
        ossUserDO.setNickName(name);
        ossUserDO.setFullName(name);
        ossUserDO.setPassword(BizConstant.BD_DEFAULT_PWD);
        ossUserDO.setCooperatorId(1L);
        ossUserDO.setAgentId(merchantDO.getAgentId());
        // 数据权限类型：0全部1本人2本部门3本部门及以下'
        ossUserDO.setDataPerType(1);
        // 类型 3.商户
        ossUserDO.setType(USER_TYPE_MERCHANT);
        // 用户根节点默认为000
        ossUserDO.setDepartmentCode("000");
        ossUserDO.setCreator("system");
        ossUserDO.setStatus(CommonConstants.INTEGER_ZERO);
        return ossUserDO;
    }
}
