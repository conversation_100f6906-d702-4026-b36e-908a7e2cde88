package com.chargebolt.agent.dto;

import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.request.agent.SearchAgentRequest;
import lombok.Data;

import java.util.List;

/**
 * 代理商查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/5 17:08
 */
@Data
public class AgentQueryDTO {


    private Long parentId;

    /**
     * 查询条件指标
     * <p>
     * 0.所有
     * 1.代理商名称
     * 2.代理商ID
     * 3.手机号码
     * 4.负责人
     */
    private Integer type;

    private String content;
    /**
     * 合作状态
     * <p>
     * 1.未合作
     * 2.已合作
     * 3.已终止
     */
    private Integer cooperatingState;
    /**
     * 代理商账号是否启用
     * <p>
     * 1.启用
     * 2.禁用
     */
    private Integer userAccountState;

    /**
     * 公司ID
     */
    private Long agentId;
    /**
     * 公司名称
     */
    private String agentName;
    /**
     * 手机号
     */
    private String contractMobile;
    /**
     * 负责人ID
     */
    private Long sellerId;

    private UserDataAuthorityContext userContext;

    private List<Long> agentIds;

    private Boolean multistageAgentFlag;

    public AgentQueryDTO(SearchAgentRequest request) {
        this.type = request.getType();
        this.content = request.getContent();
        this.cooperatingState = request.getCooperatingState();
        this.userAccountState = request.getUserAccountState();
        this.parentId= request.getParentId();
        this.agentId = request.getAgentId();
        this.agentName = request.getAgentName();
        this.contractMobile = request.getContractMobile();
        this.sellerId = request.getSellerId();
        this.multistageAgentFlag= request.getMultistageAgentFlag();
    }
    public AgentQueryDTO() {
    }

}
