package com.chargebolt.basic.request.shop;

import com.chargebolt.basic.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 门店列表请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22 15:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShopListRequest extends PageRequest implements Serializable {


    private String sellerName;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 门店状态
     * 0.待签约
     * 1.待安装
     * 2.已安装
     */
    private Integer shopStatus;

    /**
     * 纬度
     */
    private Double poiLatitude;

    /**
     * 经度
     */
    private Double poiLongitude;


    private Long agentId;
    private Long sellerId;


}
