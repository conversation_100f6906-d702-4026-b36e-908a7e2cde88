/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.basic.request.shop;

import com.chargebolt.basic.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopOrdersRequest.java, v 1.0 2023-12-04 3:26 PM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShopOrdersRequest extends PageRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202312338152649L;
    /**
     * 门店ID
     */
    private Long shopId;
    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 借出时间（毫秒时间戳）
     * 根据传入拼接起止
     */
    private Long loanTime;

}