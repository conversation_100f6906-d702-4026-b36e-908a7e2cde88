/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.basic.response.area;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: Area.java, v 1.0 2024-07-04 上午10:52 Exp $
 */
@Data
public class Area implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202407186105251L;
    /**
     * 省code
     */
    private String code;

    /**
     * 省名称
     */
    private String name;

    private List<Area> children;

    public Area findAreaByCode(String code) {
        if (this.code.equals(code)) {
            return this;
        }
        if (children != null) {
            for (Area child : children) {
                Area result = child.findAreaByCode(code);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }
}