/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.commons.constant;

/**
 * redis 缓存key前缀常量
 *
 * <AUTHOR>
 * @version: CacheKeyConstant.java, v 1.0 2023-10-26 4:02 PM Exp $
 */
public class CacheKeyConstant {
    /**
     * 资金账户创建
     */
    public static final String CREATE_ACCOUNT_LOCK= "__CACHE_CREATE_ACCOUNT_LOCK_";

    /**
     * 资金账户冻结、解冻
     */
    public static final String ACCOUNT_FROZEN_LOCK= "__CACHE_ACCOUNT_FROZEN_LOCK_";

    /**
     * 资金账户流水操作key
     */
    public static final String ACCOUNT_BALANCE_FLOW_LOCK= "__CACHE_ACCOUNT_BALANCE_FLOW_LOCK_";
    /**
     * 用户数据权限结合key
     */
    public static final String USER_DATA_AUTHORITY_KEY= "__CACHE_USER_DATA_AUTHORITY_";
    /**
     * 自动退款押金
     */
    public static final String DEPOSIT_AUTO_REFUND_KEY= "__CACHE_DEPOSIT_AUTO_REFUND_";
    /**
     * 押金抵扣订单
     */
    public static final String ORDER_AUTO_PAY_BY_DEPOSIT_KEY= "__CACHE_ORDER_AUTO_PAY_BY_DEPOSIT_";


    // =================== 数据看板缓存 =================

    /**
     * 缓存时间 10分钟
     */
    public static final Integer DATA_PANEL_CACHE_TIME= 10;
    /**
     * 订单数据看板缓存
     */
    public static final String PANEL_ORDER_STATISTICS_CACHE_KEY = "__CACHE_PANEL_ORDER_STATISTICS_";
    /**
     * 门店数据看板缓存
     */
    public static final String PANEL_SHOP_STATISTICS_CACHE_KEY = "__CACHE_PANEL_SHOP_STATISTICS_";
    /**
     * 门店管理数据看板缓存
     */
    public static final String PANEL_SHOP_MANAGER_STATISTIC_CACHE_KEY = "__CACHE_PC_SHOP_MANAGER_STATISTICS_";
}