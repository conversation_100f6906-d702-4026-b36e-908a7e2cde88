package com.chargebolt.commons.enums;

/**
 * 权限等级
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/19 10:57
 */
public enum AuthorityLevelEnum {


    ALL(1, "所有"),
    AGENT(2, "代理商维度"),
    USER(3, "用户维度");

    private final Integer code;
    private final String desc;

    AuthorityLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
