/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DataAuthorityConditionType.java, v 1.0 2024-09-05 上午9:53 Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataAuthorityConditionType {
    AGENT_LIST(1, "代理商集合"),
    STAFF_LIST(2, "员工集合"),
    ;

    /**
     * 枚举编码
     */
    @Getter
    private final Integer code;

    /**
     * 枚举说明
     */
    @Getter
    private final String desc;
}