/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DataCycleEnum.java, v 1.0 2023-11-28 5:45 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataCycleEnum {
    ALL(0, "不限制"),
    DAY(1, "日"),
    WEEK(2, "周"),
    MONTH(3, "月"),
    UNKNOWN(-1, "不支持类型"),;
    /**
     * 枚举编码
     */
    @Getter
    private final Integer code;

    /**
     * 枚举说明
     */
    @Getter
    private final String descr;

    public static DataCycleEnum getById(Integer code) {
        for (DataCycleEnum value : DataCycleEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}