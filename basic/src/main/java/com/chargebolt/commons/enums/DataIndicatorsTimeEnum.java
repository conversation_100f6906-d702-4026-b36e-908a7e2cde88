/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据查询时间指标
 *
 * <AUTHOR>
 * @version: DataIndicatorsTimeEnum.java, v 1.0 2023-11-28 5:45 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataIndicatorsTimeEnum {
    ALL(0, "不限制"),
    YESTERDAY(1, "昨日"),
    LAST_7_DAYS(2, "最近7日"),
    LAST_30_DAYS(3, "最近30日"),
    NATURE_DAY(4, "自然日"),
    NATURE_WEEK(5, "自然周"),
    NATURE_MONTH(6, "自然月"),
    UNKNOWN(-1, "不支持类型"),;

    /**
     * 枚举编码
     */
    @Getter
    private final Integer code;

    /**
     * 枚举说明
     */
    @Getter
    private final String descr;

    public static DataIndicatorsTimeEnum getById(Integer code) {
        for (DataIndicatorsTimeEnum value : DataIndicatorsTimeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}