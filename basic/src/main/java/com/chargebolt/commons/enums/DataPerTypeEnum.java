package com.chargebolt.commons.enums;

import lombok.Getter;

/**
 * 数据权限类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/5 10:47
 */
public enum DataPerTypeEnum {

    ALL(0, "全部"),
    SELF(1, "本人"),
    DEPARTMENT(2, "本部门"),
    DEPARTMENT_AND_BELOW(3, "本部门及以下");

    @Getter
    private final Integer value;
    @Getter
    private final String desc;

    DataPerTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
