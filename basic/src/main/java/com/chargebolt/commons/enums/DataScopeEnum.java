/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DataScopeEnum.java, v 1.0 2024-09-05 上午9:53 Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataScopeEnum {
    /**
     * 预留
     */
    ALL(1, "所有(子集)数据"),
    ONLY_ONESELF(2, "仅自己本级数据"),
//    CHILD(3, "子集数据"),

    ;

    /**
     * 枚举编码
     */
    @Getter
    private final Integer code;

    /**
     * 枚举说明
     */
    @Getter
    private final String desc;
}