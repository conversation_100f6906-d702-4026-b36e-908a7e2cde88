package com.chargebolt.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IndicatorKeyEnum {

    DEVICE_OFFLINE_HOURS("device_offline_hours",
            IndicatorScopeEnum.DEVICE_SCOPE.getScope(),
            "deviceOfflineHoursMatchStrategy",
            "设备离线时长"),
    DEVICE_TYPE("device_type",
            IndicatorScopeEnum.DEVICE_SCOPE.getScope(),
            "deviceTypeMatchStrategy",
            "设备类型"),
    SHOP_POWERBANK_COUNT("shop_powerbank_count",
            IndicatorScopeEnum.DEVICE_SCOPE.getScope(),
            "shopPowerbankCountStrategy",
            "店铺充电宝总数"),
    DEVICE_POWERBANK_COUNT("device_powerbank_count",
            IndicatorScopeEnum.DEVICE_SCOPE.getScope(),
            "devicePowerbankCountStrategy",
            "设备充电宝总数"),

    ;

    private String key;
    private Integer scope;
    private String calculateStrategy;
    private String desc;

    public static String getCalculateStrategy(String key) {
        for (IndicatorKeyEnum indicatorKeyEnum : IndicatorKeyEnum.values()) {
            if (indicatorKeyEnum.getKey().equals(key)) {
                return indicatorKeyEnum.getCalculateStrategy();
            }
        }
        return null;
    }

    public static Integer getScope(String key) {
        for (IndicatorKeyEnum indicatorKeyEnum : IndicatorKeyEnum.values()) {
            if (indicatorKeyEnum.getKey().equals(key)) {
                return indicatorKeyEnum.getScope();
            }
        }
        return null;
    }

    public static IndicatorKeyEnum getByKey(String key) {
        for (IndicatorKeyEnum indicatorKeyEnum : IndicatorKeyEnum.values()) {
            if (indicatorKeyEnum.getKey().equals(key)) {
                return indicatorKeyEnum;
            }
        }
        return null;
    }

}
