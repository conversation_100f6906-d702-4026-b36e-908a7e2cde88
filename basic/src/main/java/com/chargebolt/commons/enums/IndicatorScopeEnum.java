package com.chargebolt.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IndicatorScopeEnum {

    DEVICE_SCOPE(1, "设备域"),
    SHOP_SCOPE(2, "门店域"),

    ;

    private Integer scope;
    private String desc;

    public static String getDesc(Integer scope) {
        for (IndicatorScopeEnum scopeEnum : IndicatorScopeEnum.values()) {
            if (scopeEnum.getScope().equals(scope)) {
                return scopeEnum.getDesc();
            }
        }
        return null;
    }

}
