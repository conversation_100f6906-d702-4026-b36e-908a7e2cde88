/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 多语种枚举定义
 *
 * <AUTHOR>
 * @version: MerchantTypeEnum.java, v 1.0 2024-01-09 4:03 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum MerchantTypeEnum {
    MERCHANT(1,"MERCHANT"),
    AGENT(2, "AGENT");

    @Getter
    private final Integer id;
    @Getter
    private final String desc;

}