/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: MqttStatusEnum.java, v 1.0 2023-11-30 3:40 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum MqttStatusEnum {
    OFFLINE(0, "离线"),
    ONLINE(1, "在线"),
    UNKNOWN(-1, "未知类型"),;
    /**
     * 枚举编码
     */
    @Getter
    private final Integer code;

    /**
     * 枚举说明
     */
    @Getter
    private final String descr;
}