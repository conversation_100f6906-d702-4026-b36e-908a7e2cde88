/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * 多语种枚举定义
 * Locale 为系统已支持的语言，languageCodes对应系统支持语言
 * 比如："zh-CN", "zh-TW", "zh-MO"返回简体中文
 * 如果需要支持繁体，需要独立增加支持
 * CHINESE_TRADITIONAL(Locale.TRADITIONAL_CHINESE,Arrays.asList("zh-HK","zh-TW"))
 *
 * <AUTHOR>
 * @version: MultilingualEnum.java, v 1.0 2024-01-09 4:03 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum MultilingualEnum {
    CHINESE("简体中文",Locale.SIMPLIFIED_CHINESE,Arrays.asList("zh-CN","zh-MO")),
    CHINESE_TRADITIONAL("繁体中文",new Locale("zh", "HK"),Arrays.asList("zh-HK", "zh-TW")),
    ENGLISH("英文",Locale.UK,Arrays.asList("en-GB","en-US")),
    VIETNAMESE("越南语",new Locale("vi", "VN"),Arrays.asList("vi-VN")),
    //    THAI("泰语",new Locale("th", "TH"),Arrays.asList("th-TH")),
    // in-ID ISO 639标准，最新的ISO 3166标准，值是id-ID
    INDONESIAN("印尼语",new Locale("id", "ID"),Arrays.asList("id-ID", "in-ID")),
    ARABIC("阿拉伯语",new Locale("ar", "AE"),Arrays.asList("ar-AE")),
    ;
    @Getter
    private final String name;
    @Getter
    private final Locale locale;
    @Getter
    private final List<String> languageCodes;

    public static MultilingualEnum findLanguageEnum(String languageCode) {
        for (MultilingualEnum enumValue : values()) {
            for (String code : enumValue.getLanguageCodes()) {
                if (code.equalsIgnoreCase(languageCode)) {
                    return enumValue;
                }
            }
        }
        return ENGLISH;
    }


}
