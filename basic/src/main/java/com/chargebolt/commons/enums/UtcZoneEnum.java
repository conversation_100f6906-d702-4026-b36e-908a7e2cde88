/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: UtcZoneEnum.java, v 1.0 2024-11-13 17:30 Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum UtcZoneEnum {
    UTC_0_12("UTC-12", "Pacific/Kwajalein"),
    UTC_0_11("UTC-11", "Pacific/Midway"),
    UTC_0_10("UTC-10", "Pacific/Honolulu"),
    UTC_0_9("UTC-9", "America/Anchorage"),
    UTC_0_8("UTC-8", "America/Los_Angeles"),
    UTC_0_7("UTC-7", "America/Denver"),
    UTC_0_6("UTC-6", "America/Chicago"),
    UTC_0_5("UTC-5", "America/New_York"),
    UTC_0_4("UTC-4", "America/Caracas"),
    UTC_0_3("UTC-3", "America/Sao_Paulo"),
    UTC_0_2("UTC-2", "Atlantic/South_Georgia"),
    UTC_0_1("UTC-1", "Atlantic/Cape_Verde"),
    UTC_0("UTC+0", "Europe/London"),
    UTC_1_1("UTC+1", "Europe/Paris"),
    UTC_1_2("UTC+2", "Europe/Helsinki"),
    UTC_1_3("UTC+3", "Europe/Moscow"),
    UTC_1_4("UTC+4", "Asia/Baku"),
    UTC_1_5("UTC+5", "Asia/Karachi"),
    UTC_1_6("UTC+6", "Asia/Dhaka"),
    UTC_1_7("UTC+7", "Asia/Bangkok"),
    UTC_1_8("UTC+8", "Asia/Shanghai"),
    UTC_1_9("UTC+9", "Asia/Tokyo"),
    UTC_1_10("UTC+10", "Australia/Sydney"),
    UTC_1_11("UTC+11", "Pacific/Noumea"),
    UTC_1_12("UTC+12", "Pacific/Auckland"),
    ;

    /**
     * 枚举编码
     */

    @Getter
    private final String utc;

    /**
     * 枚举说明
     */
    @Getter
    private final String timeZone;
    /**
     * 根据 UTC 获取对应的时区
     *
     * @param utc UTC 值
     * @return 对应的时区
     */
    public static String getTimeZoneByUtc(String utc) {
        for (UtcZoneEnum enumValue : UtcZoneEnum.values()) {
            if (enumValue.getUtc().equals(utc)) {
                return enumValue.getTimeZone();
            }
        }
        return UTC_0.timeZone;
    }
}