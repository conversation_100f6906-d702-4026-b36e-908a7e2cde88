/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import com.chargebolt.commons.enums.MultilingualEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.repository.init.ResourceReader;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.mofa3.lang.util.JsonUtil;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: BillStateEnum.java, v 1.0 2023-10-30 3:15 PM Exp $
 */
@Slf4j
public enum BillStateEnum {
    UNSETTLE(1, "未出账", "Account not issued"),
    SETTLED(2, "已出账", "Account has been issued"),
    OTHER(-1, "其他", "Other")

    ;

    private static final Map<Integer, Map<MultilingualEnum, String>> TRANSLATIONS_CACHE = new ConcurrentHashMap<>();
    private final int value;
    private Map<MultilingualEnum, String> translations;

    BillStateEnum(int value, String... language) {
        this.value = value;
    }

    // Static initialization block to load translations once
    static {
        ClassLoader classLoader = ResourceReader.class.getClassLoader();
        String langFile = BillStateEnum.class.getSimpleName() + ".json";
        try (InputStream inputStream = classLoader.getResourceAsStream("basic/enums/" + langFile)) {
            if (inputStream != null) {
                String fileContent = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8)).lines().collect(Collectors.joining(""));
                Map<Integer, Map<String, String>> multilingualMap = JsonUtil.jsonToBean(fileContent, new TypeReference<Map<Integer, Map<String, String>>>() {});

                for (Map.Entry<Integer, Map<String, String>> entry : multilingualMap.entrySet()) {
                    Map<MultilingualEnum, String> translationForValue = entry.getValue().entrySet().stream()
                            .collect(Collectors.toMap(
                                    e -> MultilingualEnum.findLanguageEnum(e.getKey()),
                                    Map.Entry::getValue,
                                    (v1, v2) -> v1
                            ));
                    TRANSLATIONS_CACHE.put(entry.getKey(), translationForValue);
                }

                Arrays.stream(BillStateEnum.values())
                        .forEach(status -> status.translations = Optional.ofNullable(TRANSLATIONS_CACHE.get(status.value))
                                .orElse(Collections.emptyMap()));
            } else {
                log.error("Resource file not found: basic/enums/{}", langFile);
            }
        } catch (Exception e) {
            log.error("File loading exception: {}", e);
        }
    }

    public int getValue() {
        return value;
    }

    public String getTranslation(String lang) {
        return translations.getOrDefault(MultilingualEnum.findLanguageEnum(lang), "Language not supported");
    }

    public static String getTranslationByValue(int value) {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return getTranslationByValueAndLang(value, lang);
    }
    public static String getTranslationByValueAndLang(int value, String lang) {
        Map<MultilingualEnum, String> translations = TRANSLATIONS_CACHE.get(value);
        return translations != null ? translations.getOrDefault(MultilingualEnum.findLanguageEnum(lang), "Language not supported") :
                "Unknown value: " + value;
    }

}