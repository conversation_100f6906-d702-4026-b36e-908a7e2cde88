/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DataDetailLanguageEnum.java, v 1.0 2024-01-09 3:49 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataDetailLanguageEnum {
        ALL(0,"所有","All"),
     ;

    @Getter
    private final Integer code;
        /**
         * 中文
         */
        @Getter
        private final String chinese;

        /**
         * 英文说明
         */
        @Getter
        private final String english;



        public  String getEnumValue() {
            String lang= ThreadLanguageHolder.getCurrentLang();
            return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
        }


}