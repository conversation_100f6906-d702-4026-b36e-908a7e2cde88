/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DeviceDetailInstallStateLanguageEnum.java, v 1.0 2024-01-09 4:49 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DeviceDetailInstallStateLanguageEnum {

    INSTALLED(1,"已安装","Installed"),
    RECYCLED(2,"已回收","Recycled"),
    WAREHOUSE(3,"仓库","Warehouse"),
    ;
    @Getter
    private final Integer code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;

    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }
}