/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DeviceDetailIndicatorsLanguageEnum.java, v 1.0 2024-01-09 4:49 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DeviceDetailStateLanguageEnum {

    OFFLINE(0,"离线","Offline"),
    ONLINE(1,"在线","Online"),
    OTHER(-1, "其他", "Other"),
    ;
    @Getter
    private final Integer code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;

    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }
    public static String getDescrById(Integer code) {
        String lang= ThreadLanguageHolder.getCurrentLang();
        for (DeviceDetailStateLanguageEnum value : DeviceDetailStateLanguageEnum.values()) {
            if (value.getCode().equals(code)) {
                return StringUtils.equalsIgnoreCase("zh-cn", lang)?value.chinese:value.english;
            }
        }
        return StringUtils.equalsIgnoreCase("zh-cn", lang)?OTHER.chinese:OTHER.english;
    }
}