package com.chargebolt.commons.enums.language;

import com.chargebolt.framework.i18n.I18nEnumInterface;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * FAQ业务枚举
 * 包含FAQ相关的业务标识和异常信息
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@AllArgsConstructor
public enum FaqBizEnum implements I18nEnumInterface {
    // ==================== 业务标识 ====================
    FAQ_CATEGORY("faq_category", "FAQ分类"),
    FAQ_RECORD("faq_record", "FAQ记录"),

    // ==================== 参数验证异常 (800001-800020) ====================
    FAQ_PARAM_NULL("800001", "参数不能为空"),
    FAQ_PARAM_INVALID("800002", "参数无效"),
    FAQ_ACTION_UNSUPPORTED("800003", "不支持的操作类型"),
    FAQ_ID_SHOULD_BE_NULL("800004", "新增操作时，ID应该为空"),
    FAQ_ID_REQUIRED("800005", "编辑操作时，ID不能为空"),
    FAQ_CATEGORY_NAME_NULL("800006", "分类名称不能为空"),
    FAQ_QUESTION_NULL("800007", "FAQ问题不能为空"),
    FAQ_ANSWER_NULL("800008", "FAQ答案不能为空"),
    FAQ_LIST_EMPTY("800009", "FAQ列表不能为空"),
    FAQ_AGENT_ID_NULL("800010", "代理商ID不能为空"),
    FAQ_CATEGORY_NAME_OVERLENGTH("800011","Faq分类标签长度不能超过30字"),
    FAQ_QUESTION_OVERLENGTH("800012","Faq问题长度不能超过200字"),
    FAQ_ANSWER_OVERLENGTH("800013","Faq答案长度不能超过300字"),

    // ==================== 业务逻辑异常 (800021-800040) ====================
    FAQ_CATEGORY_EXISTS("800021", "分类已存在"),
    FAQ_CATEGORY_NOT_FOUND("800022", "分类不存在"),
    FAQ_NOT_FOUND("800023", "FAQ不存在"),
    FAQ_CATEGORY_DATA_NULL("800024", "分类数据为空"),
    FAQ_LIST_IS_EMPTY("800025", "FAQ列表为空"),
    FAQ_TRANSLATION_ERROR("800026", "翻译处理异常"),
    FAQ_TRANSLATION_SAVE_FAILED("800027", "翻译数据保存失败"),
    FAQ_ORIGIN_CONTENT_REQUIRED("800028", "原文内容必须提供"),
    FAQ_CATEGORY_ID_REQUIRED("800029", "分类ID必须提供"),
    FAQ_UNDEFINED_ACTION("800030", "未定义的操作类型");

    private final String code;
    private final String name;

    @Override
    public String getI18nCode() {
        return this.code;
    }

    @Override
    public String getDefaultMsg() {
        return this.name;
    }
}
