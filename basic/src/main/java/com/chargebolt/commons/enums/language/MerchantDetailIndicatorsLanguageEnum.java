/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: MerchantDetailIndicatorsLanguageEnum.java, v 1.0 2024-01-09 4:49 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum MerchantDetailIndicatorsLanguageEnum {

    ALL_MERCHANT(1,"商家总数","Total merchants"),
    OPEN_DIVIDE(2,"开通分成数","Open"),

    ;
    @Getter
    private final Integer code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;

    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }
}