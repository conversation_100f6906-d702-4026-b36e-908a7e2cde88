/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: MerchantDivideLanguageEnum.java, v 1.0 2024-01-10 6:16 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum MerchantDivideLanguageEnum {
    NOT_ACTIVATED(0,"未开通","Not activated"),
    ACTIVATED(1,"已开通","Activated"),
    OTHER(-1, "其他", "Other"),
    ;
    @Getter
    private final Integer code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;

    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }
    public static String getDescrById(Integer code) {
        String lang= ThreadLanguageHolder.getCurrentLang();
        for (MerchantDivideLanguageEnum value : MerchantDivideLanguageEnum.values()) {
            if (value.getCode().equals(code)) {
                return StringUtils.equalsIgnoreCase("zh-cn", lang)?value.chinese:value.english;
            }
        }
        return StringUtils.equalsIgnoreCase("zh-cn", lang)?OTHER.chinese:OTHER.english;
    }
}