package com.chargebolt.commons.enums.language;

import java.util.Arrays;

import com.chargebolt.framework.i18n.I18nEnumInterface;
import com.chargebolt.framework.i18n.I18nUtil;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MsgTemplateBizTypeEnum implements I18nEnumInterface {

    SYSTEM(1, "message.template.biztype.system", "System"),
    SHOP(2, "message.template.biztype.shop", "Shop"),
    CUSTOMER(3, "message.template.biztype.customer", "Customer"),
    DEVICE(4, "message.template.biztype.device", "Device"),
    ;

    private final Integer bizType;
    private final String i18nCode;
    private final String defaultMsg;

    @Override
    public String getDefaultMsg() {
        return defaultMsg;
    }

    @Override
    public String getI18nCode() {
        return i18nCode;
    }

    public static MsgTemplateBizTypeEnum getByCode(Integer code) {
        return Arrays.stream(MsgTemplateBizTypeEnum.values())
                .filter(e -> e.getBizType().equals(code))
                .findFirst()
                .orElse(null);
    }
}
