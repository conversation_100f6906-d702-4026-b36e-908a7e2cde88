package com.chargebolt.commons.enums.language;

import java.util.Arrays;

import com.chargebolt.framework.i18n.I18nEnumInterface;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MsgTemplateStatusEnum implements I18nEnumInterface{

    ENABLE(1, "message.template.status.enable", "Enable"),
    DISABLE(2, "message.template.status.disable", "Disable");

    private final Integer status;
    private final String i18nCode;
    private final String defaultMsg;

    @Override
    public String getI18nCode() {
        return i18nCode;
    }

    @Override
    public String getDefaultMsg() {
        return defaultMsg;
    }

    public static MsgTemplateStatusEnum getByCode(Integer code) {
        return Arrays.stream(MsgTemplateStatusEnum.values())
                .filter(e -> e.getStatus().equals(code))
                .findFirst()
                .orElse(null);
    }
}
