package com.chargebolt.commons.enums.language;

import java.util.Arrays;

import com.chargebolt.framework.i18n.I18nEnumInterface;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NotifyRuleScheduleEnum implements I18nEnumInterface {
    ONE_HOUR(1, "notify.rule.schedule.one.hour", "One Hour"),
    THREE_HOUR(3, "notify.rule.schedule.three.hour", "Three Hour"),
    SIX_HOUR(6, "notify.rule.schedule.six.hour", "Six Hour"),
    TWELVE_HOUR(12, "notify.rule.schedule.twelve.hour", "Twelve Hour"),
    ONE_DAY(24, "notify.rule.schedule.one.day", "One Day"),
    WEEK(7, "notify.rule.schedule.week", "Week"),
    MONTH(30, "notify.rule.schedule.month", "Month");

    private final int code;
    private final String name;
    private final String defaultMsg;

    @Override
    public String getI18nCode() {
        return name;
    }

    @Override
    public String getDefaultMsg() {
        return defaultMsg;
    }

    public static NotifyRuleScheduleEnum getByCode(int code) {
        return Arrays.stream(NotifyRuleScheduleEnum.values())
                .filter(e -> e.getCode() == code)
                .findFirst()
                .orElse(null);
    }
}
