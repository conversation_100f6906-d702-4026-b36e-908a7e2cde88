package com.chargebolt.commons.enums.language;

import java.util.Arrays;

import com.chargebolt.framework.i18n.I18nEnumInterface;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NotifyRuleStatusEnum implements I18nEnumInterface {
    ENABLE(1, "notify.rule.status.enable", "Enable"),
    DISABLE(0, "notify.rule.status.disable", "Disable");

    private final int code;
    private final String name;
    private final String defaultMsg;

    public static NotifyRuleStatusEnum getByCode(int code) {
        return Arrays.stream(NotifyRuleStatusEnum.values())
                .filter(e -> e.getCode() == code)
                .findFirst()
                .orElse(null);
    }

    @Override
    public String getI18nCode() {
        return name;
    }

    @Override
    public String getDefaultMsg() {
        return defaultMsg;
    }
}
