package com.chargebolt.commons.enums.language;

import java.util.Arrays;

import com.chargebolt.framework.i18n.I18nEnumInterface;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NotifyRuleTargetStrategyEnum implements I18nEnumInterface {
    SHOP_BD(0, "notify.rule.target.strategy.shop.bd", "Shop BD"),
    SHOP_BD_SUPERIOR(1, "notify.rule.target.strategy.shop.bd.superior", "Shop BD Superior"),
    SHOP_BD_BOSS(2, "notify.rule.target.strategy.shop.bd.boss", "Shop BD Boss");

    private final int code;
    private final String name;
    private final String defaultMsg;

    @Override
    public String getI18nCode() {
        return name;
    }

    @Override
    public String getDefaultMsg() {
        return defaultMsg;
    }

    public static NotifyRuleTargetStrategyEnum getByCode(int code) {
        return Arrays.stream(NotifyRuleTargetStrategyEnum.values())
                .filter(e -> e.getCode() == code)
                .findFirst()
                .orElse(null);
    }
}
