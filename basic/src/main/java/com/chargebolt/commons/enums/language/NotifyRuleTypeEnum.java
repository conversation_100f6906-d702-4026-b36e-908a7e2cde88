package com.chargebolt.commons.enums.language;

import java.util.Arrays;

import com.chargebolt.framework.i18n.I18nEnumInterface;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知规则类型枚举
 */
@Getter
@AllArgsConstructor
public enum NotifyRuleTypeEnum implements I18nEnumInterface {

    DEVICE_OFFLINE_REMAINDER(0, "notify.rule.type.device.offline.remainder", "Device Offline Remainder"),
    DEVICE_LACK_POWERBANK(1, "notify.rule.type.device.lack.powerbank", "Device Lack Powerbank"),
    DEVICE_OVERFLOW_POWERBANK(2, "notify.rule.type.device.overflow.powerbank", "Device Overflow Powerbank"),
    ;

    private final Integer code;
    private final String desc;
    private final String defaultMsg;

    @Override
    public String getI18nCode() {
        return desc;
    }

    @Override
    public String getDefaultMsg() {
        return defaultMsg;
    }

    public static NotifyRuleTypeEnum getByCode(int code) {
        return Arrays.stream(NotifyRuleTypeEnum.values())
                .filter(e -> e.getCode() == code)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 检查类型编码是否有效
     *
     * @param code 类型编码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        if (code == null) {
            return false;
        }
        for (NotifyRuleTypeEnum type : NotifyRuleTypeEnum.values()) {
            if (type.getCode() == code) {
                return true;
            }
        }
        return false;
    }
} 