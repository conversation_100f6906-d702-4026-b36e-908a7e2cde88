/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderDetailIndicatorsLanguageEnum.java, v 1.0 2024-01-09 4:49 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum OrderDetailIndicatorsLanguageEnum {

    SUCCESS_ORDERS(1,"成功订单数","Successful orders"),
    PAID_ORDERS(2,"付费订单数","Paid orders"),
    REFUND_ORDERS(3,"退款订单数","Refund orders"),
    SALES_ORDERS(4,"售宝订单数","Sales orders"),
    FREE_ORDERS(5,"0元订单数","Free orders"),

    ;
    @Getter
    private final Integer code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;

    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }
}