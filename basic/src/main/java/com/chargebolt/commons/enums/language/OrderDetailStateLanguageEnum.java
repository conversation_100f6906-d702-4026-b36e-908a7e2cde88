/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderDetailStateLanguageEnum.java, v 1.0 2024-01-09 5:00 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum OrderDetailStateLanguageEnum {
    NEW(1,"新建","New"),
    RUNNING(2,"进行中","In progress"),
    WAIT_PAY(3, "待支付", "To be paid"),
    PAYING(4,"支付中","Paying"),
    PAID(5, "已支付","Paid"),
    REFUNDING(6,"退款中", "Refunding"),
    REFUND(7,"已退款","Refunded"),
    CANCELING(8,"取消中", "Canceling"),
    CANCEL(9,"已取消", "Cancelled"),
    OTHER(-1, "其他", "Other"),
    ;
    @Getter
    private final Integer code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;



    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }
    public static String getDescrById(Integer code) {
        String lang= ThreadLanguageHolder.getCurrentLang();
        for (OrderDetailStateLanguageEnum value : OrderDetailStateLanguageEnum.values()) {
            if (value.getCode().equals(code)) {
                return StringUtils.equalsIgnoreCase("zh-cn", lang)?value.chinese:value.english;
            }
        }
        return StringUtils.equalsIgnoreCase("zh-cn", lang)?OTHER.chinese:OTHER.english;
    }
}