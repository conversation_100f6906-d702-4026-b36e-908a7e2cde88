/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import so.dian.eros.interceptor.ThreadLanguageHolder;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PaymentClientTypeLanguageEnum.java, v 1.0 2024-09-10 上午10:21 Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PaymentClientTypeLanguageEnum {
    H5("H5","H5","H5"),
    APP("APP","APP","APP"),
    WECHAT("WECHAT","微信小程序","Wechat"),
    ALIPAY_HK("ALIPAY_HK","支付宝-香港","Alipay HK"),
    ALIPAY_CN("ALIPAY_CN","支付宝-大陆","Alipay CN"),
    ZALOPAY("<PERSON><PERSON><PERSON>","<PERSON>alo","Zalo VN"),
    ;

    @Getter
    private final String code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;


    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }

}