/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import so.dian.eros.interceptor.ThreadLanguageHolder;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ProtocolTypeEnum.java, v 1.0 2024-09-10 上午10:21 Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ProtocolTypeLanguageEnum {
//    共享充电服务协议，消费者隐私协议，消费者付款协议，商家端隐私协议一、商家端隐私协议二
    CDFWXY("CDFWXY","共享充电服务协议","共享充电服务协议"),
    CDFWYSXY("CDFWYSXY","共享充电服务隐私政策","共享充电服务隐私政策"),
    XFZFKXY("XFZFKXY","消费者付款协议","消费者付款协议"),
    SJHTYHXY("SJHTYHXY","商家后台用户协议","商家后台用户协议"),
//    SJXY_B("SJXY_B","商家端隐私协议-B","商家端隐私协议-B"),
    ;

    @Getter
    private final String code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;


    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }

}