/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderDetailIndicatorsLanguageEnum.java, v 1.0 2024-01-09 4:49 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ShopDetailIndicatorsLanguageEnum {

    ALL_SHOP(1,"所有门店","All shops"),
    INSTALLED_SHOP(2,"已安装门店","Installed shops"),

    ;
    @Getter
    private final Integer code;
    /**
     * 中文
     */
    @Getter
    private final String chinese;

    /**
     * 英文说明
     */
    @Getter
    private final String english;

    public  String getEnumValue() {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return StringUtils.equalsIgnoreCase("zh-cn", lang)? chinese:english;
    }
}