/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.data.repository.init.ResourceReader;

import com.chargebolt.commons.enums.MultilingualEnum;
import com.fasterxml.jackson.core.type.TypeReference;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopStatusLanguageEnum.java, v 1.0 2024-01-10 10:45 AM Exp $
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ShopStatusLanguageEnum {
    TO_SIGN_UP(0, "待签约"),
    TO_BE_INSTALLED(1, "待安装"),
    INSTALLED(2, "已安装"),
    OTHER(-1, "其他"),
    ;


    private static final Map<Integer, Map<MultilingualEnum, String>> TRANSLATIONS_CACHE = new ConcurrentHashMap<>();
    private final int value;
    private Map<MultilingualEnum, String> translations;

    ShopStatusLanguageEnum(int value, String... language) {
        this.value = value;
    }

    // Static initialization block to load translations once
    static {
        ClassLoader classLoader = ResourceReader.class.getClassLoader();
        String langFile = ShopStatusLanguageEnum.class.getSimpleName() + ".json";
        try (InputStream inputStream = classLoader.getResourceAsStream("basic/enums/" + langFile)) {
            if (inputStream != null) {
                String fileContent = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8)).lines().collect(Collectors.joining(""));
                Map<Integer, Map<String, String>> multilingualMap = JsonUtil.jsonToBean(fileContent, new TypeReference<Map<Integer, Map<String, String>>>() {});

                for (Map.Entry<Integer, Map<String, String>> entry : multilingualMap.entrySet()) {
                    Map<MultilingualEnum, String> translationForValue = entry.getValue().entrySet().stream()
                            .collect(Collectors.toMap(
                                    e -> MultilingualEnum.findLanguageEnum(e.getKey()),
                                    Map.Entry::getValue,
                                    (v1, v2) -> v1
                            ));
                    TRANSLATIONS_CACHE.put(entry.getKey(), translationForValue);
                }

                Arrays.stream(ShopStatusLanguageEnum.values())
                        .forEach(status -> status.translations = Optional.ofNullable(TRANSLATIONS_CACHE.get(status.value))
                                .orElse(Collections.emptyMap()));
            } else {
                log.error("Resource file not found: basic/enums/{}", langFile);
            }
        } catch (Exception e) {
            log.error("File loading exception: {}", e);
        }
    }

    public int getValue() {
        return value;
    }

    public String getTranslation(String lang) {
        return translations.getOrDefault(MultilingualEnum.findLanguageEnum(lang), "Language not supported");
    }

    public static String getTranslationByValue(int value) {
        String lang= ThreadLanguageHolder.getCurrentLang();
        return getTranslationByValueAndLang(value, lang);
    }
    public static String getTranslationByValueAndLang(int value, String lang) {
        Map<MultilingualEnum, String> translations = TRANSLATIONS_CACHE.get(value);
        return translations != null ? translations.getOrDefault(MultilingualEnum.findLanguageEnum(lang), "Language not supported") :
                "Unknown value: " + value;
    }
}