/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.enums.language;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

import org.springframework.data.repository.init.ResourceReader;

import com.chargebolt.commons.enums.MultilingualEnum;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: StatusEnum.java, v 1.0 2024-01-23 6:17 PM Exp $
 */
public enum StatusEnum {
    NEW(1, "新建"),
    RUNNING(2, "进行中");
    private final int value;
    private final Map<MultilingualEnum, String> translations;

    StatusEnum(int value, String... language) {
        this.value = value;
        this.translations = new HashMap<>();
//            this.translations.put(MultilingualEnum.ENGLISH, enUs);
        // 可以根据需要添加更多语种
    }

    private void init(){
        ClassLoader classLoader = ResourceReader.class.getClassLoader();
        String langFile = this.getClass().getSimpleName() + ".json";
        try (InputStream inputStream = classLoader.getResourceAsStream("enums/"+langFile);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split("=");
                if (parts.length == 2) {
                    String lang = parts[0].trim().toLowerCase();
                    String translation = parts[1].trim();
                    translations.put(null, translation);
                }
            }
        } catch (IOException e) {
            e.printStackTrace(); // 处理文件读取异常
        }
    }
    public int getValue() {
        return value;
    }

    public String getTranslation(String lang) {
        return translations.getOrDefault(MultilingualEnum.findLanguageEnum(lang), "");
    }

    public static String getTranslationByValue(int value, String lang) {
        for (StatusEnum status : StatusEnum.values()) {
            if (status.getValue() == value) {
                return status.getTranslation(lang);
            }
        }
        return "Unknown value: " + value;
    }
}