/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.exception;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentExceptionEnum.java, v 1.0 2024-03-20 3:30 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum AgentExceptionEnum {
    AGENT_NOT_EXIST("208001", "代理商不存在"),
    AGENT_ID_NULL("208002", "代理商ID不能为空"),
    AGENT_NAME_NULL("208003", "代理商名称不能为空"),
    AGENT_CONTRACT_NAME_NULL("208004", "代理商联系人名称不能为空"),
    AGENT_CONTRACT_MOBILE_NULL("208005", "代理商联系人手机号码不能为空"),
    AGENT_AREA_NULL("208006", "请选择地区"),
    AGENT_ADDRESS_NULL("208007", "详细地址不能为空"),
    AGENT_RATE_OUT_OF_RANG("208009", "分成比例为0-100"),
    AGENT_SELLER_NULL("208010", "负责人不能为空"),

    REGIONAL_CODE_NULL("208011", "国家地区不能为空"),
    CURRENCY_CODE_NULL("208012", "币种代码不能为空"),
    ZONE_UTC_NULL("208013", "所属时区不能为空"),
    SYS_VERSION_NULL("208014", "系统版本不能为空"),
    AGENT_SERVICE_FEE_RATE_OUT_OF_RANG("208015", "服务费比例为0-100"),
    AGENT_IS_NULL("208016", "代理商不存在"),
    TENANT_DEPOSIT_AMOUNT_IS_NULL("208017", "租户押金未配置"),
    TENANT_DEFAULT_SHOP_IS_NULL("208018", "租户默认门店未配置"),
    TENANT_AGENT_RELATED_IS_NULL("208019", "租户绑定代理关系不存在"),

    AGENT_BILL_CANNOT_ADD_ONESELF("208020", "不能给自己新增账单"),
    ;
    @Getter
    private final String code;
    @Getter
    private final String desc;

}