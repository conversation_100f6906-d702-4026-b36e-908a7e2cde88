/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.exception;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: CommonExceptionEnum.java, v 1.0 2024-03-20 5:24 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum CommonExceptionEnum {
    OVERSTEP_AUTHORITY("9000", "数据越权"),
    /**
     * 通用的提示，一般正常选择不会出现该异常
     */
    TYPE_NOT_SUPPORTED("9001", "类型不支持"),
    ID_NOT_NULL("9002", "ID不能为空"),
    DATA_SCOPE_NOT_NULL("9003", "数据权限范围不能为空!"),
    ;
    @Getter
    private final String code;
    @Getter
    private final String desc;

}