/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.commons.exception;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DataPlanErrorCodeEnum.java, v 1.0 2023-10-27 2:37 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DataPlanErrorCodeEnum {
    FUND_NOT_EXIST("206001", "the fund account does not exist", "门店"),

    ;
    /**
     * 枚举编码
     */
    @Getter
    private final String code;

    /**
     * 英文
     */
    @Getter
    private final String english;


    /**
     * 中文
     */
    @Getter
    private final String chinese;
}