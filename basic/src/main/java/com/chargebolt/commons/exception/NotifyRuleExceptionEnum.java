package com.chargebolt.commons.exception;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知规则异常枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum NotifyRuleExceptionEnum {
    /**
     * 通知规则不存在
     */
    RULE_NOT_EXIST("209001", "notify.rule.not.exist"),

    /**
     * 无权操作该通知规则
     */
    NO_PERMISSION("209002", "notify.rule.no.permission"),

    /**
     * 规则ID不能为空
     */
    RULE_ID_EMPTY("209003", "notify.rule.id.empty"),

    /**
     * 规则名称不能为空
     */
    RULE_NAME_EMPTY("209004", "notify.rule.name.empty"),

    /**
     * 规则类型不能为空
     */
    RULE_TYPE_EMPTY("209005", "notify.rule.type.empty"),

    /**
     * 规则类型不合法
     */
    RULE_TYPE_INVALID("209010", "notify.rule.type.invalid"),

    /**
     * 规则条件不能为空
     */
    RULE_CONDITION_EMPTY("209006", "notify.rule.condition.empty"),

    /**
     * 规则调度周期不合法
     */
    RULE_SCHEDULE_INVALID("209007", "notify.rule.schedule.invalid"),

    /**
     * 规则目标策略不合法
     */
    RULE_TARGET_STRATEGY_INVALID("209008", "notify.rule.target.strategy.invalid"),

    /**
     * 模板代码不能为空
     */
    TEMPLATE_CODE_EMPTY("209009", "notify.rule.template.code.empty"),

    /**
     * 规则条件超出限额
     */
    RULE_CONDITION_EXCEED_LIMIT("209010", "notify.rule.condition.exceed.limit"),

    /**
     * 规则已存在
     */
    RULE_EXIST("209011", "notify.rule.exist"),;

    @Getter
    private final String code;
    @Getter
    private final String desc;
}