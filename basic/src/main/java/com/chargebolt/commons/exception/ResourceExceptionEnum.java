package com.chargebolt.commons.exception;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源管理异常类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/5 11:05
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ResourceExceptionEnum {
    RESOURCE_CODE_EXIST("207001", "资源code已存在!"),
    RESOURCE_ID_NULL("207002", "资源ID不能为空!"),
    RESOURCE_NAME_NULL("207003", "资源名称不能为空!"),
    RESOURCE_TYPE_NULL("207004", "资源类型不能为空!"),
    RESOURCE_PARENT_CODE_NULL("207005", "上级资源code不能为空!"),
    RESOURCE_SORT_NULL("207008", "排序不能为空!"),
    RESOURCE_CODE_NULL("207009", "资源code不能为空!"),
    RESOURCE_CHILD_EXIST("207010", "当前资源有关联的子资源，不允许删除!"),
    RESOURCE_VERSION_NULL("207011", "资源版本不能为空!"),
    AGENT_ID_NULL("207012", "代理商ID不能为空!"),
    ROLE_NAME_NULL("207013", "角色名称不能为空!"),
    ROLE_ID_NULL("207014", "角色ID不能为空!"),
    RESOURCE_NOT_EXIST("207015", "资源不存在!"),
    RESOURCE_CODE_MODIFY("207016", "资源code不能修改!"),
    ROLE_NOT_EXIST("207017", "角色不存在!"),
    AGENT_VERSION_NOT_SPECIFIED("207018", "代理商未指定软件版本!"),

    ;
    private final String code;

    private final String desc;
}
