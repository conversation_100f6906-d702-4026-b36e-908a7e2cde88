/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ExtendDateUtil.java, v 1.0 2024-01-12 9:52 AM Exp $
 */
public class ExtendDateUtil {
    /**
     * 判断给定的时间戳是否表示的是今天
     * 使用系统默认时区
     *
     * @param timestamp 时间戳，单位：毫秒
     * @return
     */
    public static boolean isToday(long timestamp) {
        LocalDate today = LocalDate.now();
        LocalDate inputDate = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();

        return today.isEqual(inputDate);
    }
}