/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.commons.utils;

import lombok.experimental.UtilityClass;

/**
 * 信息脱敏工具类
 *
 * <AUTHOR>
 * @version: MaskUtil.java, v 1.0 2024-09-30 下午4:35 Exp $
 */
@UtilityClass
public class MaskUtil {
    /**
     * 手机号码脱敏
     *
     * @param number
     * @return
     */
    public static String maskPhoneNumber(String number) {
        if (number == null || (number.length() < 5)) {
            return number;
        }
        String regex = "^(\\d{1,3})\\d+(\\d{3})$";
        String replacement = "$1****$2";
        return number.replaceAll(regex, replacement);
    }

    /**
     * 身份证、卡号脱敏
     * 
     * @param number
     * @return
     */
    public static String maskCardNumber(String number) {
        if (number == null || (number.length() < 5)) {
            return number;
        }
        String regex = "^(\\d{1,3})\\d+(\\d{4})$";
        String replacement = "$1**** ****$2";
        return number.replaceAll(regex, replacement);
    }
}