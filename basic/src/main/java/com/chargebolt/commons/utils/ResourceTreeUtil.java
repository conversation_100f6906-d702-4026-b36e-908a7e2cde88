package com.chargebolt.commons.utils;

import com.chargebolt.dao.aeacus.model.SystemResources;
import com.chargebolt.response.aeacus.SystemResourcesResponse;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * &#x8D44;&#x6E90;&#x6811;&#x6784;&#x5EFA;&#x7C7B;
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/4 11:00
 */
public class ResourceTreeUtil {

    public static final String ROOT = "ROOT";
    public static final String APP_CODE = "app-root";
    public static final String PC_CODE = "pc-root";


    /**
     * 构建资源树，从根节点开始
     *
     * @param responseList 资源列表
     * @return 资源树
     */
    public static List<SystemResourcesResponse> build(List<SystemResourcesResponse> responseList) {

        // 将所有资源按 code 存储到 map 中
        Map<String, SystemResourcesResponse> resourceMap = new HashMap<>();
        responseList.forEach(resource -> resourceMap.put(resource.getCode(), resource));
        // 构建树形结构根节点:parentCode 为 ROOT 的资源作为根节点
        List<SystemResourcesResponse> rootList = responseList.stream()
                .filter(resource -> ROOT.equals(resource.getParentCode()))
                .sorted(SystemResourcesResponse.SORT_ORDER)
                .collect(Collectors.toList());
        for (SystemResourcesResponse resource : responseList) {
            if (!ROOT.equals(resource.getParentCode())) {
                // 获取除根节点以外的资源的父级节点
                SystemResourcesResponse resourcesResponse = resourceMap.get(resource.getParentCode());
                // 父级节点不为空，将当前节点添加到父级节点的子节点列表中
                if (resourcesResponse != null) {
                    resource.setParentName(resourcesResponse.getName());
                    resourcesResponse.getChildren().add(resource);
                    // 资源排序
                    resourcesResponse.getChildren().sort(SystemResourcesResponse.SORT_ORDER);
                }
            }
        }
        rootList.sort(SystemResourcesResponse.SORT_ORDER);
        return rootList;

    }

    /**
     * 指定资源code开始构建资源树
     *
     * @param resources 资源列表
     * @param code      资源code
     * @return 资源树
     */
    public static SystemResourcesResponse build(List<SystemResources> resources, String code) {

        // 按父级节点parentCode分组
        Map<String, List<SystemResources>> childMap = resources.stream()
                .filter(r -> r.getParentCode() != null).collect(Collectors.groupingBy(SystemResources::getParentCode));
        //根据code获取资源
        Optional<SystemResources> first = resources.stream().filter(r -> r.getCode().equals(code)).findFirst();
        if (!first.isPresent()) {
            throw new IllegalArgumentException("未找到指定的资源");
        }
        // 获取所有子节点
        List<SystemResourcesResponse> childList = getAllChildren(first.get(), childMap).stream()
                .map(SystemResourcesResponse::new).collect(Collectors.toList());
        // 构建资源树
        Map<String, SystemResourcesResponse> responseMap = childList.stream()
                .collect(Collectors.toMap(SystemResourcesResponse::getCode, Function.identity()));
        for (SystemResourcesResponse resource : childList) {
            // 排除根节点
            if (!code.equals(resource.getCode())) {
                // 获取除指定节点以外的资源的父级节点
                SystemResourcesResponse resourcesResponse = responseMap.get(resource.getParentCode());
                // 父级节点不为空，将当前节点添加到父级节点的子节点列表中
                if (resourcesResponse != null) {
                    resource.setParentName(resourcesResponse.getName());
                    resourcesResponse.getChildren().add(resource);
                    // 资源排序
                    resourcesResponse.getChildren().sort(SystemResourcesResponse.SORT_ORDER);
                }
            }
        }
        // 找到根节点的子节点
        childList=childList.stream()
             .filter(r -> code.equals(r.getParentCode())).collect(Collectors.toList());
        // 构建资源树
        SystemResourcesResponse systemResourcesResponse = new SystemResourcesResponse(first.get());
        systemResourcesResponse.setChildren(childList);
        return systemResourcesResponse;
    }

    /**
     * 根据资源ID构建资源树
     *
     * @param allResourceList 全量资源列表
     * @param resourceIds     资源ID列表
     * @return 资源树
     */
    public static List<SystemResourcesResponse> build(List<SystemResources> allResourceList, List<Long> resourceIds) {

        Set<SystemResources> rs = buildResourceList(allResourceList, resourceIds);
        List<SystemResourcesResponse> responseList = rs.stream()
                .map(SystemResourcesResponse::new).collect(Collectors.toList());
        setParentId(allResourceList,responseList);
        return build(responseList);
    }


    /**
     * 根据代理商资源构建资源树
     *
     * @param allResourceList  全量资源列表
     * @param agentResourceIds 资源ID列表
     * @return 资源列表
     */
    public static List<SystemResourcesResponse> build(List<SystemResources> allResourceList, List<Long> agentResourceIds, List<Long> roleResourceIds) {

        Set<SystemResources> rs = buildResourceList(allResourceList, agentResourceIds);
        List<SystemResourcesResponse> responseList = rs.stream().map(resource -> {
            SystemResourcesResponse response = new SystemResourcesResponse(resource);
            // 设置角色的资源选中状态
            if (roleResourceIds.contains(resource.getId())) {
                response.setChecked(true);
            }
            return response;
        }).collect(Collectors.toList());
        setParentId(allResourceList,responseList);
        return build(responseList);
    }

    private static void setParentId(List<SystemResources> allResourceList,List<SystemResourcesResponse> responseList){
        // 创建code和id的映射
        Map<String, Long> codeIdMap = allResourceList
                .stream().collect(Collectors.toMap(SystemResources::getCode, SystemResources::getId));
        responseList.forEach(response ->
           response.setParentId(codeIdMap.get(response.getParentCode()))
        );
    }

    /**
     * 构建资源列表
     *
     * @param allResourceList 全量资源列表
     * @param resourceIds     资源ID列表
     * @return
     */
    private static Set<SystemResources> buildResourceList(List<SystemResources> allResourceList, List<Long> resourceIds) {

        Map<Long, SystemResources> resourceIdMap = allResourceList.stream()
                .collect(Collectors.toMap(SystemResources::getId, Function.identity()));
        // 将所有资源按 code 存储到 map 中
        Map<String, SystemResources> resourceCodeMap = allResourceList.stream()
                .collect(Collectors.toMap(SystemResources::getCode, Function.identity()));
        Set<SystemResources> rs = new HashSet<>();
        // 获取资源的上级节点
        for (Long resourceId : resourceIds) {
            SystemResources systemResources = resourceIdMap.get(resourceId);
            if (systemResources != null) {
                rs.add(systemResources);
                rs.addAll(getAllParents(systemResources, resourceCodeMap));
            }
        }
        return rs;
    }


    /**
     * 获取所有父级节点
     */
    private static List<SystemResources> getAllParents(SystemResources resource, Map<String, SystemResources> resourceCodeMap) {

        List<SystemResources> parents = new ArrayList<>();
        while (resource != null
                && resource.getParentCode() != null) {
            // 获取父级节点
            resource = resourceCodeMap.get(resource.getParentCode());
            if (resource != null) {
                parents.add(resource);
            }
        }
        return parents;
    }

    /**
     * 获取所有子节点
     */
    private static List<SystemResources> getAllChildren(SystemResources resource, Map<String, List<SystemResources>> childMap) {
        List<SystemResources> children = new ArrayList<>();
        // 获取直接子节点
        List<SystemResources> directChildren = childMap.get(resource.getCode());
        if (CollectionUtils.isNotEmpty(directChildren)) {
            // 添加到结果集
            children.addAll(directChildren);
            // 递归获取子节点的子节点
            for (SystemResources child : directChildren) {
                children.addAll(getAllChildren(child, childMap));
            }
        }
        return children;
    }

}
