/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.annotation;
import com.chargebolt.commons.enums.DataAuthorityConditionType;
import com.chargebolt.commons.enums.DataScopeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限范围查询，限定DataAuthoritySql的拼接处理逻辑
 *
 * 需要和DataAuthoritySql一起使用
 *
 *
 * <AUTHOR>
 * @version: DataAuthorityScope.java, v 1.0 2024-09-04 下午3:08 Exp $
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface DataAuthorityScope {
    DataScopeEnum scope() default DataScopeEnum.ONLY_ONESELF;
}