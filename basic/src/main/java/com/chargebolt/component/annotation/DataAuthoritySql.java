/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.annotation;
import com.chargebolt.commons.enums.DataAuthorityConditionType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
/**
 * 注解下一定需要登录用户信息，根据用户拥有的数据权限范围查询
 * 没有登录，查询不到数据，如有不限制获取数据的需求，请新增方法支持
 *
 * 该注解只支持在Mapper DAO 接口上使用
 *
 * <AUTHOR>
 * @version: DataAuthoritySql.java, v 1.0 2024-09-04 下午3:08 Exp $
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface DataAuthoritySql {
    String[] conditionField() default {"agent_id", "user_id"};
    DataAuthorityConditionType[] conditionValue() default {};
}