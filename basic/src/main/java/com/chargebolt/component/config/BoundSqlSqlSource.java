/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.config;

import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.SqlSource;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: BoundSqlSqlSource.java, v 1.0 2024-09-04 下午5:34 Exp $
 */
public class BoundSqlSqlSource implements SqlSource {

    private final BoundSql boundSql;

    public BoundSqlSqlSource(BoundSql boundSql) {
        this.boundSql = boundSql;
    }

    @Override
    public BoundSql getBoundSql(Object parameterObject) {
        return boundSql;
    }
}