/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.component.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: CorsDomain.java, v 1.0 2023-11-15 9:39 AM Exp $
 */
@Component
@ConfigurationProperties(prefix = "cors")
@Data
public class CorsDomain implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202311319093959L;
    /**
     * 域名
     */
    private List<String> domainList;
}