/*
 * Dian.so Inc.
 * Copyright (c) 2016-2021 All Rights Reserved.
 */
package com.chargebolt.component.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;
import java.util.List;

/**
 * 跨域配置
 *
 * <AUTHOR>
 * @version: DomainCorsConfig.java, v 1.0 2021-08-02 6:36 下午 Exp $
 */
@Slf4j
@Configuration
public class DomainCorsConfig {
    @Autowired
    private CorsDomain corsDomain;
    @Bean
    public FilterRegistrationBean corsFilter() {
        log.info("跨域配置初始化....");
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置你要允许的网站域名，spring 5不支持全*通配，需要指定域名，比如*.dian.so
        config.setAllowedOriginPatterns(corsDomain.getDomainList());

        // 如果要限制 HEADER 或 METHOD 请自行更改
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        config.addExposedHeader("*");
        source.registerCorsConfiguration("/**", config);
        FilterRegistrationBean bean = new FilterRegistrationBean(new CorsFilter(source));
        // 这个顺序很重要哦，为避免麻烦请设置在最前
        bean.setOrder(0);
        return bean;
    }
}