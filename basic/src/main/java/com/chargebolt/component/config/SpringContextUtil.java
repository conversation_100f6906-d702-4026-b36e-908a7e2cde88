package com.chargebolt.component.config;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


/**
 * SpringContextUtil

 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/19 10:57
 */
@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext appCtx;

    @Override
    public void setApplicationContext(ApplicationContext appCtx) throws BeansException {
        this.appCtx = appCtx;
    }

    public static ApplicationContext getAppCtx() {
        return appCtx;
    }

    /**
     * 通过class获取Bean.
     *
     * @param clazz Class
     * @param <T>   T
     * @return T
     */
    public static <T> T getBean(Class<T> clazz) {
        if (getAppCtx() == null) {
            return null;
        }
        return getAppCtx().getBean(clazz);
    }
}
