package com.chargebolt.component.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

@Slf4j
@Configuration
@ConditionalOnProperty(name = "swagger.enable", havingValue = "true")
public class SwaggerConfiguration  {

    /***
     * 构建Swagger3.0文档说明
     * @return 返回 OpenAPI
     */
    @Bean
    public OpenAPI customOpenAPI() {
        Contact contact = new Contact()
                .name("麦可")
                .email("<EMAIL>")
                .extensions(new HashMap<String, Object>());

        Info info = new Info()
                .title("ezreal 接口文档")
                .version("1.0.0")
                .contact(contact);
        // 返回信息
        return new OpenAPI()
                .openapi("3.0.0")
                .info(info);
    }



}
