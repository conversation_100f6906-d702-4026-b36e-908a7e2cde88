/*
 * Dian.com Inc.
 * Copyright (c) 2004-2018 All Rights Reserved.
 */
package com.chargebolt.component.interceptor;

import com.chargebolt.commons.constant.AppConstant;
import com.chargebolt.component.annotation.AppAuth;
import com.chargebolt.context.MerchantLoginContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.talos.pojo.entity.MerchantDO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * PC端用户登录身份验证拦截器
 *
 * <AUTHOR>
 * @version $Id: AppAuthInterceptor.java, v 0.1 2023年10月31日 11:34 AM Exp $
 */
@Slf4j
public class AppAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 用户身份鉴权
     * <p>
     * 鉴权对象：对于有@AppAuth注解的方法
     * 鉴权步骤:
     * 1 从header中获取token字符串
     * 2 从缓存中获取token对象
     * 3 如果没有，鉴权失败，返回登录
     *
     * @param request  request
     * @param response response
     * @param handler  handler
     * @return true/false
     * @throws Exception exception
     */
    @Override
    public boolean preHandle(final HttpServletRequest request, final HttpServletResponse response, final Object handler) throws Exception {
        // 如果不是 HandlerMethod，说明可能是 404 或其他资源，让 Spring 继续处理
        if (!(handler instanceof HandlerMethod)) {
            return Boolean.TRUE;
        }
        HandlerMethod method = (HandlerMethod) handler;
        AppAuth auth = method.getMethod().getAnnotation(AppAuth.class);
        if (auth == null) {
            return Boolean.TRUE;
        }
        String loginToken = request.getHeader(AppConstant.APP_HEADERS_TOKEN_KEY);
        if (StringUtils.isBlank(loginToken)) {
            responseErrorJson(response, "2002", "You need to login");
            return Boolean.FALSE;
        }

        RBucket<MerchantLoginContext> bucket= redissonClient.getBucket(loginToken);
        if(Objects.isNull(bucket)|| Objects.isNull(bucket.get())){
            responseErrorJson(response, "2002", "You need to login");
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 将错误信息封装成JSON对象返回给前端
     *
     * @param response  servlet response对象
     * @param errorCode 错误码
     * @param errorMsg  错误信息
     */
    private void responseErrorJson(HttpServletResponse response, String errorCode, String errorMsg) throws IOException {
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");

        PrintWriter out = response.getWriter();
        Map<String, Object> resMap = new HashMap<>(5);
        resMap.put("success", false);
        resMap.put("code", errorCode);
        resMap.put("msg", errorMsg);
        out.print(JsonUtil.beanToJson(resMap));
        out.flush();
        out.close();
    }

    @Override
    public void postHandle(final HttpServletRequest request, final HttpServletResponse response, final Object handler,
                           final ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(final HttpServletRequest request, final HttpServletResponse response, final Object handler,
                                final Exception ex) {

    }
}