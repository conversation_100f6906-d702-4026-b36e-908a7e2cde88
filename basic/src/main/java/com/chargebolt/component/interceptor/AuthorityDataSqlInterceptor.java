package com.chargebolt.component.interceptor;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.enums.DataScopeEnum;
import com.chargebolt.commons.exception.CommonExceptionEnum;
import com.chargebolt.component.annotation.DataAuthoritySql;
import com.chargebolt.component.config.BoundSqlSqlSource;
import com.chargebolt.component.config.SpringContextUtil;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.service.authority.LoginUserDataAuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
import org.apache.ibatis.reflection.wrapper.DefaultObjectWrapperFactory;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import so.dian.mofa3.lang.util.JsonUtil;

import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * 用户权限拦截器
 * 查询数据，通过登录用户权限范围，拼接查询条件
 *
 * 只对 SqlCommandType.SELECT 类型的查询语句才会处理
 */
@Slf4j(topic = "SQL-FILE_DIGEST")
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class AuthorityDataSqlInterceptor implements Interceptor {

    private Properties properties = new Properties();

    private static final String DATA_FILTER = "@{data_filter}";


    private LoginUserDataAuthorityService loginUserDataAuthorityService;

    /**
     * 初始化 LoginUserDataAuthorityService
     */
    private void initService() {
        if (loginUserDataAuthorityService == null) {
            synchronized (this) {
                loginUserDataAuthorityService = SpringContextUtil.getBean(LoginUserDataAuthorityService.class);
            }
        }
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameterObject = invocation.getArgs()[1];

        // 只处理 SELECT 语句
        if (mappedStatement.getSqlCommandType() != SqlCommandType.SELECT) {
            return invocation.proceed();
        }
        BoundSql boundSql = mappedStatement.getBoundSql(parameterObject);
        String originalSql = boundSql.getSql();

        // 获取当前执行的Mapper方法
        String id = mappedStatement.getId();
        int index = id.lastIndexOf('.');
        String mapperInterfaceName = index > 0 ? id.substring(0, index) : id;
        Class<?> mapperInterface = Class.forName(mapperInterfaceName);

        // 从方法上获取 DataAuthoritySql 注解
        Method method = null;
        try {
            method = mapperInterface.getMethod(id.substring(index + 1),
                    parameterObject == null ? new Class[0] : new Class[]{parameterObject.getClass()});
        } catch (NoSuchMethodException e) {
            for (Method m : mapperInterface.getMethods()) {
                if (m.getName().equals(id.substring(index + 1))) {
                    method = m;
                    break;
                }
            }
            if (method == null) {
                throw new NoSuchMethodException("Method not found: " + id);
            }
        }
        DataAuthoritySql dataAuthoritySql = method.getAnnotation(DataAuthoritySql.class);
        DataScopeEnum dataScopeEnum= DataAuthorityScopeHolder.getDataAuthorityScope();
        if (Objects.nonNull(dataAuthoritySql)){

            initService();
            String[] fields = dataAuthoritySql.conditionField();
            String agentField = fields[0];
            String userField = fields[1];
            UserDataAuthorityContext userDataAuthorityContext = loginUserDataAuthorityService.getLoginUserDataAuthority();
            // 以下几种场景不做数据鉴权：1、获取用户登录权限为空（消息或者定时任务、Controller没有@Login注解），2、接口没有指定数据范围 3、平台用户
            if (Objects.isNull(userDataAuthorityContext)
                    || AuthorityLevelEnum.ALL.getCode().equals(userDataAuthorityContext.getAuthorityLevel())
                    || Objects.isNull(dataScopeEnum)) {

                originalSql = originalSql.replace(DATA_FILTER, " ");
            } else if (AuthorityLevelEnum.AGENT.getCode().equals(userDataAuthorityContext.getAuthorityLevel())) {
                // 如果是代理商权限，则拼接代理商ID
                String replaceStr = replaceStr(dataScopeEnum, userDataAuthorityContext);
                originalSql = originalSql.replace(DATA_FILTER, " and " + agentField + " in (" + replaceStr + ") ");
            } else if (AuthorityLevelEnum.USER.getCode().equals(userDataAuthorityContext.getAuthorityLevel())) {
                // 如果是用户权限，则拼接用户ID
                String replaceStr = replaceStr(dataScopeEnum, userDataAuthorityContext);
                originalSql = originalSql.replace(DATA_FILTER, " and " + userField + " in (" + replaceStr + ") ");
            }
            // 重置sql调用对象
            resetSql2Invocation(invocation, originalSql);
            log.info("Execute SQL: {} | params：{}", originalSql.replaceAll("[\\r\\n]+", " "), JsonUtil.beanToJson(parameterObject));
            return invocation.proceed();

        }
        originalSql=originalSql.replace(DATA_FILTER, "");
        resetSql2Invocation(invocation, originalSql);
        log.info("Execute SQL: {} | params：{}", originalSql.replaceAll("[\\r\\n]+", " "), JsonUtil.beanToJson(parameterObject));
        return invocation.proceed();
    }


    public String  replaceStr(DataScopeEnum dataScopeEnum, UserDataAuthorityContext userDataAuthorityContext){

        List<Long> rs = new ArrayList<>();

        if (Objects.isNull(dataScopeEnum)){
            throw new I18nMessageException(CommonExceptionEnum.DATA_SCOPE_NOT_NULL.getCode(), CommonExceptionEnum.DATA_SCOPE_NOT_NULL.getDesc());
        }
        if (AuthorityLevelEnum.AGENT.getCode().equals(userDataAuthorityContext.getAuthorityLevel())){
            if (DataScopeEnum.ONLY_ONESELF.equals(dataScopeEnum)){
                rs.add(userDataAuthorityContext.getAgentId());
            }

            if (DataScopeEnum.ALL.equals(dataScopeEnum)){
                rs.addAll(userDataAuthorityContext.getQuery().getAgentIds());
            }
        }

        if (AuthorityLevelEnum.USER.getCode().equals(userDataAuthorityContext.getAuthorityLevel())){
            if (DataScopeEnum.ONLY_ONESELF.equals(dataScopeEnum)){
                rs.add(userDataAuthorityContext.getUserId());
            }

            if (DataScopeEnum.ALL.equals(dataScopeEnum)){
                rs.addAll(userDataAuthorityContext.getQuery().getUserIds());
            }
        }
        return rs.stream().map(Objects::toString).collect(Collectors.joining(","));
    }



    /**
     * 包装sql后，重置到invocation中
     *
     * @param invocation invocation
     * @param sql sql
     * @throws SQLException
     */
    private void resetSql2Invocation(Invocation invocation, String sql) throws SQLException {
        final Object[] args = invocation.getArgs();
        MappedStatement statement = (MappedStatement) args[0];
        Object parameterObject = args[1];
        BoundSql boundSql = statement.getBoundSql(parameterObject);
        MappedStatement newStatement = newMappedStatement(statement, new BoundSqlSqlSource(boundSql));
        MetaObject msObject =  MetaObject.forObject(newStatement, new DefaultObjectFactory(), new DefaultObjectWrapperFactory(),new DefaultReflectorFactory());
        msObject.setValue("sqlSource.boundSql.sql", sql);
        args[0] = newStatement;
    }

    /**
     * 复制一个新的MappedStatement对象
     *
     */
    private MappedStatement newMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder =
                new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        if (ms.getKeyProperties() != null && ms.getKeyProperties().length != 0) {
            StringBuilder keyProperties = new StringBuilder();
            for (String keyProperty : ms.getKeyProperties()) {
                keyProperties.append(keyProperty).append(",");
            }
            keyProperties.delete(keyProperties.length() - 1, keyProperties.length());
            builder.keyProperty(keyProperties.toString());
        }
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());
        return builder.build();
    }
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }
}