/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.interceptor;

import com.chargebolt.commons.enums.DataScopeEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DataAuthorityScopeHolder.java, v 1.0 2024-09-23 上午9:52 Exp $
 */
@Slf4j
public class DataAuthorityScopeHolder {
    private static final ThreadLocal<DataScopeEnum> dataAuthorityThreadLocal = new ThreadLocal<>();

    public static DataScopeEnum getDataAuthorityScope() {
        return dataAuthorityThreadLocal.get();
    }

    public static void setDataAuthorityScope(DataScopeEnum dataScopeEnum) {
        dataAuthorityThreadLocal.set(dataScopeEnum);
    }

    public static void clearDataAuthorityScope() {
        dataAuthorityThreadLocal.remove();

    }
}