/*
 * Dian.com Inc.
 * Copyright (c) 2004-2018 All Rights Reserved.
 */
package com.chargebolt.component.interceptor;

import com.chargebolt.commons.constant.AppConstant;
import com.chargebolt.commons.enums.DataScopeEnum;
import com.chargebolt.component.annotation.AppAuth;
import com.chargebolt.component.annotation.DataAuthorityScope;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.talos.pojo.entity.MerchantDO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * PC端用户登录身份验证拦截器
 *
 * <AUTHOR>
 * @version $Id: AppAuthInterceptor.java, v 0.1 2023年10月31日 11:34 AM Exp $
 */
@Slf4j
public class DataAuthorityScopeInterceptor implements HandlerInterceptor {

    /**
     * 用户身份鉴权
     * <p>
     * 鉴权对象：对于有@AppAuth注解的方法
     * 鉴权步骤:
     * 1 从header中获取token字符串
     * 2 从缓存中获取token对象
     * 3 如果没有，鉴权失败，返回登录
     *
     * @param request  request
     * @param response response
     * @param handler  handler
     * @return true/false
     * @throws Exception exception
     */
    @Override
    public boolean preHandle(final HttpServletRequest request, final HttpServletResponse response, final Object handler) throws Exception {
        // 如果不是 HandlerMethod，说明可能是 404 或其他资源，让 Spring 继续处理
        if (!(handler instanceof HandlerMethod)) {
            return Boolean.TRUE;
        }
        HandlerMethod method = (HandlerMethod) handler;
        DataAuthorityScope auth = method.getMethod().getAnnotation(DataAuthorityScope.class);
        if (auth == null) {
            return Boolean.TRUE;
        }
        DataScopeEnum dataScopeEnum = auth.scope();
        DataAuthorityScopeHolder.setDataAuthorityScope(dataScopeEnum);
        return Boolean.TRUE;
    }


    @Override
    public void postHandle(final HttpServletRequest request, final HttpServletResponse response, final Object handler,
                           final ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(final HttpServletRequest request, final HttpServletResponse response, final Object handler,
                                final Exception ex) {
        DataAuthorityScopeHolder.clearDataAuthorityScope();
    }
}