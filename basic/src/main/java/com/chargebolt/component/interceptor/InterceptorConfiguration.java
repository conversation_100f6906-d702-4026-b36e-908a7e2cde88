/*
 * Dian.com Inc.
 * Copyright (c) 2004-2018 All Rights Reserved.
 */
package com.chargebolt.component.interceptor;

import so.dian.eros.interceptor.LanguageInterceptor;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * 注册拦截器
 *
 * <AUTHOR>
 */
@Configuration
public class InterceptorConfiguration extends WebMvcConfigurerAdapter {

    @Bean
    public AppAuthInterceptor appAuthInterceptor(){
        return new AppAuthInterceptor();
    }

    @Bean
    public LanguageInterceptor i18nMessageInterceptor(){
        return new LanguageInterceptor();
    }
    @Bean
    public DataAuthorityScopeInterceptor dataAuthorityScopeInterceptor(){
        return new DataAuthorityScopeInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //注册自定义拦截器，添加拦截路径和排除拦截路径
        registry.addInterceptor(appAuthInterceptor()).addPathPatterns("/app/**").excludePathPatterns("/swagger-ui/**","/api-documents");
        registry.addInterceptor(i18nMessageInterceptor()).addPathPatterns("/**").excludePathPatterns("/swagger-ui/**","/api-documents");
        registry.addInterceptor(dataAuthorityScopeInterceptor()).addPathPatterns("/**").excludePathPatterns("/swagger-ui/**","/api-documents");
    }


}
