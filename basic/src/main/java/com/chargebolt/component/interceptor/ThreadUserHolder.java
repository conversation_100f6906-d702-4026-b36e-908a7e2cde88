/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.component.interceptor;

import com.chargebolt.context.OssUserContext;
import lombok.extern.slf4j.Slf4j;

/**
 * 登录用户全局变量透传
 *
 * <AUTHOR>
 * @version: ThreadUserHolder.java, v 1.0 2023-10-26 11:13 AM Exp $
 */
@Slf4j
public class ThreadUserHolder {

    private static final ThreadLocal<OssUserContext> userThreadLocal = new ThreadLocal<>();

    public static OssUserContext getCurrentUser() {
        return userThreadLocal.get();
    }

    public static void setCurrentUser(OssUserContext userContext) {
        userThreadLocal.set(userContext);
    }

    public static void clearCurrentUser() {
        userThreadLocal.remove();

    }
}