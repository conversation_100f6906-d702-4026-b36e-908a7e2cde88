/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.rocketmq;

import com.chargebolt.context.AgentTenantContext;
import io.github.rhwayfun.springboot.rocketmq.starter.common.DefaultRocketMqProducer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentTenantProducer.java, v 1.0 2024-10-21 下午4:26 Exp $
 */
@Slf4j
@Component
public class AgentTenantProducer {
    @Resource
    private DefaultRocketMqProducer producer;

    @Getter
    @Value("${spring.rocketmq.self-producer.topic}")
    private String topic;
    @Getter
    @Value("${spring.rocketmq.self-producer.agent-tenant.tag}")
    private String tag;

    public void sendMsg(AgentTenantContext message) {
        log.info("二代代理商创建消息发送：{}", JsonUtil.beanToJson(message));
        Message msg = new Message(topic, tag, JsonUtil.beanToJson(message).getBytes(StandardCharsets.UTF_8));
        boolean result = producer.sendMsg(msg);
        if (Boolean.FALSE.equals(result)) {
            log.info("二代代理商创建消息发送msg: {} ", JsonUtil.beanToJson(message));
        }
    }
}