/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.rocketmq;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * FAQ删除事件消费者配置
 * 配置FAQ删除事件的RocketMQ消费者
 *
 * <AUTHOR>
 * @version: FaqDeleteConsumerConfig.java, v 1.0 2024-12-20 Exp $
 */
@Slf4j
@Configuration
public class FaqDeleteConsumerConfig {

    @Getter
    @Value("${spring.rocketmq.name-server}")
    private String nameServer;

    @Getter
    @Value("${spring.rocketmq.self-producer.topic}")
    private String topic;

    /**
     * 发送同一类消息的设置为同一个group，保证唯一,
     * 默认不需要设置，rocketmq会使用ip@pid(pid代表jvm名字)作为唯一标示
     */
    @Getter
    @Value("${spring.rocketmq.self-producer.faq-delete.consumer-group}")
    private String groupName;

    @Getter
    @Value("${spring.rocketmq.self-producer.faq-delete.tag}")
    private String tag;

    @Autowired
    private FaqDeleteListener faqDeleteListener;

    /**
     * FAQ删除MQ消费者配置
     *
     * @return DefaultMQPushConsumer
     * @throws MQClientException MQ客户端异常
     */
    @PostConstruct
    public DefaultMQPushConsumer defaultConsumer() throws MQClientException {
        log.info("RocketMQ 初始化【FAQ删除国际化清理】....");
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(groupName);
        consumer.setNamesrvAddr(nameServer);
        consumer.setConsumeThreadMin(1);
        consumer.setConsumeThreadMax(2);

        // 设置监听器
        consumer.registerMessageListener(faqDeleteListener);

        /**
         * 设置consumer第一次启动是从队列头部开始还是队列尾部开始
         * 如果不是第一次启动，那么按照上次消费的位置继续消费
         */
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.subscribe(topic, tag);
        consumer.setInstanceName(groupName + "_instance");
        consumer.start();

        log.info("FAQ删除国际化清理consumer创建成功 groupName={}, topics={}, tags={}", groupName, topic, tag);
        return consumer;
    }
}