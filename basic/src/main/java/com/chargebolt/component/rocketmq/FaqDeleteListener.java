/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.rocketmq;

import javax.annotation.Resource;
import com.chargebolt.context.FaqDeleteContext;
import com.chargebolt.theseus.service.I18nTextsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.mofa3.lang.common.constant.HttpConstants;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.mofa3.lang.util.RandomCodeUtil;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * FAQ删除国际化清理MQ监听器
 * 负责处理FAQ删除事件，批量清理对应的国际化文案
 *
 * <AUTHOR>
 * @version: FaqDeleteListener.java, v 1.0 2024-12-20 Exp $
 */
@Slf4j
@Component
public class FaqDeleteListener implements MessageListenerConcurrently {

    @Resource
    private I18nTextsService i18nTextsService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(final List<MessageExt> msgs,
            final ConsumeConcurrentlyContext context) {
        if (CollectionUtils.isEmpty(msgs)) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        try {
            // 收集所有需要删除的国际化编码
            List<String> allKeysToDelete = new ArrayList<>();
            for (MessageExt msg : msgs) {
                String body = new String(msg.getBody(), StandardCharsets.UTF_8);

                FaqDeleteContext context1 = JsonUtil.jsonToBean(body, FaqDeleteContext.class);
                if (context1 == null) {
                    log.warn("无法解析FAQ删除上下文，跳过处理: {}", body);
                    continue;
                }

                // 收集需要删除的keys
                List<String> keysToDelete = collectKeysToDelete(context1);
                allKeysToDelete.addAll(keysToDelete);
            }

            // 批量删除国际化文案
            if (!allKeysToDelete.isEmpty()) {
                Integer deletedCount = i18nTextsService.batchDeleteText(allKeysToDelete, System.currentTimeMillis());
                log.info("FAQ删除国际化清理完成，删除数量: {}", deletedCount);
            }

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("处理FAQ删除MQ消息异常，body:{}", JsonUtil.beanToJson(msgs), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }

    /**
     * 收集需要删除的国际化编码
     * 
     * @param context FAQ删除上下文
     * @return 需要删除的国际化编码列表
     */
    private List<String> collectKeysToDelete(FaqDeleteContext context) {
        List<String> keysToDelete = new ArrayList<>();

        if ("category".equals(context.getDeleteType())) {
            // 分类删除：收集分类和子FAQ的国际化编码
            keysToDelete.addAll(collectCategoryKeys(context));
        } else if ("faq".equals(context.getDeleteType())) {
            // FAQ删除：收集FAQ的国际化编码
            keysToDelete.addAll(collectFaqKeys(context));
        }

        return keysToDelete;
    }

    /**
     * 收集分类删除时需要清理的国际化编码
     * 
     * @param context FAQ删除上下文
     * @return 需要删除的国际化编码列表
     */
    private List<String> collectCategoryKeys(FaqDeleteContext context) {
        List<String> keysToDelete = new ArrayList<>();

        // 1. 添加分类的国际化编码
        String categoryKey = String.format("faq_category_%d_%d", context.getAgentId(), context.getCategoryId());
        keysToDelete.add(categoryKey);

        // 2. 如果有子FAQ，添加子FAQ的国际化编码
        if (!CollectionUtils.isEmpty(context.getFaqIds())) {
            for (Long faqId : context.getFaqIds()) {
                // FAQ问题的国际化编码
                String questionKey = String.format("faq_question_%d_%d", context.getAgentId(), faqId);
                keysToDelete.add(questionKey);

                // FAQ答案的国际化编码
                String answerKey = String.format("faq_answer_%d_%d", context.getAgentId(), faqId);
                keysToDelete.add(answerKey);
            }
        }

        return keysToDelete;
    }

    /**
     * 收集FAQ删除时需要清理的国际化编码
     * 
     * @param context FAQ删除上下文
     * @return 需要删除的国际化编码列表
     */
    private List<String> collectFaqKeys(FaqDeleteContext context) {
        List<String> keysToDelete = new ArrayList<>();

        if (!CollectionUtils.isEmpty(context.getFaqIds())) {
            for (Long faqId : context.getFaqIds()) {
                // FAQ问题的国际化编码
                String questionKey = String.format("faq_question_%d_%d", context.getAgentId(), faqId);
                keysToDelete.add(questionKey);

                // FAQ答案的国际化编码
                String answerKey = String.format("faq_answer_%d_%d", context.getAgentId(), faqId);
                keysToDelete.add(answerKey);
            }
        }

        return keysToDelete;
    }
}