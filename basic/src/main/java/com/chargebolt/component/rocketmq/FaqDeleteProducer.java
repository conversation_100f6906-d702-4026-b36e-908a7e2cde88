/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.component.rocketmq;

import com.chargebolt.context.FaqDeleteContext;
import io.github.rhwayfun.springboot.rocketmq.starter.common.DefaultRocketMqProducer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * FAQ删除事件消息生产者
 * 当FAQ或分类被删除时，发送通知消息用于清理国际化文案
 *
 * <AUTHOR>
 * @version: FaqDeleteProducer.java, v 1.0 2024-12-20 Exp $
 */
@Slf4j
@Component
public class FaqDeleteProducer {

    @Resource
    private DefaultRocketMqProducer producer;

    @Getter
    @Value("${spring.rocketmq.self-producer.topic}")
    private String topic;

    @Getter
    @Value("${spring.rocketmq.self-producer.faq-delete.tag}")
    private String tag;

    /**
     * 发送FAQ删除通知消息
     * 
     * @param message FAQ删除上下文
     */
    public void sendMsg(FaqDeleteContext message) {
        log.info("发送FAQ删除通知消息：{}", JsonUtil.beanToJson(message));

        try {
            Message msg = new Message(topic, tag, JsonUtil.beanToJson(message).getBytes(StandardCharsets.UTF_8));
            boolean result = producer.sendMsg(msg);

            if (Boolean.FALSE.equals(result)) {
                log.error("FAQ删除通知消息发送失败：{}", JsonUtil.beanToJson(message));
            } else {
                log.info("FAQ删除通知消息发送成功，类型：{}，操作时间：{}",
                        message.getDeleteType(), message.getOperateTime());
            }
        } catch (Exception e) {
            log.error("FAQ删除通知消息发送异常：{}", JsonUtil.beanToJson(message), e);
        }
    }
}