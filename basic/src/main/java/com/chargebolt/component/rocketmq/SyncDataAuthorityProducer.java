/*
 * Dian.so Inc.
 * Copyright (c) 2016-2022 All Rights Reserved.
 */
package com.chargebolt.component.rocketmq;

import com.chargebolt.context.PercentageOpenContext;
import io.github.rhwayfun.springboot.rocketmq.starter.common.DefaultRocketMqProducer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: SyncDataAuthorityProducer.java, v 1.0 2022-11-17 4:32 PM Exp $
 */
@Slf4j
@Component
public class SyncDataAuthorityProducer {
    @Resource
    private DefaultRocketMqProducer producer;

    @Getter
    @Value("${spring.rocketmq.self-producer.topic}")
    private String topic;
    @Getter
    @Value("${spring.rocketmq.self-producer.data-authority.tag}")
    private String tag;
    public void sendMsg(List<Long> message) {
        log.info("用户数据权限刷新消息发送：{}", JsonUtil.beanToJson(message));
        Message msg = new Message(topic, tag, JsonUtil.beanToJson(message).getBytes(StandardCharsets.UTF_8));
        boolean result = producer.sendMsg(msg);
        if (Boolean.FALSE.equals(result)) {
            log.info("用户数据权限刷新消息发送失败msg: {} ", JsonUtil.beanToJson(message));
        }
    }
}