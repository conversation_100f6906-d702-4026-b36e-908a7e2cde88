/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.context;

import java.io.Serializable;

/**
 * 数据看板
 *
 * <AUTHOR>
 * @version: DataPanelSyncContext.java, v 1.0 2024-11-18 11:21 Exp $
 */
public class DataPanelSyncContext implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202411323112244L;
    /**
     * 看板类型
     * 1.首页实时数据
     *
     */
    private Integer panelType;
}