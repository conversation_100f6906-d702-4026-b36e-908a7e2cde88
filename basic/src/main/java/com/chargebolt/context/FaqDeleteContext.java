package com.chargebolt.context;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * FAQ删除事件上下文
 * 用于通知多语言模块删除对应的国际化文案
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FaqDeleteContext {

    /**
     * 删除的分类ID（如果是分类删除）
     */
    private Long categoryId;

    /**
     * 删除的FAQ ID列表（如果是FAQ删除）
     */
    private List<Long> faqIds;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 删除类型：category=分类删除, faq=FAQ删除
     */
    private String deleteType;

    /**
     * 操作时间戳
     */
    private Long operateTime;

    /**
     * 创建分类删除上下文
     * 
     * @param categoryId 分类ID
     * @param agentId    代理商ID
     * @param operatorId 操作人ID
     * @return 删除上下文
     */
    public static FaqDeleteContext createCategoryDelete(Long categoryId, Long agentId, Long operatorId) {
        FaqDeleteContext context = new FaqDeleteContext();
        context.setCategoryId(categoryId);
        context.setAgentId(agentId);
        context.setOperatorId(operatorId);
        context.setDeleteType("category");
        context.setOperateTime(System.currentTimeMillis());
        return context;
    }

    /**
     * 创建FAQ删除上下文
     * 
     * @param faqIds     FAQ ID列表
     * @param agentId    代理商ID
     * @param operatorId 操作人ID
     * @return 删除上下文
     */
    public static FaqDeleteContext createFaqDelete(List<Long> faqIds, Long agentId, Long operatorId) {
        FaqDeleteContext context = new FaqDeleteContext();
        context.setFaqIds(faqIds);
        context.setAgentId(agentId);
        context.setOperatorId(operatorId);
        context.setDeleteType("faq");
        context.setOperateTime(System.currentTimeMillis());
        return context;
    }
}