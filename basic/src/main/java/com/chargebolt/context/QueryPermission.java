/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.context;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: QueryPermission.java, v 1.0 2024-09-13 上午10:38 Exp $
 */
@Data
public class QueryPermission implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409257103844L;

    /**
     * 拥有权限的代理商集合
     */
    private List<Long> agentIds;
    /**
     * 拥有权限的用户集合-本级代理
     */
    private List<Long> userIds;

    /**
     * 拥有权限的用户集合-子代理用户集合
     * 不包含本级代理
     */
    private List<Long> childUserIds;
}