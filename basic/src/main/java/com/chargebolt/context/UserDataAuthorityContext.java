package com.chargebolt.context;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: UserDataAuthorityContext.java, v 1.0 2024-07-09 上午10:27 Exp $
 */
@Data
public class UserDataAuthorityContext implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409257103917L;

    private Long userId;
    /**
     * 代理商id
     */
    private Long agentId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 权限等级
     * 1.所有
     * 2.代理商维度
     * 3.用户维度
     *
     * 1 所有，忽略agentIds、userIds条件
     * 2 3同时传递优先层级2>3
     *
     */
    private Integer authorityLevel;

    private QueryPermission query;
    private WritePermission write;

    private String aaa;


    /**
     * 拥有权限的代理商集合
     */
    @Deprecated
    private List<Long> agentIds;
    /**
     * 拥有权限的用户集合
     */
    @Deprecated
    private List<Long> userIds;


}
