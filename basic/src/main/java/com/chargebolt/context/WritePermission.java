/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.context;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 常用的写数据权限预存，可以直接获取这个数据，作为数据越权检查
 * 代理商
 * 商户
 * 门店
 *
 * <AUTHOR>
 * @version: WritePermission.java, v 1.0 2024-09-13 上午10:38 Exp $
 */
@Data
public class WritePermission implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 17120240786499634L;

    private List<Long> agentIds;
    /**
     * 商户ID集合，只包含本级，不含代理
     */
    private List<Long> mchIds;
    private List<Long> shopIds;
    private List<Long> userIds;
}