package com.chargebolt.controller;

import com.chargebolt.aeacus.annotation.Authentication;
import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.basic.request.merchant.ReAllocateUserParam;
import com.chargebolt.merchant.service.PCMerchantService;
import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.controller.BaseController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class PCMerchantController extends BaseChargeboltController {


    @Resource
    private PCMerchantService pcMerchantService;

    /**
     * 商家管理-重新分配-账号查询
     */
    @Login
    @Authentication(permissionCode = "60001")
    @GetMapping(value = "/merchant/allocateUserList")
    public BizResult queryAllocateUser(
            @RequestParam(value = "principalName", required = false) String principalName,
            @RequestParam(value = "currentPrincipalId", required = false) Long currentPrincipalId
    ) {
        return BizResult.create(pcMerchantService.queryAllocateUsersByName(principalName, currentPrincipalId, getUser(), getUserDataAuthorityContext()));
    }

    /**
     * 商家管理-重新分配
     */
    @Login
    @Authentication(permissionCode = "60009")
    @PostMapping(value = "/merchant/allocateForPC")
    public BizResult reAllocateUser(@RequestBody @Valid ReAllocateUserParam reAllocateUserParam) {
        pcMerchantService.reAllocateUser(reAllocateUserParam, getUser());
        return BizResult.create(null);
    }
}
