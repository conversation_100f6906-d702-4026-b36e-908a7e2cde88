/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.controller;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.basic.request.them.ThemRequest;
import com.chargebolt.basic.response.system.LanguagesResponse;
import com.chargebolt.basic.response.them.ThemResponse;
import com.chargebolt.commons.constant.AppConstant;
import com.chargebolt.commons.enums.MultilingualEnum;
import com.chargebolt.template.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;
import so.dian.talos.client.enums.LanguageEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VersionController.java, v 1.0 2023-11-27 4:59 PM Exp $
 */
@RestController
public class VersionController extends BaseController {
//    private static final String VERSION_KEY= "__CACHE_SYSTEM_VERSION";
    private static final String THEM_LOGO_KEY= "__CACHE_THEM_LOGO";
    @Autowired
    private RedissonClient redissonClient;
    @GetMapping(value = "/system/1.0/getVersion")
    public Result<Integer> getVersion() {
        return template.execute(new ControllerCallback<Integer>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {

            }

            @Override
            public Integer execute() {
                RBucket<Integer> bucket=redissonClient.getBucket(AppConstant.VERSION_KEY);
                if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
                    return bucket.get();
                }
                // 默认返回基础版
                return 1;
            }
        });
    }

    @GetMapping(value = "/system/1.0/setVersion")
    public Result<Integer> setVersion(@RequestParam("version") Integer version) {
        return template.execute(new ControllerCallback<Integer>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {

            }

            @Override
            public Integer execute() {
                RBucket<Integer> bucket=redissonClient.getBucket(AppConstant.VERSION_KEY);
                bucket.set(version, Integer.MAX_VALUE, TimeUnit.HOURS);
                return version;
            }
        });
    }

    @GetMapping(value = "/system/1.0/getTheme")
    public Result<ThemResponse> getTheme() {
        return template.execute(new ControllerCallback<ThemResponse>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {

            }

            @Override
            public ThemResponse execute() {
                RBucket<ThemResponse> bucket=redissonClient.getBucket(THEM_LOGO_KEY);
                if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
                    return bucket.get();
                }
                ThemResponse response= new ThemResponse();
                response.setLogoImg("http://lhc-image.oss-cn-beijing.aliyuncs.com/lhc/2023/12/07/290w_71h_59B0E1701918474.png");
                response.setThem("#0073F6");
                return response;
            }
        });
    }

    @PostMapping(value = "/system/1.0/saveTheme")
    public Result<ThemResponse> saveTheme(@RequestBody ThemRequest request) {
        return template.execute(new ControllerCallback<ThemResponse>() {
            @Override
            public void checkParam() {
                if(StringUtils.isBlank(request.getThem())){
                    throw new I18nMessageException("1","主题Id不能为空");
                }
                if(Objects.isNull(request.getLogoImg())){
                    throw new I18nMessageException("1","Logo不能为空");
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public ThemResponse execute() {
                ThemResponse response= new ThemResponse();
                response.setLogoImg(request.getLogoImg());
                response.setThem(request.getThem());
                RBucket<ThemResponse> bucket=redissonClient.getBucket(THEM_LOGO_KEY);
                bucket.set(response, Integer.MAX_VALUE, TimeUnit.HOURS);
                return response;
            }
        });
    }

    @GetMapping(value = "/system/languages")
    public Result<List<LanguagesResponse>> languages() {
        return template.execute(new ControllerCallback<List<LanguagesResponse>>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {

            }

            @Override
            public List<LanguagesResponse> execute() {
                return Arrays.stream(MultilingualEnum.values())
                        .map(enumValue -> {
                            LanguagesResponse languagesResponse = new LanguagesResponse();
                            languagesResponse.setLang(enumValue.getLocale().toLanguageTag());
                            languagesResponse.setName(enumValue.getName());
                            return languagesResponse;
                        })
                        .collect(Collectors.toList());
            }
        });
    }
}