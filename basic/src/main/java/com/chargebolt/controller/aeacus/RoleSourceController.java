package com.chargebolt.controller.aeacus;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.commons.enums.DataScopeEnum;
import com.chargebolt.commons.exception.ResourceExceptionEnum;
import com.chargebolt.component.annotation.DataAuthorityScope;
import com.chargebolt.request.aeacus.EditRoleRequest;
import com.chargebolt.request.aeacus.RoleSourceRequest;
import com.chargebolt.response.aeacus.RoleResponse;
import com.chargebolt.service.aeacus.RoleSourceMappingService;
import com.chargebolt.template.BaseChargeboltController;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 角色管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/6 15:17
 */
@RestController
public class RoleSourceController extends BaseChargeboltController {

    @Resource
    private RoleSourceMappingService roleSourceMappingService;

    /**
     * PC端-角色列表
     */
    @Login
    @DataAuthorityScope(scope = DataScopeEnum.ONLY_ONESELF)
    @GetMapping(value = "/role/source/list")
    public Result list(@RequestParam(value = "pageNo", defaultValue = "1",required = false) Integer pageNo,
                                               @RequestParam(value = "pageSize", defaultValue = "10",required = false) Integer pageSize,
                                               @RequestParam(value = "agentId", required = false) Long agentId,
                                               @RequestParam(value = "roleName", required = false) String roleName) {
        RoleSourceRequest request = new RoleSourceRequest();
        return template.execute(new ControllerCallback<PageData>() {
            @Override
            public void checkParam() {


            }

            @Override
            public void buildContext() {
                request.setAgentId(agentId);
                request.setRoleName(roleName);
                request.setPageNo(pageNo);
                request.setPageSize(pageSize);
            }

            @Override
            public PageData execute() {
                Page<Object> startPage = PageMethod.startPage(request.getPageNo(), request.getPageSize());
                List<RoleResponse> roleList = roleSourceMappingService.getRoleList(request);
                return PageData.create(roleList, startPage.getTotal(), pageNo.longValue(), pageSize);
            }
        });

    }

    /**
     * PC端-角色列表
     */
    @Login
    @GetMapping(value = "/role/source/info")
    public Result<RoleResponse> getRoleInfo(@RequestParam Long roleId) {
        return template.execute(new ControllerCallback<RoleResponse>() {
            @Override
            public void checkParam() {


            }

            @Override
            public void buildContext() {

            }

            @Override
            public RoleResponse execute() {
                return roleSourceMappingService.getRoleInfo(roleId);
            }
        });

    }

    /**
     * 保存角色信息
     */
    @Login
    @PostMapping(value = "/role/source/save")
    public Result save(@RequestBody EditRoleRequest request) {
        return template.execute(new ControllerCallback() {
            @Override
            public void checkParam() {

                if (StringUtils.isBlank(request.getRoleName())) {
                    throw new I18nMessageException(ResourceExceptionEnum.ROLE_NAME_NULL.getCode(), ResourceExceptionEnum.ROLE_NAME_NULL.getDesc());
                }
                if (Objects.isNull(request.getAgentId())) {
                    throw new I18nMessageException(ResourceExceptionEnum.AGENT_ID_NULL.getCode(), ResourceExceptionEnum.AGENT_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public Object execute() {
                roleSourceMappingService.saveResource(request, getUser());
                return "保存成功！";
            }
        });
    }

    /**
     * 更新角色信息
     */
    @Login
    @PutMapping(value = "/role/source/update")
    public Result update(@RequestBody EditRoleRequest request) {
        return template.execute(new ControllerCallback() {
            @Override
            public void checkParam() {

                if (StringUtils.isBlank(request.getRoleName())) {
                    throw new I18nMessageException(ResourceExceptionEnum.ROLE_NAME_NULL.getCode(), ResourceExceptionEnum.ROLE_NAME_NULL.getDesc());
                }
                if (Objects.isNull(request.getRoleId())) {
                    throw new I18nMessageException(ResourceExceptionEnum.ROLE_ID_NULL.getCode(), ResourceExceptionEnum.ROLE_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public Object execute() {
                roleSourceMappingService.updateResource(request, getUser());
                return "保存成功！";
            }
        });
    }

    /**
     * 删除角色
     */
    @Login
    @DeleteMapping(value = "/role/source/remove")
    public Result remove(@RequestParam Long roleId) {
        return template.execute(new ControllerCallback<String>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(roleId)) {
                    throw new I18nMessageException(ResourceExceptionEnum.ROLE_ID_NULL.getCode(), ResourceExceptionEnum.ROLE_ID_NULL.getDesc());
                }

            }

            @Override
            public void buildContext() {
            }

            @Override
            public String execute() {
                roleSourceMappingService.removeByRoleId(roleId);
                return "删除成功！";
            }
        });
    }

}
