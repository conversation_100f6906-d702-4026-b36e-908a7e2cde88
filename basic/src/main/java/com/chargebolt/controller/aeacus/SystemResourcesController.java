package com.chargebolt.controller.aeacus;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.commons.exception.ResourceExceptionEnum;
import com.chargebolt.request.aeacus.SystemResourcesRequest;
import com.chargebolt.request.aeacus.VersionTemplateRequest;
import com.chargebolt.response.aeacus.RoleResourceResponse;
import com.chargebolt.response.aeacus.SystemResourcesResponse;
import com.chargebolt.service.aeacus.SystemResourcesService;
import com.chargebolt.template.BaseChargeboltController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 资源管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/4 10:42
 */
@RestController
public class SystemResourcesController extends BaseChargeboltController {

    @Resource
    private SystemResourcesService systemResourcesService;


    /**
     * PC端-资源列表
     */
    @Login
    @GetMapping(value = "/resource/list")
    public Result<PageData<SystemResourcesResponse>> list() {


        // TODO 非管理员权限控制
        return template.execute(new ControllerCallback<PageData<SystemResourcesResponse>>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public PageData<SystemResourcesResponse> execute() {

                return PageData.create(systemResourcesService.listContainVersion());
            }
        });

    }



    /**
     * 获取code下级资源列表
     */
    @Login
    @GetMapping(value = "/resource/getSubList")
    public Result<SystemResourcesResponse> getPCSubList(String code) {

        return template.execute(new ControllerCallback<SystemResourcesResponse>() {
            @Override
            public void checkParam() {
                if (StringUtils.isBlank(code)) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CODE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_CODE_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public SystemResourcesResponse execute() {

                return systemResourcesService.getSubResourceByCode(code);
            }
        });

    }

    /**
     * 获取代理商资源列表
     */
    @Login
    @GetMapping(value = "/resource/getAgentResourceList")
    public Result<RoleResourceResponse> getAgentResourceList(@RequestParam Long agentId) {
        return template.execute(new ControllerCallback<RoleResourceResponse>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(agentId)) {
                    throw new I18nMessageException(ResourceExceptionEnum.AGENT_ID_NULL.getCode(), ResourceExceptionEnum.AGENT_ID_NULL.getDesc());
                }
            }
            @Override
            public void buildContext() {
            }

            @Override
            public RoleResourceResponse execute() {

                return systemResourcesService.getResourceListByAgentId(agentId);
            }
        });
    }

    /**
     * 获取角色的资源列表
     */
    @Login
    @GetMapping(value = "/resource/getRoleResourceList")
    public Result<RoleResourceResponse> getRoleResourceList(@RequestParam Long roleId) {
        return template.execute(new ControllerCallback<RoleResourceResponse>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(roleId)) {
                    throw new I18nMessageException(ResourceExceptionEnum.ROLE_ID_NULL.getCode(), ResourceExceptionEnum.ROLE_ID_NULL.getDesc());
                }
            }
            @Override
            public void buildContext() {
            }

            @Override
            public RoleResourceResponse execute() {

                return systemResourcesService.getResourceListByRoleId(roleId);
            }
        });
    }

    /**
     * 保存资源信息
     */
    @Login
    @PostMapping(value = "/resource/save")
    public Result save(@RequestBody SystemResourcesRequest request) {
        return template.execute(new ControllerCallback<String>() {
            @Override
            public void checkParam() {
                if (StringUtils.isBlank(request.getParentCode())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_PARENT_CODE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_PARENT_CODE_NULL.getDesc());
                }
                if (StringUtils.isBlank(request.getCode())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CODE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_CODE_NULL.getDesc());
                }
                if (StringUtils.isBlank(request.getName())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_NAME_NULL.getCode(), ResourceExceptionEnum.RESOURCE_NAME_NULL.getDesc());
                }
                if (Objects.isNull(request.getResourceType())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_TYPE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_TYPE_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public String execute() {
                systemResourcesService.saveRecord(request.toDO());
                return "保存成功！";
            }
        });
    }

    /**
     * 删除资源信息
     */
    @Login
    @DeleteMapping(value = "/resource/remove")
    public Result remove(@RequestParam String code) {
        return template.execute(new ControllerCallback<String>() {
            @Override
            public void checkParam() {
                if (StringUtils.isBlank(code)) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CODE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_CODE_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public String execute() {
                systemResourcesService.removeRecord(code);
                return "删除成功！";
            }
        });
    }

    /**
     * 更新资源信息
     */
    @Login
    @PutMapping(value = "/resource/update")
    public Result update(@RequestBody SystemResourcesRequest request) {
        return template.execute(new ControllerCallback<String>() {
            @Override
            public void checkParam() {
                if (StringUtils.isBlank(request.getParentCode())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_PARENT_CODE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_PARENT_CODE_NULL.getDesc());
                }
                if (StringUtils.isBlank(request.getCode())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CODE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_CODE_NULL.getDesc());
                }
                if (StringUtils.isBlank(request.getName())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_NAME_NULL.getCode(), ResourceExceptionEnum.RESOURCE_NAME_NULL.getDesc());
                }
                if (Objects.isNull(request.getResourceType())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_TYPE_NULL.getCode(), ResourceExceptionEnum.RESOURCE_TYPE_NULL.getDesc());
                }
                if (Objects.isNull(request.getId())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_ID_NULL.getCode(), ResourceExceptionEnum.RESOURCE_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public String execute() {
                systemResourcesService.updateRecord(request.toDO());
                return "更新成功！";
            }
        });
    }

//    /**
//     * 获取资源绑定的版本
//     * @param resourceId 资源ID
//     */
//    @Login
//    @GetMapping(value = "/resource/versions/{resourceId}")
//    public Result<List<VersionTemplateResponse>> getVersions(@PathVariable Long resourceId) {
//        return template.execute(new ControllerCallback<List<VersionTemplateResponse>>() {
//            @Override
//            public void checkParam() {
//
//            }
//
//            @Override
//            public void buildContext() {
//            }
//
//            @Override
//            public List<VersionTemplateResponse> execute() {
//
//                return systemResourcesService.getResourceTemplate(resourceId);
//            }
//        });
//    }

    /**
     * 资源绑定版本
     *
     * @param request 资源绑定版本请求
     * @return 返回结果
     */
    @PutMapping(value = "/resource/bindVersion")
    public Result bindVersion(@RequestBody VersionTemplateRequest request) {
        return template.execute(new ControllerCallback<String>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(request.getResourceId())) {
                    throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_ID_NULL.getCode(), ResourceExceptionEnum.RESOURCE_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public String execute() {
                systemResourcesService.bindVersion(request);
                return "保存成功！";
            }
        });
    }

}
