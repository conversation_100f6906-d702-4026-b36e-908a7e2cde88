/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.controller.common;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStream;
/**
 * TODO
 *
 * <AUTHOR>
 * @version: DownloadFileController.java, v 1.0 2024-02-21 6:54 PM Exp $
 */
@RestController
public class DownloadFileController {
    // 如果是开发环境或者非打包部署环境，可以从文件系统的绝对路径读取
    @GetMapping("/download/shopSkuTemplate")
    public ResponseEntity<Resource> shopSkuTemplate() throws IOException {
        // 获取实际文件路径，这里假设是在项目根目录下的resource/download
        ClassPathResource resource = new ClassPathResource("download/shop_sku.xls");
        if (!resource.exists()) {
            return ResponseEntity.notFound().build();
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "shop_sku.xls");

        // 返回资源
        return ResponseEntity.ok()
                .headers(headers)
                .body(resource);
    }
}