/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.controller.common;

import com.chargebolt.aeacus.dto.OssAuthenticationDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.AeacusUserService;
import com.chargebolt.aeacus.service.LoginService;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.basic.response.area.Area;
import com.chargebolt.response.agent.AppAgentResponse;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.service.area.AreaService;
import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.demeter.response.DeviceStatisticResponse;
import so.dian.eros.common.util.RequestUtils;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.lang.exception.CustomCodeException;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: GeoNamesController.java, v 1.0 2024-07-04 上午10:17 Exp $
 */
@Slf4j
@RestController
public class GeoNamesController extends BaseChargeboltController {
    @Autowired
    private AreaService areaService;
    @Resource
    private LoginService loginService;
    @Resource
    private UserManager userManager;
    @Resource
    private AgentService agentService;

    /**
     * 获取国家/地区行政区数据
     *
     * 不传入区域code，根据登录人所属代理商归属地code
     *
     * @param regionalCode
     * @return
     */
    @GetMapping(value = "/geo/getNames")
    public Result<List<Area>> geoGetNames(@RequestParam(value = "regionalCode", required = false) String regionalCode) {

        return template.execute(new ControllerCallback<List<Area>>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public List<Area> execute() {
                String areaCode= regionalCode;
                if(StringUtils.isBlank(areaCode)){
                    HttpServletRequest request  = RequestUtils.getRequest();
                    String userToken = request.getHeader(RequestUtils.HEADER_KEY);
                    OssAuthenticationDTO ossAuthenticationDTO =new OssAuthenticationDTO();
                    ossAuthenticationDTO.setUserToken(userToken);
                    Long userId= loginService.getUserIdByUserToken(userToken);
                    if(Objects.isNull(userId)){
                        areaCode= "HK";
                    }else{
                        OssUserDO userDO = userManager.getById(userId);
                        if(Objects.isNull(userDO)){
                            areaCode= "HK";
                        }else{
                            AppAgentResponse agentResponse= agentService.getAgentDetail(userDO.getAgentId());
                            if(Objects.isNull(agentResponse)){
                                areaCode= "HK";
                            }else{
                                areaCode= agentResponse.getRegionalCode();
                            }
                        }
                    }

                }

                return areaService.getArea(areaCode);
            }
        });
    }

}


