/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.controller.common;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.dao.common.model.Regionals;
import com.chargebolt.ezreal.response.commons.RegionalResponse;
import com.chargebolt.service.area.RegionalsService;
import com.chargebolt.template.BaseChargeboltController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import java.util.List;

/**
 * 国家地区
 *
 * <AUTHOR>
 * @version: RegionalsController.java, v 1.0 2024-09-06 上午11:54 Exp $
 */
@RestController
public class RegionalsController extends BaseChargeboltController {
    @Autowired
    private RegionalsService regionalsService;

    @GetMapping(value = "/regionals/list")
    public Result<List<RegionalResponse>> regionalsList(@RequestParam(value = "agentId", required = false) Long agentId) {
        return template.execute(new ControllerCallback<List<RegionalResponse>>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public List<RegionalResponse> execute() {
                return regionalsService.regionalsList(agentId);
            }
        });
    }

    @GetMapping(value = "/regionals/getByPhoneCode")
    public Result<RegionalResponse> getByPhoneCode(@RequestParam(value = "nationCode", required = false) String nationCode) {
        return template.execute(new ControllerCallback<RegionalResponse>() {
            @Override
            public void checkParam() {
                if(StringUtils.isBlank(nationCode)){
                    throw new I18nMessageException("2001","nationCode is null");
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public RegionalResponse execute() {
                RegionalResponse response= new RegionalResponse();
                Regionals regionals= regionalsService.getByPhoneCode(nationCode);
                BeanUtils.copyProperties(regionals,response);
                return response;
            }
        });
    }
}