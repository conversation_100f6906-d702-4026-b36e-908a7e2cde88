/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.controller.common;

import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import so.dian.mofa3.lang.domain.Result;
import so.dian.talos.common.util.UploadHelper;

import javax.annotation.Resource;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: UploadController.java, v 1.0 2024-10-21 下午1:35 Exp $
 */
@Slf4j
@RestController
public class UploadController extends BaseChargeboltController {
    @Resource
    private UploadHelper uploadHelper;
    @PostMapping(value = {"/1.0/file/upload"})
    public Result<String> upload(@RequestParam("file") MultipartFile file) {
        log.info("upload Image begin----------------");
        try {
            String clientFileName = file.getOriginalFilename();
            String fileName = clientFileName.trim().toLowerCase();
            log.info("upload fileName:{}", fileName);
            return Result.success(uploadHelper.ossUploadFile(fileName, file.getInputStream()));
        } catch (Exception e) {
            log.error("oss upload failed: ", e);
            return Result.fail("file upload failed");
        }
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return "";
    }
}