/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.controller.common;

import com.chargebolt.convert.common.VersionTemplateConverter;
import com.chargebolt.dao.aeacus.model.VersionTemplate;
import com.chargebolt.response.aeacus.VersionTemplateResponse;
import com.chargebolt.service.aeacus.VersionTemplateService;
import com.chargebolt.template.BaseChargeboltController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VersionTemplateController.java, v 1.0 2024-09-30 下午4:07 Exp $
 */
@RestController
public class VersionTemplateController extends BaseChargeboltController {
    @Autowired
    private VersionTemplateService versionTemplateService;
    @GetMapping(value = "/version/template/list")
    public Result<List<VersionTemplateResponse>> templateList() {
        return template.execute(new ControllerCallback<List<VersionTemplateResponse>>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public List<VersionTemplateResponse> execute() {
                List<VersionTemplate> list= versionTemplateService.listRecord(new VersionTemplate());
                return list.stream()
                        .map(VersionTemplateConverter.INSTANCE::versionTemplateConvert)
                        .collect(Collectors.toList());
            }
        });
    }
}