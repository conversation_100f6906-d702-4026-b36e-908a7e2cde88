/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.controller.device;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.basic.request.shop.ShopOrdersRequest;
import com.chargebolt.basic.response.shop.ShopOrdersResponse;
import com.chargebolt.ezreal.response.agent.AgentSimpleResponse;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.template.BaseChargeboltController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.demeter.common.exception.BizErrorCodeEnum;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.template.controller.ControllerCallback;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: NewDeviceController.java, v 1.0 2024-10-11 下午5:04 Exp $
 */
@RestController
public class NewDeviceController extends BaseChargeboltController {
    @Autowired
    private AgentService agentService;

    /**
     * 获取设备所归属的一级代理商
     *
     * @param deviceNo
     * @return
     */
    @GetMapping(value = "/inner/device/getDeviceTopAgent")
    public Result<AgentSimpleResponse> getDeviceTopAgent(@RequestParam(name = "deviceNo", required = false) String deviceNo) {
        return template.execute(new ControllerCallback<AgentSimpleResponse>() {
            @Override
            public void checkParam() {
                if(StringUtils.isBlank(deviceNo)){
                    throw new I18nMessageException(BizErrorCodeEnum.DEVICE_NOT_EXISTED.getCode().toString(), BizErrorCodeEnum.DEVICE_NOT_EXISTED.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public AgentSimpleResponse execute() {
                return agentService.getDeviceTopLevelAgent(deviceNo);
            }
        });
    }
}