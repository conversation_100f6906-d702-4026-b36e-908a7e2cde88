package com.chargebolt.controller.messagecenter;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.pheidi.request.MessageQuery;
import com.chargebolt.pheidi.response.MessageGroupVo;
import com.chargebolt.pheidi.response.MessageListItemVo;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.request.messagecenter.MarkReadRequest;
import com.chargebolt.request.messagecenter.MessageQueryReq;
import com.chargebolt.service.messagecenter.MessageCenterService;
import com.chargebolt.service.messagecenter.OneSignalUserService;
import com.chargebolt.template.BaseChargeboltController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import so.dian.mofa3.lang.domain.Result;
@Slf4j
@Tag(name = "MessageCenterControllerAPI", description = "消息中心")
@RestController
@RequestMapping("/messagecenter")
public class MessageCenterController extends BaseChargeboltController {

    @Resource
    private MessageCenterService messageService;
    @Resource
    private OneSignalUserService oneSignalUserService;

    /**
     * 标记已读
     */
    @Login
    @Operation(summary = "标记已读")
    @PostMapping("/message-center/read")
    public Result<Boolean> read(@RequestBody MarkReadRequest param) {
        OssUserDTO user = getUser();
        messageService.markReadStatus(oneSignalUserService.getExternalId(user), param.getBizType());
        return Result.success(true);
    }

    /**
     * 拉取消息数量
     */
    @Login
    @Operation(summary = "拉取消息数量")
    @GetMapping("/message-center/unread/num")
    public Result<Integer> num() {
        OssUserDTO user = getUser();
        int num = messageService.unreadNum(oneSignalUserService.getExternalId(user));
        return Result.success(num);
    }

    /**
     * 预拉取消息，分组数量
     */
    @Login
    @Operation(summary = "预拉取消息，分组数量")
    @GetMapping("/message-center/group/list")
    public Result<PageData<MessageGroupVo>> groupList() {
        OssUserDTO user = getUser();
        PageData<MessageGroupVo> vos = messageService.groupList(oneSignalUserService.getExternalId(user));
        return Result.success(vos);
    }

    /**
     * 按组拉取消息
     */
    @Operation(summary = "按组拉取消息")
    @Login
    @PostMapping("/message-center/messages")
    public Result<PageData<MessageListItemVo>> messages(@RequestBody MessageQueryReq param) {
        OssUserDTO user = getUser();
        MessageQuery messageQuery = new MessageQuery();
        messageQuery.setExternalId(oneSignalUserService.getExternalId(user));
        messageQuery.setBizType(param.getBizType());
        messageQuery.setPageNo(param.getPageNo());
        messageQuery.setPageSize(param.getPageSize());
        PageData<MessageListItemVo> page = messageService.messages(messageQuery);
        return Result.success(page);
    }
}
