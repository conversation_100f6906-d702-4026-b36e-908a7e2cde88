package com.chargebolt.controller.messagecenter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.enums.language.MsgTemplateBizTypeEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.framework.i18n.I18nUtil;
import com.chargebolt.pheidi.dto.MapDTO;
import com.chargebolt.pheidi.request.MessageTemplateChangeStatus;
import com.chargebolt.pheidi.request.MessageTemplateCreate;
import com.chargebolt.pheidi.request.MessageTemplateEdit;
import com.chargebolt.pheidi.request.MessageTemplateQuery;
import com.chargebolt.pheidi.response.MessageTemplateDetailResp;
import com.chargebolt.pheidi.response.MessageTemplateListItemResp;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.service.messagecenter.MessageTemplateService;
import com.chargebolt.template.BaseChargeboltController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CustomCodeException;
import so.dian.mofa3.template.controller.ControllerCallback;

@Slf4j
@Tag(name = "MessageTemplateControllerAPI", description = "消息模板")
@RestController
@RequestMapping("/messagetemplte")
public class MessageTemplateController extends BaseChargeboltController {

    @Resource
    private MessageTemplateService messageTemplateService;
    @Resource
    private I18nUtil i18nUtil;

    /**
     * 新增模板
     */
    @Login
    @Operation(summary = "新增模板")
    @PostMapping("/message-template/create")
    @SuppressWarnings("unchecked")
    public Result<Long> create(@RequestBody MessageTemplateCreate param) {
        return template.execute(new ControllerCallback<Long>() {
            @Override
            public void checkParam() {
                if (param == null) {
                    throw new CustomCodeException("120001", "参数不能为空");
                }
                if (StringUtils.isBlank(param.getName())) {
                    throw new CustomCodeException("120001", "name不能为空");
                }
                if (Objects.isNull(param.getType())) {
                    throw new CustomCodeException("120001", "type不能为空");
                }
                if (StringUtils.isBlank(param.getTitle())) {
                    throw new CustomCodeException("120001", "title不能为空");
                }
                if (StringUtils.isBlank(param.getContent())) {
                    throw new CustomCodeException("120001", "content不能为空");
                }
                if (Objects.isNull(param.getChannel()) || param.getChannel().isEmpty()) {
                    throw new CustomCodeException("120001", "channel不能为空");
                }
                if (Objects.isNull(param.getRedirectGoal())) {
                    throw new CustomCodeException("120001", "redirectGoal不能为空");
                }
                if (param.getName().length() > 100) {
                    throw new CustomCodeException("120001", "name不能超过100个字符");
                }
                if (param.getTitle().length() > 100) {
                    throw new CustomCodeException("120001", "title不能超过100个字符");
                }
                if (param.getContent().length() > 300) {
                    throw new CustomCodeException("120001", "content不能超过300个字符");
                }
                if (param.getRedirectLink() != null && param.getRedirectLink().length() > 100) {
                    throw new CustomCodeException("120001", "redirectLink不能超过100个字符");
                }

                UserDataAuthorityContext authority = getUserDataAuthorityContext();
                if (authority.getAuthorityLevel() != AuthorityLevelEnum.ALL.getCode()) {
                    throw new CustomCodeException("120001", "无权限操作");
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public Long execute() {
                Long templateId = messageTemplateService.create(param);
                return templateId;
            }
        });
    }

    /**
     * 编辑模板
     */
    @Login
    @Operation(summary = "编辑模板")
    @PostMapping("/message-template/edit")
    @SuppressWarnings("unchecked")
    public Result<Boolean> edit(@RequestBody MessageTemplateEdit param) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if (param == null) {
                    throw new CustomCodeException("120001", "参数不能为空");
                }
                if (StringUtils.isBlank(param.getName())) {
                    throw new CustomCodeException("120001", "name不能为空");
                }
                if (Objects.isNull(param.getType())) {
                    throw new CustomCodeException("120001", "type不能为空");
                }
                if (StringUtils.isBlank(param.getTitle())) {
                    throw new CustomCodeException("120001", "title不能为空");
                }
                if (StringUtils.isBlank(param.getContent())) {
                    throw new CustomCodeException("120001", "content不能为空");
                }
                if (Objects.isNull(param.getChannel()) || param.getChannel().isEmpty()) {
                    throw new CustomCodeException("120001", "channel不能为空");
                }
                if (Objects.isNull(param.getRedirectGoal())) {
                    throw new CustomCodeException("120001", "redirectGoal不能为空");
                }
                if (param.getName().length() > 100) {
                    throw new CustomCodeException("120001", "name长度不能超过100个字符");
                }
                if (param.getTitle().length() > 100) {
                    throw new CustomCodeException("120001", "title长度不能超过100个字符");
                }
                if (param.getContent().length() > 300) {
                    throw new CustomCodeException("120001", "content长度不能超过300个字符");
                }
                if (param.getRedirectLink() != null && param.getRedirectLink().length() > 100) {
                    throw new CustomCodeException("120001", "redirectLink长度不能超过100个字符");
                }
                UserDataAuthorityContext authority = getUserDataAuthorityContext();
                if (authority.getAuthorityLevel() != AuthorityLevelEnum.ALL.getCode()) {
                    throw new CustomCodeException("120001", "无权限操作");
                }
            }

            @Override
            public Boolean execute() {
                Boolean success = messageTemplateService.edit(param);
                return success;
            }
        });
    }

    /**
     * 模板列表
     */
    @Login
    @Operation(summary = "模板列表")
    @PostMapping("/message-template/list")
    @SuppressWarnings("unchecked")
    public Result<PageData<MessageTemplateListItemResp>> list(@RequestBody MessageTemplateQuery param) {
        return template.execute(new ControllerCallback<PageData<MessageTemplateListItemResp>>() {

            @Override
            public void checkParam() {
                UserDataAuthorityContext authority = getUserDataAuthorityContext();
                if (authority.getAuthorityLevel() != AuthorityLevelEnum.ALL.getCode()) {
                    throw new CustomCodeException("120001", "无权限操作");
                }
            }

            @Override
            public PageData<MessageTemplateListItemResp> execute() {
                PageData<MessageTemplateListItemResp> pageData = messageTemplateService.list(param);
                return pageData;
            }
        });
    }

    /**
     * 模板详情
     */
    @Login
    @SuppressWarnings("unchecked")
    @Operation(summary = "模板详情")
    @GetMapping("/message-template/detail/{id}")
    public Result<MessageTemplateDetailResp> detail(@PathVariable Long id) {
        return template.execute(new ControllerCallback<MessageTemplateDetailResp>() {
            @Override
            public void checkParam() {
                UserDataAuthorityContext authority = getUserDataAuthorityContext();
                if (authority.getAuthorityLevel() != AuthorityLevelEnum.ALL.getCode()) {
                    throw new CustomCodeException("120001", "无权限操作");
                }
            }

            @Override
            public MessageTemplateDetailResp execute() {
                MessageTemplateDetailResp detail = messageTemplateService.detail(id);
                return detail;
            }
        });
    }

    /**
     * 业务类型枚举
     */
    @Operation(summary = "业务类型枚举")
    @GetMapping("/message-template/biz-type/select-items")
    public Result<List<MapDTO>> bizTypeSelectItems() {
        List<MapDTO> items = Arrays.stream(MsgTemplateBizTypeEnum.values())
                .map(e -> new MapDTO(e.getBizType(), i18nUtil.getMessage(e)))
                .collect(Collectors.toList());
        return Result.success(items);
    }

    // 查询模板 Id和名称的映射，使用 MapDTO 列表返回
    @Operation(summary = "查询模板 Id和名称的映射")
    @GetMapping("/message-template/templateCodeNameMap")
    public Result<List<MapDTO>> templateCodeNameMap() {
        return Result.success(messageTemplateService.templateCodeNameMap());
    }

    /**
     * 修改模板状态
     */
    @Login
    @Operation(summary = "修改模板状态")
    @PostMapping("/message-template/changeStatus")
    @SuppressWarnings("unchecked")
    public Result<Boolean> changeStatus(@RequestBody MessageTemplateChangeStatus param) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                UserDataAuthorityContext authority = getUserDataAuthorityContext();
                if (authority.getAuthorityLevel() != AuthorityLevelEnum.ALL.getCode()) {
                    throw new CustomCodeException("120001", "无权限操作");
                }
            }

            @Override
            public Boolean execute() {
                Boolean success = messageTemplateService.changeStatus(param);
                return success;
            }
        });
    }
}
