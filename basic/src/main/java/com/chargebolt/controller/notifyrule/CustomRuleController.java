package com.chargebolt.controller.notifyrule;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.commons.enums.language.NotifyRuleScheduleEnum;
import com.chargebolt.commons.enums.language.NotifyRuleStatusEnum;
import com.chargebolt.commons.enums.language.NotifyRuleTargetStrategyEnum;
import com.chargebolt.commons.enums.language.NotifyRuleTypeEnum;
import com.chargebolt.commons.exception.NotifyRuleExceptionEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.framework.i18n.I18nUtil;
import com.chargebolt.request.notifyrule.CustomRuleEditReq;
import com.chargebolt.request.notifyrule.CustomRuleEnableReq;
import com.chargebolt.request.notifyrule.CustomRuleQuery;
import com.chargebolt.response.notifyrule.CustomRuleDetailResp;
import com.chargebolt.response.notifyrule.CustomRuleListItemResp;
import com.chargebolt.response.notifyrule.MapDTO;
import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.notifyrule.NotifyRuleService;
import com.chargebolt.task.NotifyRuleScheduleTask;
import com.chargebolt.template.BaseChargeboltController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CustomCodeException;
import so.dian.mofa3.template.controller.ControllerCallback;

/**
 * 消息通知的自定义规则
 */
@Tag(name = "CustomRuleControllerAPI", description = "消息通知的自定义规则")
@Slf4j
@RestController
public class CustomRuleController extends BaseChargeboltController {

    @Resource
    private NotifyRuleService notifyRuleService;
    @Resource
    private NotifyRuleScheduleTask notifyRuleScheduleTask;
    @Resource
    private I18nUtil i18nUtil;

    /**
     * 非业务使用，用于测试通知规则
     * 
     * @return
     */
    @GetMapping("/task/test")
    public Result<Boolean> shopList() {
        notifyRuleScheduleTask.notifyRuleScheduleTask();
        return Result.success(true);
    }

    /**
     * 非业务使用，用于初始化通知规则
     * 
     * @param agentId
     * @return
     */
    @Operation(summary = "初始化通知规则")
    @GetMapping("/customrule/index/init")
    @Login
    public Result<Boolean> init() {
        UserDataAuthorityContext userDataAuthorityContext = getUserDataAuthorityContext();
        Long agentId = userDataAuthorityContext.getAgentId();
        log.info("初始化通知规则，代理商ID：{}", agentId);
        notifyRuleService.initNotifyRule(agentId);
        return Result.success(true);
    }

    @Login
    @Operation(summary = "自定义规则列表")
    @PostMapping("/customrule/index/list")
    public Result<PageData<CustomRuleListItemResp>> list(@RequestBody CustomRuleQuery query,
            HttpServletRequest httpServletRequest) {
        // 对query进行校验
        if (query.getPageSize() == null || query.getPageSize() <= 0) {
            query.setPageSize(10);
        }
        if (query.getPageNo() == null || query.getPageNo() <= 0) {
            query.setPageNo(1);
        }
        UserDataAuthorityContext userDataAuthorityContext = getUserDataAuthorityContext();
        return Result.success(notifyRuleService.list(query, userDataAuthorityContext));
    }

    @Login
    @Operation(summary = "查看规则详情")
    @GetMapping("/customrule/index/detail/{ruleId}")
    public Result<CustomRuleDetailResp> detail(@PathVariable("ruleId") String ruleId,
            HttpServletRequest httpServletRequest) {
        return Result.success(notifyRuleService.detail(Long.valueOf(ruleId.trim())));
    }

    @SuppressWarnings("unchecked")
    @Login
    @Operation(summary = "编辑规则")
    @PostMapping("/customrule/index/edit")
    public Result<Long> edit(@RequestBody CustomRuleEditReq editReq, HttpServletRequest httpServletRequest) {
        return template.execute(new ControllerCallback<Long>() {
            @Override
            public void checkParam() {
                // 校验规则ID
                if (editReq.getRuleId() == null) {
                    log.warn("规则ID为空");
                    throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_ID_EMPTY.getCode(),
                            NotifyRuleExceptionEnum.RULE_ID_EMPTY.getDesc());
                }

                // 校验规则名称
                if (StringUtils.isBlank(editReq.getRuleName())) {
                    log.warn("规则名称为空，规则ID：{}", editReq.getRuleId());
                    throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_NAME_EMPTY.getCode(),
                            NotifyRuleExceptionEnum.RULE_NAME_EMPTY.getDesc());
                }

                // 校验规则类型
                if (editReq.getRuleType() == null) {
                    log.warn("规则类型为空，规则ID：{}", editReq.getRuleId());
                    throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_TYPE_EMPTY.getCode(),
                            NotifyRuleExceptionEnum.RULE_TYPE_EMPTY.getDesc());
                }

                // 校验规则类型是否有效
                if (!NotifyRuleTypeEnum.isValid(editReq.getRuleType())) {
                    log.warn("规则类型不合法，规则ID：{}，类型：{}", editReq.getRuleId(), editReq.getRuleType());
                    throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_TYPE_INVALID.getCode(),
                            NotifyRuleExceptionEnum.RULE_TYPE_INVALID.getDesc());
                }

                // 校验规则条件
                if (CollectionUtils.isEmpty(editReq.getConditions())) {
                    log.warn("规则条件为空，规则ID：{}", editReq.getRuleId());
                    throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_CONDITION_EMPTY.getCode(),
                            NotifyRuleExceptionEnum.RULE_CONDITION_EMPTY.getDesc());
                }

                if (editReq.getConditions().size() > 10) {
                    throw new CustomCodeException(NotifyRuleExceptionEnum.RULE_CONDITION_EXCEED_LIMIT.getCode(),
                            NotifyRuleExceptionEnum.RULE_CONDITION_EXCEED_LIMIT.getDesc());
                }
                for (List<RuleCondition> condition : editReq.getConditions()) {
                    if (condition.size() > 10) {
                        throw new CustomCodeException(NotifyRuleExceptionEnum.RULE_CONDITION_EXCEED_LIMIT.getCode(),
                                NotifyRuleExceptionEnum.RULE_CONDITION_EXCEED_LIMIT.getDesc());
                    }
                }
                if (JSON.toJSONString(editReq.getConditions()).length() >= 1024) {
                    throw new CustomCodeException(NotifyRuleExceptionEnum.RULE_CONDITION_EXCEED_LIMIT.getCode(),
                            NotifyRuleExceptionEnum.RULE_CONDITION_EXCEED_LIMIT.getDesc());
                }

                // 校验调度周期
                boolean scheduleValid = false;
                for (NotifyRuleScheduleEnum scheduleEnum : NotifyRuleScheduleEnum.values()) {
                    if (scheduleEnum.getCode() == editReq.getSchedule()) {
                        scheduleValid = true;
                        break;
                    }
                }
                if (!scheduleValid) {
                    log.warn("规则调度周期不合法，规则ID：{}，调度周期：{}", editReq.getRuleId(), editReq.getSchedule());
                    throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_SCHEDULE_INVALID.getCode(),
                            NotifyRuleExceptionEnum.RULE_SCHEDULE_INVALID.getDesc());
                }

                // 校验目标策略
                boolean targetStrategyValid = false;
                for (NotifyRuleTargetStrategyEnum strategyEnum : NotifyRuleTargetStrategyEnum.values()) {
                    if (strategyEnum.getCode() == editReq.getTargetStrategy()) {
                        targetStrategyValid = true;
                        break;
                    }
                }
                if (!targetStrategyValid) {
                    log.warn("规则目标策略不合法，规则ID：{}，目标策略：{}", editReq.getRuleId(), editReq.getTargetStrategy());
                    throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_TARGET_STRATEGY_INVALID.getCode(),
                            NotifyRuleExceptionEnum.RULE_TARGET_STRATEGY_INVALID.getDesc());
                }

                // 校验模板代码
                if (StringUtils.isBlank(editReq.getTemplateCode())) {
                    log.warn("模板代码为空，规则ID：{}", editReq.getRuleId());
                    throw new I18nMessageException(NotifyRuleExceptionEnum.TEMPLATE_CODE_EMPTY.getCode(),
                            NotifyRuleExceptionEnum.TEMPLATE_CODE_EMPTY.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public Long execute() {
                return notifyRuleService.edit(editReq);
            }
        });
    }

    @Login
    @Operation(summary = "启用和停用自定义规则")
    @PostMapping("/customrule/index/enable")
    public Result<Boolean> enable(@RequestBody CustomRuleEnableReq enableReq, HttpServletRequest httpServletRequest) {
        notifyRuleService.enable(enableReq);
        return Result.success(true);
    }

    @Login
    @Operation(summary = "重置自定义规则")
    @PostMapping("/customrule/index/reset/{ruleId}")
    public Result<Boolean> reset(@PathVariable Long ruleId, HttpServletRequest httpServletRequest) {
        notifyRuleService.reset(ruleId);
        return Result.success(true);
    }

    @Login
    @Operation(summary = "获取通知规则时间间隔枚举")
    @GetMapping("/customrule/index/schedule/selectItems")
    public Result<List<MapDTO>> scheduleSelectItems() {
        List<MapDTO> result = Arrays.stream(NotifyRuleScheduleEnum.values())
                .map(item -> new MapDTO(item.getCode(), i18nUtil.getMessage(item.getName())))
                .collect(Collectors.toList());
        return Result.success(result);
    }

    @Login
    @Operation(summary = "获取通知规则目标策略枚举")
    @GetMapping("/customrule/index/targetstrategy/selectItems")
    public Result<List<MapDTO>> targetStrategySelectItems() {
        List<MapDTO> result = Arrays.stream(NotifyRuleTargetStrategyEnum.values())
                .map(item -> new MapDTO(item.getCode(), i18nUtil.getMessage(item.getName())))
                .collect(Collectors.toList());
        return Result.success(result);
    }

    /**
     * 获取规则类型列表
     * 
     * @return 规则类型列表
     */
    @Login
    @Operation(summary = "获取规则类型列表")
    @GetMapping("/customrule/index/ruleMapList")
    public Result<List<MapDTO>> getRuleTypeList() {
        List<MapDTO> result = Arrays.stream(NotifyRuleTypeEnum.values())
                .map(item -> new MapDTO(item.getCode(), i18nUtil.getMessage(item.getDesc())))
                .collect(Collectors.toList());
        return Result.success(result);
    }

    @Login
    @Operation(summary = "获取规则状态列表")
    @GetMapping("/customrule/index/ruleStatusSelectItems")
    public Result<List<MapDTO>> ruleStatusSelectItems() {
        List<MapDTO> result = Arrays.stream(NotifyRuleStatusEnum.values())
                .map(item -> new MapDTO(item.getCode(), i18nUtil.getMessage(item.getName())))
                .collect(Collectors.toList());
        return Result.success(result);
    }
}
