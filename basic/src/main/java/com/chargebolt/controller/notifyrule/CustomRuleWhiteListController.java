package com.chargebolt.controller.notifyrule;

import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.dao.notifyrule.NotifyWhitelistDao;
import com.chargebolt.dao.notifyrule.model.NotifyWhitelist;
import com.chargebolt.response.notifyrule.NotifyWhitelistRow;
import com.chargebolt.service.notifyrule.NotifyRuleService;

import cn.idev.excel.FastExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.read.listener.ReadListener;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import so.dian.mofa3.lang.domain.Result;

/**
 * 消息通知自定义规则的白名单
 */
@Tag(name = "消息通知自定义规则的白名单")
@Slf4j
@RestController
public class CustomRuleWhiteListController {

    @Resource
    private NotifyWhitelistDao notifyWhitelistDao;
    @Resource
    private NotifyRuleService notifyRuleService;

    /**
     * 下载白名单上传模板
     *
     * @param httpServletRequest HTTP请求
     * @param response HTTP响应
     */
    @Login
    @Operation(summary = "下载白名单上传模板")
    @PostMapping("customrule/whitelist/template/download")
    public void downloadTemplate(HttpServletRequest httpServletRequest, HttpServletResponse response) {
        log.info("开始下载白名单上传模板");
        
        // 定义模板文件名
        final String TEMPLATE_NAME = "whitelist_upload_template.xls";
        final String SHEET_NAME = "whitelist";
        
        // 设置响应头
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setStatus(207);
        
        try {
            String encodedFileName = URLEncoder.encode(TEMPLATE_NAME, StandardCharsets.UTF_8.name())
                    .replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        } catch (UnsupportedEncodingException e) {
            log.error("文件名编码失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件名编码失败", e);
        }

        // 写入Excel
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            FastExcel.write(outputStream, NotifyWhitelistRow.class)
                    .sheet(SHEET_NAME)
                    .doWrite(Collections.emptyList());
            outputStream.flush();
            log.info("白名单上传模板下载成功");
        } catch (IOException e) {
            log.error("生成Excel模板文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成Excel模板文件失败", e);
        }
    }

    @Login
    @Operation(summary = "上传白名单")
    @PostMapping("customrule/whitelist/excelImport")
    public Result<Boolean> excelImport(@RequestParam("file") MultipartFile file, @RequestParam("ruleId") Long ruleId,
            HttpServletResponse response)
            throws IOException {
        FastExcel.read(file.getInputStream(), NotifyWhitelistRow.class, new WhitelistDataListener(ruleId)).sheet()
                .doRead();
        return Result.success(true);
    }

    /**
     * 下载白名单数据
     *
     * @param httpServletRequest HTTP请求
     * @param ruleId 规则ID
     * @param response HTTP响应
     */
    @Login
    @Operation(summary = "下载白名单")
    @PostMapping("customrule/whitelist/data/download")
    public void downloadData(HttpServletRequest httpServletRequest, @RequestParam("ruleId") Long ruleId,
            HttpServletResponse response) {
        log.info("开始下载白名单数据，规则ID：{}", ruleId);
        
        // 参数校验
        if (ruleId == null) {
            throw new IllegalArgumentException("param is null");
        }

        // 构建文件名
        String fileName = String.format("whitelist_rule_%d.xls", ruleId);
        // 设置响应头
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            log.error("文件名编码失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件名编码失败", e);
        }
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setStatus(207);

        // 获取数据
        List<NotifyWhitelistRow> rowList = notifyRuleService.findWhiteListRowByRuleId(ruleId);
        if (rowList == null) {
            rowList = Collections.emptyList();
        }

        // 写入Excel
        try (OutputStream outputStream = response.getOutputStream()) {
            FastExcel.write(outputStream, NotifyWhitelistRow.class)
                    .sheet(fileName.replace(".xls", ""))
                    .doWrite(rowList);
            outputStream.flush();
            log.info("白名单数据下载成功，规则ID：{}，数据条数：{}", ruleId, rowList.size());
        } catch (IOException e) {
            log.error("生成Excel文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成Excel文件失败", e);
        }
    }

    @Login
    // 根据 ruleId 查询白名单中的数量
    @Operation(summary = "查询白名单数量")
    @GetMapping("customrule/whitelist/count")
    public Result<Integer> count(@RequestParam("ruleId") Long ruleId, HttpServletRequest httpServletRequest) {
        return Result.success(notifyRuleService.countWhiteListByRuleId(ruleId));
    }

    @Login
    // 删除白名单
    @Operation(summary = "清除白名单")
    @GetMapping("customrule/whitelist/clear")
    public Result<Boolean> clear(@RequestParam("ruleId") Long ruleId, HttpServletRequest httpServletRequest) {

        notifyRuleService.clearAllWhiteList(ruleId);
        return Result.success(true);
    }

    public class WhitelistDataListener implements ReadListener<NotifyWhitelistRow> {
        private Long ruleId;

        public WhitelistDataListener(Long ruleId) {
            this.ruleId = ruleId;
        }

        @Override
        public void invoke(NotifyWhitelistRow data, AnalysisContext context) {
            System.out.println("解析到一条数据" + JSON.toJSONString(data));
            NotifyWhitelist notifyWhitelist = new NotifyWhitelist();
            notifyWhitelist.setRuleId(ruleId);
            notifyWhitelist.setObjId(Long.parseLong(data.getShopId()));
            notifyWhitelist.setObjType(0);
            notifyWhitelist.setGmtCreate(System.currentTimeMillis());
            notifyWhitelist.setGmtUpdate(System.currentTimeMillis());
            notifyWhitelist.setDeleted(0);
            notifyWhitelistDao.insert(notifyWhitelist);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            System.out.println("所有数据解析完成！");
        }
    }
}
