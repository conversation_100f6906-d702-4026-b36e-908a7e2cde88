package com.chargebolt.controller.notifyrule;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.response.notifyrule.IndicatorListItemResp;
import com.chargebolt.service.notifyrule.IndicatorService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import so.dian.mofa3.lang.domain.Result;

@Tag(name = "消息通知自定义规则指标")
@Slf4j
@RestController
public class CustomerRuleIndicatorController {

    @Resource
    private IndicatorService indicatorService;

    @Login
    @GetMapping("/customrule/indicator/list")
    @Operation(summary = "指标选项列表")
    public Result<List<IndicatorListItemResp>> list(HttpServletRequest httpServletRequest) {
        return Result.success(indicatorService.list());
    }

    @Login
    @Operation(summary = "指标详情")
    @GetMapping("/customrule/indicator/detail/{indicatorId}")
    public Result<IndicatorListItemResp> detail(@PathVariable("indicatorId") Long indicatorId,
            HttpServletRequest httpServletRequest) {
        return Result.success(indicatorService.detail(indicatorId));
    }
}
