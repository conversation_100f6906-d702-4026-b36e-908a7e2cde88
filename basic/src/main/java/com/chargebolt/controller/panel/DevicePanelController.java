/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.controller.panel;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.commons.enums.DataScopeEnum;
import com.chargebolt.component.annotation.DataAuthorityScope;
import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.demeter.biz.service.DeviceStatisticService;
import so.dian.demeter.response.DeviceStatisticResponse;
import so.dian.demeter.response.DeviceUsageResponse;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;
import so.dian.talos.biz.service.ShopStatisticService;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DevicePanelController.java, v 1.0 2023-12-05 9:38 AM Exp $
 */
@Slf4j
@RestController
public class DevicePanelController extends BaseChargeboltController {
    @Autowired
    private DeviceStatisticService deviceStatisticService;
    @Autowired
    private ShopStatisticService shopStatisticService;

    /**
     * 设备数统计汇总
     *
     * @return
     */
    @Login
    @GetMapping(value = "/data/panel/deviceCount")
    public Result<DeviceStatisticResponse> deviceCount() {
        return template.execute(new ControllerCallback<DeviceStatisticResponse>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public DeviceStatisticResponse execute() {
                return deviceStatisticService.getDeviceStatistic(getUserDataAuthorityContext());
            }
        });
    }

    @Login
    @DataAuthorityScope(scope = DataScopeEnum.ONLY_ONESELF)
    @GetMapping(value = "/data/panel/deviceUsage")
    public Result<DeviceUsageResponse> deviceUsage() {
        return template.execute(new ControllerCallback<DeviceUsageResponse>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void buildContext() {
            }

            @Override
            public DeviceUsageResponse execute() {
                return deviceStatisticService.getDeviceUsage(getUserDataAuthorityContext());

            }
        });
    }
}