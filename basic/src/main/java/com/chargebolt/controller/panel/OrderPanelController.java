/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.controller.panel;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.commons.enums.DataCycleEnum;
import com.chargebolt.commons.enums.DataScopeEnum;
import com.chargebolt.component.annotation.DataAuthorityScope;
import com.chargebolt.service.order.request.OrderStatisticRequest;
import com.chargebolt.service.order.response.OrderStatisticsResponse;
import com.chargebolt.service.order.OrderStatisticsService;
import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.template.controller.ControllerCallback;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderPanelController.java, v 1.0 2023-12-05 9:38 AM Exp $
 */
@Slf4j
@RestController
public class OrderPanelController extends BaseChargeboltController {
    @Autowired
    private OrderStatisticsService orderStatisticsService;
    @Login
    @DataAuthorityScope(scope = DataScopeEnum.ONLY_ONESELF)
    @GetMapping(value = "/data/panel/orderPanel")
    public Result<OrderStatisticsResponse> orderPanel(@RequestParam("type") Integer type, @RequestParam(name = "time", required = false) Long time) {
        OrderStatisticRequest request= new OrderStatisticRequest();
        return template.execute(new ControllerCallback<OrderStatisticsResponse>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(type)){
                    throw new CheckParamException("type不能为空");
                }
                if(Objects.equals(DataCycleEnum.UNKNOWN.getCode(),DataCycleEnum.getById(type).getCode())){
                    throw new CheckParamException("type error");
                }
            }

            @Override
            public void buildContext() {
                request.setType(type);
                request.setTime(time);
            }

            @Override
            public OrderStatisticsResponse execute() {
                return orderStatisticsService.getOrderStatistics(request, getUserDataAuthorityContext());
            }
        });
    }
}