/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.controller.panel;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.commons.enums.DataCycleEnum;
import com.chargebolt.commons.enums.DataScopeEnum;
import com.chargebolt.component.annotation.DataAuthorityScope;
import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.template.controller.ControllerCallback;
import so.dian.talos.biz.service.ShopStatisticService;
import so.dian.talos.request.ShopStatisticRequest;
import so.dian.talos.response.ShopStatisticResponse;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopPanelController.java, v 1.0 2023-12-05 9:38 AM Exp $
 */
@Slf4j
@RestController
public class ShopPanelController extends BaseChargeboltController {
    @Autowired
    private ShopStatisticService shopStatisticService;

    @Login
    @DataAuthorityScope(scope = DataScopeEnum.ONLY_ONESELF)
    @GetMapping(value = "/data/panel/shopExpansionPanel")
    public Result<ShopStatisticResponse> shopExpansionPanel(@RequestParam("type") Integer type, @RequestParam(name = "time", required = false) Long time) {
        ShopStatisticRequest request= new ShopStatisticRequest();
        return template.execute(new ControllerCallback<ShopStatisticResponse>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(type)){
                    throw new CheckParamException("type不能为空");
                }
                if(Objects.equals(DataCycleEnum.UNKNOWN.getCode(),DataCycleEnum.getById(type).getCode())){
                    throw new CheckParamException("type error");
                }
            }

            @Override
            public void buildContext() {
                request.setType(type);
                request.setTime(time);
            }

            @Override
            public ShopStatisticResponse execute() {
                return shopStatisticService.getShopStatistic(request, getUserDataAuthorityContext());
            }
        });
    }
}