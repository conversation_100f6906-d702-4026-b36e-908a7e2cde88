/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.controller.shop;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.basic.request.shop.ShopOrdersRequest;
import com.chargebolt.basic.response.shop.ShopOrdersResponse;
import com.chargebolt.ezreal.response.shop.ShopDeviceDetailResponse;
import com.chargebolt.ezreal.response.shop.ShopOrderCombinationResponse;
import com.chargebolt.service.order.OrderStatisticsService;
import com.chargebolt.template.BaseChargeboltController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.common.constant.CommonConstants;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopOrdersController.java, v 1.0 2023-12-04 3:18 PM Exp $
 */
@Slf4j
@RestController
public class ShopOrdersController extends BaseChargeboltController {
    @Autowired
    private OrderStatisticsService orderStatisticsService;

    @PostMapping(value = "/shop/orderList")
    public Result<ShopOrdersResponse> orderList(@RequestBody ShopOrdersRequest request) {
        return template.execute(new ControllerCallback<ShopOrdersResponse>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(request.getShopId())) {
                    throw new CheckParamException("门店ID不能为空");
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public ShopOrdersResponse execute() {
                return orderStatisticsService.getShopOrders(request, getUserDataAuthorityContext());
            }
        });
    }


//    shop/getOrderInfo
//@Login
//@GetMapping(value = "/shop/getOrderInfo")
//@ResponseBody
//public BizResult getOrderInfo(@RequestParam("shopId") @NotNull(message = "shopId is null") Long shopId,
//                              @RequestParam("startTime") @NotNull(message = "startTime is null") String startTime,
//                              @RequestParam("endTime") @NotNull(message = "endTime is null") String endTime,
//                              @RequestParam(value = "pageNum", required = false) Integer pageNum) {
//    if (null == pageNum || pageNum <= CommonConstants.ZERO) {
//        pageNum = 1;
//    }
//    return erosShopFacade.getOrderInfo(getUser().getCooperatorId(), shopId, startTime, endTime, pageNum, ThreadLanguageHolder.getCurrentLang());
//}

    @Login
    @GetMapping(value = "/shop/getOrderInfo")
    public Result<ShopOrderCombinationResponse> getOrderInfo(@RequestParam("shopId") @NotNull(message = "shopId is null") Long shopId,
                              @RequestParam("startTime")  String startTime,
                              @RequestParam("endTime") String endTime,
                              @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum) {

        return template.execute(new ControllerCallback<ShopOrderCombinationResponse>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(shopId)) {
                    throw new CheckParamException("门店ID不能为空");
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public ShopOrderCombinationResponse execute() {
                Date startTimeDate;
                Date endTimeDate;
                if(StringUtils.isBlank(startTime)){
                    startTimeDate= new DateBuild().addToDay(-30).start().toDate();
                }else{
                    startTimeDate= DateUtil.strToDate(startTime, DateBuild.DATE_FORMAT);
                }

                if(StringUtils.isBlank(endTime)){
                    endTimeDate= new DateBuild().end().toDate();
                }else {
                    endTimeDate= DateUtil.strToDate(endTime, DateBuild.DATE_FORMAT);
                }
                return orderStatisticsService.getShopOrderCombination(shopId, startTimeDate, endTimeDate, pageNum);
            }
        });
    }

    @Login
    @GetMapping(value = "/shop/getdeviceInfo")
    public Result<List<ShopDeviceDetailResponse>> getdeviceInfo(@RequestParam("shopId") @NotNull(message = "shopId is null") Long shopId) {
        return template.execute(new ControllerCallback<List<ShopDeviceDetailResponse>>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(shopId)) {
                    throw new CheckParamException("门店ID不能为空");
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public List<ShopDeviceDetailResponse> execute() {
                return orderStatisticsService.listShopDeviceStatistics(shopId);
            }
        });
    }

}