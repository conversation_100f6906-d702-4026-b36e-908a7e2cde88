/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.convert.common;

import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.ezreal.response.agent.AgentSimpleResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentConverter.java, v 1.0 2024-10-11 下午6:36 Exp $
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AgentConverter {
    AgentConverter INSTANCE = Mappers.getMapper(AgentConverter.class);

    @Mapping(source = "agent.id", target = "id")
    @Mapping(source = "baseConfig.regionalCode", target = "regionalCode")
    @Mapping(source = "baseConfig.defaultLang", target = "defaultLang")
    @Mapping(source = "baseConfig.currencyCode", target = "currencyCode")
    AgentSimpleResponse agentSimpleResponseConvert(Agent agent, AgentBaseConfig baseConfig);

}