/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.convert.common;

import com.chargebolt.dao.aeacus.model.VersionTemplate;
import com.chargebolt.response.aeacus.VersionTemplateResponse;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VersionTemplateConverter.java, v 1.0 2024-09-30 下午4:18 Exp $
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface VersionTemplateConverter {
    VersionTemplateConverter INSTANCE = Mappers.getMapper(VersionTemplateConverter.class);

    VersionTemplateResponse versionTemplateConvert(VersionTemplate model);
}