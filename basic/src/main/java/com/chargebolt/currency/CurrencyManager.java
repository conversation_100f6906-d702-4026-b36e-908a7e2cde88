/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.currency;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.mofa3.lang.money.CurrencyEnum;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: CurrencyManager.java, v 1.0 2023-10-30 10:07 AM Exp $
 */
@Slf4j
@Component
public class CurrencyManager {
    @Value("${hermes.currency}")
    private String currencyCode;


    /**
     * 指定code获取币种
     *
     * @param code
     * @return
     */
    public CurrencyEnum getCurrency(String code) {
        CurrencyEnum currencyEnum = CurrencyEnum.getByCurrencyCode(code);
        if (Objects.isNull(currencyEnum)) {
            return CurrencyEnum.getByCurrencyCode(currencyCode);
        }
        return currencyEnum;
    }
}