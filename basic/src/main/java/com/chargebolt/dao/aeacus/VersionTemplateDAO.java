/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.aeacus;

import com.chargebolt.dao.aeacus.model.VersionTemplate;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: VersionTemplateDAO.java, v 0.1 2024-08-30 16:10:19 Exp $
 */
@Mapper
public interface VersionTemplateDAO {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<VersionTemplate>     返回结果
     */
    List<VersionTemplate> listRecord(VersionTemplate model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return VersionTemplate     返回结果
     */
    VersionTemplate getRecord(VersionTemplate model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(VersionTemplate model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(VersionTemplate model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(VersionTemplate model);


}