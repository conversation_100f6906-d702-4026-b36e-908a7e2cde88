/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.aeacus.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * role_sources_mapping
 *
 * <AUTHOR>
 * @version $Id: RoleSourcesMapping.java, v 0.1 2024-08-30 16:10:03 Exp $
 */
@Data
public class RoleSourcesMapping implements Serializable{
    /** serialVersionUID */
    private static final long serialVersionUID = 178886236344780094L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间(ms)
     */
    private Long gmtCreate;

    /**
     * 更新时间(ms)
     */
    private Long gmtUpdate;
}