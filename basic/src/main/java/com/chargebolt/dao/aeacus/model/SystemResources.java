/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.aeacus.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * system_resources
 *
 * <AUTHOR>
 * @version $Id: SystemResources.java, v 0.1 2024-08-30 16:10:08 Exp $
 */
@Data
public class SystemResources {
    /** serialVersionUID */
    private static final long serialVersionUID = 176413648990353333L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 父级资源code
     */
    private String parentCode;

    /**
     * 资源code
     */
    private String code;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源类型 1菜单 2功能
     */
    private Integer resourceType;

    /**
     * 菜单logo
     */
    private String icon;

    /**
     * 页面地址
     */
    private String url;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}