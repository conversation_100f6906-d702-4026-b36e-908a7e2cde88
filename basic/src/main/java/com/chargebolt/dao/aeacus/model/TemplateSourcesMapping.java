/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.aeacus.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * template_sources_mapping
 *
 * <AUTHOR>
 * @version $Id: TemplateSourcesMapping.java, v 0.1 2024-08-30 16:10:14 Exp $
 */
@Data
public class TemplateSourcesMapping {
    /** serialVersionUID */
    private static final long serialVersionUID = 177745412718234907L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 版本模板ID
     */
    private Long templateId;

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}