/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.aeacus.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * version_template
 *
 * <AUTHOR>
 * @version $Id: VersionTemplate.java, v 0.1 2024-08-30 16:10:19 Exp $
 */
@Data
public class VersionTemplate implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 172248126728537916L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 版本模板名称
     */
    private String name;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}