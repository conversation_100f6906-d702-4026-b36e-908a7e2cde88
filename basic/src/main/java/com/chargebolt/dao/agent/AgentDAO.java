/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.agent;

import java.util.List;

import com.chargebolt.agent.dto.AgentQueryDTO;
import com.chargebolt.component.annotation.DataAuthoritySql;
import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.agent.model.ResourceAgentDO;
import com.chargebolt.dao.agent.model.ResourceAgentQueryDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: AgentDAO.java, v 0.1 2024-03-19 16:36:59 Exp $
 */
@Mapper
public interface AgentDAO {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<Agent>     返回结果
     */
    List<Agent> listRecord(Agent model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return Agent     返回结果
     */
    Agent getRecord(Agent model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(Agent model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(Agent model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(Agent model);

    /**
     * 分页查询代理商数据
     */
    @DataAuthoritySql(conditionField = {"a.parent_id","a.seller_id"})
    List<Agent> queryList(AgentQueryDTO dto);

    /**
     * 根据id查询代理商
     * @param ids 代理商id
     * @return
     */
    List<Agent> queryListByIds(List<Long> ids);

    List<ResourceAgentDO> listSourceAgentRecord(ResourceAgentQueryDO model);
}