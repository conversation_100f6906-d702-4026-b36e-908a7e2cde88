/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.agent.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * agent
 *
 * <AUTHOR>
 * @version $Id: Agent.java, v 0.1 2024-03-19 16:36:59 Exp $
 */
@Data
public class Agent {
    /** serialVersionUID */
    private static final long serialVersionUID = 171666366503593618L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 联系人名称
     */
    private String contractName;

    /**
     * 区号
     */
    private String nationCode;

    /**
     * 联系人电话
     */
    private String contractMobile;

    /**
     * 省code
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市code
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 状态 1启用 2禁用
     */
    private Integer state;

    /**
     * 分成状态
     * NOT_COOPERATING(1, "未合作"),
     * COOPERATING(2, "已合作"),
     * TERMINATED(3, "已终止"),
     */
    private Integer openState;

    /**
     * 上级代理商ID
     */
    private Long parentId;

    /**
     * 负责人ID
     */
    private Long sellerId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;

    // =======扩展查询========
    private String content;

    /**
     * 负责人ID
     */
    private String sellerName;

    private List<Long> agentIds;
}