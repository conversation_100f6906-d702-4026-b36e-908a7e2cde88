/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.agent.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.mofa3.template.model.BaseModel;

/**
 * agent_base_config
 *
 * <AUTHOR>
 * @version $Id: AgentBaseConfig.java, v 0.1 2024-09-06 15:23:35 Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentBaseConfig extends BaseModel<AgentBaseConfig> {
    /** serialVersionUID */
    private static final long serialVersionUID = 170745531215772675L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 服务费费率，百分比值
     */
    private Double serviceFeeRate;

    /**
     * 国家地区代码
     */
    private String regionalCode;

    /**
     * 官方使用语言，BCP 47值
     */
    private String defaultLang;

    /**
     * 币种代码
     */
    private String currencyCode;
    private String regionalCurrency;

    /**
     * 时区 UTC
     */
    private String zoneUtc;

    private String timeZone;

    /**
     * 常用时间格式
     */
    private String dateFormat;

    /**
     * 软件版本ID
     */
    private Long sysVersion;

    /**
     * 代理商层级，越高层级越深
     */
    private Integer agentLevel;
}