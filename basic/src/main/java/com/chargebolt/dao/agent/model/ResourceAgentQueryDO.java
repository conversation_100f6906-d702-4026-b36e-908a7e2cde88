/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.agent.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ResourceAgentDO.java, v 1.0 2024-09-29 下午6:02 Exp $
 */
@Data
public class ResourceAgentQueryDO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409273180244L;

    /**
     * 代理商ID，指定ID查询
     */
    private Long agentId;
    private List<Long> parentAgentIds;
    /**
     * 代理商名称
     */
    private String agentName;
    /**
     * 手机号
     */
    private String contractMobile;

    /**
     * 合作状态
     *
     * 1.未合作
     * 2.已合作
     * 3.已终止
     */
    private Integer cooperatingState;
    /**
     * 代理商层级
     * 1.一级代理商
     * 2.二级代理商
     * >=3.多级代理商
     */
    private Integer agentLevel;
    private Long parentId;
}