/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.common.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * regionals
 *
 * <AUTHOR>
 * @version $Id: Regionals.java, v 0.1 2024-08-30 16:41:08 Exp $
 */
@Data
public class Regionals {
    /** serialVersionUID */
    private static final long serialVersionUID = 170078285298174070L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称 没有对应语种的默认显示
     */
    private String name;

    /**
     * 官方使用语言
     */
    private String defaultLang;

    /**
     * 国家地区代码
     */
    private String regionalCode;

    /**
     * 国家地区区号
     */
    private String phoneCode;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 国旗emoji图标
     */
    private String regionalEmoji;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}