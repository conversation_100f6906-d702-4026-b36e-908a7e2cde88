package com.chargebolt.dao.notifyrule;

import com.chargebolt.dao.notifyrule.model.NotifyRule;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface NotifyRuleDao {

    NotifyRule findById(Long id);

    void insert(NotifyRule notifyRule);

    void update(NotifyRule notifyRule);

    List<NotifyRule> findAll();

    List<NotifyRule> listRecord(NotifyRule model);

    void updateLastScheduleTime(@Param("lastScheduleTime") LocalDateTime lastScheduleTime, @Param("ruleId") Long ruleId,
                                @Param("gmtUpdate") Long gmtUpdate);

    int countByAgentId(@Param("agentId") Long agentId);
}
