package com.chargebolt.dao.notifyrule;

import com.chargebolt.dao.notifyrule.model.NotifyWhitelist;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface NotifyWhitelistDao {

    void insert(NotifyWhitelist notify<PERSON>hitelist);

    void batchInsert(List<NotifyWhitelist> notifyWhitelists);

    /**
     * 清除指定规则下所有的白名单
     * @param ruleId
     */
    void clearAll(@Param("ruleId") Long ruleId);

    /**
     * 查询指定规则下所有的白名单
     * @param ruleId
     * @return
     */
    List<NotifyWhitelist> findByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 查询指定规则下白名单的数量
     * @param ruleId
     * @return
     */
    Integer countByRuleId(@Param("ruleId") Long ruleId);
}
