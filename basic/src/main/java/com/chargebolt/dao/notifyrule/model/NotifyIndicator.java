package com.chargebolt.dao.notifyrule.model;

import lombok.Data;

@Data
public class NotifyIndicator {
    /**
     * 指标项 ID
     */
    private Long id;

    /**
     * 指标项 key
     */
    private String indicatorKey;

    /**
     * 指标项名称
     */
    private String nameI18n;

    /**
     * 指标口径
     */
    private String caliberI18n;

    /**
     * 指标描述
     */
    private String descriptionI18n;

    /**
     * 支持的运算符，["eq","nq","lt","gt"]
     */
    private String operators;

    /**
     * 比较值，json
     * @see com.chargebolt.dao.notifyrule.value.IndicatorValue
     */
    private String value;

    private Long gmtCreate;

    private Long gmtUpdate;

    private Integer deleted;

}
