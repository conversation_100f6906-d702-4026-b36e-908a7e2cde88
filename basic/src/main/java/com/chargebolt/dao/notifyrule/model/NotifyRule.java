package com.chargebolt.dao.notifyrule.model;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class NotifyRule {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 规则类型
     */
    private Integer type;

    /**
     * 状态，0：停用，1：启用
     */
    private Integer status;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 执行频率，例如每天，每周，每月
     */
    private Integer schedule;

    /**
     * 上次执行时间
     */
    private LocalDateTime lastScheduleTime;

    /**
     * 下发策略，寻找接收人的方式。例如寻找门店BD
     */
    private Integer targetStrategy;

    /**
     * List<List<RuleCondition>>
     * @see com.chargebolt.response.notifyrule.RuleCondition
     */
    private String conditions;

    /**
     * 消息模板code
     */
    private String templateCode;

    /**
     * 重置源标签
     */
    private Integer resetSourceTag;

    private Long gmtCreate;

    private Long gmtUpdate;

    private Integer deleted;
}
