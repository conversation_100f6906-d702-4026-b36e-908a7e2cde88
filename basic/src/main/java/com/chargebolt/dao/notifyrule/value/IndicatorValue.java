package com.chargebolt.dao.notifyrule.value;

import com.chargebolt.response.notifyrule.IndicatorOption;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class IndicatorValue {

    /**
     * 值类型：int,string,select
     */
    @Schema(description = "值类型：number,string,select")
    private String type;

    @Schema(description = "候选值")
    private String placeholder;

    @Schema(description = "最小值,number有效")
    private String minValue;

    @Schema(description = "最大值,number有效")
    private String maxValue;

    @Schema(description = "最小长度,string有效")
    private String minLength;

    @Schema(description = "最大长度,string有效")
    private String maxLength;

    @Schema(description = "可选值,select有效")
    private List<IndicatorOption> options;
}
