/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.dao.pg;

import com.chargebolt.dao.pg.model.OrderDataStatisticsModel;
import com.chargebolt.dao.pg.model.OrderStatisticsModel;
import com.chargebolt.dao.pg.model.ShopOrderDO;
import com.chargebolt.dao.pg.model.ShopOrderModel;
import com.chargebolt.dao.pg.model.ShopOrderStatisticsDO;
import com.chargebolt.service.order.response.GenericOrderStatisticsResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderStatisticsDAO.java, v 1.0 2023-12-01 4:53 PM Exp $
 */
@Mapper
public interface OrderStatisticsDAO {

    GenericOrderStatisticsResponse getGenericOrderStatistics(OrderStatisticsModel model);

    /**
     * 门店订单列表
     *
     * @param model
     * @return
     */
    List<ShopOrderDO> shopOrderList(ShopOrderModel model);

    /**
     * 门店订单已支付、已退款金额统计
     *
     * @param model
     * @return
     */
    ShopOrderStatisticsDO getShopOrderStatistics(ShopOrderModel model);

    /**
     * 查询时间段内产生订单的门店
     * 用于做数据快照生成，无限制条数返回
     *
     * @param startCreateTime
     * @param endCreateTime
     * @return
     */
    List<Long> getShopIds(@Param("startCreateTime") Date startCreateTime, @Param("endCreateTime") Date endCreateTime);
    /**
     * 统计订单数
     * 用于按状态统计订单数
     * 适用于统计订单总数、支付成功订单总数、退款订单总数、0元订单总数
     */
    ShopOrderStatisticsDO getOrderCountAndAmount(OrderDataStatisticsModel model);

    /**
     * 根据设备维度统计订单数
     *
     * @param model
     * @return
     */
    List<ShopOrderStatisticsDO> getOrderCountByDevices(OrderDataStatisticsModel model);
    /**
     * 根据门店维度统计订单数
     *
     * @param model
     * @return
     */
    List<ShopOrderStatisticsDO> getOrderCountByShopIds(OrderDataStatisticsModel model);
}