/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.dao.pg.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderStatisticsModel.java, v 1.0 2023-12-01 4:59 PM Exp $
 */
@Data
public class OrderDataStatisticsModel implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202312335165954L;
    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 门店集合
     */
    private List<Long> shopIds;

    /**
     * 商户集合
     */
    private List<Long> merchantIds;
    /**
     * 员工ID
     */
    private Long sellerId;
    private List<Long> sellerIds;

    private List<String> deviceNos;

    /**
     * 创建开始时间
     */
    private Date startCreateTime;
    /**
     * 创建结束时间
     */
    private Date endCreateTime;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 支付金额大于0
     * 组合orderStatus=5
     */
    private Boolean payOrderGT0;

    /**
     * 封顶价订单
     */
    private Boolean cappingOrder;

}