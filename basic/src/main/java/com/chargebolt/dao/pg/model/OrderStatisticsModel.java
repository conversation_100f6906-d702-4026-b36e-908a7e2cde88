/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.dao.pg.model;

import lombok.Data;
import so.dian.hermes.client.pojo.enums.OrderStatusEnum;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderStatisticsModel.java, v 1.0 2023-12-01 4:59 PM Exp $
 */
@Data
public class OrderStatisticsModel implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202312335165954L;
    /**
     * 门店集合
     */
    private List<Long> shopIds;
    /**
     * 开始时间
     * 根据统计类型选择sql条件
     */
    private Date startTime;
    /**
     * 结束时间
     * 根据统计类型选择sql条件
     */
    private Date endTime;

    /**
     * 状态
     * 5支付
     * 7退款
     * {@link OrderStatusEnum}
     */
    private Integer status;

}