/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.dao.pg.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopOrderModel.java, v 1.0 2023-12-01 4:59 PM Exp $
 */
@Data
public class ShopOrderDO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1712023198536520804L;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 支付金额
     */
    private Long payAmount;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 借出时间
     */
    private Date loanTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 订单时间
     */
    private Date orderTime;
    /**
     * 设备编号
     */
    private String loanBoxNo;
    /**
     * 充电宝编号
     */
    private String powerbankNo;
    /**
     * 归还时间
     */
    private Date returnTime;
    /**
     * 币种代码
     */
    private String currency;
    /**
     * 借出门店ID
     */
    private Long loanShopId;
}