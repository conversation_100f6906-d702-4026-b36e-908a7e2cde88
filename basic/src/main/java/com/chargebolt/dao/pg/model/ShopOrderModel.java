/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.dao.pg.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopOrderModel.java, v 1.0 2023-12-01 4:59 PM Exp $
 */
@Data
public class ShopOrderModel implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1712023123865329804L;
    /**
     * 门店ID
     */
    private Long shopId;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 借出时间-起（毫秒时间戳）
     */
    private Date loanStartTime;
    /**
     * 借出时间-止（毫秒时间戳）
     */
    private Date loanEndTime;
    private List<Long> loanShopIds;

}