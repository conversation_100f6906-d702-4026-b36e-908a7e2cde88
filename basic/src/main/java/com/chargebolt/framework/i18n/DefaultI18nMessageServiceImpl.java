package com.chargebolt.framework.i18n;

import java.util.Locale;

import javax.annotation.Resource;

import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.LocaleResolver;

import com.chargebolt.commons.enums.MultilingualEnum;

import so.dian.eros.interceptor.ThreadLanguageHolder;

@Component
public class DefaultI18nMessageServiceImpl implements I18nMessageService {

    @Resource
    private MessageSource messageSource;
    @Resource
    private LocaleResolver localeResolver;

    @Override
    public String getMessage(String code, Locale locale, Object... args) {
        try {
            return messageSource.getMessage(code, args, locale);
        } catch (NoSuchMessageException e) {
            // 如果找不到消息，返回null，由调用方处理
            return null;
        }
    }

    @Override
    public Locale getCurrentLocale() {
        String langId = ThreadLanguageHolder.getCurrentLang();
        MultilingualEnum langEnum = MultilingualEnum.findLanguageEnum(langId);
        if (langEnum == null) {
            return Locale.getDefault();
        }
        return langEnum.getLocale();
    }
}
