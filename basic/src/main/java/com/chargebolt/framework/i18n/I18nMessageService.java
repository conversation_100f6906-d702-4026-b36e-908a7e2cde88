package com.chargebolt.framework.i18n;

import java.util.Locale;

public interface I18nMessageService {
    /**
     * 获取国际化消息
     * 
     * @param code   错误码
     * @param locale 语言环境
     * @param args   消息参数
     * @return 国际化后的消息
     */
    String getMessage(String code, Locale locale, Object... args);

    /**
     * 获取当前线程的语言环境
     * 
     * @return 当前语言环境
     */
    Locale getCurrentLocale();
}
