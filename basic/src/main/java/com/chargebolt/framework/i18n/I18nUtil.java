package com.chargebolt.framework.i18n;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.commons.enums.MultilingualEnum;

import lombok.extern.slf4j.Slf4j;
import so.dian.eros.interceptor.ThreadLanguageHolder;

@Slf4j
@Component
public class I18nUtil {
    @Resource
    private MessageSource messageSource;

    @Resource
    private HttpServletRequest request;

    public String getMessage(String code) {
        return getMessage(code, null);
    }

    public String getMessage(String code, Object[] args) {
        String langId = ThreadLanguageHolder.getCurrentLang();
        MultilingualEnum langEnum = MultilingualEnum.findLanguageEnum(langId);
        log.info("I18nUtil-getMessage, langId=[{}], code=[{}], langEnum=[{}]", langId, code,
                JSON.toJSONString(langEnum));
        return messageSource.getMessage(code, args, langEnum.getLocale());
    }

    public String getMessage(I18nEnumInterface i18nEnum) {
        String msg = getMessage(i18nEnum.getI18nCode());
        if (msg == null) {
            return i18nEnum.getDefaultMsg();
        }
        return msg;
    }
}
