package com.chargebolt.merchant.rds;/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import so.dian.eros.pojo.dto.MerchantUserRelationDTO;

import java.util.List;

/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: MerchantUserRelationDTODAO.java, v 0.1 2023-12-14 14:51:49 Exp $
 */

@Mapper
public interface MchUserRelationDAO {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<MerchantUserRelationDTO>     返回结果
     */
    List<MerchantUserRelationDTO> listRecord(MerchantUserRelationDTO model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return MerchantUserRelationDTO     返回结果
     */
    MerchantUserRelationDTO getRecord(MerchantUserRelationDTO model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(MerchantUserRelationDTO model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(MerchantUserRelationDTO model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(MerchantUserRelationDTO model);

    List<MerchantUserRelationDTO> queryByIds(@Param("ids") List<Long> ids);
}