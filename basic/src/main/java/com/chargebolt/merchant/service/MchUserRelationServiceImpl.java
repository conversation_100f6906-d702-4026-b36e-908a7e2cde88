/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.merchant.service;

import com.chargebolt.merchant.rds.MchUserRelationDAO;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.eros.pojo.dto.MerchantUserRelationDTO;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: MchUserRelationServiceImpl.java, v 0.1 2023-12-14 14:51:49 Exp $
 */
@Service
public class MchUserRelationServiceImpl implements MchUserRelationService {
    @Override
    public List<MerchantUserRelationDTO> listRecord(MerchantUserRelationDTO model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return merchantUserRelationDAO.listRecord(model);
    }

    @Override
    public MerchantUserRelationDTO getRecord(MerchantUserRelationDTO model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return merchantUserRelationDAO.getRecord(model);
    }

    @Override
    public int saveRecord(MerchantUserRelationDTO model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return merchantUserRelationDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(MerchantUserRelationDTO model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return merchantUserRelationDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(MerchantUserRelationDTO model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return merchantUserRelationDAO.updateRecord(model);
    }


    /**
     * 推荐使用构造器注入
     */
    private final MchUserRelationDAO merchantUserRelationDAO;
    public MchUserRelationServiceImpl(ObjectProvider<MchUserRelationDAO> merchantUserRelationProvider) {
        this.merchantUserRelationDAO= merchantUserRelationProvider.getIfUnique();
    }
}