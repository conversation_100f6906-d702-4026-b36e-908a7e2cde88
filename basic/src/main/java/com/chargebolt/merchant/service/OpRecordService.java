/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.merchant.service;


import so.dian.eros.pojo.dto.OperateRecordDTO;

import java.util.List;

/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: OperateRecordService.java, v 0.1 2023-12-14 14:51:55 Exp $
 */

public interface OpRecordService{
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<OperateRecord>     返回结果
     */
    List<OperateRecordDTO> listRecord(OperateRecordDTO model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return OperateRecord     返回结果
     */
    OperateRecordDTO getRecord(OperateRecordDTO model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(OperateRecordDTO model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(OperateRecordDTO model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(OperateRecordDTO model);


}