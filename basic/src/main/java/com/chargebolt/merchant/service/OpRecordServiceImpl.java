/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.merchant.service;

import com.chargebolt.merchant.rds.OpRecordDAO;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.eros.pojo.dto.OperateRecordDTO;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: OpRecordServiceImpl.java, v 0.1 2023-12-14 14:51:55 Exp $
 */
@Service
public class OpRecordServiceImpl implements OpRecordService {
    @Override
    public List<OperateRecordDTO> listRecord(OperateRecordDTO model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return operateRecordDAO.listRecord(model);
    }

    @Override
    public OperateRecordDTO getRecord(OperateRecordDTO model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return operateRecordDAO.getRecord(model);
    }

    @Override
    public int saveRecord(OperateRecordDTO model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return operateRecordDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(OperateRecordDTO model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return operateRecordDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(OperateRecordDTO model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return operateRecordDAO.updateRecord(model);
    }


    /**
     * 推荐使用构造器注入
     */
    private final OpRecordDAO operateRecordDAO;

    public OpRecordServiceImpl(ObjectProvider<OpRecordDAO> operateRecordProvider) {
        this.operateRecordDAO = operateRecordProvider.getIfUnique();
    }
}