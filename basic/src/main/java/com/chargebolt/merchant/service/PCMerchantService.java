package com.chargebolt.merchant.service;

import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.basic.request.merchant.ReAllocateUserParam;
import com.chargebolt.context.UserDataAuthorityContext;
import so.dian.eros.pojo.dto.AllocateUserDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PCMerchantService {
    void reAllocateUser(ReAllocateUserParam reAllocateUserParam, OssUserDTO ossUserDTO);

    List<AllocateUserDTO> queryAllocateUsersByName(String principalName, Long currentPrincipalId, OssUserDTO ossUserDTO, UserDataAuthorityContext authorityContext);
}
