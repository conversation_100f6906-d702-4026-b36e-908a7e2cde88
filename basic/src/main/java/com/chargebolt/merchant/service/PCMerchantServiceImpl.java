package com.chargebolt.merchant.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.basic.request.merchant.ReAllocateUserParam;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.enums.language.OpLogTypeEnum;
import com.chargebolt.component.rocketmq.SyncDataAuthorityProducer;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.merchant.enums.MchUserTypeEnum;
import com.chargebolt.merchant.enums.OpBizTypeEnum;

import lombok.extern.slf4j.Slf4j;
import so.dian.eros.pojo.dto.AllocateUserDTO;
import so.dian.eros.pojo.dto.MerchantUserRelationDTO;
import so.dian.eros.pojo.dto.OperateRecordDTO;
import so.dian.talos.biz.service.MerchantService;
import so.dian.talos.common.exception.MerchantErrorEnum;
import so.dian.talos.pojo.entity.MerchantDO;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PCMerchantServiceImpl implements PCMerchantService {

    @Resource
    private MchUserRelationService mchUserRelationService;
    @Resource
    private OpRecordService opRecordService;
    @Resource
    private UserManager userManager;
    @Resource
    private MerchantService merchantService;
    @Resource
    private SyncDataAuthorityProducer syncDataAuthorityProducer;

    /**
     * 商家管理-重新分配
     *
     * @param reAllocateUserParam 重新分配参数
     * @param ossUserDTO 操作人信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reAllocateUser(ReAllocateUserParam reAllocateUserParam, OssUserDTO ossUserDTO) {

        merchantService.checkEditAuth(reAllocateUserParam.getMerchantId());
        // 列表商家负责人变更为新负责人，并生成一条操作记录
        if (reAllocateUserParam.getPrincipalId() == null || reAllocateUserParam.getMerchantId() == null) {
            log.error("负责人信息为空，不做重新分配！");
            throw new I18nMessageException(MerchantErrorEnum.MERCHANT_PRINCIPAL_EMPTY);
        }
        //当前分配用户
        OssUserDO allocateUser = userManager.getById(reAllocateUserParam.getPrincipalId());
        if (Objects.isNull(allocateUser)) {
            log.error("负责人不存在，不做重新分配！");
            throw new I18nMessageException(MerchantErrorEnum.MERCHANT_PRINCIPAL_NOT_EXIST);
        }
        if (AeacusConstsnts.STATUS_DISABLE.equals(allocateUser.getStatus())) {
            log.error("负责人被禁用，不做重新分配！");
            throw new I18nMessageException(MerchantErrorEnum.MERCHANT_PRINCIPAL_DISABLE);
        }
        if (!ossUserDTO.getAgentId().equals(allocateUser.getAgentId())) {
            log.error("只能分配给所在公司的员工，无法跨公司分配");
            throw new I18nMessageException(MerchantErrorEnum.MERCHANT_PRINCIPAL_DISABLE);
        }
        //当前商户
        MerchantDO merchantDO = merchantService.queryById(reAllocateUserParam.getMerchantId());
        if (Objects.isNull(merchantDO)) {
            log.error("商户不存在，不做重新分配！");
            throw new I18nMessageException(MerchantErrorEnum.MERCHANT_NOT_EXIST);
        }

        MerchantUserRelationDTO userRelationDTO = new MerchantUserRelationDTO();
        userRelationDTO.setMerchantId(reAllocateUserParam.getMerchantId());
        userRelationDTO.setUserType(MchUserTypeEnum.PRINCIPAL.getCode());
        MerchantUserRelationDTO record = mchUserRelationService.getRecord(userRelationDTO);

        OperateRecordDTO opRecordDTO = new OperateRecordDTO();
        opRecordDTO.setUserId(reAllocateUserParam.getPrincipalId());
        opRecordDTO.setUserType(MchUserTypeEnum.PRINCIPAL.getCode());
        opRecordDTO.setOpType(OpLogTypeEnum.REALLOCATE.getValue());
        opRecordDTO.setBizType(OpBizTypeEnum.MERCHANT_INFO.getCode());
        opRecordDTO.setBizId(reAllocateUserParam.getMerchantId().toString());
        opRecordDTO.setOperatorId(ossUserDTO.getUserId());
        opRecordDTO.setOperatorName(ossUserDTO.getName());


        opRecordDTO.setCurrentInfo(allocateUser.getName());
        // 如果未分配负责人（预留给存量数据，目前新建自动绑定且没有解绑操作，不存在未分配的），则新建分配给当前用户，并添加记录
        if (record == null) {
            log.info("商户未分配负责人，新建分配！");
            MerchantUserRelationDTO saveModel = new MerchantUserRelationDTO();
            saveModel.setMerchantId(reAllocateUserParam.getMerchantId());
            saveModel.setUserType(MchUserTypeEnum.PRINCIPAL.getCode());
            saveModel.setUserId(reAllocateUserParam.getPrincipalId());
            saveModel.setCreatorId(reAllocateUserParam.getPrincipalId());
            saveModel.setUpdaterId(reAllocateUserParam.getPrincipalId());
            saveModel.setCooperaterId(allocateUser.getCooperatorId());
            mchUserRelationService.saveRecord(saveModel);

            opRecordDTO.setBeforeInfo(null);
            opRecordService.saveRecord(opRecordDTO);

        } else {
            // 如果已分配负责人，则修改负责人，添加记录
            if (record.getUserId().equals(reAllocateUserParam.getPrincipalId())) {
                log.warn("负责人与当前负责人相同，不做重新分配！");
                throw new I18nMessageException(MerchantErrorEnum.MERCHANT_PRINCIPAL_SAME);
            } else {
                MerchantUserRelationDTO updateModel = new MerchantUserRelationDTO();
                updateModel.setId(record.getId());
                updateModel.setMerchantId(reAllocateUserParam.getMerchantId());
                updateModel.setUserId(reAllocateUserParam.getPrincipalId());
                updateModel.setUpdaterId(ossUserDTO.getUserId());
                updateModel.setCooperaterId(allocateUser.getCooperatorId());
                mchUserRelationService.updateRecord(updateModel);

                OssUserDO userBeforeDO = userManager.getById(record.getUserId());
                opRecordDTO.setBeforeInfo(userBeforeDO.getName());
                opRecordService.saveRecord(opRecordDTO);
                log.info("商户负责人, oldUserId = {} 变更为新负责人: userId = {}", record.getUserId(), reAllocateUserParam.getPrincipalId());
            }
        }

        // 发送用户数据权限变动消息
        syncDataAuthorityProducer.sendMsg(Arrays.asList(allocateUser.getId(), Objects.nonNull(record)?record.getUserId():-1L));

    }

    @Override
    public List<AllocateUserDTO> queryAllocateUsersByName(String principalName, Long currentPrincipalId,
                                                          OssUserDTO ossUserDTO, UserDataAuthorityContext authorityContext) {
        List<AllocateUserDTO> result = new ArrayList<>();
        if (AuthorityLevelEnum.USER.getCode().equals(authorityContext.getAuthorityLevel())) {
            return result;
        }
        List<Long> agentIds = new ArrayList<>();
        if (AuthorityLevelEnum.AGENT.getCode().equals(authorityContext.getAuthorityLevel())
                && CollectionUtils.isNotEmpty(authorityContext.getQuery().getAgentIds())) {
            agentIds.addAll(authorityContext.getQuery().getAgentIds());
        }
        List<AllocateUserDTO> allocateUserDTOS = userManager.queryAllocateUsersByName(principalName, agentIds);
        if (CollectionUtils.isNotEmpty(allocateUserDTOS)) {
            result = allocateUserDTOS.stream().filter(allocateUserDTO -> !allocateUserDTO.getUserId().equals(currentPrincipalId))
                    .collect(java.util.stream.Collectors.toList());
        }
        return result;
    }

    public Boolean adminRole(OssUserDTO ossUserDTO){
        return ossUserDTO.getRoleDtos().stream()
                .anyMatch(ossRole -> Objects.equals(ossRole.getId(), AeacusConstsnts.SUPER_ROLE_ID));

    }
}
