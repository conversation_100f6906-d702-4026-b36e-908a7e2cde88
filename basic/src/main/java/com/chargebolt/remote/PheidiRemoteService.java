package com.chargebolt.remote;

import java.util.List;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.pheidi.dto.MapDTO;
import com.chargebolt.pheidi.request.MarkReadRequest;
import com.chargebolt.pheidi.request.MessageBody;
import com.chargebolt.pheidi.request.MessageQuery;
import com.chargebolt.pheidi.request.MessageTemplateCreate;
import com.chargebolt.pheidi.request.MessageTemplateEdit;
import com.chargebolt.pheidi.request.MessageTemplateQuery;
import com.chargebolt.pheidi.request.UnReadMessageCountQuery;
import com.chargebolt.pheidi.response.MessageGroupVo;
import com.chargebolt.pheidi.response.MessageListItemVo;
import com.chargebolt.pheidi.response.MessageTemplateDetailResp;
import com.chargebolt.pheidi.response.MessageTemplateListItemResp;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.remote.PheidiRemoteService.PheidiFallback;
import com.chargebolt.pheidi.request.MessageTemplateChangeStatus;


import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.mofa3.lang.domain.Result;

@FeignClient(name = "pheidi", url = "${remote.url.pheidi}",fallback = PheidiFallback.class)
public interface PheidiRemoteService {

    @PostMapping("/message-center/sendMessage")
    Result<Boolean> sendMessage(@RequestBody MessageBody param);

    @PostMapping("/message-center/read")
    Result<Boolean> read(@RequestBody MarkReadRequest param);

    @PostMapping("/message-center/unread/num")
    Result<Integer> num(@RequestBody UnReadMessageCountQuery param);

    @PostMapping("/message-center/group/list")
    Result<PageData<MessageGroupVo>> groupList(@RequestBody UnReadMessageCountQuery param);
    
    @PostMapping("/message-center/messages")
    Result<PageData<MessageListItemVo>> messages(@RequestBody MessageQuery param);

    @PostMapping("/message-template/create")
    Result<Long> create(@RequestBody MessageTemplateCreate param);

    @PostMapping("/message-template/edit")
    Result<Boolean> edit(@RequestBody MessageTemplateEdit param);

    @PostMapping("/message-template/list")
    Result<PageData<MessageTemplateListItemResp>> list(@RequestBody MessageTemplateQuery param);

    @GetMapping("/message-template/detail/{id}")
    Result<MessageTemplateDetailResp> detail(@PathVariable("id") Long id);
    
    @GetMapping("/message-template/templateCodeNameMap")
    Result<List<MapDTO>> templateCodeNameMap();

    @PostMapping("/message-template/changeStatus")
    Result<Boolean> changeStatus(@RequestBody MessageTemplateChangeStatus param);

    @GetMapping("/message-template/redirect-goal/select-items")
    Result<List<MapDTO>> redirectGoalSelectItems();

    @GetMapping("/message-template/channel-push/select-items")
    Result<List<MapDTO>> channelPushSelectItems();

    @Slf4j
    @Component
    public class PheidiFallback implements FallbackFactory<PheidiRemoteService> {
        @Override
        public PheidiRemoteService create(Throwable cause) {
            return new PheidiRemoteService() {

                @Override
                public Result<Boolean> read(MarkReadRequest param) {
                    log.error("|PheidiRemoteService fallback| read |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<Integer> num(UnReadMessageCountQuery param) {
                    log.error("|PheidiRemoteService fallback| num |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<PageData<MessageGroupVo>> groupList(UnReadMessageCountQuery param) {
                    log.error("|PheidiRemoteService fallback| groupList |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<PageData<MessageListItemVo>> messages(MessageQuery param) {
                    log.error("|PheidiRemoteService fallback| messages |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<Long> create(MessageTemplateCreate param) {
                    log.error("|PheidiRemoteService fallback| create |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<Boolean> edit(MessageTemplateEdit param) {
                    log.error("|PheidiRemoteService fallback| edit |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<PageData<MessageTemplateListItemResp>> list(MessageTemplateQuery param) {
                    log.error("|PheidiRemoteService fallback| list |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<MessageTemplateDetailResp> detail(Long id) {
                    log.error("|PheidiRemoteService fallback| detail |id:{}", id);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<Boolean> sendMessage(MessageBody param) {
                    log.error("|PheidiRemoteService fallback| sendMessage |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<List<MapDTO>> templateCodeNameMap() {
                    log.error("|PheidiRemoteService fallback| templateCodeNameMap");
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<Boolean> changeStatus(MessageTemplateChangeStatus param) {
                    log.error("|PheidiRemoteService fallback| changeStatus |param:{}", param);
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<List<MapDTO>> redirectGoalSelectItems() {
                    log.error("|PheidiRemoteService fallback| redirectGoalSelectItems");
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }

                @Override
                public Result<List<MapDTO>> channelPushSelectItems() {
                    log.error("|PheidiRemoteService fallback| channelPushSelectItems");
                    throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
                }
                
            };
        }
    
        
    }
}
