package com.chargebolt.request.aeacus;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色资源请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/9 14:14
 */
@Data
public class EditRoleRequest implements Serializable {
    private static final long serialVersionUID = 178886236344781194L;


    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 资源ID
     */
    private List<Long> resourceIds;

}
