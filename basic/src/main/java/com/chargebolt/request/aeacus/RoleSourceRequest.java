package com.chargebolt.request.aeacus;

import com.chargebolt.aeacus.dto.BasePageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 角色资源请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/9 14:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoleSourceRequest extends BasePageParam implements Serializable {
    private static final long serialVersionUID = 178886236344780094L;


    /**
     * 角色名称
     */
    private String roleName;



    /**
     * 代理商
     */
    private Long agentId;

}
