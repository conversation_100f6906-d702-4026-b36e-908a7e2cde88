package com.chargebolt.request.aeacus;

import com.chargebolt.dao.aeacus.model.SystemResources;
import lombok.Data;

/**
 * 资源管理请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/4 10:54
 */
@Data
public class SystemResourcesRequest implements java.io.Serializable {

    private static final long serialVersionUID = 17120240910541000L;

    /**
     * 主键
     */
    private Long  id;
    /**
     * 父级资源code
     */
    private String parentCode;

    /**
     * 资源code
     */
    private String code;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源类型 1菜单 2功能
     */
    private Integer resourceType;

    /**
     * 菜单logo
     */
    private String icon;

    /**
     * 页面地址
     */
    private String url;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 排序
     */
    private Integer sort;

    public  SystemResources toDO() {
        SystemResources systemResources = new SystemResources();
        systemResources.setId(this.id);
        systemResources.setParentCode(this.parentCode);
        systemResources.setCode(this.code);
        systemResources.setName(this.name);
        systemResources.setResourceType(this.resourceType);
        systemResources.setIcon(this.icon);
        systemResources.setUrl(this.url);
        systemResources.setNotes(this.notes);
        systemResources.setSort(this.sort);
        return systemResources;

    }

}
