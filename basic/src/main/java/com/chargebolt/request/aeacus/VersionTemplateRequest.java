package com.chargebolt.request.aeacus;

import com.chargebolt.response.aeacus.VersionTemplateResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 软件版本绑定资源请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/6 10:03
 */
@Data
public class VersionTemplateRequest implements Serializable {

    private static final long serialVersionUID = 17120240910541000L;

    /**
     * 资源ID
     */
    private Long resourceId;


    /**
     * 软件版本
     */
//    private List<VersionTemplateResponse> templateVersions;

    private List<Long> templateVersions;

}
