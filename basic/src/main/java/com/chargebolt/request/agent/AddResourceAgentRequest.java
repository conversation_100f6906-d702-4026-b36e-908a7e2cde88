/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.request.agent;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AddResourceAgentRequest.java, v 1.0 2024-03-20 2:05 PM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddResourceAgentRequest extends AddAgentRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1712024088636420L;
    /**
     * 服务费费率，百分比值
     */
    private Double serviceFeeRate;

    /**
     * 国家地区代码
     */
    private String regionalCode;

    /**
     * 官方使用语言，BCP 47值
     */
    private String defaultLang;

    /**
     * 币种代码
     */
    private String currencyCode;
    private String regionalCurrency;

    /**
     * 时区 UTC
     */
    private String zoneUtc;

    /**
     * 常用时间格式
     */
    private String dateFormat;

    /**
     * 软件版本ID
     */
    private Long sysVersion;
    /**
     * 附件
     */
    private List<String> files;
}