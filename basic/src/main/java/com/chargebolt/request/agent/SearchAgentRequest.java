/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.request.agent;

import com.chargebolt.aeacus.dto.BasePageParam;
import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AppAgentRequest.java, v 1.0 2024-03-19 6:17 PM Exp $
 */
@Data
public class SearchAgentRequest extends BasePageParam implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 17120240379181828L;
    /**
     * 查询条件指标
     *
     * 0.所有
     * 1.代理商名称
     * 2.代理商ID
     * 3.手机号码
     * 4.负责人
     */
    private Integer type;

    private String content;
    /**
     * 合作状态
     *
     * 1.未合作
     * 2.已合作
     * 3.已终止
     */
    private Integer cooperatingState;
    /**
     * 代理商账号是否启用
     *
     * 1.启用
     * 2.禁用
     */
    private Integer userAccountState;

    /**
     * 公司ID，指定ID查询
     */
    private Long agentId;
    /**
     * 查询所属代理下的数据
     */
    private Long parentId;
    /**
     * 公司名称
     */
    private String agentName;
    /**
     * 手机号
     */
    private String contractMobile;
    /**
     * 负责人ID
     */
    private Long sellerId;
    /**
     * 多级代理查询标记，二级及以下的代理商数据
     * 不包含本级
     */
    private Boolean multistageAgentFlag;
    /**
     * 使用DataAuthorityScope做数据鉴权
     */
    @Deprecated
    private boolean adminRole;


}