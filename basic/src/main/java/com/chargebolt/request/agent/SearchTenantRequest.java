/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.request.agent;

import com.chargebolt.aeacus.dto.BasePageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: QueryTenantRequest.java, v 1.0 2024-09-29 下午5:14 Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchTenantRequest extends BasePageParam implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409273171407L;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;
    private Long parentAgentId;
    /**
     * 手机号
     */
    private String contractMobile;

    /**
     * 合作状态
     *
     * 1.未合作
     * 2.已合作
     * 3.已终止
     */
    private Integer cooperatingState;
    /**
     * 代理商层级
     * 1.一级代理商
     * 2.二级代理商
     * >=3.多级代理商
     */
    private Integer agentLevel;

}