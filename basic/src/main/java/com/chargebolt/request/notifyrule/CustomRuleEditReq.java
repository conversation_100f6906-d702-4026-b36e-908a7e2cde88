package com.chargebolt.request.notifyrule;

import java.util.List;

import com.chargebolt.response.notifyrule.RuleCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "自定义规则编辑请求")
public class CustomRuleEditReq {

    @Schema(description = "规则ID")
    private Long ruleId;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "规则类型")
    private Integer ruleType;

    @Schema(description = "执行频率，例如每天，每周，每月")
    private Integer schedule;

    @Schema(description = "下发策略，寻找接收人的方式。例如寻找门店BD")
    private Integer targetStrategy;

    @Schema(description = "规则条件")
    private List<List<RuleCondition>> conditions;

    @Schema(description = "消息模板code")
    private String templateCode;
}
