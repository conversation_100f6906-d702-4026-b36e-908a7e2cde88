package com.chargebolt.request.notifyrule;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

@Data
@Tag(name = "CustomRuleEnableReq", description = "自定义规则启用和停用请求参数")
public class CustomRuleEnableReq {
    /**
     * 规则编号
     */
    @Schema(description = "规则编号")
    private Long ruleId;
    /**
     * 启用或是不启用
     */
    @Schema(description = "启用或是不启用")
    private Boolean status;
}
