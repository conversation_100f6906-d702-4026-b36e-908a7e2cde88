package com.chargebolt.request.notifyrule;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

@Data
@Tag(name = "CustomRuleEnableReq", description = "自定义规列表参数")
public class CustomRuleQuery {

    /**
     * 代理商 ID
     */
    @Schema(description = "代理商Id")
    private Long agentId;
    /**
     * offset
     */
    @Schema(description = "pageNo")
    private Integer pageNo;
    /**
     * pageSize
     */
    @Schema(description = "pageSize")
    private Integer pageSize;
    /**
     * 规则名称
     */
    @Schema(description = "规则类型")
    private Integer ruleType;
    /**
     * 规则状态
     */
    @Schema(description = "规则状态")
    private Integer status;
}
