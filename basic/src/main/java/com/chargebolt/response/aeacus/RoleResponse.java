/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.response.aeacus;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 角色信息返回类
 */
@Data
@Builder
public class RoleResponse implements Serializable{

    /** serialVersionUID */
    private static final long serialVersionUID = 178886236344780094L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;


    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 更新人
     */
    private String updater;


    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 创建人
     */
    private String creator;


    /**
     * 创建时间
     */
    private Long createTime;

}