package com.chargebolt.response.aeacus;

import com.chargebolt.dao.aeacus.model.SystemResources;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 资源管理返回类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/4 10:54
 */
@Data
public class SystemResourcesResponse implements Serializable {

    private static final long serialVersionUID = 17120240910541000L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 父级资源code
     */
    private String parentCode;

    /**
     * 父级资源Id
     */
    private Long parentId;
    /**
     * 父级资源名称
     */
    private String parentName;

    /**
     * 资源code
     */
    private String code;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源类型 1菜单 2功能
     */
    private Integer resourceType;

    /**
     * 菜单logo
     */
    private String icon;

    /**
     * 页面地址
     */
    private String url;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 排序
     */
    private Integer sort;


    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;

    /**
     * 是否选中
     */
    private boolean checked;

    private List<VersionTemplateResponse> versionTemplates = new ArrayList<>();
    /**
     * 子级资源
     */
    private List<SystemResourcesResponse> children = new ArrayList<>();


    public static final Comparator<SystemResourcesResponse> SORT_ORDER = Comparator.comparingInt(SystemResourcesResponse::getSort);

    public SystemResourcesResponse() {
    }

    public SystemResourcesResponse(SystemResources systemResources) {
        this.id = systemResources.getId();
        this.parentCode = systemResources.getParentCode();
        this.code = systemResources.getCode();
        this.name = systemResources.getName();
        this.resourceType = systemResources.getResourceType();
        this.icon = systemResources.getIcon();
        this.url = systemResources.getUrl();
        this.notes = systemResources.getNotes();
        this.sort = systemResources.getSort();
        this.gmtCreate = systemResources.getGmtCreate();
        this.gmtUpdate = systemResources.getGmtUpdate();
    }

}
