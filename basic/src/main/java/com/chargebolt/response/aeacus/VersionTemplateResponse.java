/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.response.aeacus;

import com.chargebolt.dao.aeacus.model.VersionTemplate;
import lombok.Data;

import java.io.Serializable;


@Data
public class VersionTemplateResponse implements Serializable {

    private static final long serialVersionUID = 172248126728537916L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 版本模板名称
     */
    private String name;

    /**
     * 是否有权限
     */
    private Boolean hasPermission;

    public VersionTemplateResponse () {

    }

    public VersionTemplateResponse (VersionTemplate versionTemplate,Boolean hasPermission) {
        this.id = versionTemplate.getId();
        this.name = versionTemplate.getName();
        this.hasPermission = hasPermission;
    }

}