/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.response.agent;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AppAgentResponse.java, v 1.0 2024-03-19 5:56 PM Exp $
 */
@Data
public class AppAgentResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 17120240379175642L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 联系人名称
     */
    private String contractName;

    /**
     * 区号
     */
    private String nationCode;

    /**
     * 联系人电话
     */
    private String contractMobile;

    /**
     * 省code
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市code
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 合作状态
     * 1未开通 2已开通
     */
    private Integer cooperatingState;
    private String cooperatingStateDesc;

    /**
     * 状态 1启用 2禁用
     */
    private Integer userAccountState;
    private String userAccountStateDesc;
    /**
     * 分成比例
     */
    private Integer rate;
    /**
     * 上级代理商ID
     */
    private Long parentId;

    /**
     * 负责人ID
     */
    private Long sellerId;

    private String sellerName;
    private String sellerMobile;

    private String regionalCode;
    private String adminAccounts;
}