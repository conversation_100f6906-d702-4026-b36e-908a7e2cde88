/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.response.agent;

import com.chargebolt.request.agent.AddAgentRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ResourceAgentDetailResponse.java, v 1.0 2024-03-20 2:05 PM Exp $
 */
@Data
public class ResourceAgentDetailResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1712024087537113L;

    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 联系人名称
     */
    private String contractName;

    /**
     * 区号
     */
    private String nationCode;

    /**
     * 联系人电话
     */
    private String contractMobile;

    /**
     * 省code
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市code
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 合作状态
     * 1未开通 2已开通
     */
    private Integer cooperatingState;
    private String cooperatingStateDesc;

    /**
     * 状态 1启用 2禁用
     */
    private Integer userAccountState;
    private String userAccountStateDesc;
    /**
     * 分成比例
     */
    private Integer rate;
    /**
     * 上级代理商ID
     */
    private Long parentId;

    /**
     * 负责人ID
     */
    private Long sellerId;

    private String sellerName;
    private String sellerMobile;

    private Long tenantId;
    /**
     * 服务费费率，百分比值
     */
    private Double serviceFeeRate;

    /**
     * 国家地区代码
     */
    private String regionalCode;
    private String regionalName;

    /**
     * 官方使用语言，BCP 47值
     */
    private String defaultLang;

    /**
     * 币种代码
     */
    private String currencyCode;
    private String regionalCurrency;

    /**
     * 时区 UTC
     */
    private String zoneUtc;

    /**
     * 常用时间格式
     */
    private String dateFormat;

    /**
     * 软件版本ID
     */
    private Long sysVersion;
    private String sysVersionName;

    private List<String> files;
    private String adminAccounts;
}