/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.response.agent;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TenantAgentResponse.java, v 1.0 2024-09-29 下午5:43 Exp $
 */
@Data
public class TenantAgentResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409273174327L;
    private Long tenantId;
    private Long agentId;
    private String agentName;
    private String parentAgentName;
    /**
     * 联系人名称
     */
    private String contractName;

    /**
     * 区号
     */
    private String nationCode;

    /**
     * 联系人电话
     */
    private String contractMobile;
    private Integer agentLevel;
    private String agentLevelDesc;
    /**
     * 合作状态
     * 1未开通 2已开通
     */
    private Integer cooperatingState;
    private String cooperatingStateDesc;
}