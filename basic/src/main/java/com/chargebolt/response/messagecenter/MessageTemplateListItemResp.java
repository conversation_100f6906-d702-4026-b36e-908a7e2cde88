package com.chargebolt.response.messagecenter;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.util.List;

@Data
@Tag(name = "消息模板列表项")
public class MessageTemplateListItemResp {
    @Schema(description = "模板id")
    private Long templateId;
    @Schema(description = "推送渠道")
    private List<String> channels;
    @Schema(description = "模板code")
    private String code;
    @Schema(description = "模板名称")
    private String name;
    @Schema(description = "业务类型")
    private Integer type;
    @Schema(description = "业务类型")
    private String typeStr;
    @Schema(description = "启用状态")
    private Integer status;
    @Schema(description = "启用状态")
    private String statusStr;
}
