package com.chargebolt.response.notifyrule;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.util.List;

@Data
@Tag(name = "CustomRuleDetailResp", description = "自定义规则详情响应参数")
public class CustomRuleDetailResp {
    /**
     * 代理商 ID
     */
    @Schema(description = "代理商 ID")
    private Long agentId;
    /**
     * 代理商公司名称
     */
    @Schema(description = "代理商公司名称")
    private String agentName;
    /**
     * 触发条件
     */
    @Schema(description = "触发条件")
    private List<List<RuleCondition>> conditions;
    /**
     * 规则编号
     */
    @Schema(description = "规则编号")
    private Long ruleId;
    /**
     * 规则名称
     */
    @Schema(description = "规则名称")
    private String ruleName;
    /**
     * 执行频率
     */
    @Schema(description = "执行频率")
    private Integer schedule;

    /**
     * 执行频率文案
     */
    @Schema(description = "执行频率文案")
    private String scheduleStr;
    /**
     * 规则状态
     */
    @Schema(description = "规则状态")
    private Integer status;
    /**
     * 规则状态
     */
    @Schema(description = "规则状态")
    private String statusStr;
    /**
     * 目标投递策略
     */
    @Schema(description = "目标投递策略")
    private  Integer targetStrategy;

    /**
     * 目标投递策略
     */
    @Schema(description = "目标投递策略")
    private String targetStrategyStr;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private String updateTime;

    /**
     * 消息模板Code
     */
    @Schema(description = "消息模板Code")
    private String templateCode;

    /**
     * 规则类型
     */
    @Schema(description = "规则类型")
    private Integer ruleType;
}
