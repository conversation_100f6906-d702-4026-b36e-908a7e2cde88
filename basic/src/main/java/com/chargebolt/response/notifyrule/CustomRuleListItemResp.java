package com.chargebolt.response.notifyrule;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

@Data
@Tag(name = "CustomRuleListItemResp", description = "自定义规则列表响应参数")
public class CustomRuleListItemResp {
    @Schema(description = "代理商ID")
    private Long agentId;
    @Schema(description = "代理商名称")
    private String agentName;
    @Schema(description = "规则ID")
    private Long ruleId;
    @Schema(description = "规则名称")
    private String ruleName;
    @Schema(description = "规则类型")
    private Integer ruleType;
    @Schema(description = "规则状态")
    private Integer status;
    @Schema(description = "规则状态")
    private String statusStr;
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
