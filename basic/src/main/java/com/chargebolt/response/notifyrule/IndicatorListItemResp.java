package com.chargebolt.response.notifyrule;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.util.List;

@Data
@Tag(name = "指标项")
public class IndicatorListItemResp {

    @Schema(description = "指标ID")
    private Long id;
    @Schema(description = "指标key")
    private String indicatorKey;
    @Schema(description = "指标名称")
    private String name;
    @Schema(description = "指标口径")
    private String caliber;
    @Schema(description = "指标说明")
    private String description;
    /**
     * eq,ne,gt,lt,gte,lte
     */
    @Schema(description = "运算符")
    private List<IndicatorOperator> operators;

    @Schema(description = "指标值")
    private IndicatorValue value;

}
