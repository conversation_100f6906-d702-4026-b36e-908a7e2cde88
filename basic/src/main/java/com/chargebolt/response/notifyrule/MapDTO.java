package com.chargebolt.response.notifyrule;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Tag(name = "MapDTO", description = "MapDTO")
@NoArgsConstructor
@AllArgsConstructor
public class MapDTO {

    @Schema(name = "id", description = "id")
    private Integer id;
    @Schema(name = "name", description = "name")
    private String name;
}
