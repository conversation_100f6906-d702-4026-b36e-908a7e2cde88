package com.chargebolt.response.notifyrule;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

@Data
@Tag(name = "RuleCondition", description = "规则条件")
public class RuleCondition {

    /**
     * 指标key
     */
    @Schema(description = "指标key")
    private String indicatorKey;
    /**
     * 运算符
     */
    @Schema(description = "运算符")
    private String operator;
    /**
     * 比较值
     */
    @Schema(description = "比较值")
    private String value;
}
