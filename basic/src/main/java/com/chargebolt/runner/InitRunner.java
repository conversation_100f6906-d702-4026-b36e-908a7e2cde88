/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.runner;

import com.chargebolt.task.ShopGeoTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import so.dian.talos.biz.redis.ShopTypeCache;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: InitRunner.java, v 1.0 2023-11-27 4:28 PM Exp $
 */
@Component
public class InitRunner implements ApplicationRunner {
    @Autowired
    private Environment environment;
    @Autowired
    private ShopTypeCache shopTypeCache;
    @Autowired
    private ShopGeoTask shopGeoTask;
    @Override
    public void run(final ApplicationArguments args) throws Exception {
        String profileName = getActiveProfiles(environment);
        if (Objects.equals(profileName, "local")) {
            return;
        }
        shopGeoTask.init();
    }
    private static String getActiveProfiles(Environment env) {
        String[] profiles = env.getActiveProfiles();
        if (profiles.length == 0) {
            return "NULL";
        }
        return profiles[0];
    }


}