/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.aeacus;

import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.dao.aeacus.model.RoleSourcesMapping;
import com.chargebolt.request.aeacus.EditRoleRequest;
import com.chargebolt.request.aeacus.RoleSourceRequest;
import com.chargebolt.response.aeacus.RoleResponse;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: RoleSourcesMappingService.java, v 0.1 2024-08-30 16:10:03 Exp $
 */

public interface RoleSourceMappingService {
    /**
     * listRecord 查询列表
     */
    List<RoleSourcesMapping> listRecord(RoleSourcesMapping model);


    /**
     * 获取角色列表
     *
     * @param request 请求参数
     * @return 角色列表
     */
    List<RoleResponse> getRoleList(RoleSourceRequest request);


    /**
     * 根据角色ID获取角色信息
     *
     * @param roleId 角色ID
     * @return
     */
    RoleResponse getRoleInfo(Long roleId);


    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model 实体model
     * @return RoleSourcesMapping     返回结果
     */
    RoleSourcesMapping getRecord(RoleSourcesMapping model);

    /**
     * saveRecord 记录保存
     *
     * @param model 实体model
     * @return insert条数（单条1）
     */
    int saveRecord(RoleSourcesMapping model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model 实体model
     * @return 逻辑删除数据条数
     */
    int removeRecord(RoleSourcesMapping model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model 实体model
     * @return updateRecord更新数据条数
     */
    int updateRecord(RoleSourcesMapping model);

    /**
     * 根据角色ID获取资源列表
     *
     * @param roleId 角色ID
     * @return 资源列表
     */
    List<RoleSourcesMapping> getByRoleId(Long roleId);

    /**
     * 新增角色资源
     *
     * @param request
     */
    void saveResource(EditRoleRequest request, OssUserDTO currentUser);

    /**
     * 编辑角色资源
     *
     * @param request 编辑角色资源请求
     */
    void updateResource(EditRoleRequest request, OssUserDTO currentUser);


    /**
     * 根据角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 删除结果
     */
    int removeByRoleId(Long roleId);

    /**
     * 根据资源ID删除角色
     *
     * @param resourceId 资源ID
     * @return 删除结果
     */
    int removeByResourceId(Long resourceId);


    /**
     * 根据角色ID获取角色信息
     *
     * @param id 角色ID
     * @return 角色信息
     */
    RoleDO getRoleById(Long id);

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<OssUserRoleMappingDO> getUserRoleMapping(Long userId);

    /**
     * 根据角色ID角色资源关系
     *
     * @param roleIds 角色ID
     * @return 角色资源关系
     */
    List<RoleSourcesMapping> getRoleSources(List<Long> roleIds);

    int saveBatchRecord(List<RoleSourcesMapping> roleSourcesMappings);
}