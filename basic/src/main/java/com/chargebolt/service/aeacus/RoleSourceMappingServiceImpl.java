/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.aeacus;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dao.OssUserRoleMappingDAO;
import com.chargebolt.aeacus.dao.RoleDAO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.aeacus.service.support.RoleSupport;
import com.chargebolt.commons.exception.ResourceExceptionEnum;
import com.chargebolt.dao.aeacus.RoleSourcesMappingDAO;
import com.chargebolt.dao.aeacus.model.RoleSourcesMapping;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.request.aeacus.EditRoleRequest;
import com.chargebolt.request.aeacus.RoleSourceRequest;
import com.chargebolt.response.aeacus.RoleResponse;
import com.chargebolt.service.agent.AgentService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 资源管理服务实现
 */
@Service
public class RoleSourceMappingServiceImpl implements RoleSourceMappingService {


    @Override
    public List<RoleSourcesMapping> listRecord(RoleSourcesMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return roleSourcesMappingDAO.listRecord(model);
    }

    public List<RoleResponse> getRoleList(RoleSourceRequest request) {

        // 获取有权限的角色列表
        List<RoleDO> roleDOList = roleDAO.selectByPage(request.getRoleName(), request.getAgentId());
        // 获取公司信息
        List<Agent> agentList = agentService.queryListByIds(roleDOList.stream().map(RoleDO::getAgentId).collect(Collectors.toList()));
        Map<Long, String> agentMap = agentList.stream().collect(Collectors.toMap(Agent::getId, Agent::getAgentName));
        return roleDOList.stream()
                .map(roleDO -> roleSupport.convert2Response(roleDO,agentMap))
                .collect(Collectors.toList());

    }

    /**
     *  根据角色ID获取角色信息
     * @param roleId 角色ID
     * @return 角色信息
     */
    @Override
    public RoleResponse getRoleInfo(Long roleId) {

        RoleDO roleDO = roleDAO.queryById(roleId);
        if (Objects.isNull(roleDO)) {
            throw new I18nMessageException(ResourceExceptionEnum.ROLE_NOT_EXIST.getCode(), ResourceExceptionEnum.ROLE_NOT_EXIST.getDesc());
        }
        // 获取公司信息
        List<Agent> agentList = agentService.queryListByIds(Lists.newArrayList(roleDO.getAgentId()));
        Map<Long, String> agentMap = agentList.stream().collect(Collectors.toMap(Agent::getId, Agent::getAgentName));
        return roleSupport.convert2Response(roleDO, agentMap);
    }

    @Override
    public RoleSourcesMapping getRecord(RoleSourcesMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return roleSourcesMappingDAO.getRecord(model);
    }

    @Override
    public int saveRecord(RoleSourcesMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return roleSourcesMappingDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(RoleSourcesMapping model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return roleSourcesMappingDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(RoleSourcesMapping model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return roleSourcesMappingDAO.updateRecord(model);
    }

/**
     * 根据角色ID获取资源列表
     * @param roleId 角色ID
     * @return 资源列表
     */
    @Override
    public List<RoleSourcesMapping> getByRoleId(Long roleId){
        RoleSourcesMapping model = new RoleSourcesMapping();
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        model.setRoleId(roleId);
        return roleSourcesMappingDAO.listRecord(model);
    }

    /**
     * 根据角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 删除结果
     */
    @Transactional
    @Override
    public int removeByRoleId(Long roleId) {
        // 删除角色
        roleDAO.removeById(roleId);
        // 删除角色资源
        RoleSourcesMapping model = new RoleSourcesMapping();
        model.setRoleId(roleId);
        return roleSourcesMappingDAO.removeByRoleId(model);
    }

    /**
     * 根据资源ID删除角色
     *
     * @param resourceId 资源ID
     * @return 删除结果
     */
    @Override
    public int removeByResourceId(Long resourceId) {

        RoleSourcesMapping model = new RoleSourcesMapping();
        model.setResourceId(resourceId);
        return roleSourcesMappingDAO.removeByResourceId(model);
    }

    /**
     * 新增角色资源
     *
     * @param request     编辑角色资源请求
     * @param currentUser 当前用户
     */
    @Override
    @Transactional
    public void saveResource(EditRoleRequest request,OssUserDTO currentUser){
        // 1.保存角色信息
        RoleDO roleDO = new RoleDO();
        roleDO.setName(request.getRoleName());
        roleDO.setAgentId(request.getAgentId());
        roleDO.setUpdator(currentUser.getName());
        roleDO.setCreator(currentUser.getName());
        roleDO.setModuleId(0L);
        roleDAO.insert(roleDO);
        // 2.保存角色资源关系
        request.getResourceIds().forEach(resourceId -> {
            RoleSourcesMapping roleSourcesMapping = new RoleSourcesMapping();
            roleSourcesMapping.setRoleId(roleDO.getId());
            roleSourcesMapping.setResourceId(resourceId);
            saveRecord(roleSourcesMapping);
        });

    }

    /**
     * 编辑角色资源
     *
     * @param request     编辑角色资源请求
     * @param currentUser 当前用户
     */
    @Transactional
    @Override
    public void updateResource(EditRoleRequest request, OssUserDTO currentUser){

        // 更新角色
        RoleDO roleDO = new RoleDO();
        roleDO.setUpdator(currentUser.getName());
        roleDO.setId(request.getRoleId());
        roleDO.setName(request.getRoleName());
        roleDAO.update(roleDO);
        // 根据角色查询资源列表
        List<RoleSourcesMapping> roleSourcesMappings = getByRoleId(request.getRoleId());
        Map<Long, Long> roleSourceMap = roleSourcesMappings.stream()
                .collect(Collectors.toMap(RoleSourcesMapping::getResourceId, RoleSourcesMapping::getId));
        List<Long> newResourceIds = request.getResourceIds();
        for (Long newResourceId : newResourceIds) {
            // 如果最新的资源列表在数据中不存在，新增记录
            if (!roleSourceMap.containsKey(newResourceId)) {
                RoleSourcesMapping roleSourcesMapping = new RoleSourcesMapping();
                roleSourcesMapping.setRoleId(request.getRoleId());
                roleSourcesMapping.setResourceId(newResourceId);
                saveRecord(roleSourcesMapping);
            }
        }
        // 删除oldResourceIds中不存在的记录
        roleSourceMap.forEach((k, v) -> {
            if (!newResourceIds.contains(k)) {
                RoleSourcesMapping roleSourcesMapping = new RoleSourcesMapping();
                roleSourcesMapping.setId(v);
                roleSourcesMappingDAO.removeRecord(roleSourcesMapping);
            }
        });
    }

    /**
     * 根据角色ID获取角色信息
     *
     * @param id 角色ID
     * @return 角色信息
     */
    public RoleDO getRoleById(Long id){
        List<RoleDO> roleDOS = roleDAO.queryByIds(Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(roleDOS)){
            return null;
        }else {
            return roleDOS.get(0);
        }
    }

    /**
     * 根据用户ID获取角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<OssUserRoleMappingDO> getUserRoleMapping(Long userId){
        return  ossUserRoleMappingDAO.queryByUser(userId);
    }

    /**
     * 根据角色ID角色资源关系
     *
     * @param roleIds 角色ID
     * @return 角色资源关系
     */
    public List<RoleSourcesMapping> getRoleSources(List<Long> roleIds) {

        return roleSourcesMappingDAO.selectByRoleIds(roleIds);
    }

    @Override
    public int saveBatchRecord(List<RoleSourcesMapping> roleSourcesMappings) {
        Long currentTimeMillis= DateUtil.timeStampMilli();
        List<RoleSourcesMapping> saveRoleSourcesMappings = roleSourcesMappings.stream()
                .peek(roleSourcesMapping -> {
                        roleSourcesMapping.setDeleted(LogicDeleteEnum.FALSE.getDelete());
                        roleSourcesMapping.setGmtCreate(currentTimeMillis);
                        roleSourcesMapping.setGmtUpdate(currentTimeMillis);
                })
                .collect(Collectors.toList());
        return roleSourcesMappingDAO.saveBatchRecord(saveRoleSourcesMappings);
    }


    /**
     * 推荐使用构造器注入
     */
    private final RoleSourcesMappingDAO roleSourcesMappingDAO;
    private final RoleDAO roleDAO;
    private final AgentService agentService;
    private final RoleSupport roleSupport;
    private final OssUserRoleMappingDAO ossUserRoleMappingDAO;
    public RoleSourceMappingServiceImpl(ObjectProvider<RoleSourcesMappingDAO> roleSourcesMappingProvider,
                                        ObjectProvider<RoleDAO> roleDAOObjectProvider,
                                        ObjectProvider<AgentService> agentServiceProvider,
                                        ObjectProvider<OssUserRoleMappingDAO> ossUserRoleMappingDAOProvider,
                                        ObjectProvider<RoleSupport> roleSupportProvider) {

        this.roleSourcesMappingDAO = roleSourcesMappingProvider.getIfUnique();
        this.roleDAO = roleDAOObjectProvider.getIfUnique();
        this.agentService = agentServiceProvider.getIfUnique();
        this.roleSupport = roleSupportProvider.getIfUnique();
        this.ossUserRoleMappingDAO = ossUserRoleMappingDAOProvider.getIfUnique();
    }
}