/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.aeacus;

import com.chargebolt.dao.aeacus.model.SystemResources;
import com.chargebolt.request.aeacus.VersionTemplateRequest;
import com.chargebolt.response.aeacus.RoleResourceResponse;
import com.chargebolt.response.aeacus.SystemResourcesResponse;
import com.chargebolt.response.aeacus.VersionTemplateResponse;

import java.util.List;


/**
 * 资源管理服务接口
 */
public interface SystemResourcesService {
    /**
     * listRecord 查询列表
     *
     * @return List<SystemResources>     返回结果
     */
    List<SystemResources> listRecord();


    /**
     * 获取所有资源列表，包含版本信息
     *
     * @return List<SystemResources>     返回结果
     */
    List<SystemResourcesResponse> listContainVersion();


    /**
     * 根据code获取所有下级资源
     *
     * @param code 资源code
     * @return 资源列表
     */
    SystemResourcesResponse getSubResourceByCode(String code);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model 实体model
     * @return SystemResources     返回结果
     */
    SystemResources getRecord(SystemResources model);

    /**
     * saveRecord 记录保存
     *
     * @param model 实体model
     * @return insert条数（单条1）
     */
    int saveRecord(SystemResources model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     *
     * @return 逻辑删除数据条数
     */
    int removeRecord(String code);

    /**
     * 根据主键ID删除记录
     *
     * @return 逻辑删除数据条数
     */
    int removeRecord(Long id);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model 实体model
     * @return updateRecord更新数据条数
     */
    int updateRecord(SystemResources model);


    /**
     * 根据资源code获取资源信息
     */
    SystemResources getRecordByCode(String code);


    /**
     * 根据parentCode获取资源信息
     */
    List<SystemResources> getRecordByParentCode(String parentCode);

    /**
     * 根据ID获取资源信息
     */
    SystemResources getById(Long id);

    /**
     * 获取资源对应的软件版本信息
     *
     * @param resourceId 资源 ID
     * @return List<VersionTemplateResponse> 软件版本信息列表
     */
    List<VersionTemplateResponse> getResourceTemplate(Long resourceId);


    /**
     * 绑定版本
     *
     * @param request 绑定版本请求
     */
    void bindVersion(VersionTemplateRequest request);

    /**
     * 根据代理商获取资源列表
     *
     * @param agentId 代理商ID
     * @return 资源列表
     */
    RoleResourceResponse getResourceListByAgentId(Long agentId);

    /**
     * 根据角色取资源列表
     *
     * @param roleId 角色ID
     * @return 资源列表
     */
    RoleResourceResponse getResourceListByRoleId(Long roleId);

}