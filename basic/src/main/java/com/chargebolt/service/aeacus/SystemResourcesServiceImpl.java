/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.aeacus;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dao.OssUserRoleMappingDAO;
import com.chargebolt.aeacus.dao.RoleDAO;
import com.chargebolt.aeacus.entity.dataobject.OssUserRoleMappingDO;
import com.chargebolt.aeacus.entity.dataobject.RoleDO;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.exception.ResourceExceptionEnum;
import com.chargebolt.commons.utils.ResourceTreeUtil;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.dao.aeacus.SystemResourcesDAO;
import com.chargebolt.dao.aeacus.model.RoleSourcesMapping;
import com.chargebolt.dao.aeacus.model.SystemResources;

import java.util.*;
import java.util.stream.Collectors;

import com.chargebolt.dao.aeacus.model.TemplateSourcesMapping;
import com.chargebolt.dao.aeacus.model.VersionTemplate;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.request.aeacus.VersionTemplateRequest;
import com.chargebolt.response.aeacus.RoleResourceResponse;
import com.chargebolt.response.aeacus.SystemResourcesResponse;
import com.chargebolt.response.aeacus.VersionTemplateResponse;
import com.chargebolt.service.agent.AgentBaseConfigService;
import com.chargebolt.service.authority.LoginUserDataAuthorityService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 资源管理服务实现
 */
@Service
public class SystemResourcesServiceImpl implements SystemResourcesService {

    @Override
    public List<SystemResources> listRecord() {
        SystemResources model = new SystemResources();
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return systemResourcesDAO.listRecord(model);
    }
    @Override
    public List<SystemResourcesResponse> listContainVersion() {

        List<SystemResourcesResponse> rsList = new ArrayList<>();
        // 获取所有资源列表
        List<SystemResources> srList = listRecord();
        if (CollectionUtils.isNotEmpty(srList)) {
            // 创建code和id的映射
            Map<String, Long> codeIdMap = srList
                    .stream().collect(Collectors.toMap(SystemResources::getCode, SystemResources::getId));
            // 获取版本列表
            List<VersionTemplate> vtList = versionTemplateService.listRecord(new VersionTemplate());
            // 获取资源和版本的关联信息
            List<TemplateSourcesMapping> templateSourcesMappings = templateSourcesMappingService.listRecord(new TemplateSourcesMapping());
            // 按资源ID分组,资源->版本列表
            Map<Long, List<TemplateSourcesMapping>> rtMap = templateSourcesMappings.stream()
                    .collect(Collectors.groupingBy(TemplateSourcesMapping::getResourceId));
            // 封装资源对应的版本信息
            srList.forEach(resource -> {
                List<VersionTemplateResponse> vtrList = new ArrayList<>();
                VersionTemplateResponse vtr;
                // 循环所有版本，判断是否有资源权限
                for (VersionTemplate version : vtList) {
                    List<TemplateSourcesMapping> sourcesMappingList = rtMap.get(resource.getId());
                    boolean hasVersionPermission = CollectionUtils.isNotEmpty(sourcesMappingList)
                            && sourcesMappingList.stream().anyMatch(m -> m.getTemplateId().equals(version.getId()));
                    vtr = new VersionTemplateResponse(version, hasVersionPermission);
                    vtrList.add(vtr);
                }
                SystemResourcesResponse srp = new SystemResourcesResponse(resource);
                srp.setVersionTemplates(vtrList);
                srp.setParentId(codeIdMap.get(resource.getParentCode()));
                rsList.add(srp);
            });
        }

       return ResourceTreeUtil.build(rsList);
    }

    /**
     * 根据code获取所有下级资源
     *
     * @param code 资源code
     * @return 资源列表
     */
    public SystemResourcesResponse getSubResourceByCode(String code) {

        List<SystemResources> srList;
        SystemResources sr = getRecordByCode(code);
        if (Objects.isNull(sr)) {
            throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_NOT_EXIST.getCode(), ResourceExceptionEnum.RESOURCE_NOT_EXIST.getDesc());
        }
        UserDataAuthorityContext authority = loginUserDataAuthorityService.getLoginUserDataAuthority();
//        // 管理员用户，获取所有资源
//        if (AuthorityLevelEnum.ALL.getCode().equals(authority.getAuthorityLevel())) {
//            srList = listRecord();
//        } else {
            // 获取当前用户有哪些角色
            List<OssUserRoleMappingDO> rmList = roleSourceMappingService.getUserRoleMapping(authority.getUserId());
            if (CollectionUtils.isEmpty(rmList)) {
                return new SystemResourcesResponse();
            }
            List<Long> roleIds = rmList.stream().map(OssUserRoleMappingDO::getRoleId).collect(Collectors.toList());
            // 获取角色资源
            List<RoleSourcesMapping> roleSources = roleSourceMappingService.getRoleSources(roleIds);
            List<Long> resourceIds = roleSources.stream().map(RoleSourcesMapping::getResourceId).distinct().collect(Collectors.toList());
            // 获取资源列表
            srList = systemResourcesDAO.selectByIds(resourceIds);
//        }
        srList.add(sr);
        return ResourceTreeUtil.build(srList,code);
    }

    @Override
    public SystemResources getRecord(SystemResources model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return systemResourcesDAO.getRecord(model);
    }

    @Override
    public int saveRecord(SystemResources model) {
        // TODO 幂等性处理

        // 校验code是否存在
        if (Objects.nonNull(getRecordByCode(model.getCode()))) {
            throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CODE_EXIST.getCode(), ResourceExceptionEnum.RESOURCE_CODE_EXIST.getDesc());
        }
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return systemResourcesDAO.saveRecord(model);
    }


    @Transactional
    @Override
    public int removeRecord(String code) {

        // 判断是否存在子资源
        List<SystemResources> child = getRecordByParentCode(code);
        if (CollectionUtils.isNotEmpty(child)) {
            throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CHILD_EXIST.getCode(), ResourceExceptionEnum.RESOURCE_CHILD_EXIST.getDesc());
        }
        // 删除资源与版本的关联
        SystemResources sr = getRecordByCode(code);
        templateSourcesMappingService.removeByResourceId(sr.getId());
        // 删除资源与角色的关联
        roleSourceMappingService.removeByResourceId(sr.getId());
        // 根据code删除记录
        SystemResources model = new SystemResources();
        model.setCode(code);
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return systemResourcesDAO.removeRecord(model);
    }

    @Override
    public int removeRecord(Long id) {

        SystemResources systemResources = getById(id);
        // 判断是否存在子资源
        List<SystemResources> child = getRecordByParentCode(systemResources.getCode());
        if (CollectionUtils.isNotEmpty(child)) {
            throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CHILD_EXIST.getCode(), ResourceExceptionEnum.RESOURCE_CHILD_EXIST.getDesc());
        }
        // 根据code删除记录
        SystemResources model = new SystemResources();
        model.setCode(systemResources.getCode());
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return systemResourcesDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(SystemResources model) {

        // 校验资源是否存在
        SystemResources resources = getById(model.getId());
        if (Objects.isNull(resources)){
            throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_NOT_EXIST.getCode(), ResourceExceptionEnum.RESOURCE_NOT_EXIST.getDesc());
        }
        // 校验资源code是否修改
        if (!model.getCode().equals(resources.getCode())) {
            throw new I18nMessageException(ResourceExceptionEnum.RESOURCE_CODE_MODIFY.getCode(), ResourceExceptionEnum.RESOURCE_CODE_MODIFY.getDesc());
        }
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return systemResourcesDAO.updateRecord(model);
    }

    /**
     * 根据资源code获取资源信息
     */
    @Override
    public SystemResources getRecordByCode(String code) {
        SystemResources model = new SystemResources();
        model.setCode(code);
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return systemResourcesDAO.getRecord(model);
    }

    /**
     * 根据parentCode获取资源信息
     */
    @Override
    public List<SystemResources> getRecordByParentCode(String parentCode) {
        SystemResources model = new SystemResources();
        model.setParentCode(parentCode);
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return systemResourcesDAO.listRecord(model);
    }

    /**
     * 根据ID获取资源信息
     */
    @Override
    public SystemResources getById(Long id) {
        SystemResources model = new SystemResources();
        model.setId(id);
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return systemResourcesDAO.getRecord(model);
    }


    /**
     * 获取资源对应的软件版本信息
     *
     * @param resourceId 资源 ID
     * @return List<VersionTemplateResponse> 软件版本信息列表
     */
    @Override
    public List<VersionTemplateResponse> getResourceTemplate(Long resourceId) {

        List<VersionTemplateResponse> responses = new ArrayList<>();
        // 1、获取版本模板
        List<VersionTemplate> versionTemplates = versionTemplateService.listRecord(new VersionTemplate());
        List<TemplateSourcesMapping> templateSourcesMappings = templateSourcesMappingService.listRecord(new TemplateSourcesMapping());
        Map<Long, List<TemplateSourcesMapping>> collect = templateSourcesMappings.stream().collect(Collectors.groupingBy(TemplateSourcesMapping::getResourceId));
        List<TemplateSourcesMapping> sourcesMappingList = collect.get(resourceId);
        VersionTemplateResponse response;
        for (VersionTemplate versionTemplate : versionTemplates) {
            // 2、根据版本判断是否有资源的权限
            boolean hasVersionPermission = CollectionUtils.isNotEmpty(sourcesMappingList)
                    && sourcesMappingList.stream().anyMatch(m -> m.getTemplateId().equals(versionTemplate.getId()));
            response = new VersionTemplateResponse(versionTemplate, hasVersionPermission);
            responses.add(response);
        }

        return responses;
    }

    /**
     * 绑定版本
     *
     * @param request 绑定版本请求
     */
    @Transactional
    @Override
    public void bindVersion(VersionTemplateRequest request) {

        List<Long> templateVersions = request.getTemplateVersions();
        // 获取当前资源数据库中关联的版本列表
        Map<Long, Long> mappingMap = new HashMap<>();
        List<TemplateSourcesMapping> sourcesMappings = templateSourcesMappingService.getByResourceId(request.getResourceId());
        if (CollectionUtils.isNotEmpty(sourcesMappings)) {
            mappingMap = sourcesMappings.stream().collect(Collectors.toMap(TemplateSourcesMapping::getTemplateId, TemplateSourcesMapping::getId));
        }
        mappingMap.forEach((k, v) -> {
            if (!templateVersions.contains(k)) {
                // 新绑定版本不存在，删除记录
                templateSourcesMappingService.removeById(v);
            }
        });
        for (Long templateVersion : templateVersions) {
            // 新绑定版本不存在数据库，添加新记录
            if (!mappingMap.containsKey(templateVersion)) {
                TemplateSourcesMapping mapping = new TemplateSourcesMapping();
                mapping.setTemplateId(templateVersion);
                mapping.setResourceId(request.getResourceId());
                templateSourcesMappingService.saveRecord(mapping);
            }
        }
    }


    /**
     * 根据代理商获取资源列表
     *
     * @param agentId 代理商ID
     * @return 资源列表
     */
    @Override
    public RoleResourceResponse getResourceListByAgentId(Long agentId) {

        AgentBaseConfig abConfig = agentBaseConfigService.getByAgentId(agentId);
        if (Objects.isNull(abConfig)) {
            throw new I18nMessageException(ResourceExceptionEnum.AGENT_VERSION_NOT_SPECIFIED.getCode(), ResourceExceptionEnum.AGENT_VERSION_NOT_SPECIFIED.getDesc());
        }
        // 根据代理商的软件版本获取用户拥有的资源
        List<TemplateSourcesMapping> tsmList = templateSourcesMappingService.getByTemplateId(abConfig.getSysVersion());
        // 全部资源
        List<SystemResources> systemResources = this.listRecord();
        List<Long> resourceIds = tsmList.stream().map(TemplateSourcesMapping::getResourceId).collect(Collectors.toList());
        // 根据代理的资源和角色拥有的资源构建资源树
        List<SystemResourcesResponse> rsList = ResourceTreeUtil.build(systemResources, resourceIds);
        RoleResourceResponse rs = new RoleResourceResponse();
        // 应用端数量
        rs.setAppNum(rsList.size());
        // 总资源数量
        rs.setResourceNum(tsmList.size());
        rs.setResources(rsList);
        return rs;

    }


    /**
     * 根据角色构建资源树
     *
     * @param roleId 角色ID
     * @return 资源列表
     */
    @Override
    public RoleResourceResponse getResourceListByRoleId(Long roleId) {

        RoleDO roleDO = roleSourceMappingService.getRoleById(roleId);
        AgentBaseConfig abConfig = agentBaseConfigService.getByAgentId(roleDO.getAgentId());
        //  根据代理商ID获取软件版本
        Long templateId = abConfig.getSysVersion();
        // 根据代理商的软件版本拥有的资源
        List<TemplateSourcesMapping> tsmList = templateSourcesMappingService.getByTemplateId(templateId);
        // 获取角色的资源
        List<RoleSourcesMapping> rsMapping = roleSourceMappingService.getByRoleId(roleId);
        // 获取全部资源
        List<SystemResources> allResource = this.listRecord();
        UserDataAuthorityContext authority = loginUserDataAuthorityService.getLoginUserDataAuthority();

        List<Long> agentResourceIds = new ArrayList<>();
        if (AuthorityLevelEnum.ALL.getCode().equals(authority.getAuthorityLevel())) {
            agentResourceIds=allResource.stream().map(SystemResources::getId).collect(Collectors.toList());
        }else{
            agentResourceIds = tsmList.stream().map(TemplateSourcesMapping::getResourceId).collect(Collectors.toList());
        }
        List<Long> roleResourceIds = rsMapping.stream().map(RoleSourcesMapping::getResourceId).collect(Collectors.toList());
        // 根据代理的资源和角色拥有的资源构建资源树
        List<SystemResourcesResponse> rsList = ResourceTreeUtil.build(allResource, agentResourceIds,roleResourceIds);
        RoleResourceResponse rs = new RoleResourceResponse();
        // 应用端数量
        rs.setAppNum(rsList.size());
        // 总资源数量
        rs.setResourceNum(tsmList.size());
        rs.setSelectResourceNum(rsMapping.size());
        // 判断rsList的元素中是否存在checked=true的元素
        int num=0;
        for (SystemResourcesResponse s : rsList) {
            num = hasChecked(s.getChildren()) ? num + 1 : num;
        }
        rs.setSelectAppNum(num);
        rs.setResources(rsList);
        return rs;

    }

    private boolean hasChecked(List<SystemResourcesResponse> child){

        if(CollectionUtils.isNotEmpty(child)){
            for (SystemResourcesResponse s : child) {
                if (s.isChecked()){
                    return true;
                }
                hasChecked(s.getChildren());
            }
        }
        return false;

    }


    /**
     * 推荐使用构造器注入
     */
    private final SystemResourcesDAO systemResourcesDAO;
    private final TemplateSourcesMappingService templateSourcesMappingService;
    private final VersionTemplateService versionTemplateService;
    private final AgentBaseConfigService agentBaseConfigService;
    private final RoleSourceMappingService roleSourceMappingService;
    private final LoginUserDataAuthorityService loginUserDataAuthorityService;
    private final OssUserRoleMappingDAO  userRoleMappingDAO;

    public SystemResourcesServiceImpl(ObjectProvider<SystemResourcesDAO> systemResourcesProvider
            , ObjectProvider<TemplateSourcesMappingService> templateSourcesMappingServiceProvider
            , ObjectProvider<VersionTemplateService> versionTemplateServiceProvider
            , ObjectProvider<RoleDAO> roleDAOObjectProvider
            , ObjectProvider<AgentBaseConfigService> agentBaseConfigServiceProvider
            , ObjectProvider<LoginUserDataAuthorityService> loginUserDataAuthorityServiceProvider
            , ObjectProvider<OssUserRoleMappingDAO> userRoleMappingDAOProvider
    ,         ObjectProvider<RoleSourceMappingService> roleSourceMappingServiceProvider) {
        this.systemResourcesDAO = systemResourcesProvider.getIfUnique();
        this.templateSourcesMappingService = templateSourcesMappingServiceProvider.getIfUnique();
        this.versionTemplateService = versionTemplateServiceProvider.getIfUnique();
        this.agentBaseConfigService = agentBaseConfigServiceProvider.getIfUnique();
        this.loginUserDataAuthorityService = loginUserDataAuthorityServiceProvider.getIfUnique();
        this.roleSourceMappingService = roleSourceMappingServiceProvider.getIfUnique();
        this.userRoleMappingDAO = userRoleMappingDAOProvider.getIfUnique();
    }
}