/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.aeacus;

import com.chargebolt.dao.aeacus.model.TemplateSourcesMapping;

import java.util.List;

/**
 * 模板资源映射服务接口
 */
public interface TemplateSourcesMappingService {
    /**
     * listRecord 查询列表
     *
     * @param model 实体model
     * @return List<TemplateSourcesMapping>     返回结果
     */
    List<TemplateSourcesMapping> listRecord(TemplateSourcesMapping model);


    /**
     * 根据资源 ID 获取记录列表
     *
     * @param resourceId 资源 ID
     * @return List<TemplateSourcesMapping> 记录列表，如果没有找到则返回空列表
     */
    List<TemplateSourcesMapping> getByResourceId(Long resourceId);

    /**
     * 根据资源 ID 获取记录列表
     *
     * @param templateId 版本ID
     * @return List<TemplateSourcesMapping> 记录列表，如果没有找到则返回空列表
     */
    List<TemplateSourcesMapping> getByTemplateId(Long templateId);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model 实体model
     * @return TemplateSourcesMapping     返回结果
     */
    TemplateSourcesMapping getRecord(TemplateSourcesMapping model);

    /**
     * saveRecord 记录保存
     *
     * @param model 实体model
     * @return insert条数（单条1）
     */
    int saveRecord(TemplateSourcesMapping model);

    /**
     * 根据 ID 删除记录
     */
    int removeById(Long id);

    /**
     * 根据资源 ID 删除记录
     * @param resourceId 资源 ID
     * @return
     */
    int removeByResourceId(Long resourceId);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model 实体model
     * @return updateRecord更新数据条数
     */
    int updateRecord(TemplateSourcesMapping model);


}