/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.aeacus;

import com.chargebolt.dao.aeacus.TemplateSourcesMappingDAO;
import com.chargebolt.dao.aeacus.model.TemplateSourcesMapping;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 模板资源映射服务接口实现
 */
@Service
public class TemplateSourcesMappingServiceImpl implements TemplateSourcesMappingService {
    @Override
    public List<TemplateSourcesMapping> listRecord(TemplateSourcesMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return templateSourcesMappingDAO.listRecord(model);
    }


    @Override
    public List<TemplateSourcesMapping> getByResourceId(Long  resourceId) {
        TemplateSourcesMapping model = new TemplateSourcesMapping();
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        model.setResourceId(resourceId);
        return templateSourcesMappingDAO.listRecord(model);
    }

    @Override
    public List<TemplateSourcesMapping> getByTemplateId(Long  templateId) {
        TemplateSourcesMapping model = new TemplateSourcesMapping();
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        model.setTemplateId(templateId);
        return templateSourcesMappingDAO.listRecord(model);
    }

    @Override
    public TemplateSourcesMapping getRecord(TemplateSourcesMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return templateSourcesMappingDAO.getRecord(model);
    }

    @Override
    public int saveRecord(TemplateSourcesMapping model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return templateSourcesMappingDAO.saveRecord(model);
    }

    @Override
    public int removeById(Long id) {
        TemplateSourcesMapping model = new TemplateSourcesMapping();
        model.setId(id);
        return templateSourcesMappingDAO.removeRecord(model);
    }

    public int removeByResourceId(Long resourceId) {
        TemplateSourcesMapping model = new TemplateSourcesMapping();
        model.setResourceId(resourceId);
        return templateSourcesMappingDAO.removeByResourceId(model);
    }

    @Override
    public int updateRecord(TemplateSourcesMapping model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return templateSourcesMappingDAO.updateRecord(model);
    }

    /**
     * 推荐使用构造器注入
     */
    private final TemplateSourcesMappingDAO templateSourcesMappingDAO;
    public TemplateSourcesMappingServiceImpl(ObjectProvider<TemplateSourcesMappingDAO> templateSourcesMappingProvider) {
        this.templateSourcesMappingDAO= templateSourcesMappingProvider.getIfUnique();
    }
}