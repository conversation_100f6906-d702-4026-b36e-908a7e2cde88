/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.aeacus;

import com.chargebolt.dao.aeacus.VersionTemplateDAO;
import com.chargebolt.dao.aeacus.model.VersionTemplate;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 版本模板服务接口实现
 */
@Service
public class VersionTemplateServiceImpl implements VersionTemplateService {
    @Override
    public List<VersionTemplate> listRecord(VersionTemplate model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return versionTemplateDAO.listRecord(model);
    }

    @Override
    public VersionTemplate getRecord(VersionTemplate model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return versionTemplateDAO.getRecord(model);
    }

    @Override
    public int saveRecord(VersionTemplate model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return versionTemplateDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(VersionTemplate model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return versionTemplateDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(VersionTemplate model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return versionTemplateDAO.updateRecord(model);
    }

    @Override
    public VersionTemplate getTemplateByVersion(final Long version) {
        VersionTemplate model = new VersionTemplate();
        model.setId(version);
        return getRecord(model);
    }


    /**
     * 推荐使用构造器注入
     */
    private final VersionTemplateDAO versionTemplateDAO;
    public VersionTemplateServiceImpl(ObjectProvider<VersionTemplateDAO> versionTemplateProvider) {
        this.versionTemplateDAO= versionTemplateProvider.getIfUnique();
    }
}