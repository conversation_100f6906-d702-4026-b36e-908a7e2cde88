/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.agent;

import com.chargebolt.dao.agent.model.AgentBaseConfig;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: AgentBaseConfigService.java, v 0.1 2024-09-06 15:27:40 Exp $
 */

public interface AgentBaseConfigService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<AgentBaseConfig>     返回结果
     */
    List<AgentBaseConfig> listRecord(AgentBaseConfig model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return AgentBaseConfig     返回结果
     */
    AgentBaseConfig getRecord(AgentBaseConfig model);


    /**
     * getByAgentId 根据代理商ID查询
     *
     * @param agentId              代理商ID
     * @return AgentBaseConfig     返回结果
     */
    AgentBaseConfig getByAgentId(Long agentId);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(AgentBaseConfig model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(AgentBaseConfig model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(AgentBaseConfig model);

    int saveOrUpdate(AgentBaseConfig model);
}