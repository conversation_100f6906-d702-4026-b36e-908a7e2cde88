/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.agent;
import java.util.List;
import java.util.Objects;

import com.chargebolt.commons.enums.UtcZoneEnum;
import com.chargebolt.dao.agent.AgentBaseConfigDAO;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.ezreal.constant.TenantConfigExtConstant;
import com.chargebolt.tenant.dao.model.TenantExtConfig;
import com.chargebolt.tenant.service.TenantAgentRelatedService;
import com.chargebolt.tenant.service.TenantExtConfigService;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: AgentBaseConfigServiceImpl.java, v 0.1 2024-09-06 15:27:40 Exp $
 */
@Service
public class AgentBaseConfigServiceImpl implements AgentBaseConfigService {
    @Override
    public List<AgentBaseConfig> listRecord(AgentBaseConfig model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return agentBaseConfigDAO.listRecord(model);
    }

    @Override
    public AgentBaseConfig getRecord(AgentBaseConfig model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return agentBaseConfigDAO.getRecord(model);
    }

    @Override
    public AgentBaseConfig getByAgentId(Long agentId) {
        AgentBaseConfig model=new AgentBaseConfig();
        model.setAgentId(agentId);
        AgentBaseConfig agentBaseConfig= this.getRecord(model);
        if(Objects.nonNull(agentBaseConfig)){
            Long tenantId =tenantAgentRelatedService.getTenantId(agentId);
            TenantExtConfig zoneTimeConfig= tenantExtConfigService.getTenantExtConfig(tenantId, TenantConfigExtConstant.TENANT_TIMEZONE_KEY);
            if(Objects.nonNull(zoneTimeConfig)){
                agentBaseConfig.setTimeZone(zoneTimeConfig.getConfigValue());
            }else{
                agentBaseConfig.setTimeZone(UtcZoneEnum.getTimeZoneByUtc(agentBaseConfig.getZoneUtc()));
            }
        }
        return agentBaseConfig;
    }

    @Override
    public int saveRecord(AgentBaseConfig model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return agentBaseConfigDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(AgentBaseConfig model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return agentBaseConfigDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(AgentBaseConfig model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return agentBaseConfigDAO.updateRecord(model);
    }

    @Override
    public int saveOrUpdate(AgentBaseConfig model) {
        AgentBaseConfig record = new AgentBaseConfig();
        record.setAgentId(model.getAgentId());
        record= getRecord(record);
        if(Objects.isNull(record)){
            return saveRecord(model);
        }
        model.setId(record.getId());
        return updateRecord(model);
    }


    /**
     * 推荐使用构造器注入
     */
    private final AgentBaseConfigDAO agentBaseConfigDAO;
    private final TenantAgentRelatedService tenantAgentRelatedService;
    private final TenantExtConfigService tenantExtConfigService;
    public AgentBaseConfigServiceImpl(ObjectProvider<AgentBaseConfigDAO> agentBaseConfigProvider,
                                      ObjectProvider<TenantAgentRelatedService> tenantAgentRelatedServiceProvider,
                                      ObjectProvider<TenantExtConfigService> tenantExtConfigServiceProvider
                                      ) {
        this.agentBaseConfigDAO= agentBaseConfigProvider.getIfUnique();
        this.tenantAgentRelatedService= tenantAgentRelatedServiceProvider.getIfUnique();
        this.tenantExtConfigService= tenantExtConfigServiceProvider.getIfUnique();
    }
}