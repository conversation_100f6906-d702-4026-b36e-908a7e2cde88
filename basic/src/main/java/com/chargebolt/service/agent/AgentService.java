/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.agent;

import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.agent.dto.AgentQueryDTO;
import com.chargebolt.commons.enums.language.CooperatingStateEnum;
import com.chargebolt.commons.enums.language.UserAccountStateEnum;
import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.agent.model.ResourceAgentDO;
import com.chargebolt.dao.agent.model.ResourceAgentQueryDO;
import com.chargebolt.ezreal.response.agent.AgentSimpleResponse;
import com.chargebolt.request.agent.AddAgentRequest;
import com.chargebolt.request.agent.SearchAgentRequest;
import com.chargebolt.response.agent.AppAgentResponse;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: AgentService.java, v 0.1 2024-03-19 16:36:59 Exp $
 */

public interface AgentService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<Agent>     返回结果
     */
    List<Agent> listRecord(Agent model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return Agent     返回结果
     */
    Agent getRecord(Agent model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(Agent model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(Agent model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(Agent model);

    /**
     * app 代理商列表（分页查询）
     *
     * @param request
     * @return
     */
    PageData<AppAgentResponse> queryOneselfList(SearchAgentRequest request);

    /**
     * 查询用户拥二代公司列表
     * 用于查询代理商列表，下拉选择框，默认返回100条，大于100，通过agentName 模糊查询
     *
     * @param agentQueryDTO 查询条件
     * @return List<AppAgentResponse>
     */
    List<AppAgentResponse> querySelectorList(AgentQueryDTO agentQueryDTO);

    /**
     * 查询代理商详情信息
     *
     * @param agentId
     * @return
     */
    AppAgentResponse getAgentDetail(Long agentId);

    /**
     * 创建代理商
     *
     * 该入口都是二代，Base tenant都是来源于一级代理商，且不可修改
     *
     * @param request
     * @return
     */
    Long createAgent(AddAgentRequest request);

    /**
     * 更新代理商
     *
     * @param request
     * @return
     */
    int updateAgent(AddAgentRequest request);

    /**
     * 账号启用、禁用
     *
     * @param agentId
     * @param stateEnum
     * @return
     */
    int updateAgentAccountState(Long agentId, UserAccountStateEnum stateEnum);

    /**
     * 分成开通
     *
     * @param agentId
     * @param stateEnum
     * @return
     */
    int updateAgentCooperatingState(Long agentId, CooperatingStateEnum stateEnum);


    /**
     * 根据id查询代理商数据
     *
     * @param ids 代理商id
     * @return List
     */
    List<Agent> queryListByIds(List<Long> ids);

    /**
     * 获取代理商顶层一级代理信息
     *
     * 如果本身是已经，返回自己
     * 优先缓存获取数据，数据缓存10分钟
     *
     * @param agentId
     * @return
     */
    Agent getTopLevelAgent(Long agentId);
    AgentSimpleResponse getDeviceTopLevelAgent(String deviceNo);
    List<ResourceAgentDO> listSourceAgentRecord(ResourceAgentQueryDO model);
    int getAgentLevel(Long tenantId);

    /**
     * 是否为一级代理
     *
     * @param tenantId
     * @return
     */
    Boolean levelOneAgent(Long tenantId);

    List<AppAgentResponse> selectorListExcludeSubAgent(AgentQueryDTO agentQueryDTO);
}