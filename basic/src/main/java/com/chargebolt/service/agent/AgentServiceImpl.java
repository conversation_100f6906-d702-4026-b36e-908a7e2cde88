/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.agent;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.agent.dto.AgentQueryDTO;
import com.chargebolt.basic.response.area.Area;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.enums.language.CooperatingStateEnum;
import com.chargebolt.commons.enums.language.UserAccountStateEnum;
import com.chargebolt.commons.exception.AgentExceptionEnum;
import com.chargebolt.component.rocketmq.AgentBossRoleSyncProducer;
import com.chargebolt.component.rocketmq.SyncDataAuthorityProducer;
import com.chargebolt.context.AgentBossSyncContext;
import com.chargebolt.convert.common.AgentConverter;
import com.chargebolt.dao.agent.AgentDAO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.dao.agent.model.ResourceAgentDO;
import com.chargebolt.dao.agent.model.ResourceAgentQueryDO;
import com.chargebolt.dao.common.model.Regionals;
import com.chargebolt.ezreal.response.agent.AgentSimpleResponse;
import com.chargebolt.request.agent.AddAgentRequest;
import com.chargebolt.request.agent.SearchAgentRequest;
import com.chargebolt.response.agent.AppAgentResponse;
import com.chargebolt.service.area.AreaService;
import com.chargebolt.service.area.RegionalsService;
import com.chargebolt.tenant.dao.model.TenantAgentRelated;
import com.chargebolt.tenant.service.TenantAgentRelatedService;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.demeter.biz.service.DeviceService;
import so.dian.demeter.common.exception.BizErrorCodeEnum;
import so.dian.demeter.pojo.bo.DeviceBO;
import so.dian.epeius.api.ShopService;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.talos.biz.facade.ShopFacade;
import so.dian.talos.client.dto.ShopDTO;

/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: AgentServiceImpl.java, v 0.1 2024-03-19 16:36:59 Exp $
 */
@Slf4j
@Service
public class AgentServiceImpl implements AgentService {
    /**
     * 顶层一级代理商缓存key
     * 数据缓存10分钟
     */
    private static final String REMOTE_CACHE_AGENT_TOP_LEVEL_KEY="__CACHE_AGENT_TOP_LEVEL_";
    /**
     * 推荐使用构造器注入
     */
    private final AgentDAO agentDAO;
    private final UserManager userManager;
    private final AreaService areaService;
    private final RedissonClient redissonClient;
    private final DeviceService deviceService;
    private final AgentBaseConfigService agentBaseConfigService;
    private final RegionalsService regionalsService;
    private final TenantAgentRelatedService tenantAgentRelatedService;
    private final SyncDataAuthorityProducer syncDataAuthorityProducer;
    private final AgentBossRoleSyncProducer agentBossRoleSyncProducer;

    public AgentServiceImpl(ObjectProvider<AgentDAO> agentProvider,
                            ObjectProvider<UserManager> userManagerProvider,
                            ObjectProvider<RedissonClient> redissonClientProvider,
                            ObjectProvider<AreaService> areaServiceProvider,
                            ObjectProvider<DeviceService> deviceServiceProvider,
                            ObjectProvider<AgentBaseConfigService> agentBaseConfigServiceProvider,
                            ObjectProvider<RegionalsService> regionalsServiceProvider,
                            ObjectProvider<TenantAgentRelatedService> tenantAgentRelatedServiceProvider,
                            ObjectProvider<SyncDataAuthorityProducer> syncDataAuthorityProducerProvider,
                            ObjectProvider<AgentBossRoleSyncProducer> agentBossRoleSyncProducerProvider
                            ) {
        this.agentDAO= agentProvider.getIfUnique();
        this.userManager= userManagerProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
        this.areaService= areaServiceProvider.getIfUnique();
        this.deviceService= deviceServiceProvider.getIfUnique();
        this.agentBaseConfigService= agentBaseConfigServiceProvider.getIfUnique();
        this.regionalsService= regionalsServiceProvider.getIfUnique();
        this.tenantAgentRelatedService= tenantAgentRelatedServiceProvider.getIfUnique();
        this.syncDataAuthorityProducer= syncDataAuthorityProducerProvider.getIfUnique();
        this.agentBossRoleSyncProducer= agentBossRoleSyncProducerProvider.getIfUnique();
    }
    @Override
    public List<Agent> listRecord(Agent model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return agentDAO.listRecord(model);
    }

    @Override
    public Agent getRecord(Agent model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return agentDAO.getRecord(model);
    }

    @Override
    public int saveRecord(Agent model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return agentDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(Agent model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return agentDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(Agent model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return agentDAO.updateRecord(model);
    }

    @Override
    public PageData<AppAgentResponse> queryOneselfList(final SearchAgentRequest request) {

        log.info("代理商列表查询条件：{}", JsonUtil.beanToJson(request));
        Page<Agent> agentList = PageHelper.startPage(request.getPageNo(), request.getPageSize(), Boolean.TRUE)
                .setOrderBy(" a.id DESC").doSelectPage(new ISelect(){
                    @Override
                    public void doSelect() {
                        agentDAO.queryList(new AgentQueryDTO(request));
                    }
                });
        List<AppAgentResponse> list= new ArrayList<>();
        for(Agent agent: agentList){
            AppAgentResponse response= new AppAgentResponse();
            BeanUtils.copyProperties(agent, response);
            response.setCooperatingState(agent.getOpenState());
            response.setCooperatingStateDesc(CooperatingStateEnum.getTranslationByValue(agent.getOpenState()));
            response.setUserAccountState(agent.getState());
            response.setUserAccountStateDesc(UserAccountStateEnum.getTranslationByValue(agent.getState()));

            list.add(response);
        }
        return PageData.create(list, agentList.getTotal(), Long.valueOf(agentList.getPageNum()), agentList.getPageSize(), null);
    }


    /**
     * 查询用户拥二代公司列表，下拉框列表使用选择
     *
     * @param agentQueryDTO 查询条件
     * @return List<AppAgentResponse>
     */
    public List<AppAgentResponse> querySelectorList(AgentQueryDTO agentQueryDTO) {
        if(Objects.nonNull(agentQueryDTO.getAgentName())&& agentQueryDTO.getAgentName().length()<2){
            return new ArrayList<>();
        }
        Agent queryAgent = new Agent();
        queryAgent.setAgentName(agentQueryDTO.getAgentName());
        // 非平台用户
        if(!Objects.equals(agentQueryDTO.getUserContext().getAuthorityLevel(), AuthorityLevelEnum.ALL.getCode())){
            queryAgent.setAgentIds(agentQueryDTO.getUserContext().getQuery().getAgentIds());
        }
        Page<Agent> agentList = PageHelper.startPage(1, 1000, Boolean.FALSE)
                .setOrderBy(" id DESC").doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        agentDAO.listRecord(queryAgent);
                    }
                });

        List<AppAgentResponse> list= new ArrayList<>();
        for(Agent agent: agentList.getResult()){
            AppAgentResponse response= new AppAgentResponse();
            BeanUtils.copyProperties(agent, response);
            response.setCooperatingState(agent.getOpenState());
            response.setCooperatingStateDesc(CooperatingStateEnum.getTranslationByValue(agent.getOpenState()));
            response.setUserAccountState(agent.getState());
            response.setUserAccountStateDesc(UserAccountStateEnum.getTranslationByValue(agent.getState()));
            list.add(response);
        }
        return list;
    }

    @Override
    public List<AppAgentResponse> selectorListExcludeSubAgent(AgentQueryDTO agentQueryDTO) {
        if(Objects.nonNull(agentQueryDTO.getAgentName())&& agentQueryDTO.getAgentName().length()<2){
            return new ArrayList<>();
        }
        Agent queryAgent = new Agent();
        queryAgent.setAgentName(agentQueryDTO.getAgentName());
        // 非平台用户
        if(!Objects.equals(agentQueryDTO.getUserContext().getAuthorityLevel(), AuthorityLevelEnum.ALL.getCode())){
            queryAgent.setAgentIds(Collections.singletonList(agentQueryDTO.getAgentId()));
        }
        Page<Agent> agentList = PageHelper.startPage(1, 1000, Boolean.FALSE)
                .setOrderBy(" id DESC").doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        agentDAO.listRecord(queryAgent);
                    }
                });

        List<AppAgentResponse> list= new ArrayList<>();
        for(Agent agent: agentList.getResult()){
            AppAgentResponse response= new AppAgentResponse();
            BeanUtils.copyProperties(agent, response);
            response.setCooperatingState(agent.getOpenState());
            response.setCooperatingStateDesc(CooperatingStateEnum.getTranslationByValue(agent.getOpenState()));
            response.setUserAccountState(agent.getState());
            response.setUserAccountStateDesc(UserAccountStateEnum.getTranslationByValue(agent.getState()));
            list.add(response);
        }
        return list;
    }
    
    @Override
    public AppAgentResponse getAgentDetail(final Long agentId) {
        Agent agent= new Agent();
        agent.setId(agentId);
        agent= getRecord(agent);
        if(Objects.nonNull(agent)){
            AppAgentResponse response= new AppAgentResponse();
            BeanUtils.copyProperties(agent, response);
            response.setCooperatingState(agent.getOpenState());
            response.setCooperatingStateDesc(CooperatingStateEnum.getTranslationByValue(agent.getOpenState()));
            response.setUserAccountState(agent.getState());
            response.setUserAccountStateDesc(UserAccountStateEnum.getTranslationByValue(agent.getState()));
            OssUserDO ossUserDO= userManager.getById(agent.getSellerId());
            response.setSellerName(Objects.isNull(ossUserDO)?"": ossUserDO.getName());
            response.setSellerMobile(Objects.isNull(ossUserDO)?"":ossUserDO.getMobile());
            Regionals regionals= regionalsService.getByPhoneCode(agent.getNationCode());
            response.setRegionalCode(Objects.isNull(regionals)?"":regionals.getRegionalCode());
            return response;
        }
        return null;
    }

    @Override
    public Long createAgent(final AddAgentRequest request) {
        Agent agent= new Agent();
        agent.setParentId(request.getParentId());
        agent.setAgentName(request.getAgentName());
        agent.setContractName(request.getContractName());
        agent.setNationCode(request.getNationCode());
        agent.setContractMobile(request.getMobile());

        // 省 市

        Area province= areaService.getAreaByCode(request.getProvinceCode());
        Area city= areaService.getAreaByCode(request.getCityCode());
        if(Objects.isNull(province)){
            agent.setProvinceCode("");
            agent.setProvinceName("");
        }else{
            agent.setProvinceCode(province.getCode());
            agent.setProvinceName(province.getName());
        }

        if(Objects.isNull(city)){
            agent.setCityCode("");
            agent.setCityName("");
        }else{
            agent.setCityCode(city.getCode());
            agent.setCityName(city.getName());
        }

        agent.setAddress(request.getAddress());
        agent.setState(UserAccountStateEnum.ENABLE.getValue());
        agent.setOpenState(CooperatingStateEnum.NOT_COOPERATING.getValue());
        agent.setSellerId(request.getSellerId());
        agent.setSellerName(request.getSellerName());
        saveRecord(agent);
        syncDataAuthorityProducer.sendMsg(Arrays.asList(agent.getSellerId()));

        return agent.getId();
    }

    @Override
    public int updateAgent(final AddAgentRequest request) {
        Agent agent= new Agent();
        agent.setId(request.getId());
        agent= getRecord(agent);
        if(Objects.isNull(agent)){
            throw new I18nMessageException(AgentExceptionEnum.AGENT_NOT_EXIST.getCode(), AgentExceptionEnum.AGENT_NOT_EXIST.getDesc());
        }
        agent.setAgentName(request.getAgentName());
        agent.setContractName(request.getContractName());
        agent.setNationCode(request.getNationCode());
        agent.setContractMobile(request.getMobile());

        // 省 市
        Area province= areaService.getAreaByCode(request.getProvinceCode());
        Area city= areaService.getAreaByCode(request.getCityCode());
        if(Objects.isNull(province)){
            agent.setProvinceCode("");
            agent.setProvinceName("");
        }else{
            agent.setProvinceCode(province.getCode());
            agent.setProvinceName(province.getName());
        }

        if(Objects.isNull(city)){
            agent.setCityCode("");
            agent.setCityName("");
        }else{
            agent.setCityCode(city.getCode());
            agent.setCityName(city.getName());
        }

        agent.setAddress(request.getAddress());
        return updateRecord(agent);
    }

    @Override
    public int updateAgentAccountState(final Long agentId, final UserAccountStateEnum stateEnum) {
        Agent agent= new Agent();
        agent.setId(agentId);
        agent= getRecord(agent);
        if(Objects.isNull(agent)){
            throw new I18nMessageException(AgentExceptionEnum.AGENT_NOT_EXIST.getCode(), AgentExceptionEnum.AGENT_NOT_EXIST.getDesc());
        }
        agent.setState(stateEnum.getValue());
        List<OssUserDO> ossUserDOList= userManager.getByAgentId(agentId);
        ossUserDOList.forEach(ossUserDO -> {
            // 值转换UserAccountStateEnum==>0
            ossUserDO.setStatus(stateEnum.getValue()==1?0:1);
            userManager.update(ossUserDO);
        });
        return updateRecord(agent);
    }

    @Override
    public int updateAgentCooperatingState(final Long agentId, final CooperatingStateEnum stateEnum) {
        Agent agent= new Agent();
        agent.setId(agentId);
        agent= getRecord(agent);
        if(Objects.isNull(agent)){
            throw new I18nMessageException(AgentExceptionEnum.AGENT_NOT_EXIST.getCode(), AgentExceptionEnum.AGENT_NOT_EXIST.getDesc());
        }
        agent.setOpenState(stateEnum.getValue());
        return updateRecord(agent);
    }

    /**
     * 根据id查询代理商数据
     * @param ids 代理商id
     * @return
     */
    public List<Agent> queryListByIds(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>(0);
        }
        return agentDAO.queryListByIds(ids);
    }

    @Override
    public Agent getTopLevelAgent(final Long agentId) {
        RBucket<Agent> rBucket= redissonClient.getBucket(REMOTE_CACHE_AGENT_TOP_LEVEL_KEY+agentId);
        if(Objects.nonNull(rBucket)&& Objects.nonNull(rBucket.get())){
            return rBucket.get();
        }
        Agent query= new Agent();
        query.setId(agentId);
        Agent currentAgent = getRecord(query);

        while (currentAgent != null && currentAgent.getParentId()> 0) {
            Agent queryParent= new Agent();
            queryParent.setId(currentAgent.getParentId());
            currentAgent = getRecord(queryParent);
        }
        if(Objects.nonNull(currentAgent)){
            rBucket.set(currentAgent, 1, TimeUnit.MINUTES);
        }
        return currentAgent;
    }

    @Override
    public AgentSimpleResponse getDeviceTopLevelAgent(final String deviceNo) {
        List<DeviceBO> deviceBOs= deviceService.listByDeviceNoList(Arrays.asList(deviceNo));
        if(CollectionUtils.isEmpty(deviceBOs)){
            throw new I18nMessageException(BizErrorCodeEnum.DEVICE_NOT_EXISTED.getCode().toString(), BizErrorCodeEnum.DEVICE_NOT_EXISTED.getDesc());
        }
        Long currentAgentId= deviceBOs.get(0).getAgentId();
        Agent agent= getTopLevelAgent(currentAgentId);
        AgentBaseConfig agentBaseConfig= new AgentBaseConfig();
        agentBaseConfig.setAgentId(currentAgentId);
        agentBaseConfig= agentBaseConfigService.getRecord(agentBaseConfig);
        return AgentConverter.INSTANCE.agentSimpleResponseConvert(agent, agentBaseConfig);
    }

    @Override
    public List<ResourceAgentDO> listSourceAgentRecord(final ResourceAgentQueryDO model) {
        return agentDAO.listSourceAgentRecord(model);
    }

    @Override
    public int getAgentLevel(final Long tenantId) {
        TenantAgentRelated model = new TenantAgentRelated();
        model.setTenantId(tenantId);
        TenantAgentRelated record = tenantAgentRelatedService.getRecord(model);
        if (Objects.nonNull(record)) {
            Long agentId = record.getAgentId();
            if (Objects.nonNull(agentId)) {
                AgentBaseConfig agentBaseConfig= new AgentBaseConfig();
                agentBaseConfig.setAgentId(agentId);
                return agentBaseConfigService.getRecord(agentBaseConfig).getAgentLevel();
            }
        }
        return 99;
    }

    @Override
    public Boolean levelOneAgent(final Long tenantId) {
        return getAgentLevel(tenantId) == 1;
    }
}