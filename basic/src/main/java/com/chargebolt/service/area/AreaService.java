/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.area;

import com.chargebolt.basic.response.area.Area;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AreaService.java, v 1.0 2024-07-04 下午2:24 Exp $
 */
public interface AreaService {
    /**
     * 获取行政区域数据
     * 240704，语言先预留，本次不考虑多语言切换场景
     *
     * @param regionalCode
     * @return
     */
    List<Area> getArea(String regionalCode);

    Area getAreaByCode(String code);
}