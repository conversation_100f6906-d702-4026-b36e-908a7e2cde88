/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.area;

import com.chargebolt.basic.response.area.Area;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.repository.init.ResourceReader;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.JarURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * TODO
 *
 * <AUTHOR>
 * @version: AreaServiceImpl.java, v 1.0 2024-07-04 下午2:32 Exp $
 */
@Slf4j
@Service
public class AreaServiceImpl implements AreaService {
    private static List<Area> AREA_LIST_CACHE = new ArrayList<>();
    private static Map<String, List<Area>> AREA_MAP_CACHE = new HashMap<>();
    private static final String AREA_REGEX="area\\.(\\w+)\\.json";

    @PostConstruct
    public void init() {
        log.info("初始化行政区数据");
        ClassLoader classLoader = ResourceReader.class.getClassLoader();
        String resourcePath = "multilingual/area/";
        // 获取指定目录下的所有资源
        Enumeration<URL> resources;
        try {
            resources = classLoader.getResources(resourcePath);
            while (resources.hasMoreElements()) {
                URL resourceDirUrl = resources.nextElement();
                processDirectory(resourceDirUrl, classLoader, resourcePath);
            }
        } catch (IOException e) {
            log.error("Failed to load resources from path: {}", resourcePath, e);
        }
    }

    private void processDirectory(URL resourceDirUrl, ClassLoader classLoader, String resourcePath) throws IOException {
        log.info("行政区数据地址：{}=={}", resourcePath, resourceDirUrl.getProtocol());
        // 根据URL获取目录下的文件列表
        List<String> filenames = new ArrayList<>();
        if ("file".equals(resourceDirUrl.getProtocol())) {
            File resourceDir = new File(resourceDirUrl.getFile());
            filenames = Arrays.stream(Objects.requireNonNull(resourceDir.listFiles()))
                    .filter(file -> file.isFile() && file.getName().matches(AREA_REGEX))
                    .map(File::getName)
                    .collect(Collectors.toList());
        }else if ("jar".equals(resourceDirUrl.getProtocol())) {
            JarURLConnection jarConnection = (JarURLConnection) resourceDirUrl.openConnection();
            JarFile jarFile = jarConnection.getJarFile();
            Enumeration<JarEntry> entries = jarFile.entries();
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                String fileName = entry.getName().substring(entry.getName().lastIndexOf('/') + 1);
                if (entry.getName().startsWith(resourcePath) && !entry.isDirectory()
                        && fileName.matches(AREA_REGEX)) {
                    filenames.add(entry.getName().substring(resourcePath.length()));
                }
            }
        }
        log.info("行政区数据地址：{}，文件列表：{}", resourcePath, JsonUtil.beanToJson(filenames));
        // 读取文件并解析
        for (String filename : filenames) {
            try (InputStream inputStream = classLoader.getResourceAsStream(resourcePath + filename)) {
                if (inputStream != null) {
                    String fileContent = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                            .lines()
                            .collect(Collectors.joining("\n"));
                    String countryCode = extractCountryCode(filename);
                    List<Area> areas = JsonUtil.jsonToBean(fileContent, new TypeReference<List<Area>>() {});
                    AREA_LIST_CACHE.addAll(areas);
                    AREA_MAP_CACHE.put(countryCode, areas);
                } else {
                    log.error("Resource file not found: {}", filename);
                }
            } catch (Exception e) {
                log.error("Error loading file: {}", filename, e);
            }
        }
    }
    private String extractCountryCode(String filename) {
        Pattern pattern = Pattern.compile(AREA_REGEX);
        Matcher matcher = pattern.matcher(filename);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
    @Override
    public List<Area> getArea(final String regionalCode) {
        return AREA_MAP_CACHE.get(regionalCode);
    }

    @Override
    public Area getAreaByCode(final String code) {
        List<Area> allLocations = flattenLocations(AREA_LIST_CACHE);
        return allLocations.stream()
                .filter(location -> location.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    private  List<Area> flattenLocations(List<Area> provinces) {
        List<Area> flatList = new ArrayList<>();
        for (Area province : provinces) {
            flatList.add(province); // 将Province添加到列表中
            if (province.getChildren() != null) {
                for (Area district : province.getChildren()) {
                    flatList.add(district); // 将District添加到列表中
                }
            }
        }
        return flatList;
    }

}