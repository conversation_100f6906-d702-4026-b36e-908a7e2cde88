/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.area;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.chargebolt.commons.enums.MultilingualEnum;
import com.chargebolt.dao.common.RegionalsDAO;
import com.chargebolt.dao.common.model.Regionals;
import com.chargebolt.ezreal.response.commons.RegionalResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.data.repository.init.ResourceReader;
import org.springframework.stereotype.Service;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.PostConstruct;

/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: RegionalsServiceImpl.java, v 0.1 2024-08-30 16:41:08 Exp $
 */
@Slf4j
@Service
public class RegionalsServiceImpl implements RegionalsService {
    private static Map<String, Map<String, String>> REGIONALS_MAP_CACHE = new HashMap<>();
    private static final String REGIONALS_LANG_REGEX="regionals\\.(\\w+-\\w+)\\.json";
    /**
     * 电话区号缓存的地区数据
     */
    private static final String CACHE_REGIONALS_PHONE_CODE="__CACHE_REGIONALS_PHONE_CODE_";
    @PostConstruct
    public void init() {
        ClassLoader classLoader = ResourceReader.class.getClassLoader();
        String resourcePath = "multilingual/regionals/";
        // 获取指定目录下的所有资源
        Enumeration<URL> resources;
        try {
            resources = classLoader.getResources(resourcePath);
            while (resources.hasMoreElements()) {
                URL resourceDirUrl = resources.nextElement();
                processDirectory(resourceDirUrl, classLoader, resourcePath);
            }
        } catch (IOException e) {
            log.error("Failed to load resources from path: {}", resourcePath, e);
        }
    }
    private void processDirectory(URL resourceDirUrl, ClassLoader classLoader, String resourcePath) throws IOException {
        // 根据URL获取目录下的文件列表
        List<String> filenames = new ArrayList<>();
        if ("file".equals(resourceDirUrl.getProtocol())) {
            File resourceDir = new File(resourceDirUrl.getFile());
            filenames = Arrays.stream(Objects.requireNonNull(resourceDir.listFiles()))
                    .filter(file -> file.isFile() && file.getName().matches(REGIONALS_LANG_REGEX))
                    .map(File::getName)
                    .collect(Collectors.toList());
        }
        // 读取文件并解析
        for (String filename : filenames) {
            try (InputStream inputStream = classLoader.getResourceAsStream(resourcePath + filename)) {
                if (inputStream != null) {
                    String fileContent = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                            .lines()
                            .collect(Collectors.joining("\n"));
                    String lang = extractLang(filename);
                    Map<String, String> langMap = JsonUtil.jsonToBean(fileContent, new TypeReference<Map<String, String>>() {});
                    REGIONALS_MAP_CACHE.put(lang, langMap);
                } else {
                    log.error("Resource file not found: {}", filename);
                }
            } catch (Exception e) {
                log.error("Error loading file: {}", filename, e);
            }
        }
    }
    private String extractLang(String filename) {
        Pattern pattern = Pattern.compile(REGIONALS_LANG_REGEX);
        Matcher matcher = pattern.matcher(filename);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
    @Override
    public List<Regionals> listRecord(Regionals model) {
        String lang= ThreadLanguageHolder.getCurrentLang();
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        List<Regionals> regionals= regionalsDAO.listRecord(model);
        MultilingualEnum langEnum= MultilingualEnum.findLanguageEnum(lang);
        return regionals.stream()
                .peek(regional -> {
                    if(Objects.nonNull(REGIONALS_MAP_CACHE.get(langEnum.getLocale().toLanguageTag()))){
                        String name= REGIONALS_MAP_CACHE.get(langEnum.getLocale().toLanguageTag()).get(regional.getRegionalCode()+regional.getPhoneCode());
                        if(StringUtils.isNotBlank(name)){
                            regional.setName(name);
                        }
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public Regionals getRecord(Regionals model) {
        String lang= ThreadLanguageHolder.getCurrentLang();
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        model= regionalsDAO.getRecord(model);
        MultilingualEnum langEnum= MultilingualEnum.findLanguageEnum(lang);

        if(Objects.nonNull(model)&& Objects.nonNull(REGIONALS_MAP_CACHE.get(langEnum.getLocale().toLanguageTag()))){
            String name= REGIONALS_MAP_CACHE.get(langEnum.getLocale().toLanguageTag()).get(model.getRegionalCode()+model.getPhoneCode());
            if(StringUtils.isNotBlank(name)){
                model.setName(name);
            }
        }
        return model;
    }

    @Override
    public int saveRecord(Regionals model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return regionalsDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(Regionals model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return regionalsDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(Regionals model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return regionalsDAO.updateRecord(model);
    }

    @Override
    public List<RegionalResponse> regionalsList(final Long agentId) {
        List<Regionals> list= listRecord(new Regionals());

        List<RegionalResponse> response = list.stream()
                .map(regional -> {
                    RegionalResponse regionalResponse = new RegionalResponse();
                    BeanUtils.copyProperties(regional, regionalResponse);
                    return regionalResponse;
                })
                .collect(Collectors.toList());
        if(Objects.nonNull(agentId)){
            // 获取代理商预设顺序
        }else{
            // 默认顺序返回
        }
        return response;
    }

    @Override
    public Regionals getByPhoneCode(final String phoneCode) {
        RBucket<Regionals> cache= redissonClient.getBucket(CACHE_REGIONALS_PHONE_CODE+phoneCode);
        if(Objects.nonNull(cache)&& Objects.nonNull(cache.get())){
            return cache.get();
        }
        Regionals model= new Regionals();
        model.setPhoneCode(phoneCode);
        model= getRecord(model);
        if(Objects.nonNull(model)){
            cache.set(model, 10, TimeUnit.MINUTES);
        }
        return model;
    }

    @Override
    public Regionals getByRegionalCode(final String regionalCode) {
        Regionals model= new Regionals();
        model.setRegionalCode(regionalCode);
        List<Regionals> list= listRecord(model);
        return list.isEmpty()?null:list.get(0);
    }

    /**
     * 根据给定的 phoneCode 优先级列表对 Regionals 列表进行排序。
     *
     * @param regionals 区域列表
     * @param phoneCodes 优先级列表
     */
    public static void sortRegionalsByPhoneCode(List<RegionalResponse> regionals, List<String> phoneCodes) {
        Comparator<RegionalResponse> comparator = Comparator.comparingInt(regionalsItem -> {
            int index = phoneCodes.indexOf(regionalsItem.getPhoneCode());
            return index == -1 ? phoneCodes.size() : index;
        });
        regionals.sort(comparator);
    }


    /**
     * 推荐使用构造器注入
     */
    private final RegionalsDAO regionalsDAO;
    private final RedissonClient redissonClient;
    public RegionalsServiceImpl(ObjectProvider<RegionalsDAO> regionalsProvider,
                                ObjectProvider<RedissonClient> redissonClientProvider) {
        this.regionalsDAO= regionalsProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
    }
}