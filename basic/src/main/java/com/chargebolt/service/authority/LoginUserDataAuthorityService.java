/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.authority;

import com.chargebolt.commons.enums.CheckDataTypeEnum;
import com.chargebolt.context.UserDataAuthorityContext;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: LoginUserDataAuthorityService.java, v 1.0 2024-09-19 上午11:27 Exp $
 */
public interface LoginUserDataAuthorityService {

    /**
     * 检查用户数据的写权限
     * 常用的数据写检查，将用户已拥有的数据权限初始化到缓存，直接通过数据权限id判断
     * 常用的数据代理商、商户、门店
     *
     * @param userId
     * @param dataId
     * @param dataType
     * @return
     */
    boolean checkUserDataWritePermission(Long userId, Long dataId, CheckDataTypeEnum dataType);

    /**
     * 获取用户数据权限
     * 正常流程下一定会获取到拥有数据权限的对象，应为已经有其他消息初始化
     * 如果没有，一般是新注册用户，没有来得及初始化就登录系统，这是返回没有权限的默认值
     * 发送初始化消息
     *
     * @param userId
     * @return
     */
    UserDataAuthorityContext getUserDataAuthority(Long userId);


    /**
     * 获取登录用户的数据权限
     * 需要登录才能获取到权限
     * 通过LocalThread传递用户信息
     *
     * @return
     */
    UserDataAuthorityContext getLoginUserDataAuthority();
}