/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service.authority;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.commons.constant.CacheKeyConstant;
import com.chargebolt.commons.enums.CheckDataTypeEnum;
import com.chargebolt.component.interceptor.ThreadUserHolder;
import com.chargebolt.component.rocketmq.SyncDataAuthorityProducer;
import com.chargebolt.context.OssUserContext;
import com.chargebolt.context.QueryPermission;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.context.WritePermission;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: LoginUserDataAuthorityServiceImpl.java, v 1.0 2024-09-19 上午11:28 Exp $
 */
@Slf4j
@Component
public class LoginUserDataAuthorityServiceImpl implements LoginUserDataAuthorityService {
    /**
     * 没有权限返回的集合
     */
    private static final List<Long> NO_PERMISSION_IDS= Collections.singletonList(-99L);
    private final UserManager userManager;
    private final RedissonClient redissonClient;
    private final SyncDataAuthorityProducer syncDataAuthorityProducer;
    public LoginUserDataAuthorityServiceImpl(ObjectProvider<UserManager> userManagerProvider,
                                             ObjectProvider<RedissonClient> redissonClientProvider,
                                             ObjectProvider<SyncDataAuthorityProducer> syncDataAuthorityProducerProvider){
        this.userManager= userManagerProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
        this.syncDataAuthorityProducer= syncDataAuthorityProducerProvider.getIfUnique();
    }
    @Override
    public boolean checkUserDataWritePermission(final Long userId, final Long dataId, final CheckDataTypeEnum dataType) {
        if(Objects.nonNull(userId)){
            UserDataAuthorityContext userDataAuthorityContext= getUserDataAuthority(userId);
            if(Objects.equals(userDataAuthorityContext.getAuthorityLevel(), AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_1)){
                return Boolean.TRUE;
            }
            switch (dataType){
                case AGENT:
                    return userDataAuthorityContext.getWrite().getAgentIds().contains(dataId);
                case MERCHANT:
                    return userDataAuthorityContext.getWrite().getMchIds().contains(dataId);
                case SHOP:
                    return userDataAuthorityContext.getWrite().getShopIds().contains(dataId);
            }
        }
        return Boolean.FALSE;
    }


    @Override
    public UserDataAuthorityContext getUserDataAuthority(final Long userId) {
        RBucket<UserDataAuthorityContext> userBucket = redissonClient.getBucket(CacheKeyConstant.USER_DATA_AUTHORITY_KEY + userId);
        if(Objects.nonNull(userBucket)&& Objects.nonNull(userBucket.get())){
            return userBucket.get();
        }
        // 没有权限数据缓存，构建无权限对象，发送数据权限初始化消息
        OssUserDO ossUserDO= userManager.getById(userId);
        UserDataAuthorityContext userDataAuthorityContext= new UserDataAuthorityContext();
        userDataAuthorityContext.setUserId(userId);
        userDataAuthorityContext.setAgentId(ossUserDO.getAgentId());
        userDataAuthorityContext.setAuthorityLevel(AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_3);
        QueryPermission queryPermission= new QueryPermission();
        queryPermission.setAgentIds(NO_PERMISSION_IDS);
        queryPermission.setUserIds(NO_PERMISSION_IDS);
        userDataAuthorityContext.setQuery(queryPermission);

//        userDataAuthorityContext.getQuery().setAgentIds(NO_PERMISSION_IDS);
//        userDataAuthorityContext.getQuery().setUserIds(NO_PERMISSION_IDS);
        WritePermission writePermission= new WritePermission();
        writePermission.setShopIds(NO_PERMISSION_IDS);
        writePermission.setMchIds(NO_PERMISSION_IDS);
        writePermission.setAgentIds(NO_PERMISSION_IDS);
        userDataAuthorityContext.setWrite(writePermission);
//        userDataAuthorityContext.getWrite().setAgentIds(NO_PERMISSION_IDS);
//        userDataAuthorityContext.getWrite().setMchIds(NO_PERMISSION_IDS);
//        userDataAuthorityContext.getWrite().setShopIds(NO_PERMISSION_IDS);
        // 发送数据权限初始化消息
        syncDataAuthorityProducer.sendMsg(Arrays.asList(userId));
        return userDataAuthorityContext;
    }

    @Override
    public UserDataAuthorityContext getLoginUserDataAuthority() {
        OssUserContext userContext = ThreadUserHolder.getCurrentUser();
        // 用户没有登陆或者Controller没有@Login注解，返回null
        return Objects.isNull(userContext) ? null : getUserDataAuthority(userContext.getUserId());
    }
}