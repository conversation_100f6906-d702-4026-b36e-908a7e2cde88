package com.chargebolt.service.messagecenter;

import com.chargebolt.pheidi.request.MessageQuery;
import com.chargebolt.pheidi.response.MessageGroupVo;
import com.chargebolt.pheidi.response.MessageListItemVo;
import com.chargebolt.pheidi.response.PageData;

public interface MessageCenterService {

    Boolean markReadStatus(String externalId, Integer bizType);

    int unreadNum(String externalId);

    PageData<MessageGroupVo> groupList(String externalId);

    PageData<MessageListItemVo> messages(MessageQuery param);

}
