package com.chargebolt.service.messagecenter;

import java.util.List;

import com.chargebolt.pheidi.dto.MapDTO;
import com.chargebolt.pheidi.request.MessageTemplateChangeStatus;
import com.chargebolt.pheidi.request.MessageTemplateCreate;
import com.chargebolt.pheidi.request.MessageTemplateEdit;
import com.chargebolt.pheidi.request.MessageTemplateQuery;
import com.chargebolt.pheidi.response.MessageTemplateDetailResp;
import com.chargebolt.pheidi.response.MessageTemplateListItemResp;
import com.chargebolt.pheidi.response.PageData;

public interface MessageTemplateService {

    Long create(MessageTemplateCreate param);

    Boolean edit(MessageTemplateEdit param);

    PageData<MessageTemplateListItemResp> list(MessageTemplateQuery param);

    MessageTemplateDetailResp detail(Long id);

    List<MapDTO> templateCodeNameMap();

    Boolean changeStatus(MessageTemplateChangeStatus param);

    List<MapDTO> redirectGoalSelectItems();

    List<MapDTO> channelPushSelectItems();

}
