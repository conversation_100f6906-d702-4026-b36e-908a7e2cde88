package com.chargebolt.service.messagecenter;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class OneSignalUserService {

    @Value("${oneSignal.externalId.prefix}")
    private String onesignalUserPrefix;

    public String getExternalId(OssUserDO ossUserDO) {
        return String.format("os_ext_%s_%s_%s", onesignalUserPrefix, ossUserDO.getAgentId(), ossUserDO.getId());
    }

    public String getExternalId(OssUserDTO userDTO) {
        return String.format("os_ext_%s_%s_%s", onesignalUserPrefix, userDTO.getAgentId(), userDTO.getUserId());
    }

    public String getExternalId(Long agentId, Long userId) {
        return String.format("os_ext_%s_%s_%s", onesignalUserPrefix, agentId, userId);
    }
}
