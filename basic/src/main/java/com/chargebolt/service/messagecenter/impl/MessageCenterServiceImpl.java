package com.chargebolt.service.messagecenter.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.pheidi.request.MarkReadRequest;
import com.chargebolt.pheidi.request.MessageQuery;
import com.chargebolt.pheidi.request.UnReadMessageCountQuery;
import com.chargebolt.pheidi.response.MessageGroupVo;
import com.chargebolt.pheidi.response.MessageListItemVo;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.remote.PheidiRemoteService;
import com.chargebolt.service.messagecenter.MessageCenterService;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.mofa3.lang.domain.Result;

@Slf4j
@Service
public class MessageCenterServiceImpl implements MessageCenterService {

    @Resource
    private PheidiRemoteService pheidiRemoteService;

    @Override
    public Boolean markReadStatus(String externalId, Integer bizType) {
        MarkReadRequest markReadRequest = new MarkReadRequest();
        markReadRequest.setTargetId(externalId);
        markReadRequest.setBizType(bizType);
        Result<Boolean> result = pheidiRemoteService.read(markReadRequest);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public int unreadNum(String externalId) {
        UnReadMessageCountQuery unReadMessageCountQuery = new UnReadMessageCountQuery();
        unReadMessageCountQuery.setExternalId(externalId);
        Result<Integer> result = pheidiRemoteService.num(unReadMessageCountQuery);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public PageData<MessageGroupVo> groupList(String externalId) {
        UnReadMessageCountQuery messageGroupListQuery = new UnReadMessageCountQuery();
        messageGroupListQuery.setExternalId(externalId);
        Result<PageData<MessageGroupVo>> result = pheidiRemoteService.groupList(messageGroupListQuery);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public PageData<MessageListItemVo> messages(MessageQuery param) {
        Result<PageData<MessageListItemVo>> result = pheidiRemoteService.messages(param);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

}
