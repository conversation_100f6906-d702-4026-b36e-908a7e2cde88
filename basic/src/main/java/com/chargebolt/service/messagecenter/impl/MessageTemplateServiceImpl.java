package com.chargebolt.service.messagecenter.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.commons.enums.language.MsgTemplateBizTypeEnum;
import com.chargebolt.commons.enums.language.MsgTemplateStatusEnum;
import com.chargebolt.framework.i18n.I18nUtil;
import com.chargebolt.pheidi.dto.MapDTO;
import com.chargebolt.pheidi.request.MessageTemplateChangeStatus;
import com.chargebolt.pheidi.request.MessageTemplateCreate;
import com.chargebolt.pheidi.request.MessageTemplateEdit;
import com.chargebolt.pheidi.request.MessageTemplateQuery;
import com.chargebolt.pheidi.response.MessageTemplateDetailResp;
import com.chargebolt.pheidi.response.MessageTemplateListItemResp;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.remote.PheidiRemoteService;
import com.chargebolt.service.messagecenter.MessageTemplateService;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.mofa3.lang.domain.Result;

@Slf4j
@Service
public class MessageTemplateServiceImpl implements MessageTemplateService {

    @Resource
    private PheidiRemoteService pheidiRemoteService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public Long create(MessageTemplateCreate param) {

        Result<Long> result = pheidiRemoteService.create(param);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public Boolean edit(MessageTemplateEdit param) {
        Result<Boolean> result = pheidiRemoteService.edit(param);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public PageData<MessageTemplateListItemResp> list(MessageTemplateQuery param) {
        Result<PageData<MessageTemplateListItemResp>> result = pheidiRemoteService.list(param);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        PageData<MessageTemplateListItemResp> data = result.getData();
        data.getList().forEach(item -> {
            item.setTypeStr(i18nUtil.getMessage(MsgTemplateBizTypeEnum.getByCode(item.getType())));
            item.setStatusStr(i18nUtil.getMessage(MsgTemplateStatusEnum.getByCode(item.getStatus())));
        });
        return data;
    }

    @Override
    public MessageTemplateDetailResp detail(Long id) {
        Result<MessageTemplateDetailResp> result = pheidiRemoteService.detail(id);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        MessageTemplateDetailResp data = result.getData();
        return data;
    }

    @Override
    public List<MapDTO> templateCodeNameMap() {
        Result<List<MapDTO>> result = pheidiRemoteService.templateCodeNameMap();
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public Boolean changeStatus(MessageTemplateChangeStatus param) {
        Result<Boolean> result = pheidiRemoteService.changeStatus(param);
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public List<MapDTO> redirectGoalSelectItems() {
        Result<List<MapDTO>> result = pheidiRemoteService.redirectGoalSelectItems();
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }

    @Override
    public List<MapDTO> channelPushSelectItems() {
        Result<List<MapDTO>> result = pheidiRemoteService.channelPushSelectItems();
        if (!result.isSuccess()) {
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
        return result.getData();
    }
}
