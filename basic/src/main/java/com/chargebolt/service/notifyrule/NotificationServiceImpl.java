package com.chargebolt.service.notifyrule;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.commons.enums.language.NotifyRuleTargetStrategyEnum;
import com.chargebolt.dao.notifyrule.model.NotifyRule;
import com.chargebolt.pheidi.enums.TemplateCodeParamsEnum;
import com.chargebolt.pheidi.request.MessageBody;
import com.chargebolt.remote.PheidiRemoteService;
import com.chargebolt.service.notifyrule.strategy.dto.CalculateResult;
import com.chargebolt.service.messagecenter.OneSignalUserService;

import lombok.extern.slf4j.Slf4j;
import so.dian.talos.biz.service.TalosShopService;
import so.dian.talos.pojo.entity.ShopDO;

@Slf4j
@Component
public class NotificationServiceImpl implements NotificationService {

    @Resource
    private PheidiRemoteService pheidiRemoteService;
    @Resource
    private UserManager userManager;
    @Resource
    private TalosShopService talosShopService;
    @Resource
    private OneSignalUserService oneSignalUserService;

    @Override
    public void sendNotification(NotifyRule rule, List<CalculateResult> calculateResults) {
        log.info("send notification: {}", rule);
        log.info("calculateResults: {}", JSON.toJSONString(calculateResults));

        // 检查calculateResults 中提取的变量是否有冲突
        Set<String> variableSet = new HashSet<>();
        Map<String, String> variableMap = new HashMap<>();
        for (CalculateResult calculateResult : calculateResults) {
            Map<String, String> extraInfo = calculateResult.getExtraInfo();
            for (String key : extraInfo.keySet()) {
                if (variableSet.contains(key)) {
                    log.error("|sendNotification| variable name conflict: {}", key);
                }
                variableSet.add(key);
                variableMap.put(key, extraInfo.get(key));
            }
        }
        log.info("variableSet: {}", JSON.toJSONString(variableSet));
        log.info("variableMap: {}", JSON.toJSONString(variableMap));

        // 根据模板 code 获取需要获取的变量名集合
        TemplateCodeParamsEnum temlateCode = TemplateCodeParamsEnum.getByTemplateCode(rule.getTemplateCode());

        // 填充变量
        Map<String, String> contentVariable = new HashMap<>();
        temlateCode.getParamList().forEach(p -> {
            log.info("p: {}", p);
            if (!variableSet.contains(p.getParam())) {
                log.error("|sendNotification| variable lack: {}", p.getParam());
            } else {
                contentVariable.put(p.getParam(), variableMap.get(p.getParam()));
            }
        });

        Long shopId = calculateResults.stream()
                .filter(e -> e.getExtraInfo().get("shopId") != null)
                .map(e -> e.getExtraInfo().get("shopId"))
                .map(Long::parseLong).findFirst().orElse(null);

        MessageBody message = new MessageBody();
        message.setTemplateCode(rule.getTemplateCode());
        message.setContentVariable(contentVariable);
        message.setCreateTime(System.currentTimeMillis());
        message.setTargetId(getTargetIdByStrategy(rule, shopId));
        message.setTargetType(rule.getTargetStrategy());
        pheidiRemoteService.sendMessage(message);
    }

    /**
     * 根据策略获取 targetId
     * TODO 虽然这里很适合抽象设计一下，但考虑到现在还很简单，可能会过渡设计，所以先简单处理
     * 
     * @param rule
     * @param shopDO
     * @return
     */
    private String getTargetIdByStrategy(NotifyRule rule, Long shopId) {
        ShopDO shopDTO = talosShopService.queryById(shopId);
        Long bdId = shopDTO.getSellerId();
        // 获取 BD 的 agentId
        if (NotifyRuleTargetStrategyEnum.SHOP_BD.getCode() == rule.getTargetStrategy()) {
            return oneSignalUserService.getExternalId(shopDTO.getAgentId(),bdId);
        }
        if (NotifyRuleTargetStrategyEnum.SHOP_BD_SUPERIOR.getCode() == rule.getTargetStrategy()
                || NotifyRuleTargetStrategyEnum.SHOP_BD_BOSS.getCode() == rule.getTargetStrategy()) {
            List<OssUserDO> ossUserDOS = userManager.getByAgentId(shopDTO.getAgentId());
            OssUserDO boss = ossUserDOS.stream().filter(ossUserDO -> ossUserDO.getType() == 2).findFirst().orElse(null);
            if (boss != null) {
                return oneSignalUserService.getExternalId(shopDTO.getAgentId(),boss.getId());
            } else {
                log.error("|getTargetId| boss not found: {}", shopDTO.getAgentId());
                return null;
            }
        }
        return null;
    }

}
