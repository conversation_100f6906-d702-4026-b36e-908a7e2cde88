package com.chargebolt.service.notifyrule;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.chargebolt.commons.enums.language.NotifyRuleScheduleEnum;
import com.chargebolt.commons.enums.language.NotifyRuleStatusEnum;
import com.chargebolt.commons.enums.language.NotifyRuleTargetStrategyEnum;
import com.chargebolt.commons.enums.language.NotifyRuleTypeEnum;
import com.chargebolt.dao.notifyrule.NotifyRuleDao;
import com.chargebolt.dao.notifyrule.model.NotifyRule;
import com.chargebolt.response.notifyrule.RuleCondition;
import com.google.common.collect.Lists;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class NotifyRuleResetService {

    @Getter
    @AllArgsConstructor
    enum ResetSourceTagEnum {
        DEVICE_OFFLINE(1, "设备离线提醒", NotifyRuleScheduleEnum.ONE_HOUR.getCode(), NotifyRuleTargetStrategyEnum.SHOP_BD.getCode(), "device_offline"),
        DEVICE_LACK(2, "设备缺宝提醒", NotifyRuleScheduleEnum.ONE_HOUR.getCode(), NotifyRuleTargetStrategyEnum.SHOP_BD.getCode(), "device_lack"),
        DEVICE_FULL(3, "设备满宝提醒", NotifyRuleScheduleEnum.ONE_HOUR.getCode(), NotifyRuleTargetStrategyEnum.SHOP_BD.getCode(), "device_full"),;

        private Integer tag;
        private String name;
        private Integer schedule;
        private Integer targetStrategy;
        private String templateCode;

        public static ResetSourceTagEnum getByTag(Integer tag) {
            for (ResetSourceTagEnum item : ResetSourceTagEnum.values()) {
                if (item.getTag().equals(tag)) {
                    return item;
                }
            }
            return null;
        }

    }

    @Resource
    private NotifyRuleDao notifyRuleDao;

    public void reset(NotifyRule rule) {
        Integer resetSourceTag = rule.getResetSourceTag();
        NotifyRule origin = new NotifyRule();
        origin.setId(rule.getId());
        origin.setType(rule.getType());
        origin.setStatus(NotifyRuleStatusEnum.DISABLE.getCode());
        origin.setAgentId(rule.getAgentId());
        origin.setLastScheduleTime(LocalDateTime.now());
        origin.setSchedule(ResetSourceTagEnum.getByTag(resetSourceTag).getSchedule());
        origin.setTargetStrategy(ResetSourceTagEnum.getByTag(resetSourceTag).getTargetStrategy());
        origin.setTemplateCode(ResetSourceTagEnum.getByTag(resetSourceTag).getTemplateCode());
        origin.setResetSourceTag(resetSourceTag);
        origin.setGmtCreate(System.currentTimeMillis());
        origin.setGmtUpdate(System.currentTimeMillis());
        origin.setDeleted(0);
        if (resetSourceTag == 1) {
            origin.setConditions(JSON.toJSONString(defaultConditionsOfTag1()));
        } else if (resetSourceTag == 2) {
            origin.setConditions(JSON.toJSONString(defaultConditionsOfTag2()));
        } else if (resetSourceTag == 3) {
            origin.setConditions(JSON.toJSONString(defaultConditionsOfTag3()));
        }
        notifyRuleDao.update(origin);
    }

    public void init(Long agentId) {
        for (ResetSourceTagEnum item : ResetSourceTagEnum.values()) {
            NotifyRule origin = new NotifyRule();
            if (item.getTag() == 1) {
                origin.setType(NotifyRuleTypeEnum.DEVICE_OFFLINE_REMAINDER.getCode());
            } else if (item.getTag() == 2) {
                origin.setType(NotifyRuleTypeEnum.DEVICE_LACK_POWERBANK.getCode());
            } else if (item.getTag() == 3) {
                origin.setType(NotifyRuleTypeEnum.DEVICE_OVERFLOW_POWERBANK.getCode());
            }
            origin.setStatus(NotifyRuleStatusEnum.DISABLE.getCode());
            origin.setAgentId(agentId);     
            origin.setSchedule(item.getSchedule());
            origin.setLastScheduleTime(LocalDateTime.now());
            origin.setTargetStrategy(item.getTargetStrategy());
            origin.setTemplateCode(item.getTemplateCode());
            origin.setResetSourceTag(item.getTag());
            origin.setGmtCreate(System.currentTimeMillis());
            origin.setGmtUpdate(System.currentTimeMillis());
            origin.setDeleted(0);
            if (item.getTag() == 1) {
                origin.setConditions(JSON.toJSONString(defaultConditionsOfTag1()));
            } else if (item.getTag() == 2) {
                origin.setConditions(JSON.toJSONString(defaultConditionsOfTag2()));
            } else if (item.getTag() == 3) {
                origin.setConditions(JSON.toJSONString(defaultConditionsOfTag3()));
            }   
            notifyRuleDao.insert(origin);
        }
    }

    private List<List<RuleCondition>> defaultConditionsOfTag3() {
        RuleCondition condition11 = new RuleCondition();
        condition11.setIndicatorKey("device_powerbank_count");
        condition11.setOperator("gte");
        condition11.setValue("5");
        RuleCondition condition12 = new RuleCondition();
        condition12.setIndicatorKey("device_type");
        condition12.setOperator("eq");
        condition12.setValue("M6");
        List<RuleCondition> conditionsList1 = Lists.newArrayList(condition11, condition12);

        RuleCondition condition21 = new RuleCondition();
        condition21.setIndicatorKey("device_powerbank_count");
        condition21.setOperator("gte");
        condition21.setValue("11");
        RuleCondition condition22 = new RuleCondition();
        condition22.setIndicatorKey("device_type");
        condition22.setOperator("eq");
        condition22.setValue("M12");
        List<RuleCondition> conditionsList2 = Lists.newArrayList(condition21, condition22);

        List<List<RuleCondition>> conditionsListList = Lists.newArrayList();
        conditionsListList.add(conditionsList1);
        conditionsListList.add(conditionsList2);
        return conditionsListList;
    }

    private List<List<RuleCondition>> defaultConditionsOfTag2() {
        RuleCondition condition11 = new RuleCondition();
        condition11.setIndicatorKey("device_powerbank_count");
        condition11.setOperator("lte");
        condition11.setValue("2");
        RuleCondition condition12 = new RuleCondition();
        condition12.setIndicatorKey("device_type");
        condition12.setOperator("eq");
        condition12.setValue("M6");
        List<RuleCondition> conditionsList1 = Lists.newArrayList(condition11, condition12);

        RuleCondition condition21 = new RuleCondition();
        condition21.setIndicatorKey("device_powerbank_count");
        condition21.setOperator("lte");
        condition21.setValue("4");
        RuleCondition condition22 = new RuleCondition();
        condition22.setIndicatorKey("device_type");
        condition22.setOperator("eq");
        condition22.setValue("M12");
        List<RuleCondition> conditionsList2 = Lists.newArrayList(condition21, condition22);

        List<List<RuleCondition>> conditionsListList = Lists.newArrayList();
        conditionsListList.add(conditionsList1);
        conditionsListList.add(conditionsList2);
        return conditionsListList;
    }

    private List<List<RuleCondition>> defaultConditionsOfTag1() {

        RuleCondition condition11 = new RuleCondition();
        condition11.setIndicatorKey("device_offline_hours");
        condition11.setOperator("gte");
        condition11.setValue("2");
        RuleCondition condition12 = new RuleCondition();
        condition12.setIndicatorKey("device_type");
        condition12.setOperator("eq");
        condition12.setValue("M6");
        List<RuleCondition> conditionsList1 = Lists.newArrayList(condition11, condition12);

        RuleCondition condition21 = new RuleCondition();
        condition21.setIndicatorKey("device_offline_hours");
        condition21.setOperator("gte");
        condition21.setValue("2");
        RuleCondition condition22 = new RuleCondition();
        condition22.setIndicatorKey("device_type");
        condition22.setOperator("eq");
        condition22.setValue("M12");
        List<RuleCondition> conditionsList2 = Lists.newArrayList(condition21, condition22);

        List<List<RuleCondition>> conditionsListList = Lists.newArrayList();
        conditionsListList.add(conditionsList1);
        conditionsListList.add(conditionsList2);
        return conditionsListList;
    }

}
