package com.chargebolt.service.notifyrule;

import java.util.List;

import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.request.notifyrule.CustomRuleEditReq;
import com.chargebolt.request.notifyrule.CustomRuleEnableReq;
import com.chargebolt.request.notifyrule.CustomRuleQuery;
import com.chargebolt.response.notifyrule.CustomRuleDetailResp;
import com.chargebolt.response.notifyrule.CustomRuleListItemResp;
import com.chargebolt.response.notifyrule.NotifyWhitelistRow;

public interface NotifyRuleService {

    /**
     * 分页查询通知规则列表
     * 
     * @param query 查询参数
     * @param userDataAuthorityContext 用户数据权限上下文
     * @return 分页数据
     */
    PageData<CustomRuleListItemResp> list(CustomRuleQuery query, UserDataAuthorityContext userDataAuthorityContext);

    /**
     * 查询通知规则详情
     * 
     * @param ruleId 规则ID
     * @return 通知规则详情
     */
    CustomRuleDetailResp detail(Long ruleId);

    /**
     * 编辑通知规则
     * 
     * @param editReq 编辑请求
     * @return 规则ID
     */
    Long edit(CustomRuleEditReq editReq);

    /**
     * 启用/禁用通知规则
     * 
     * @param req 请求
     */
    void enable(CustomRuleEnableReq req);

    /**
     * 重置通知规则
     * 
     * @param ruleId 规则ID
     */
    void reset(Long ruleId);

    /**
     * 初始化通知规则
     * 
     * @param agentId 代理商ID
     */
    void initNotifyRule(Long agentId);

    /**
     * 查询白名单行数据
     * 
     * @param ruleId 规则ID
     * @return 白名单行数据
     */
    List<NotifyWhitelistRow> findWhiteListRowByRuleId(Long ruleId);

    /**
     * 查询白名单数量
     * 
     * @param ruleId 规则ID
     * @return 白名单数量
     */
    Integer countWhiteListByRuleId(Long ruleId);

    /**
     * 清除所有白名单
     * 
     * @param ruleId 规则ID
     */
    void clearAllWhiteList(Long ruleId);
}
