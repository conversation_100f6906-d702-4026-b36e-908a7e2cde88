package com.chargebolt.service.notifyrule;

import java.util.Arrays;

/**
 * 运算符枚举
 * 定义了指标计算支持的运算符类型及其比较逻辑
 * 
 * 支持的运算符：
 * - 等于(=)：判断两个值是否相等
 * - 不等于(!=)：判断两个值是否不相等
 * - 大于(>)：判断第一个值是否大于第二个值
 * - 小于(<)：判断第一个值是否小于第二个值
 * - 大于等于(>=)：判断第一个值是否大于等于第二个值
 * - 小于等于(<=)：判断第一个值是否小于等于第二个值
 */
public enum OperatorEnum {
    EQUALS("eq", "等于"),
    NOT_EQUALS("neq", "不等于"),
    GREATER_THAN("gt", "大于"),
    LESS_THAN("lt", "小于"),
    GREATER_THAN_OR_EQUALS("gte", "大于等于"),
    LESS_THAN_OR_EQUALS("lte", "小于等于");

    private final String operator;
    private final String description;

    OperatorEnum(String operator, String description) {
        this.operator = operator;
        this.description = description;
    }

    public static OperatorEnum getOperatorEnum(String operator) {
        return Arrays.stream(OperatorEnum.values())
                .filter(op -> op.getOperator().equals(operator))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("不支持的运算符"));
    }

    /**
     * 根据运算符比较两个值
     * 
     * @param <T>    比较值的类型，必须实现 Comparable 接口
     * @param value1 第一个值
     * @param value2 第二个值
     * @return true：满足运算符条件；false：不满足运算符条件
     * @throws IllegalArgumentException 当遇到不支持的运算符时抛出
     */
    public <T extends Comparable<T>> boolean compare(T value1, T value2) {
        int result = value1.compareTo(value2);
        switch (this) {
            case EQUALS:
                return result == 0;
            case NOT_EQUALS:
                return result != 0;
            case GREATER_THAN:
                return result > 0;
            case LESS_THAN:
                return result < 0;
            case GREATER_THAN_OR_EQUALS:
                return result >= 0;
            case LESS_THAN_OR_EQUALS:
                return result <= 0;
            default:
                throw new IllegalArgumentException("不支持的运算符");
        }
    }

    public String getOperator() {
        return operator;
    }

    public String getDescription() {
        return description;
    }
}
