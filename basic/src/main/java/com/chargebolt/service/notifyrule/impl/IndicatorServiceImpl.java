package com.chargebolt.service.notifyrule.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.chargebolt.dao.notifyrule.IndicatorDao;
import com.chargebolt.dao.notifyrule.model.NotifyIndicator;
import com.chargebolt.response.notifyrule.IndicatorListItemResp;
import com.chargebolt.response.notifyrule.IndicatorOperator;
import com.chargebolt.service.notifyrule.IndicatorService;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.eros.manager.I18nTextsManager;

@Slf4j
@Service
public class IndicatorServiceImpl implements IndicatorService {

    @Resource
    private IndicatorDao indicatorDao;
    @Resource
    private I18nTextsManager i18nTextsManager;

    @Override
    public List<IndicatorListItemResp> list() {
        List<NotifyIndicator> indicators = indicatorDao.findEnableIndicators();

        return indicators.stream().map(indicator -> {
            IndicatorListItemResp resp = new IndicatorListItemResp();
            resp.setId(indicator.getId());
            resp.setIndicatorKey(indicator.getIndicatorKey());
            resp.setOperators(JSON.parseArray(indicator.getOperators(), IndicatorOperator.class));
            resp.setValue(
                    JSON.parseObject(indicator.getValue(), com.chargebolt.response.notifyrule.IndicatorValue.class));
            String namePrefixKey = indicator.getNameI18n().replace(".name", "");
            Map<String, String> i18nTexts = i18nTextsManager.getTextByPrefixKey(ThreadLanguageHolder.getCurrentLang(),
                    namePrefixKey);
            if (MapUtils.isNotEmpty(i18nTexts)) {
                resp.setName(i18nTexts.get(indicator.getNameI18n()));
                resp.setCaliber(i18nTexts.get(indicator.getCaliberI18n()));
                resp.setDescription(i18nTexts.get(indicator.getDescriptionI18n()));
            }
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public IndicatorListItemResp detail(Long indicatorId) {
        NotifyIndicator indicator = indicatorDao.findById(indicatorId);
        if (indicator == null) {
            throw BizException.create(ErrorCodeEnum.INTERNAL_SERVER_ERROR, "Indicator not found");
        }
        IndicatorListItemResp resp = new IndicatorListItemResp();
        resp.setId(indicator.getId());
        resp.setIndicatorKey(indicator.getIndicatorKey());
        resp.setOperators(JSON.parseArray(indicator.getOperators(), IndicatorOperator.class));
        resp.setValue(JSON.parseObject(indicator.getValue(), com.chargebolt.response.notifyrule.IndicatorValue.class));
        String namePrefixKey = indicator.getNameI18n().replace(".name", "");
        Map<String, String> i18nTexts = i18nTextsManager.getTextByPrefixKey(ThreadLanguageHolder.getCurrentLang(),
                namePrefixKey);
        if (MapUtils.isNotEmpty(i18nTexts)) {
            resp.setName(i18nTexts.get(indicator.getNameI18n()));
        }
        return resp;
    }

}
