package com.chargebolt.service.notifyrule.impl;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.PageData;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.enums.language.NotifyRuleScheduleEnum;
import com.chargebolt.commons.enums.language.NotifyRuleStatusEnum;
import com.chargebolt.commons.enums.language.NotifyRuleTargetStrategyEnum;
import com.chargebolt.commons.enums.language.NotifyRuleTypeEnum;
import com.chargebolt.commons.exception.NotifyRuleExceptionEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.notifyrule.IndicatorDao;
import com.chargebolt.dao.notifyrule.NotifyRuleDao;
import com.chargebolt.dao.notifyrule.NotifyWhitelistDao;
import com.chargebolt.dao.notifyrule.model.NotifyRule;
import com.chargebolt.dao.notifyrule.model.NotifyWhitelist;
import com.chargebolt.framework.i18n.I18nUtil;
import com.chargebolt.request.notifyrule.CustomRuleEditReq;
import com.chargebolt.request.notifyrule.CustomRuleEnableReq;
import com.chargebolt.request.notifyrule.CustomRuleQuery;
import com.chargebolt.response.notifyrule.CustomRuleDetailResp;
import com.chargebolt.response.notifyrule.CustomRuleListItemResp;
import com.chargebolt.response.notifyrule.NotifyWhitelistRow;
import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.service.authority.LoginUserDataAuthorityService;
import com.chargebolt.service.notifyrule.NotifyRuleResetService;
import com.chargebolt.service.notifyrule.NotifyRuleService;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import lombok.extern.slf4j.Slf4j;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;

/**
 * 通知规则服务实现类
 * 
 * 负责通知规则的查询、编辑、启用/禁用、重置等操作
 */
@Slf4j
@Service
public class NotifyRuleServiceImpl implements NotifyRuleService {

    @Resource
    private NotifyRuleDao notifyRuleDao;
    @Resource
    private NotifyWhitelistDao notifyWhitelistDao;
    @Resource
    private IndicatorDao indicatorDao;
    @Resource
    private AgentService agentService;
    @Resource
    private NotifyRuleResetService notifyRuleResetService;
    @Resource
    private I18nUtil i18nUtil;
    @Resource
    private LoginUserDataAuthorityService loginUserDataAuthorityService;

    /**
     * 查询通知规则列表
     * 
     * @param param 查询参数
     * @return 通知规则列表
     */
    public List<NotifyRule> listRecord(CustomRuleQuery param) {
        log.debug("查询通知规则列表，参数：{}", JSON.toJSONString(param));
        NotifyRule model = new NotifyRule();
        model.setType(param.getRuleType());
        model.setStatus(param.getStatus());
        model.setAgentId(param.getAgentId());
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return notifyRuleDao.listRecord(model);
    }

    @Override
    public PageData<CustomRuleListItemResp> list(CustomRuleQuery query,
            UserDataAuthorityContext userDataAuthorityContext) {
        log.info("分页查询通知规则列表，参数：{}", JSON.toJSONString(query));

        // 如果登录人是代理商，只能查看自己的规则，agentId 为登录人的agentId
        if (userDataAuthorityContext.getAuthorityLevel() == AuthorityLevelEnum.AGENT.getCode()) {
            query.setAgentId(userDataAuthorityContext.getAgentId());
            log.debug("当前用户是代理商，只能查看自己的规则，agentId：{}", userDataAuthorityContext.getAgentId());
        }

        Page<NotifyRule> ruleList = PageHelper
                .startPage(query.getPageNo(), query.getPageSize(), Boolean.TRUE)
                .setOrderBy(" id DESC").doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        listRecord(query);
                    }
                });
        if (CollectionUtils.isEmpty(ruleList.getResult())) {
            log.debug("查询结果为空");
            return PageData.create(null);
        }
        List<CustomRuleListItemResp> customRuleList = convertCustomRuleListItemResp(ruleList.getResult());
        return PageData.create(customRuleList, ruleList.getTotal(), (long) ruleList.getPageNum(),
                ruleList.getPageSize());
    }

    @Override
    public CustomRuleDetailResp detail(Long ruleId) {
        log.info("查询通知规则详情，规则ID：{}", ruleId);

        // 获取并校验规则
        // 如果是平台用户，这里可以查看所有代理商数据
        NotifyRule rule = checkRuleExistAndPermission(ruleId);

        // 构建响应对象
        CustomRuleDetailResp resp = new CustomRuleDetailResp();
        resp.setRuleId(rule.getId());
        // 从国际化配置中取值
        resp.setRuleName(i18nUtil.getMessage(NotifyRuleTypeEnum.getByCode(rule.getType())));
        resp.setRuleType(rule.getType());
        resp.setStatus(rule.getStatus());
        resp.setStatusStr(i18nUtil.getMessage(NotifyRuleStatusEnum.getByCode(rule.getStatus())));
        resp.setAgentId(rule.getAgentId());
        resp.setSchedule(rule.getSchedule());
        resp.setScheduleStr(i18nUtil.getMessage(NotifyRuleScheduleEnum.getByCode(rule.getSchedule())));
        resp.setTargetStrategy(rule.getTargetStrategy());
        resp.setTargetStrategyStr(i18nUtil.getMessage(NotifyRuleTargetStrategyEnum.getByCode(rule.getTargetStrategy())));
        resp.setTemplateCode(rule.getTemplateCode());
        resp.setConditions(JSON.parseObject(rule.getConditions(), new TypeReference<List<List<RuleCondition>>>() {
        }));

        log.debug("查询通知规则详情成功，规则ID：{}", ruleId);
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long edit(CustomRuleEditReq editReq) {
        log.info("编辑通知规则，请求参数：{}", JSON.toJSONString(editReq));
        // 获取并校验规则
        // 如果是平台用户，这里可以编辑所有代理商数据
        NotifyRule rule = checkRuleExistAndPermission(editReq.getRuleId());

        // 更新规则
        rule.setType(editReq.getRuleType());
        rule.setSchedule(editReq.getSchedule());
        rule.setTargetStrategy(editReq.getTargetStrategy());
        rule.setConditions(JSON.toJSONString(editReq.getConditions()));
        rule.setGmtUpdate(System.currentTimeMillis());
        rule.setTemplateCode(editReq.getTemplateCode());

        notifyRuleDao.update(rule);
        log.info("编辑通知规则成功，规则ID：{}", rule.getId());

        return rule.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(CustomRuleEnableReq req) {
        log.info("修改通知规则状态，规则ID：{}，状态：{}", req.getRuleId(), req.getStatus());

        // 获取并校验规则
        NotifyRule rule = checkRuleExistAndPermission(req.getRuleId());

        // 更新状态
        if (req.getStatus()) {
            rule.setStatus(NotifyRuleStatusEnum.ENABLE.getCode());
            log.debug("启用通知规则，规则ID：{}", req.getRuleId());
        } else {
            rule.setStatus(NotifyRuleStatusEnum.DISABLE.getCode());
            log.debug("禁用通知规则，规则ID：{}", req.getRuleId());
        }

        notifyRuleDao.update(rule);
        log.info("修改通知规则状态成功，规则ID：{}", req.getRuleId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reset(Long ruleId) {
        log.info("重置通知规则，规则ID：{}", ruleId);

        // 获取并校验规则
        NotifyRule rule = checkRuleExistAndPermission(ruleId);

        // 重置规则
        notifyRuleResetService.reset(rule);
        log.info("重置通知规则成功，规则ID：{}", ruleId);
    }

    @Override
    public void initNotifyRule(Long agentId) {
        log.info("初始化通知规则，代理商ID：{}", agentId);
        int count = notifyRuleDao.countByAgentId(agentId);
        if (count == 0) {
            notifyRuleResetService.init(agentId);
        } else {
            log.info("该代理商已经存在通知规则，无需初始化，代理商ID：{}", agentId);
            throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_EXIST.getCode(),
                    NotifyRuleExceptionEnum.RULE_EXIST.getDesc());
        }
    }

    /**
     * 检查规则是否存在并且当前用户是否有权限操作
     * 
     * @param ruleId  规则ID
     * @param agentId 代理商ID
     * @return 通知规则
     */
    private NotifyRule checkRuleExistAndPermission(Long ruleId) {
        // 检查规则是否存在
        NotifyRule rule = notifyRuleDao.findById(ruleId);
        if (rule == null) {
            log.warn("通知规则不存在，规则ID：{}", ruleId);
            throw new I18nMessageException(NotifyRuleExceptionEnum.RULE_NOT_EXIST.getCode(),
                    NotifyRuleExceptionEnum.RULE_NOT_EXIST.getDesc());
        }

        UserDataAuthorityContext authority = loginUserDataAuthorityService.getLoginUserDataAuthority();
        if (authority.getAuthorityLevel() == AuthorityLevelEnum.AGENT.getCode()) {
            if (!Objects.equals(rule.getAgentId(), authority.getAgentId())) {
                log.warn("无权操作该通知规则，规则ID：{}，规则所属代理商ID：{}，当前代理商ID：{}",
                        ruleId, rule.getAgentId(), authority.getAgentId());
                throw new I18nMessageException(NotifyRuleExceptionEnum.NO_PERMISSION.getCode(),
                        NotifyRuleExceptionEnum.NO_PERMISSION.getDesc());
            }
            return rule;
        } else if (authority.getAuthorityLevel() == AuthorityLevelEnum.ALL.getCode()) {
            // 平台用户，可以操作所有代理商数据
            return rule;
        } else {
            // 用户，可以操作自己所属代理商数据
            throw new I18nMessageException(NotifyRuleExceptionEnum.NO_PERMISSION.getCode(),
                    NotifyRuleExceptionEnum.NO_PERMISSION.getDesc());
        }

    }

    /**
     * 将通知规则实体转换为列表响应对象
     * 
     * @param result 通知规则实体列表
     * @return 列表响应对象列表
     */
    private List<CustomRuleListItemResp> convertCustomRuleListItemResp(List<NotifyRule> result) {
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }

        // 获取所有代理商信息
        List<Long> agentIds = result.stream().map(NotifyRule::getAgentId).distinct().collect(Collectors.toList());
        List<Agent> agentList = agentService.queryListByIds(agentIds);
        Map<Long, String> agentMap = agentList.stream()
                .collect(Collectors.toMap(Agent::getId, Agent::getAgentName, (v1, v2) -> v1));

        return result.stream().map(rule -> {
            CustomRuleListItemResp resp = new CustomRuleListItemResp();
            resp.setRuleId(rule.getId());
            // 从国际化配置中取值
            resp.setRuleName(i18nUtil.getMessage(NotifyRuleTypeEnum.getByCode(rule.getType())));
            resp.setRuleType(rule.getType());
            resp.setStatus(rule.getStatus());
            resp.setStatusStr(i18nUtil.getMessage(NotifyRuleStatusEnum.getByCode(rule.getStatus())));
            resp.setAgentId(rule.getAgentId());
            resp.setAgentName(
                    agentMap.getOrDefault(rule.getAgentId(), i18nUtil.getMessage("notify.rule.agent.not.exist")));
            resp.setUpdateTime(
                    LocalDateTime.ofInstant(Instant.ofEpochMilli(rule.getGmtUpdate()), ZoneId.systemDefault()));
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<NotifyWhitelistRow> findWhiteListRowByRuleId(Long ruleId) {

        // 检查规则是否存在
        checkRuleExistAndPermission(ruleId);

        List<NotifyWhitelist> dataList = notifyWhitelistDao.findByRuleId(ruleId);
        List<NotifyWhitelistRow> rowList = dataList.stream().map(NotifyWhitelist::getObjId).map(e -> {
            return new NotifyWhitelistRow(String.valueOf(e));
        }).collect(Collectors.toList());

        return rowList;
    }

    @Override
    public Integer countWhiteListByRuleId(Long ruleId) {
        // 检查规则是否存在
        checkRuleExistAndPermission(ruleId);

        return notifyWhitelistDao.countByRuleId(ruleId);
    }

    @Override
    public void clearAllWhiteList(Long ruleId) {
        // 检查规则是否存在
        checkRuleExistAndPermission(ruleId);

        notifyWhitelistDao.clearAll(ruleId);
    }

}
