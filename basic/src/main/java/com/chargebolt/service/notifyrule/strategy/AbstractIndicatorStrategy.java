package com.chargebolt.service.notifyrule.strategy;

import java.util.Map;

import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.notifyrule.OperatorEnum;
import com.chargebolt.service.notifyrule.strategy.dto.CalculateResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 指标计算策略的抽象实现类
 * 提供了指标计算的通用框架，将计算逻辑分解为三个步骤：
 * 1. 验证作用域 {@link #validateScope(RuleCondition, Object)}
 * 2. 获取指标值 {@link #getIndicatorValue(Object)}
 * 3. 解析比较值 {@link #parseCompareValue(String)}
 *
 * @param <T> 指标值的类型，必须实现 Comparable 接口以支持比较操作
 *           例如：Integer 用于数量比较，String 用于字符串匹配等
 */
@Slf4j
public abstract class AbstractIndicatorStrategy<T extends Comparable<T>> implements IndicatorCalculateStrategy {

    @Override
    public CalculateResult calculate(RuleCondition condition, Object scopeDO) {
        // 验证作用域
        validateScope(condition, scopeDO);

        // 获取指标值
        T indicatorValue = getIndicatorValue(scopeDO);
        log.info("indicatorValue: {}", indicatorValue);
        log.info("condition.getIndicatorKey():{}, condition.getValue(): {}", condition.getIndicatorKey(),
                condition.getValue());
        // 获取比较值
        T compareValue = parseCompareValue(condition.getValue());

        // 根据运算符进行比较
        CalculateResult result = CalculateResult.builder()
                .result(OperatorEnum.getOperatorEnum(condition.getOperator())
                        .compare(indicatorValue, compareValue))
                .indicatorKey(condition.getIndicatorKey())
                .extraInfo(getExtraInfo())
                .build();
        return result;
    }

    /**
     * 验证作用域是否匹配
     * 检查条件的作用域和实际对象是否符合当前策略的要求
     *
     * @param condition 规则条件
     * @param scopeDO   作用域对象
     * @throws RuntimeException 当作用域不匹配时抛出异常
     */
    protected abstract void validateScope(RuleCondition condition, Object scopeDO);

    /**
     * 获取指标实际值
     * 从作用域对象中提取需要进行比较的指标值
     *
     * @param scopeDO 作用域对象
     * @return 提取出的指标值
     */
    protected abstract T getIndicatorValue(Object scopeDO);

    /**
     * 解析比较值
     * 将条件中的比较值字符串解析为实际的类型值
     *
     * @param value 比较值字符串
     * @return 解析后的比较值
     */
    protected abstract T parseCompareValue(String value);

    /**
     * 获取额外信息
     * 
     * @return 额外信息
     */
    protected abstract Map<String, String> getExtraInfo();
}
