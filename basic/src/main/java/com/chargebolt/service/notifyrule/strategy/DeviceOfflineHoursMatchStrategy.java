package com.chargebolt.service.notifyrule.strategy;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.notifyrule.strategy.dto.DeviceContext;
import com.chargebolt.service.notifyrule.strategy.dto.ShopOpenTime;
import com.chargebolt.service.agent.AgentBaseConfigService;

import lombok.extern.slf4j.Slf4j;
import so.dian.apollo.common.entity.BizResult;
import so.dian.apollo.dto.param.box.ApolloBoxParamDTO;
import so.dian.apollo.dto.result.box.ApolloBoxDTO;
import so.dian.demeter.common.enums.OnlineStatusEnum;
import so.dian.demeter.pojo.entity.DeviceDO;
import so.dian.demeter.remote.ApolloRemoteService;

@Slf4j
@Component("deviceOfflineHoursMatchStrategy")
public class DeviceOfflineHoursMatchStrategy extends AbstractIndicatorStrategy<BigDecimal> {

    public static final String DEVICE_NO = "deviceNo";
    public static final String SHOP_ID = "shopId";
    public static final String SHOP_NAME = "shopName";
    public static final String OFFLINE_TIME = "offlineTime";
    public static final String OFFLINE_HOURS = "offlineHours";

    private Map<String, String> extraInfo = new HashMap<>();

    @Resource
    private ApolloRemoteService apolloRemoteService;
    @Resource
    private AgentBaseConfigService agentBaseConfigService;

    @Override
    protected void validateScope(RuleCondition condition, Object scopeDO) {
        if (!(scopeDO instanceof DeviceContext)) {
            throw new RuntimeException("作用域对象类型不匹配");
        }
    }

    /**
     * 考虑时区问题
     * 不考虑特殊场景
     * 例如门店营业时间很短的情况。不考虑多段营业时间的场景。
     * 只考虑当前时间所在营业时间段
     */
    @Override
    protected BigDecimal getIndicatorValue(Object scopeDO) {
        DeviceContext deviceContext = (DeviceContext) scopeDO;
        extraInfo.put(DEVICE_NO, deviceContext.getDeviceDO().getDeviceNo());
        extraInfo.put(SHOP_ID, deviceContext.getShopDO().getId().toString());
        extraInfo.put(SHOP_NAME, deviceContext.getShopDO().getName());
        // 获取设备离线时长
        if (OnlineStatusEnum.ONLINE.getCode().equals(deviceContext.getDeviceDO().getOnlineStatus())) {
            return BigDecimal.ZERO;
        } else {
            String openTime = deviceContext.getShopDO().getOpenTime();
            List<ShopOpenTime> openTimeList = Lists.newArrayList();
            try {
                openTimeList = JSON.parseArray(openTime, ShopOpenTime.class);
            } catch (Exception e) {
                log.error("|getIndicatorValue| parse openTime error", e);
                return BigDecimal.ZERO;
            }
            if (openTimeList.isEmpty()) {
                return BigDecimal.ZERO;
            }
            // 查询门店所属代理商的时区
            Long agentId = deviceContext.getShopDO().getAgentId();
            AgentBaseConfig agentBaseConfig = agentBaseConfigService.getByAgentId(agentId);
            if (agentBaseConfig == null) {
                return BigDecimal.ZERO;
            }
            String timeZone = agentBaseConfig.getTimeZone();
            if (StringUtils.isBlank(timeZone)) {
                return BigDecimal.ZERO;
            }
            int offset = -1;
            // 获取门店时区对应的ZoneId
            ZoneId shopZoneId = ZoneId.of(timeZone);
            // 获取当前时间在门店时区的表示
            LocalDateTime nowAtShopZone = LocalDateTime.now(shopZoneId);
            // 获取当前日期在门店时区的表示
            LocalDate todayAtShopZone = nowAtShopZone.toLocalDate();
            log.info("|getIndicatorValue| timeZone: {}", timeZone);
            log.info("|getIndicatorValue| nowAtShopZone: {}", nowAtShopZone);
            log.info("|getIndicatorValue| openTimeList: {}", openTimeList);

            for (int i = 0; i < openTimeList.size(); i++) {
                ShopOpenTime shopOpenTime = openTimeList.get(i);
                // 使用门店时区的日期和营业时间创建起止时间
                LocalDateTime start = LocalDateTime.of(todayAtShopZone,
                        LocalTime.parse(shopOpenTime.getStart() + ":00"));
                LocalDateTime end = LocalDateTime.of(todayAtShopZone, LocalTime.parse(shopOpenTime.getEnd() + ":00"));
                log.info("|getIndicatorValue| start: {}", start);
                log.info("|getIndicatorValue| end: {}", end);
                // 使用门店时区的当前时间进行比较
                if (nowAtShopZone.isAfter(start) && nowAtShopZone.isBefore(end)) {
                    log.info(
                            "|getIndicatorValue| nowAtShopZone is in the open time. start: {}, end: {}, nowAtShopZone: {}",
                            start, end, nowAtShopZone);
                    // 在营业时间段内
                    offset = i;
                    break;
                }
            }
            // 当前时间不在营业时间段内
            if (offset == -1) {
                return BigDecimal.ZERO;
            }

            // 如果在营业时间段内，则计算离线时长
            ShopOpenTime shopOpenTime = openTimeList.get(offset);
            // 使用门店时区的日期和营业时间创建起始时间
            LocalDateTime start = LocalDateTime.of(todayAtShopZone, LocalTime.parse(shopOpenTime.getStart() + ":00"));
            log.info("|getIndicatorValue| start: {}", start);

            // 获取设备的离线开始时间
            LocalDateTime offlineStartDateTime = getOfflineStart(deviceContext.getDeviceDO());
            log.info("|getIndicatorValue| offlineStartDateTime: {}", offlineStartDateTime);

            // 将离线开始时间转换到门店时区
            LocalDateTime offlineStartDateTimeAtShopZone = offlineStartDateTime
                    .atZone(ZoneId.systemDefault())
                    .withZoneSameInstant(shopZoneId)
                    .toLocalDateTime();
            log.info("|getIndicatorValue| offlineStartDateTimeAtShopZone: {}", offlineStartDateTimeAtShopZone);

            if (offlineStartDateTimeAtShopZone.isAfter(start)) {
                // 如果设备离线开始时间大于营业开始时间，则返回 当前时间- 离线开始时间
                int offlineHours = (int) Duration.between(offlineStartDateTimeAtShopZone, nowAtShopZone).toHours();
                extraInfo.put(OFFLINE_TIME, offlineStartDateTimeAtShopZone.toString());
                extraInfo.put(OFFLINE_HOURS, String.valueOf(offlineHours));
                log.info("|getIndicatorValue| offlineHours: {}", offlineHours);
                return new BigDecimal(offlineHours);
            } else {
                // 如果设备离线开始时间小于营业开始时间，则返回 当前时间- 营业开始时间
                int offlineHours = (int) Duration.between(start, nowAtShopZone).toHours();
                extraInfo.put(OFFLINE_TIME, start.toString());
                extraInfo.put(OFFLINE_HOURS, String.valueOf(offlineHours));
                log.info("|getIndicatorValue| offlineHours: {}", offlineHours);
                return new BigDecimal(offlineHours);
            }
        }
    }

    @Override
    protected BigDecimal parseCompareValue(String value) {
        return new BigDecimal(value);
    }

    private LocalDateTime getOfflineStart(DeviceDO deviceDO) {
        ApolloBoxParamDTO apolloBoxParamDTO = new ApolloBoxParamDTO();
        apolloBoxParamDTO.setDeviceNo(deviceDO.getDeviceNo());
        Date offlineStart = null;
        try {
            BizResult<ApolloBoxDTO> bizResult = apolloRemoteService.get("apolloToken", apolloBoxParamDTO);
            if (bizResult.isSuccess()) {
                offlineStart = bizResult.getData().getLastOfflineTime();
            } else {
                // 兜底使用设备更新时间
                offlineStart = deviceDO.getUpdateTime();
            }
        } catch (Exception e) {
            log.error("|getOfflineStart| error", e);
            // 兜底使用设备更新时间
            offlineStart = deviceDO.getUpdateTime();
        }
        // 转换为系统默认时区的LocalDateTime
        LocalDateTime offlineStartDateTime = offlineStart.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return offlineStartDateTime;
    }

    @Override
    protected Map<String, String> getExtraInfo() {
        return extraInfo;
    }

}
