package com.chargebolt.service.notifyrule.strategy;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chargebolt.commons.enums.IndicatorScopeEnum;
import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.notifyrule.strategy.dto.DeviceContext;
import so.dian.demeter.biz.manager.ApolloManager;
import so.dian.demeter.pojo.bo.ApolloBoxInfoBO;

/**
 * 设备充电宝数量指标计算策略
 * 用于计算单个设备中的充电宝数量是否满足条件
 *
 * 实现说明：
 * 1. 作用域为设备级别 {@link IndicatorScopeEnum#DEVICE_SCOPE}
 * 2. 通过 ApolloManager 获取设备中已锁定的充电宝数量
 * 3. 支持与指定数值进行大小比较
 */
@Service("devicePowerbankCountStrategy")
public class DevicePowerbankCountStrategy extends AbstractIndicatorStrategy<BigDecimal> {

    public static final String DEVICE_NO = "deviceNo";
    public static final String SHOP_ID = "shopId";
    public static final String SHOP_NAME = "shopName";
    public static final String POWERBANK_NUM = "powerbankNum";

    @Resource
    private ApolloManager apolloManager;

    private Map<String, String> extraInfo = new HashMap<>();

    @Override
    protected void validateScope(RuleCondition condition, Object scopeDO) {
        if (!(scopeDO instanceof DeviceContext)) {
            throw new RuntimeException("作用域对象类型不匹配");
        }
    }

    @Override
    protected BigDecimal getIndicatorValue(Object scopeDO) {
        DeviceContext deviceContext = (DeviceContext) scopeDO;
        ApolloBoxInfoBO boxInfo = apolloManager.getBoxInfo(deviceContext.getDeviceDO().getDeviceNo(), true, false);
        int slotSize = boxInfo.getLockedSlotIndexes().size();
        extraInfo.put(SHOP_ID, deviceContext.getShopDO().getId().toString());
        extraInfo.put(SHOP_NAME, deviceContext.getShopDO().getName());
        extraInfo.put(DEVICE_NO, deviceContext.getDeviceDO().getDeviceNo());
        extraInfo.put(POWERBANK_NUM, String.valueOf(slotSize));
        return new BigDecimal(slotSize);
    }

    @Override
    protected BigDecimal parseCompareValue(String value) {
        return new BigDecimal(value);
    }

    @Override
    protected Map<String, String> getExtraInfo() {
        return extraInfo;
    }

}
