package com.chargebolt.service.notifyrule.strategy;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.chargebolt.commons.enums.IndicatorScopeEnum;
import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.notifyrule.strategy.dto.DeviceContext;

/**
 * 设备类型匹配策略
 * 用于判断设备的类型是否与指定类型匹配
 *
 * 实现说明：
 * 1. 作用域为设备级别 {@link IndicatorScopeEnum#DEVICE_SCOPE}
 * 2. 比较设备的 deviceInfoId（设备类型标识）是否与指定值匹配
 * 3. 支持字符串相等性比较
 */
@Service("deviceTypeMatchStrategy")
public class DeviceTypeMatchStrategy extends AbstractIndicatorStrategy<String> {

    public static final String DEVICE_TYPE = "deviceType";
    public static final String DEVICE_NO = "deviceNo";
    public static final String SHOP_ID = "shopId";
    public static final String SHOP_NAME = "shopName";

    private Map<String, String> extraInfo = new HashMap<>();
    
    @Override
    protected void validateScope(RuleCondition condition, Object scopeDO) {
        if (!(scopeDO instanceof DeviceContext)) {
            throw new RuntimeException("作用域对象类型不匹配");
        }
    }

    @Override
    protected String getIndicatorValue(Object scopeDO) {
        DeviceContext deviceContext = (DeviceContext) scopeDO;
        String deviceInfoId = deviceContext.getDeviceDO().getDeviceInfoId().toString();
        String deviceType = "";
        switch (deviceInfoId) {
            case "1":
                deviceType = "X4";
                break;
            case "2":
                deviceType = "X8";
                break;
            case "4":
                deviceType = "M6";
                break;
            case "5":
                deviceType = "M30";
                break;
            case "6":
                deviceType = "M12";
                break;
            default:
                deviceType = "None";
                break;
        }
        extraInfo.put(SHOP_ID, deviceContext.getShopDO().getId().toString());
        extraInfo.put(SHOP_NAME, deviceContext.getShopDO().getName());
        extraInfo.put(DEVICE_NO, deviceContext.getDeviceDO().getDeviceNo());
        extraInfo.put(DEVICE_TYPE, deviceType);
        return deviceType;
    }

    @Override
    protected String parseCompareValue(String value) {
        return value;
    }

    @Override
    protected Map<String, String> getExtraInfo() {
        return extraInfo;
    }

}
