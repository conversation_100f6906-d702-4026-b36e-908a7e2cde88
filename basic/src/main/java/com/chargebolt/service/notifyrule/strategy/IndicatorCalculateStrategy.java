package com.chargebolt.service.notifyrule.strategy;

import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.notifyrule.strategy.dto.CalculateResult;

/**
 * 指标计算策略接口
 * 该接口定义了通知规则中指标计算的标准契约
 * 不同的指标（如设备充电宝数量、门店等级等）都需要实现该接口
 * 以提供各自的计算逻辑
 *
 * @see RuleCondition 规则条件，包含指标key、运算符和比较值
 * @see com.chargebolt.service.notifyrule.strategy.AbstractIndicatorStrategy 抽象实现类，提供了通用的计算框架
 */
public interface IndicatorCalculateStrategy {

    /**
     * 计算指标是否满足条件
     *
     * @param condition 规则条件，包含指标key、运算符和比较值
     * @param scopeDO   作用域对象，可能是设备(DeviceDO)或门店(ShopDO)等
     * @return true：满足条件；false：不满足条件
     */
    CalculateResult calculate(RuleCondition condition, Object scopeDO);
}
