package com.chargebolt.service.notifyrule.strategy;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chargebolt.commons.enums.IndicatorScopeEnum;
import com.chargebolt.response.notifyrule.RuleCondition;

import so.dian.apollo.dto.result.box.ApolloBoxDTO;
import so.dian.demeter.biz.manager.ApolloManager;
import so.dian.demeter.common.enums.DeviceStorageTypeEnum;
import so.dian.demeter.dao.rds.DeviceMapper;
import so.dian.demeter.pojo.entity.DeviceDO;
import so.dian.talos.pojo.entity.ShopDO;

/**
 * 门店充电宝总数量指标计算策略
 * 用于计算一个门店下所有设备中的充电宝总数是否满足条件
 *
 * 实现说明：
 * 1. 作用域为门店级别 {@link IndicatorScopeEnum#SHOP_SCOPE}
 * 2. 通过以下步骤计算：
 *    - 查询门店下的所有设备
 *    - 遍历每个设备，通过 ApolloManager 获取设备中的充电宝数量
 *    - 累加所有设备的充电宝数量
 * 3. 支持与指定数值进行大小比较
 */
@Service("shopPowerbankCountStrategy")
public class ShopPowerbankCountStrategy extends AbstractIndicatorStrategy<BigDecimal> {

    public static final String POWERBANK_NUM = "powerbankNum";
    public static final String SHOP_ID = "shopId";
    public static final String SHOP_NAME = "shopName";

    @Resource
    private ApolloManager apolloManager;
    @Resource
    private DeviceMapper deviceMapper;

    private Map<String, String> extraInfo = new HashMap<>();

    @Override
    protected void validateScope(RuleCondition condition, Object scopeDO) {
        if (!(scopeDO instanceof ShopDO)) {
            throw new RuntimeException("作用域对象类型不匹配");
        }
    }

    @Override
    protected BigDecimal getIndicatorValue(Object scopeDO) {
        ShopDO shopDO = (ShopDO) scopeDO;
        List<DeviceDO> deviceList = deviceMapper.listByStorageTypeAndId(DeviceStorageTypeEnum.SHOP.getCode(),
                shopDO.getId());

        // 查询门店在的充电宝数量
        Map<String, ApolloBoxDTO> apolloBoxMap = apolloManager
                .getApolloBoxMap(deviceList.stream().map(DeviceDO::getDeviceNo).collect(Collectors.toList()), false);
        BigDecimal powerBankNum = BigDecimal.ZERO;
        for (DeviceDO deviceDO : deviceList) {
            ApolloBoxDTO apolloBoxDTO = apolloBoxMap.get(deviceDO.getDeviceNo());
            int size = apolloBoxDTO.getLockedSlotIndexes().size();
            powerBankNum = powerBankNum.add(new BigDecimal(size));
        }
        extraInfo.put(SHOP_ID, shopDO.getId().toString());
        extraInfo.put(SHOP_NAME, shopDO.getName());
        extraInfo.put(POWERBANK_NUM, powerBankNum.toString());
        return powerBankNum;
    }

    @Override
    protected BigDecimal parseCompareValue(String value) {
        return new BigDecimal(value);
    }

    @Override
    protected Map<String, String> getExtraInfo() {
        return extraInfo;
    }

}
