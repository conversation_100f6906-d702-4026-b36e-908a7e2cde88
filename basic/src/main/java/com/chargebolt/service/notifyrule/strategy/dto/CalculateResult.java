package com.chargebolt.service.notifyrule.strategy.dto;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculateResult {

    private boolean result;

    private String indicatorKey;

    /**
     * 额外信息，用于存储计算得出的数据。
     * map的key与indicatorKey 相关。
     */
    private Map<String, String> extraInfo;

}
