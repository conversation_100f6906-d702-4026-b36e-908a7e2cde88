/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.service.order;

import com.chargebolt.basic.request.shop.ShopOrdersRequest;
import com.chargebolt.basic.response.shop.ShopOrdersResponse;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.dao.pg.model.ShopOrderDO;
import com.chargebolt.dao.pg.model.ShopOrderModel;
import com.chargebolt.ezreal.response.shop.ShopDeviceDetailResponse;
import com.chargebolt.ezreal.response.shop.ShopOrderCombinationResponse;
import com.chargebolt.service.order.request.OrderStatisticRequest;
import com.chargebolt.service.order.response.OrderStatisticsResponse;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderStatistics.java, v 1.0 2023-12-01 3:54 PM Exp $
 */
public interface OrderStatisticsService {

    /**
     * 成功、退款统计汇总
     *
     * 平台用户应该有看板权限
     *
     * @param request
     * @param dataAuthorityContext
     * @return
     */
    OrderStatisticsResponse getOrderStatistics(OrderStatisticRequest request, UserDataAuthorityContext dataAuthorityContext);

    ShopOrdersResponse getShopOrders(ShopOrdersRequest request, UserDataAuthorityContext dataAuthorityContext);

    /**
     * 获取门店下设备统计信息
     * 订单统计
     * @return
     */
    List<ShopDeviceDetailResponse> listShopDeviceStatistics(Long shopId);
//    /**
//     * 获取门店下订单列表
//     *
//     * @param shopIds
//     * @return
//     */
//    List<ShopOrderDO> shopOrderList(List<Long> shopIds, Integer pageNum, Integer pageSize);

    /**
     * pc门店管理下订单信息统计汇总
     *
     * @param shopId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @return
     */
    ShopOrderCombinationResponse getShopOrderCombination(Long shopId, Date startTime, Date endTime, Integer pageNum);
}