/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.service.order;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.basic.request.shop.ShopOrdersRequest;
import com.chargebolt.basic.response.shop.OrderDetailResponse;
import com.chargebolt.basic.response.shop.ShopOrdersResponse;
import com.chargebolt.commons.constant.CacheKeyConstant;
import com.chargebolt.commons.enums.DataCycleEnum;
import com.chargebolt.commons.enums.language.DeviceDetailStateLanguageEnum;
import com.chargebolt.commons.enums.language.OrderDetailStateLanguageEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.dao.pg.OrderStatisticsDAO;
import com.chargebolt.dao.pg.model.OrderDataStatisticsModel;
import com.chargebolt.dao.pg.model.OrderStatisticsModel;
import com.chargebolt.dao.pg.model.ShopOrderDO;
import com.chargebolt.dao.pg.model.ShopOrderModel;
import com.chargebolt.dao.pg.model.ShopOrderStatisticsDO;
import com.chargebolt.ezreal.response.shop.ShopDeviceDetailResponse;
import com.chargebolt.ezreal.response.shop.ShopOrderCombinationResponse;
import com.chargebolt.ezreal.response.shop.ShopOrderDetailResponse;
import com.chargebolt.service.agent.AgentBaseConfigService;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.service.order.request.OrderStatisticRequest;
import com.chargebolt.tenant.service.AgentTenantConfigService;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import so.dian.demeter.biz.service.DeviceService;
import so.dian.demeter.pojo.bo.DeviceBO;
import so.dian.demeter.remote.OssUserRemoteService;
import so.dian.demeter.response.ShopAllListResponse;
import so.dian.eros.common.exception.ShopErrorEnum;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import com.chargebolt.basic.request.shop.ShopOrdersRequest;
import com.chargebolt.basic.response.shop.OrderDetailResponse;
import com.chargebolt.basic.response.shop.ShopOrdersResponse;
import com.chargebolt.commons.enums.DataCycleEnum;
import com.chargebolt.currency.CurrencyManager;
import com.chargebolt.service.order.response.GenericOrderStatisticsResponse;
import com.chargebolt.service.order.response.OrderStatisticsResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import so.dian.demeter.remote.OssUserRemoteService;
import so.dian.eros.common.util.LocationUtil;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.eros.manager.DeviceManager;
import so.dian.hermes.client.pojo.enums.OrderStatusEnum;
import so.dian.mofa3.lang.money.MoneyFormatter;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.talos.biz.service.TalosShopService;
import so.dian.talos.client.dto.ShopDTO;
import so.dian.talos.pojo.entity.ShopDO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderStatisticsServiceImpl.java, v 1.0 2023-12-01 4:16 PM Exp $
 */
@Slf4j
@Service
public class OrderStatisticsServiceImpl implements OrderStatisticsService {

    /**
     * 数据统计，支持最大的门店数量
     */
    private final Integer MAX_SHOP_LIST_SIZE= 5000;
    private final TalosShopService talosShopService;
    private final OrderStatisticsDAO orderStatisticsDAO;
    private final ApplicationContext applicationContext;
    private final AgentBaseConfigService agentTenantConfigService;
    private final RedissonClient redissonClient;
    private final DeviceService deviceService;
    private final AgentService agentService;
    private final AgentBaseConfigService agentBaseConfigService;
    private final OssUserRemoteService.OssUserRemoteServiceFallback ossUserRemoteServiceFallback;

    public OrderStatisticsServiceImpl(ObjectProvider<TalosShopService> talosShopServiceProvider,
                                      ObjectProvider<OrderStatisticsDAO> orderStatisticsDAOProvider,
                                      ObjectProvider<ApplicationContext> applicationContextProvider,
                                      ObjectProvider<AgentBaseConfigService> agentTenantConfigServiceProvider,
                                      ObjectProvider<RedissonClient> redissonClientProvider,
                                      ObjectProvider<DeviceService> deviceServiceProvider,
                                      ObjectProvider<AgentService> agentServiceProvider,
                                      ObjectProvider<AgentBaseConfigService> agentBaseConfigServiceProvider,
                                      final OssUserRemoteService.OssUserRemoteServiceFallback ossUserRemoteServiceFallback){
        this.talosShopService= talosShopServiceProvider.getIfUnique();
        this.orderStatisticsDAO= orderStatisticsDAOProvider.getIfUnique();
        this.applicationContext= applicationContextProvider.getIfUnique();
        this.agentTenantConfigService= agentTenantConfigServiceProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
        this.deviceService= deviceServiceProvider.getIfUnique();
        this.agentService= agentServiceProvider.getIfUnique();
        this.agentBaseConfigService= agentBaseConfigServiceProvider.getIfUnique();
        this.ossUserRemoteServiceFallback = ossUserRemoteServiceFallback;
    }
    @Override
    public OrderStatisticsResponse getOrderStatistics(final OrderStatisticRequest request, final UserDataAuthorityContext dataAuthorityContext) {
        OrderStatisticsResponse response= new OrderStatisticsResponse();
        List<Long> shopIds= dataAuthorityContext.getWrite().getShopIds();
        AgentBaseConfig agentBaseConfig = agentTenantConfigService.getByAgentId(dataAuthorityContext.getAgentId());
        // 支付
        GenericOrderStatisticsResponse payOrderStatisticsResponse= genericOrderStatistics(request, shopIds, OrderStatusEnum.PAID);
        // 退款
        GenericOrderStatisticsResponse refundOrderStatisticsResponse= genericOrderStatistics(request, shopIds, OrderStatusEnum.REFUND);
        response.setLastUpdateTime(Objects.nonNull(payOrderStatisticsResponse.getLastUpdateTime())?payOrderStatisticsResponse.getLastUpdateTime():System.currentTimeMillis());
        if(Objects.nonNull(payOrderStatisticsResponse)){
            response.setSuccessfulOrders(payOrderStatisticsResponse.getOrders());
            MultiCurrencyMoney payAmount= new MultiCurrencyMoney(0, agentBaseConfig.getCurrencyCode());
            payAmount.setCent(payOrderStatisticsResponse.getOrderAmount());
            response.setSuccessfulOrderAmountYuan(payAmount.getAmount().toString());
        }else{
            response.setSuccessfulOrders(0);
            response.setSuccessfulOrderAmountYuan("0");
        }

        if(Objects.nonNull(refundOrderStatisticsResponse)){
            response.setRefundOrders(refundOrderStatisticsResponse.getOrders());
            MultiCurrencyMoney refundAmount= new MultiCurrencyMoney(0, agentBaseConfig.getCurrencyCode());
            refundAmount.setCent(refundOrderStatisticsResponse.getOrderAmount());
            response.setRefundOrderAmountYuan(refundAmount.getAmount().toString());
        }else{
            response.setRefundOrders(0);
            response.setRefundOrderAmountYuan("0");
        }
        return response;
    }

    @Override
    public ShopOrdersResponse getShopOrders(final ShopOrdersRequest request, final UserDataAuthorityContext dataAuthorityContext) {
        ShopOrdersResponse response= new ShopOrdersResponse();
        AgentBaseConfig agentBaseConfig = agentTenantConfigService.getByAgentId(dataAuthorityContext.getAgentId());

        ShopOrderModel queryModel= new ShopOrderModel();
        queryModel.setShopId(request.getShopId());
        queryModel.setOrderStatus(request.getOrderStatus());
        if(Objects.nonNull(request.getLoanTime())){
            queryModel.setLoanStartTime(new DateBuild(request.getLoanTime()).start().toDate());
            queryModel.setLoanEndTime(new DateBuild(request.getLoanTime()).end().toDate());
        }
        Page<ShopOrderDO> page= PageHelper.startPage(request.getPageNum(), request.getPageSize(), Boolean.TRUE)
                .setOrderBy(" o.create_time DESC")
                .doSelectPage(() -> orderStatisticsDAO.shopOrderList(queryModel));
        if(CollectionUtils.isEmpty(page.getResult())){
            response.setOrders(0);
            response.setCurrencyCode(agentBaseConfig.getCurrencyCode());
            response.setCurrencySymbol(agentBaseConfig.getCurrencyCode());
            response.setOrdersAmountYuan("0");
            response.setList(new ArrayList<>());
            return response;
        }
        List<OrderDetailResponse> orderDetailList= new ArrayList<>();
        ShopDO shopDO= talosShopService.queryById(request.getShopId());
        for(ShopOrderDO orderDO: page.getResult()){
            OrderDetailResponse detailResponse= new OrderDetailResponse();
            BeanUtils.copyProperties(orderDO, detailResponse);
            detailResponse.setPowerBankNo(orderDO.getPowerbankNo());
            detailResponse.setLoanTime(Objects.isNull(orderDO.getLoanTime())?null:orderDO.getLoanTime().getTime());
            detailResponse.setReturnTime(Objects.isNull(orderDO.getReturnTime())?null:orderDO.getReturnTime().getTime());
            detailResponse.setShopName(shopDO.getName());
            if(Objects.nonNull(orderDO.getPayAmount())){
                MultiCurrencyMoney orderAmount= new MultiCurrencyMoney(0, agentBaseConfig.getCurrencyCode());
                orderAmount.setCent(orderDO.getPayAmount());
                detailResponse.setOrderAmountYuan(orderAmount.getAmount().toString());
            }
            String lang= ThreadLanguageHolder.getCurrentLang();
            Object[] objects = {};
            detailResponse.setStatusText(applicationContext.getMessage("BOX_ORDER_STATUS_" + orderDO.getStatus(), objects, LocationUtil.getLocale(lang)));
            orderDetailList.add(detailResponse);
        }

        response.setCurrencyCode(agentBaseConfig.getCurrencyCode());
        response.setCurrencySymbol(agentBaseConfig.getCurrencyCode());
        ShopOrderStatisticsDO orderStatisticsDO= orderStatisticsDAO.getShopOrderStatistics(queryModel);
        response.setOrders(Integer.valueOf(String.valueOf(page.getTotal())));
        if(Objects.nonNull(orderStatisticsDO)){
            if(Objects.equals(request.getOrderStatus(), OrderStatusEnum.REFUND.getCode())){
                MultiCurrencyMoney statisticsAmount= new MultiCurrencyMoney(0, agentBaseConfig.getCurrencyCode());
                statisticsAmount.setCent(orderStatisticsDO.getRefundAmount());
                response.setOrdersAmountYuan(statisticsAmount.getAmount().toString());
            }else{
                MultiCurrencyMoney statisticsAmount= new MultiCurrencyMoney(0, agentBaseConfig.getCurrencyCode());
                if(Objects.nonNull(orderStatisticsDO.getPayAmount())){
                    statisticsAmount.setCent(orderStatisticsDO.getPayAmount());
                }else{
                    statisticsAmount.setCent(orderStatisticsDO.getPayAmount());
                }
                response.setOrdersAmountYuan(statisticsAmount.getAmount().toString());
            }
        }else{
            response.setOrdersAmountYuan("0");
        }
        response.setList(orderDetailList);
        return response;
    }

    @Override
    public List<ShopDeviceDetailResponse> listShopDeviceStatistics(final Long shopId) {
        String cacheKey= CacheKeyConstant.PANEL_SHOP_MANAGER_STATISTIC_CACHE_KEY+shopId;
        RBucket<List<ShopDeviceDetailResponse>> bucket= redissonClient.getBucket(cacheKey);
        if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
            return bucket.get();
        }
        // 获取门店下设备列表
        List<DeviceBO> deviceBOList = deviceService.listShopBoxById(shopId);
        if(CollectionUtils.isEmpty(deviceBOList)){
            return null;
        }
        // 提取设备编号列表
        List<String> deviceNos = deviceBOList.stream()
                .map(DeviceBO::getDeviceNo)
                .collect(Collectors.toList());
        // 统计借出设备的订单信息
        OrderDataStatisticsModel orderDataStatisticsModel= new OrderDataStatisticsModel();
        orderDataStatisticsModel.setDeviceNos(deviceNos);
        List<ShopOrderStatisticsDO> shopOrderStatisticsDOList= orderStatisticsDAO.getOrderCountByDevices(orderDataStatisticsModel);
        // 组合结果
        List<ShopDeviceDetailResponse> shopDeviceDetailResponseList= deviceBOList.stream().map(device -> {
            ShopDeviceDetailResponse response = new ShopDeviceDetailResponse();
            response.setDeviceNo(device.getDeviceNo());
            response.setDeviceName(device.getDeviceName());
            response.setStatus(device.getOnlineStatus().getCode());
            response.setStatusText(DeviceDetailStateLanguageEnum.getDescrById(device.getOnlineStatus().getCode()));

            // 匹配统计结果
            shopOrderStatisticsDOList.stream()
                    .filter(stat -> device.getDeviceNo().equals(stat.getDeviceNo()))
                    .findFirst()
                    .ifPresent(stat -> {
                        response.setTotalOrderNum(stat.getOrderCount());
                        response.setPaymentOrderNum(stat.getPayOrderCount());
                    });

            return response;
        }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(shopDeviceDetailResponseList)){
            bucket.set(shopDeviceDetailResponseList, CacheKeyConstant.DATA_PANEL_CACHE_TIME, TimeUnit.MINUTES);
        }
        return shopDeviceDetailResponseList;
    }

//    @Override
//    public List<ShopOrderDO> shopOrderList(final List<Long> shopIds, Integer pageNum, Integer pageSize) {
//        ShopOrderModel query= new ShopOrderModel();
//        query.setLoanShopIds(shopIds);
//        query.setLoanStartTime();
//        Page<ShopOrderDO> page= PageHelper.startPage(pageNum, pageSize, Boolean.TRUE)
//                .setOrderBy(" o.create_time DESC")
//                .doSelectPage(() -> orderStatisticsDAO.shopOrderList(query));
//        return page.getResult();
//    }

    @Override
    public ShopOrderCombinationResponse getShopOrderCombination(final Long shopId,
                                                                final Date startTime,
                                                                final Date endTime,
                                                                final Integer pageNum) {

        OrderDataStatisticsModel queryModel= new OrderDataStatisticsModel();
        queryModel.setShopIds(Arrays.asList(shopId));
        queryModel.setStartCreateTime(startTime);
        queryModel.setEndCreateTime(endTime);
        List<ShopOrderStatisticsDO> shopOrderStatisticsDOList= orderStatisticsDAO.getOrderCountByShopIds(queryModel);
        if(CollectionUtils.isEmpty(shopOrderStatisticsDOList)){
            return null;
        }
        ShopDO shopDO= talosShopService.queryById(shopId);
        if(Objects.isNull(shopDO)){
            throw new I18nMessageException(ShopErrorEnum.SHOP_NOT_EXIST.getCode().toString(), ShopErrorEnum.SHOP_NOT_EXIST.getDesc());
        }
        Agent agent= agentService.getTopLevelAgent(shopDO.getAgentId());
        AgentBaseConfig agentBaseConfig= new AgentBaseConfig();
        agentBaseConfig.setAgentId(agent.getId());
        agentBaseConfig= agentBaseConfigService.getRecord(agentBaseConfig);

        ShopOrderModel query= new ShopOrderModel();
        query.setLoanShopIds(Arrays.asList(shopId));
        query.setLoanStartTime(startTime);
        query.setLoanEndTime(endTime);
        Page<ShopOrderDO> page= PageHelper.startPage(pageNum, 20, Boolean.TRUE)
                .setOrderBy(" o.id DESC")
                .doSelectPage(() -> orderStatisticsDAO.shopOrderList(query));

        //
        ShopOrderCombinationResponse response = new ShopOrderCombinationResponse();
        ShopOrderCombinationResponse.OrderInfoPage orderInfoPage = new ShopOrderCombinationResponse.OrderInfoPage();
        orderInfoPage.setPageNum(page.getPageNum());
        orderInfoPage.setPageSize(page.getPageSize());
        orderInfoPage.setTotalCount((int) page.getTotal());
        final String currency= agentBaseConfig.getCurrencyCode();
        List<ShopOrderDetailResponse> detailResponses = page.getResult().stream()
                .map(orderDO -> {
                    ShopOrderDetailResponse detail = new ShopOrderDetailResponse();
                    detail.setLoanTime(Objects.nonNull(orderDO.getCreateTime())?orderDO.getCreateTime().getTime():null);
                    detail.setOrderNo(orderDO.getOrderNo());
                    detail.setStatus(orderDO.getStatus());
                    detail.setStatusText(OrderDetailStateLanguageEnum.getDescrById(orderDO.getStatus()));
                    detail.setLoanBoxNo(orderDO.getLoanBoxNo());
                    if(Objects.nonNull(orderDO.getPayAmount())){
                        MultiCurrencyMoney  payAmount = new MultiCurrencyMoney(0, currency);
                        payAmount.setCent(orderDO.getPayAmount());
                        detail.setActualPayAmount(payAmount.getAmount().toString());
                        detail.setActualPayAmountFormatted(MoneyFormatter.format(payAmount));
                    }
                    return detail;
                })
                .collect(Collectors.toList());
        orderInfoPage.setList(detailResponses);

        // 4. 组装总统计信息
        ShopDeviceDetailResponse totalInfo = new ShopDeviceDetailResponse();
        shopOrderStatisticsDOList.forEach(stat -> {
            // 累加统计值（根据实际业务需求调整）
            totalInfo.setTotalOrderNum(totalInfo.getTotalOrderNum() + stat.getOrderCount());
            totalInfo.setPaymentOrderNum(totalInfo.getPaymentOrderNum() + stat.getPayOrderCount());
            // 其他字段累加逻辑...
        });
        response.setOrderInfo(orderInfoPage);
        response.setTotalInfo(totalInfo);

        return response;
    }

    /**
     * 统计用户拥有权限的订单数量和金额
     * 都是登录用户维度的，所以不区分归属门店，缓存5分钟，门店变更后，缓存失效后重新统计
     *
     * @param request
     * @param shopIds
     * @param orderStatusEnum
     * @return
     */
    private GenericOrderStatisticsResponse genericOrderStatistics(final OrderStatisticRequest request, List<Long> shopIds, OrderStatusEnum orderStatusEnum){

        OrderStatisticsModel queryModel= new OrderStatisticsModel();
        queryModel.setShopIds(shopIds);
        queryModel.setStatus(orderStatusEnum.getCode());
        switch (DataCycleEnum.getById(request.getType())){
            case ALL:
                if(Objects.nonNull(request.getTime())){
                    queryModel.setStartTime(new DateBuild(request.getTime()).start().toDate());
                }
                break;
            case DAY:
                if(Objects.nonNull(request.getTime())){
                    queryModel.setStartTime(new DateBuild(request.getTime()).start().toDate());
                    queryModel.setEndTime(new DateBuild(request.getTime()).end().toDate());
                }else{
                    queryModel.setStartTime(new DateBuild().start().toDate());
                    queryModel.setEndTime(new DateBuild().end().toDate());
                }
                break;
            case WEEK:
                if(Objects.nonNull(request.getTime())){
                    queryModel.setStartTime(DateUtil.firstDayOfWeek(new DateBuild(request.getTime()).start().toDate()));
                    queryModel.setEndTime(DateUtil.lastDayOfWeek(new DateBuild(request.getTime()).end().toDate()));
                }else{
                    queryModel.setStartTime(DateUtil.firstDayOfWeek(new DateBuild().start().toDate()));
                    queryModel.setEndTime(DateUtil.lastDayOfWeek(new DateBuild().end().toDate()));
                }
                break;
            case MONTH:
                if(Objects.nonNull(request.getTime())){
                    queryModel.setStartTime(DateUtil.firstDayOfMonth(new DateBuild(request.getTime()).start().toDate()));
                    queryModel.setEndTime(DateUtil.lastDayOfMonth(new DateBuild(request.getTime()).end().toDate()));
                }else{
                    queryModel.setStartTime(DateUtil.firstDayOfMonth(new DateBuild().start().toDate()));
                    queryModel.setEndTime(DateUtil.lastDayOfMonth(new DateBuild().end().toDate()));
                }
                break;
            default:
                queryModel.setStartTime(new DateBuild().start().toDate());
                queryModel.setEndTime(new DateBuild().end().toDate());
        }
        // 数据缓存key：用户ID_数据周期类型_订状态+开始时间戳
        String cacheKey= CacheKeyConstant.PANEL_ORDER_STATISTICS_CACHE_KEY+request.getUserId()+"_"
                +request.getType()+"_"+orderStatusEnum.getCode()+queryModel.getStartTime().getTime();
        RBucket<GenericOrderStatisticsResponse> bucket= redissonClient.getBucket(cacheKey);
        if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
            return bucket.get();
        }
        log.info("订单统计汇总查询条件:{}", JsonUtil.beanToJson(queryModel));
        GenericOrderStatisticsResponse orderStatistics= orderStatisticsDAO.getGenericOrderStatistics(queryModel);
        if(Objects.nonNull(orderStatistics)){
            orderStatistics.setLastUpdateTime(System.currentTimeMillis());
            bucket.set(orderStatistics, CacheKeyConstant.DATA_PANEL_CACHE_TIME, TimeUnit.SECONDS);
        }else{
            orderStatistics= new GenericOrderStatisticsResponse();
            orderStatistics.setOrderAmount(0L);
            orderStatistics.setOrders(0);
            orderStatistics.setStatus(orderStatusEnum.getCode());
            orderStatistics.setLastUpdateTime(System.currentTimeMillis());
        }
        return orderStatistics;
    }
}