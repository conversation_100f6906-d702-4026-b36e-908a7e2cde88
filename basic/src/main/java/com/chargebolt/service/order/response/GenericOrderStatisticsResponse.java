/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.service.order.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.hermes.client.pojo.enums.OrderStatusEnum;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: GenericOrderStatisticsResponse.java, v 1.0 2023-12-01 5:07 PM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GenericOrderStatisticsResponse extends BaseStatisticsResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202312335170738L;
    /**
     * 订单数
     */
    private Integer orders;
    /**
     * 订单金额（分）
     */
    private Long orderAmount;

    /**
     * 状态
     * 5支付
     * 7退款
     * {@link OrderStatusEnum}
     */
    private Integer status;

    /**
     * 最后更新时间
     */
    private Long lastUpdateTime;
}