/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.service.order.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderStatisticsResponse.java, v 1.0 2023-12-01 4:09 PM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderStatisticsResponse extends BaseStatisticsResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202312335160938L;
    /**
     * 成功订单数
     */
    private Integer successfulOrders;
    /**
     * 成功订单金额（元）
     */
    private String successfulOrderAmountYuan;

    /**
     * 退款订单数
     */
    private Integer refundOrders;
    /**
     * 退款订单金额（元）
     */
    private String refundOrderAmountYuan;

}