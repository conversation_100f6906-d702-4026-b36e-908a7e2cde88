/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.task;

import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import so.dian.demeter.biz.service.DeviceStatisticService;
import so.dian.talos.client.dto.GeoDTO;
import so.dian.talos.pojo.entity.ShopDO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DeviceStateSyncTask.java, v 1.0 2023-12-08 3:36 PM Exp $
 */
@Slf4j
@Component
public class DeviceStateSyncTask {
    private static final String DEVICE_STATE_SYNC_TASK_LOCK="__CACHE_DEVICE_STATE_SYNC_TASK_LOCK";
    private final DeviceStatisticService deviceStatisticService;
    private final RedissonClient redissonClient;
    public DeviceStateSyncTask(ObjectProvider<DeviceStatisticService> deviceStatisticServiceProvider,
                               ObjectProvider<RedissonClient> redissonClientProvider){
        this.deviceStatisticService= deviceStatisticServiceProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
    }
    @Scheduled(cron = "0 30 * * * ?")
    public void init() {
        log.info("设备状态同步....");
        RLock lock = redissonClient.getLock(DEVICE_STATE_SYNC_TASK_LOCK);
        try {
            if (lock.tryLock()) {
                deviceStatisticService.syncDeviceState();
            }else {
                log.info("task running....");
            }
        }catch (Exception e){
            log.error("device state sync exception: ", e);
        }finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("device state finished....");
    }
}