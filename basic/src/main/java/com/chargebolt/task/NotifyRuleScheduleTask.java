package com.chargebolt.task;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chargebolt.commons.enums.IndicatorKeyEnum;
import com.chargebolt.commons.enums.IndicatorScopeEnum;
import com.chargebolt.commons.enums.language.NotifyRuleStatusEnum;
import com.chargebolt.dao.notifyrule.NotifyRuleDao;
import com.chargebolt.dao.notifyrule.NotifyWhitelistDao;
import com.chargebolt.dao.notifyrule.model.NotifyRule;
import com.chargebolt.dao.notifyrule.model.NotifyWhitelist;
import com.chargebolt.response.notifyrule.RuleCondition;
import com.chargebolt.service.notifyrule.NotificationService;
import com.chargebolt.service.notifyrule.strategy.IndicatorCalculateStrategy;
import com.chargebolt.service.notifyrule.strategy.dto.CalculateResult;
import com.chargebolt.service.notifyrule.strategy.dto.DeviceContext;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import so.dian.demeter.common.enums.DeviceStorageTypeEnum;
import so.dian.demeter.dao.rds.DeviceMapper;
import so.dian.demeter.pojo.entity.DeviceDO;
import so.dian.talos.common.enums.ShopStatusEnum;
import so.dian.talos.dao.rds.ShopDAO;
import so.dian.talos.pojo.entity.ShopDO;

/**
 * 消息通知 - 自定义规则 - 调度任务
 * 该类负责定时扫描并执行通知规则，主要功能包括：
 * 1. 每小时扫描一次所有启用状态的通知规则
 * 2. 根据规则的调度间隔(schedule)判断是否需要执行
 * 3. 分别校验门店维度和设备维度的条件
 * 4. 当条件满足时触发消息通知
 */
@Slf4j
@Component
public class NotifyRuleScheduleTask {
    private static final String RETRY_TASK_LOCK_KEY = "ezreal:notify_rule_schedule_task";
    private static final long LOCK_LEASE_TIME = 5 * 60; // 锁持有时间，单位秒，设置为5分钟
    @Resource
    private NotifyRuleDao notifyRuleDao;
    @Resource
    private ShopDAO shopDAO;
    @Resource
    private Map<String, IndicatorCalculateStrategy> indicatorCalculateStrategy;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private NotificationService notificationService;
    @Resource
    private NotifyWhitelistDao notifyWhitelistDao;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 规则调度任务的入口方法，每小时执行一次
     * 执行流程：
     * 1. 获取所有通知规则
     * 2. 过滤出符合执行条件的规则并处理
     * 3. 统计处理结果
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void notifyRuleScheduleTask() {
        RLock lock = redissonClient.getLock(RETRY_TASK_LOCK_KEY);
        try {
            // 尝试立即获取锁，不等待，如果获取不到立即返回false
            boolean isLocked = lock.tryLock(0, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("未能获取到分布式锁，跳过本次任务执行");
                return;
            }
            log.info("开始执行规则调度任务，时间：{}", LocalDateTime.now());
            TaskExecutionStats stats = new TaskExecutionStats();

            List<NotifyRule> rules = notifyRuleDao.findAll();
            if (CollectionUtils.isEmpty(rules)) {
                return;
            }

            rules.stream()
                    .filter(this::isRuleEligibleForExecution)
                    .forEach(rule -> processRule(rule, stats));

            log.info("规则调度任务执行完成，处理规则数：{}，匹配规则数：{}",
                    stats.getProcessedRules(), stats.getMatchedRules());
        } catch (Exception e) {
            log.error("规则调度任务执行异常", e);
        } finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放分布式锁");
            }
        }
    }

    /**
     * 判断规则是否满足执行条件
     * 
     * @param rule 待判断的通知规则
     * @return true: 规则可以执行; false: 规则不满足执行条件
     */
    private boolean isRuleEligibleForExecution(NotifyRule rule) {
        // 检查规则是否启用
        if (NotifyRuleStatusEnum.DISABLE.getCode() == rule.getStatus()) {
            return false;
        }

        // 检查是否达到下次执行时间
        // 上次执行时间不为空，且当前时间减去调度间隔后仍在上次执行时间之前，说明未到执行时间
        if (rule.getLastScheduleTime() != null &&
                LocalDateTime.now().minusHours(rule.getSchedule()).isBefore(rule.getLastScheduleTime())) {
            log.info("还未达到调度时间, 规则ID: {}", rule.getId());
            return false;
        }
        return true;
    }

    /**
     * 处理单个通知规则
     * 
     * @param rule  待处理的通知规则
     * @param stats 任务执行统计信息
     */
    private void processRule(NotifyRule rule, TaskExecutionStats stats) {
        stats.incrementProcessedRules();

        try {

            // 更新调度时间应该在规则处理开始时就执行，不关心调度执行的结果。
            updateRuleScheduleTime(rule);

            // 解析规则条件
            List<List<RuleCondition>> conditions = parseConditions(rule.getConditions());
            if (conditions.isEmpty()) {
                return;
            }

            // 获取代理商下已安装的门店列表
            List<ShopDO> installedShops = getInstalledShops(rule.getAgentId());
            if (CollectionUtils.isEmpty(installedShops)) {
                log.info("agentId: {} 没有已安装的门店，跳过", rule.getAgentId());
                return;
            }

            // 查询白名单
            List<NotifyWhitelist> whiteList = notifyWhitelistDao.findByRuleId(rule.getId());
            if (CollectionUtils.isNotEmpty(whiteList)) {
                // 从 installedShops 中移除白名单中的门店
                installedShops = installedShops.stream()
                        .filter(shop -> !whiteList.stream().anyMatch(whitelist -> whitelist.getObjId().equals(shop.getId())))
                        .collect(Collectors.toList());
            }

            // 处理门店规则，如果有匹配则增加计数
            log.info("处理门店规则，agentId: {}, 门店ids: {}", rule.getAgentId(), installedShops.stream().map(ShopDO::getId).collect(Collectors.toList()));
            boolean isRuleMatched = processShopsForRule(installedShops, conditions, rule);
            if (isRuleMatched) {
                stats.incrementMatchedRules();
            }
        } catch (Exception e) {
            // 即使规则处理失败，也不影响调度时间的更新
            log.error("处理规则异常，规则ID：{}", rule.getId(), e);
        }
    }

    /**
     * 解析规则条件JSON字符串
     * 
     * @param conditionsJson 条件JSON字符串
     * @return 解析后的条件列表，每个子列表代表一个条件组
     */
    private List<List<RuleCondition>> parseConditions(String conditionsJson) {
        return JSON.parseObject(conditionsJson,
                new TypeReference<List<List<RuleCondition>>>() {
                });
    }

    /**
     * 获取代理商下所有已安装状态的门店
     * 
     * @param agentId 代理商ID
     * @return 已安装状态的门店列表
     */
    private List<ShopDO> getInstalledShops(Long agentId) {
        // 查询代理商下所有门店
        List<ShopDO> agentShops = shopDAO.queryByAgentId(agentId);
        if (CollectionUtils.isEmpty(agentShops)) {
            return Collections.emptyList();
        }
        // 过滤出已安装状态的门店
        return agentShops.stream()
                .filter(shop -> ShopStatusEnum.INSTALLED.getStatus() == shop.getStatus())
                .collect(Collectors.toList());
    }

    /**
     * 处理规则下的所有门店
     * 
     * @param shops      门店列表
     * @param conditions 规则条件组列表
     * @param rule       通知规则
     * @return true: 存在匹配的门店; false: 没有匹配的门店
     */
    private boolean processShopsForRule(List<ShopDO> shops, List<List<RuleCondition>> conditions, NotifyRule rule) {
        // forEach 处理需要原子类
        AtomicBoolean hasMatch = new AtomicBoolean(false);
        shops.forEach(shop -> {
            try {
                log.info("处理门店: {}", shop.getId());
                if (processShopConditions(shop, conditions, rule)) {
                    log.info("门店: {} 匹配成功", shop.getId());
                    hasMatch.set(true);
                }
            } catch (Exception e) {
                log.error("处理门店: {} 异常", shop.getId(), e);
            }
        });
        return hasMatch.get();
    }

    /**
     * 处理单个门店的条件组
     * 
     * @param shop       门店对象
     * @param conditions 条件组列表
     * @param rule       通知规则
     * @return true: 存在匹配的条件组; false: 没有匹配的条件组
     */
    private boolean processShopConditions(ShopDO shop, List<List<RuleCondition>> conditions, NotifyRule rule) {
        // 任一条件组匹配即可
        return conditions.stream()
                .anyMatch(group -> isConditionGroupMatched(group, shop, rule));
    }

    /**
     * 判断条件组是否匹配
     * 
     * @param conditionGroup 条件组
     * @param shop           门店对象
     * @param rule           通知规则
     * @return true: 条件组匹配; false: 条件组不匹配
     */
    private Boolean isConditionGroupMatched(List<RuleCondition> conditionGroup, ShopDO shop, NotifyRule rule) {
        // 处理店铺维度条件
        List<RuleCondition> shopConditions = filterConditionsByScope(conditionGroup, IndicatorScopeEnum.SHOP_SCOPE);
        log.info("shopId:{} 店铺维度条件数: {}", shop.getId(), shopConditions.size());

        List<CalculateResult> calculateResults = shopConditions.stream()
                .map(condition -> {
                    try {
                        return indicatorCalculateStrategy
                                .get(IndicatorKeyEnum.getCalculateStrategy(condition.getIndicatorKey()))
                                .calculate(condition, shop);
                    } catch (Exception e) {
                        log.error("计算指标值异常,indicatorKey: {}, indicatorValue: {}, compareValue: {}",
                                condition.getIndicatorKey(), condition.getValue(), e);
                        return CalculateResult.builder().result(false)
                                .indicatorKey(condition.getIndicatorKey())
                                .build();
                    }
                })
                .collect(Collectors.toList());
        log.info("shopId:{} 店铺维度条件计算结果: {}", shop.getId(), JSON.toJSONString(calculateResults));
        boolean isShopConditionsSatisfied = calculateResults.stream().allMatch(result -> result.isResult());
        // 店铺条件必须全部满足
        if (!isShopConditionsSatisfied) {
            log.info("shopId:{} 店铺维度条件不满足，跳过", shop.getId());
            return false;
        }
        log.info("shopId:{} 店铺维度条件满足，继续处理设备维度条件", shop.getId());
        // 处理设备维度条件
        List<RuleCondition> deviceConditions = filterConditionsByScope(conditionGroup, IndicatorScopeEnum.DEVICE_SCOPE);
        log.info("shopId:{} 设备维度条件数: {}", shop.getId(), deviceConditions.size());
        // 如果没有设备条件，则只要店铺条件满足就可以发送通知
        if (deviceConditions.isEmpty()) {
            log.info("shopId:{} 没有设备维度条件，直接发送通知", shop.getId());
            notificationService.sendNotification(rule, calculateResults.stream().filter(CalculateResult::isResult)
                    .collect(Collectors.toList()));
            return true;
        }

        // 获取店铺下的所有设备
        List<DeviceDO> devices = deviceMapper.listByStorageTypeAndIdExcludeBolt(
                DeviceStorageTypeEnum.SHOP.getCode(), shop.getId());
        log.info("shopId:{} 店铺下的设备数: {}", shop.getId(), devices.size());
        if (CollectionUtils.isEmpty(devices)) {
            log.info("shopId:{} 店铺下没有设备，跳过", shop.getId());
            return false;
        }

        return devices.stream().anyMatch(device -> {
            DeviceContext context = new DeviceContext();
            context.setShopDO(shop);
            context.setDeviceDO(device);
            // 执行设备维度条件计算
            log.info("执行设备维度条件计算,deviceNo: {}, shopId: {}", device.getDeviceNo(), shop.getId());
            List<CalculateResult> deviceCalculateResult = deviceConditions.stream()
                    .map(condition -> {
                        try {
                            return indicatorCalculateStrategy
                                    .get(IndicatorKeyEnum.getCalculateStrategy(condition.getIndicatorKey()))
                                    .calculate(condition, context);
                        } catch (Exception e) {
                            log.error("计算指标值异常,indicatorKey: {}, indicatorValue: {}, compareValue: {}",
                                    condition.getIndicatorKey(), condition.getValue(), e);
                            return CalculateResult.builder().result(false)
                                    .indicatorKey(condition.getIndicatorKey())
                                    .build();
                        }
                    })
                    .collect(Collectors.toList());
            log.info("shopId:{} 设备维度条件计算结果: {}", shop.getId(), JSON.toJSONString(deviceCalculateResult));
            boolean isDeviceConditionsSatisfied = deviceCalculateResult.stream().allMatch(result -> result.isResult());
            if (!isDeviceConditionsSatisfied) {
                log.info("shopId:{} 设备维度条件不满足，跳过", shop.getId());
                return Boolean.FALSE;
            }
            // 这里要把上面的 calculateResults 和 deviceCalculateResult 合并
            List<CalculateResult> allCalculateResults = Stream
                    .concat(calculateResults.stream(), deviceCalculateResult.stream()).collect(Collectors.toList());
            log.info("shopId:{} 合并后的计算结果: {}", shop.getId(), JSON.toJSONString(allCalculateResults));
            log.info("shopId:{} 发送通知", shop.getId());
            notificationService.sendNotification(rule, allCalculateResults);
            return Boolean.TRUE;
        });
    }

    /**
     * 按照指定范围过滤条件
     * 
     * @param conditions 条件列表
     * @param scope      条件范围（店铺维度或设备维度）
     * @return 过滤后的条件列表
     */
    private List<RuleCondition> filterConditionsByScope(List<RuleCondition> conditions, IndicatorScopeEnum scope) {
        return conditions.stream()
                .filter(e -> {
                    IndicatorKeyEnum indicatorKeyEnum = IndicatorKeyEnum.getByKey(e.getIndicatorKey());
                    if (indicatorKeyEnum == null) {
                        return false;
                    }
                    return indicatorKeyEnum.getScope().equals(scope.getScope());
                })
                .collect(Collectors.toList());
    }

    /**
     * 更新规则的最后执行时间
     * 
     * @param rule 通知规则
     */
    private void updateRuleScheduleTime(NotifyRule rule) {
        LocalDateTime now = LocalDateTime.now();
        notifyRuleDao.updateLastScheduleTime(now, rule.getId(), System.currentTimeMillis());
    }

    /**
     * 任务执行统计信息类
     */
    @Data
    private static class TaskExecutionStats {
        /** 已处理的规则数量 */
        private int processedRules;
        /** 匹配成功的规则数量 */
        private int matchedRules;

        /** 增加已处理规则数量 */
        public void incrementProcessedRules() {
            processedRules++;
        }

        /** 增加匹配成功的规则数量 */
        public void incrementMatchedRules() {
            matchedRules++;
        }
    }
}
