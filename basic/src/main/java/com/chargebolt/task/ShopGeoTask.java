/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.task;

import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.talos.cache.GeoCache;
import so.dian.talos.client.dto.GeoDTO;
import so.dian.talos.dao.rds.ShopDAO;
import so.dian.talos.pojo.entity.ShopDO;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static so.dian.mofa3.lang.util.DateBuild.SIMPLE_DATE;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopGeoTask.java, v 1.0 2023-11-14 10:15 AM Exp $
 */
@Slf4j
@Component
public class ShopGeoTask {
    private static final String SHOP_GEO_TASK_LOCK="__CACHE_SHOP_GEO_TASK_LOCK";
    private final RedissonClient redissonClient;
    private final ShopDAO shopDAO;
    private final GeoCache geoCache;

    public ShopGeoTask(ObjectProvider<RedissonClient> redissonClientProvider,
                       ObjectProvider<ShopDAO> shopDAOProvider,
                       ObjectProvider<GeoCache> geoCacheProvider){
        this.redissonClient= redissonClientProvider.getIfUnique();
        this.shopDAO= shopDAOProvider.getIfUnique();
        this.geoCache= geoCacheProvider.getIfUnique();
    }

    @Scheduled(cron = "0 30 * * * ?")
    public void init() {
        log.info("门店GEO扫描....");
        RLock lock = redissonClient.getLock(SHOP_GEO_TASK_LOCK);
        try {
            if (lock.tryLock()) {
                boolean flag= Boolean.TRUE;
                int offset= 0;
                int pageSize= 100;
                while (flag){
                    Page<ShopDO> page= PageHelper.offsetPage(offset, pageSize, Boolean.FALSE)
                            .setOrderBy(" update_time desc").doSelectPage(new ISelect() {

                        @Override
                        public void doSelect() {
                            shopDAO.queryList();
                        }
                    });
                    if(CollectionUtils.isEmpty(page.getResult())){
                        flag= Boolean.FALSE;
                        continue;
                    }
                    List<GeoDTO> geoDTOList = page.getResult().stream()
                            .filter(shopDO -> shopDO.getId() != null)
                            .map(shopDO -> new GeoDTO(shopDO.getId().toString(), shopDO.getPoiLongitude(), shopDO.getPoiLatitude()))
                            .collect(Collectors.toList());
                    geoCache.geoAddAll(geoDTOList);
                    offset= offset+pageSize;
                }
            }else {
                log.info("geo task running....");
            }
        }catch (Exception e){
            log.error("shop GEO exception: ", e);
        }finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("门店 Geo init finished....");
    }

    /**
     * 清理7天内被删除的门店GEO缓存
     */
    @Scheduled(fixedDelay = 3600000)
    public void cleanDelShop(){
        log.info("删除门店Geo cache清理");
        boolean flag= Boolean.TRUE;
        int offset= 0;
        int pageSize= 100;
        while (flag){
            Date time= new DateBuild().addToDay(-7).toDate();
            Page<ShopDO> page= PageHelper.offsetPage(offset, pageSize, Boolean.FALSE).doSelectPage(new ISelect() {
                        @Override
                        public void doSelect() {
                            shopDAO.queryByDeleted(time);
                        }
                    });
            if(CollectionUtils.isEmpty(page.getResult())){
                flag= Boolean.FALSE;
                continue;
            }
            page.getResult().forEach(shopDO -> {
                geoCache.geoDelete(shopDO.getId().toString());
            });
            offset= offset+pageSize;
        }
    }
}