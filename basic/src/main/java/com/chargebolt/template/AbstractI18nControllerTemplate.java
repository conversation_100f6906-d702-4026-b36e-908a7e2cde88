/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.template;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.exception.BizException;
import so.dian.eros.manager.I18nBizManager;
import so.dian.mofa3.lang.common.constant.HttpConstants;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.BizProcessException;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.lang.exception.CustomCodeException;
import so.dian.mofa3.lang.exception.ServiceIsNotAvailableException;
import so.dian.mofa3.lang.exception.SystemRunningException;
import so.dian.mofa3.lang.exception.ThirdPartyException;
import so.dian.mofa3.lang.util.ErrorUtil;
import so.dian.mofa3.template.controller.ControllerCallback;
import so.dian.mofa3.template.controller.ControllerTemplate;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AbstractI18nControllerTemplate.java, v 1.0 2023-10-27 10:59 AM Exp $
 */
@Slf4j
@Component("AbstractI18nControllerTemplate")
public class AbstractI18nControllerTemplate<T> implements ControllerTemplate<T> {
    @Value("${spring.application.name}")
    private String appName;

    public Result<T> doBiz(final ControllerCallback<T> action) {
        Result<T> result = new Result<>();
        String requestId = MDC.get(HttpConstants.REQUEST_ID);
        if(StringUtils.isNotBlank(requestId)){
            result.setRequestId(requestId);
        }

        try {
            //检查参数
            action.checkParam();
            // 构建上下文
            action.buildContext();
            // 幂等处理(决策无幂等操作)
            action.checkConcurrent();
            // 执行
            final T execute = action.execute();
            // 返回
            result.setData(execute);
            result.setSuccess(true);
            return result;
        } catch (CheckParamException ex) {
            log.error("参数校验不通过：{}", ex.getMessage());
            return ErrorUtil.buildCheckParam(result, ex, appName);
        } catch (BizProcessException ex) {
            log.error("业务处理异常：{}", ex.getMessage());
            return ErrorUtil.buildBizProcess(result, ex, appName);
        } catch (CustomCodeException ex) {
            log.error("自定义Code异常：{}", ex.getMessage());
            return ErrorUtil.buildCustomCode(result, ex.getCode(), ex, appName);
        } catch (I18nMessageException ex) {
            log.error("国际化异常信息返回：{}", ex.getMessage());
            return buildI18nMessageException(result, ex.getCode(), ex);
        }catch (SystemRunningException ex) {
            log.error("运行时异常：{}", ex.getMessage(), ex);
            return ErrorUtil.buildSystemRunning(result, ex, appName);
        } catch (ThirdPartyException ex) {
            log.error("第三方调用异常：{}", ex.getMessage(), ex);
            return ErrorUtil.buildThirdParty(result, ex, appName);
        } catch (ServiceIsNotAvailableException ex) {
            log.error("服务不可用：{}", ex.getMessage(), ex);
            return ErrorUtil.buildServiceIsNotAvailable(result, ex, appName);
        } catch (BizException ex) {
            log.error("国际化异常信息返回：{}", ex.getMessage());
            return buildI18nMessageException(result, ex.getCode().toString(), ex);
        }catch (Throwable ex) {
            log.error("System Error：{}", ex.getMessage(), ex);
            return ErrorUtil.buildThrowable(result, ex, appName);
        }finally {
            MDC.clear();
        }
    }

    public String getI18nMsg(String code, Exception e){
        String text = i18NBizManager.handleException(Integer.valueOf(code), e.getMessage());
        return StringUtils.isBlank(text)?e.getMessage():text;
    }

    public String getI18nMsgPlaceholder(String code, Exception e, List<String> placeholder){
        return i18NBizManager.handleExceptionPlaceholder(Integer.valueOf(code), e.getMessage(), placeholder);
    }
    public Result buildI18nMessageException(Result result, String code, I18nMessageException e) {
        result.setSuccess(false);
        if(CollectionUtils.isEmpty(e.getArgs())){
            result.setMsg(getI18nMsg(code, e));
        }else{
            result.setMsg(getI18nMsgPlaceholder(code, e, e.getArgs()));
        }
        result.setCode(code);
        return result;
    }
    public Result buildI18nMessageException(Result result, String code, BizException e) {
        result.setSuccess(false);
        result.setMsg(getI18nMsg(code, e));
        result.setCode(code);
        return result;
    }

    @Resource
    private I18nBizManager i18NBizManager;

}