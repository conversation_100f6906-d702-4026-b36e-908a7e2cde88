/*
 * Dian.com Inc.
 * Copyright (c) 2004-2018 All Rights Reserved.
 */
package com.chargebolt.template;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.OssAuthenticationDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.service.AeacusUserService;
import com.chargebolt.aeacus.service.LoginService;
import com.chargebolt.commons.constant.CacheKeyConstant;
import com.chargebolt.context.UserDataAuthorityContext;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RestController;
import so.dian.eros.common.exception.UserErrorEnum;
import so.dian.eros.common.util.RequestUtils;
import so.dian.mofa3.template.controller.ControllerTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * PC、电小二APP使用控制器基类
 *
 * <AUTHOR>
 * @version $Id: BaseController.java, v 0.1 2018-04-16 下午2:47 Exp $
 */
@SuppressWarnings("unused")
@Slf4j
@RestController
public abstract class BaseChargeboltController {
    /**
     * 操作模板
     */
    @Resource
    @Qualifier("AbstractI18nControllerTemplate")
    @SuppressWarnings("rawtypes")
    public ControllerTemplate template;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    private LoginService loginService;
    @Resource
    private AeacusUserService aeacusUserService;
    @Resource
    private RedissonClient redissonClient;
    protected Long getUserId(){
        HttpServletRequest request  = RequestUtils.getRequest();
        String userToken = request.getHeader(RequestUtils.HEADER_KEY);
        OssAuthenticationDTO ossAuthenticationDTO =new OssAuthenticationDTO();
        ossAuthenticationDTO.setUserToken(userToken);
        Long userId= loginService.getUserIdByUserToken(userToken);
        if(Objects.isNull(userId)){
            throw new I18nMessageException(UserErrorEnum.LOGIN_EXPIRED.getCode().toString(), UserErrorEnum.LOGIN_EXPIRED.getDesc());
        }
        return userId;
    }
    protected OssUserDTO getUser(){
        OssUserDTO userDTOS = aeacusUserService.queryById(getUserId());
        if (Objects.nonNull(userDTOS)){
            return userDTOS;
        }
        throw new I18nMessageException(UserErrorEnum.LOGIN_EXPIRED.getCode().toString(), UserErrorEnum.LOGIN_EXPIRED.getDesc());
    }

    /**
     * 角色是否包含role=0的admin权限
     * 使用 this#getUserDataAuthorityContext 的权限层级
     *
     * @return
     */
    @Deprecated
    public Boolean adminRole(){
        OssUserDTO ossUserDTO= getUser();
        return ossUserDTO.getRoleDtos().stream()
                .anyMatch(ossRole -> Objects.equals(ossRole.getId(), AeacusConstsnts.SUPER_ROLE_ID));

    }
    public UserDataAuthorityContext getUserDataAuthorityContext(){
        Long userId= getUserId();
        RBucket<UserDataAuthorityContext> userBucket = redissonClient.getBucket(CacheKeyConstant.USER_DATA_AUTHORITY_KEY + userId);
        if(Objects.nonNull(userBucket)&& Objects.nonNull(userBucket.get())){
            return userBucket.get();
        }
        UserDataAuthorityContext userDataAuthorityContext= new UserDataAuthorityContext();
        userDataAuthorityContext.setAuthorityLevel(AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_3);
        return userDataAuthorityContext;
    }
}