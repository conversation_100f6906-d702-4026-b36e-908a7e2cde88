/*
 * Dian.com Inc.
 * Copyright (c) 2004-2018 All Rights Reserved.
 */
package com.chargebolt.template;

import com.chargebolt.commons.constant.AppConstant;
import com.chargebolt.context.MerchantLoginContext;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RestController;
import so.dian.eros.cache.RedisClient;
import so.dian.mofa3.template.controller.ControllerTemplate;
import so.dian.talos.pojo.entity.MerchantDO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 商家APP端使用控制器基类
 *
 * <AUTHOR>
 * @version $Id: BaseController.java, v 0.1 2018-04-16 下午2:47 Exp $
 */
@SuppressWarnings("unused")
@Slf4j
@RestController
public abstract class BaseController {
    /**
     * 操作模板
     */
    @Resource
    @Qualifier("AbstractI18nControllerTemplate")
    public ControllerTemplate template;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Autowired
    private RedissonClient redissonClient;

    public MerchantLoginContext getLoginMerchant(){
        String loginToken = httpServletRequest.getHeader(AppConstant.APP_HEADERS_TOKEN_KEY);
        RBucket<MerchantLoginContext> bucket= redissonClient.getBucket(loginToken);
        return bucket.get();
    }

}