/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.commons.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TenantExceptionEnum.java, v 1.0 2024-09-09 上午11:33 Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum TenantExceptionEnum {
    TENANT_IS_NULL("208001", "租户不存在"),
    TENANT_RELATED_IS_NULL("208002", "租户关联不存在"),
    TENANT_ID_IS_NULL("208003", "租户ID不能为空"),
    TENANT_AGENT_LEVEL_GT2_NO_PERMISSION("208004", "代理商二级及以下不允许操作"),

    // 协议相关
    PROTOCOL_IS_NULL("208051", "协议不存在"),
    PROTOCOL_CODE_NOT_NULL("208052", "协议CODE不能为空"),
    PROTOCOL_TITLE_NOT_NULL("208053", "协议标题不能为空"),
    PROTOCOL_LANG_NOT_NULL("208054", "协议语言不能为空"),
    PROTOCOL_CONTENT_NOT_NULL("208055", "协议内容不能为空"),

    // 汇率转换相关
    EXCHANGE_CURRENT_CURRENCY_IS_NULL("208071", "源币种不能为空"),
    EXCHANGE_TARGET_CURRENCY_IS_NULL("208072", "目标币种不能为空"),
    EXCHANGE_CLIENT_TYPE_IS_NULL("208073", "端类型不能为空"),
    EXCHANGE_EXCHANGE_RATE_IS_NULL("208074", "汇率比率不能为空"),
    EXCHANGE_EXCHANGE_RATE_GT_THAN_0("208075", "汇率比率必须大于0"),
    EXCHANGE_EXCHANGE_EXISTED("208076", "配置已经存在"),
    ;
    @Getter
    private final String code;
    @Getter
    private final String desc;
}