/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.tenant.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TenantMapperScanConfig.java, v 1.0 2023-10-30 2:05 PM Exp $
 */
@Configuration
@MapperScan(basePackages = {"com.chargebolt.tenant.dao"}, sqlSessionFactoryRef = "sqlSessionFactory")
public class TenantMapperScanConfig {
}