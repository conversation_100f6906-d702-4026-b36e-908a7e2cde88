/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.controller;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.commons.exception.CommonExceptionEnum;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.template.BaseChargeboltController;
import com.chargebolt.tenant.commons.enums.TenantExceptionEnum;
import com.chargebolt.tenant.dao.model.CurrencyExchange;
import com.chargebolt.tenant.request.currency.AddCurrencyExchangeRequest;
import com.chargebolt.tenant.request.currency.EditCurrencyExchangeRequest;
import com.chargebolt.tenant.currency.CurrencyExchangeDetailResponse;
import com.chargebolt.tenant.service.CurrencyExchangeService;
import com.chargebolt.tenant.service.TenantAgentRelatedService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: CurrencyExchangeController.java, v 1.0 2024-09-10 下午3:28 Exp $
 */
@RestController
public class CurrencyExchangeController extends BaseChargeboltController {
    @Autowired
    private CurrencyExchangeService currencyExchangeService;
    @Autowired
    private AgentService agentService;
    /**
     * 平台管理，代理商币种转换配置
     *
     * @return
     */
    @Login
    @PostMapping(value = "/pc/currency/exchange/add")
    public Result<Integer> currencyExchangeAdd(@RequestBody AddCurrencyExchangeRequest request) {
        return template.execute(new ControllerCallback<Integer>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(request.getTenantId())){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getCurrentCurrency())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_CURRENT_CURRENCY_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_CURRENT_CURRENCY_IS_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getTargetCurrency())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_TARGET_CURRENCY_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_TARGET_CURRENCY_IS_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getClientCode())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_CLIENT_TYPE_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_CLIENT_TYPE_IS_NULL.getDesc());
                }
                if(Objects.isNull(request.getExchangeRate())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_IS_NULL.getDesc());
                }
                if(request.getExchangeRate()<=0){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_GT_THAN_0.getCode(), TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_GT_THAN_0.getDesc());
                }
                // fixme 二代及以下不允许操作
                if(Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }
            @Override
            public Integer execute() {
                return currencyExchangeService.addCurrencyExchange(request);
            }
        });
    }

    /**
     * 平台管理，代理商币种转换配置
     *
     * @return
     */
    @Login
    @PostMapping(value = "/pc/currency/exchange/update")
    public Result<Integer> currencyExchangeUpdate(@RequestBody EditCurrencyExchangeRequest request) {
        return template.execute(new ControllerCallback<Integer>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(request.getId())){
                    throw new I18nMessageException(CommonExceptionEnum.ID_NOT_NULL.getCode(), CommonExceptionEnum.ID_NOT_NULL.getDesc());
                }
                if(Objects.isNull(request.getTenantId())){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getCurrentCurrency())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_CURRENT_CURRENCY_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_CURRENT_CURRENCY_IS_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getTargetCurrency())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_TARGET_CURRENCY_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_TARGET_CURRENCY_IS_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getClientCode())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_CLIENT_TYPE_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_CLIENT_TYPE_IS_NULL.getDesc());
                }
                if(Objects.isNull(request.getExchangeRate())){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_IS_NULL.getCode(), TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_IS_NULL.getDesc());
                }
                if(request.getExchangeRate()<=0){
                    throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_GT_THAN_0.getCode(), TenantExceptionEnum.EXCHANGE_EXCHANGE_RATE_GT_THAN_0.getDesc());
                }
                // fixme 二代及以下不允许操作
                if(Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }
            @Override
            public Integer execute() {
                return currencyExchangeService.editCurrencyExchange(request);
            }
        });
    }

    /**
     * 币种汇率转换列表
     *
     * @param pageNo
     * @param pageSize
     * @param tenantId
     * @return
     */
    @Login
    @GetMapping(value = "/pc/currency/exchange/list")
    public Result<List<CurrencyExchangeDetailResponse>> currencyExchangeList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "50") Integer pageSize,
                                                             @RequestParam(name = "tenantId") Long tenantId
    ) {
        return template.execute(new ControllerCallback<List<CurrencyExchangeDetailResponse>>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(tenantId)){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }
            @Override
            public List<CurrencyExchangeDetailResponse> execute() {
                return currencyExchangeService.currencyExchangeList(pageNo, pageSize, tenantId);
            }
        });
    }

    @Login
    @PostMapping(value = "/pc/currency/exchange/delete")
    public Result<Boolean> currencyExchangeDelete(@RequestBody EditCurrencyExchangeRequest request) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(request.getId())){
                    throw new I18nMessageException(CommonExceptionEnum.ID_NOT_NULL.getCode(), CommonExceptionEnum.ID_NOT_NULL.getDesc());
                }

            }

            @Override
            public void buildContext() {
            }
            @Override
            public Boolean execute() {
                CurrencyExchange model = new CurrencyExchange();
                model.setId(request.getId());
                return currencyExchangeService.removeRecord(model)>0;
            }
        });
    }
}