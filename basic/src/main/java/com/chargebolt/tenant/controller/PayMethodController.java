/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.controller;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.request.tenant.PaywayUpdateRequest;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.template.BaseChargeboltController;
import com.chargebolt.tenant.commons.enums.TenantExceptionEnum;
import com.chargebolt.tenant.service.PaywayService;
import com.chargebolt.theseus.dto.I18nItemDTO;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import so.dian.eros.pojo.dto.theseus.StatusUpdateDTO;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PayMethodController.java, v 1.0 2024-09-29 下午2:59 Exp $
 */
@RestController
public class PayMethodController extends BaseChargeboltController {
    @Autowired
    private AgentService agentService;
    @Autowired
    private PaywayService paywayService;


    /**
     * 支付列表
     *
     * @return
     */
    // @ApiOperation("获取支付方式列表")
    @Login
    @GetMapping(value = "/pc/paymethod/get")
    public Result<List<PaywayService.PayToolGroupDTO>> payMethodList(@RequestParam(name = "tenantId") Long tenantId) {
        return template.execute(new ControllerCallback<List<PaywayService.PayToolGroupDTO>>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(tenantId)) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if (Boolean.FALSE.equals(agentService.levelOneAgent(tenantId))) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public List<PaywayService.PayToolGroupDTO> execute() {
                return paywayService.get(tenantId);
            }
        });
    }

    @Login
    @PostMapping(value = "/pc/paymethod/update")
    public Result<Boolean> payMethodUpdate(@RequestBody PaywayUpdateRequest request) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(request.getTenantId())) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if (Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public Boolean execute() {
                return paywayService.update(request);
            }
        });
    }

    @Login
    @PostMapping(value = "/pc/paymethod/status")
    public Result<Boolean> payMethodStatus(@RequestBody StatusUpdateDTO request) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(request.getTenantId())) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if (Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public Boolean execute() {
                return paywayService.updateStatus(request);
            }
        });
    }

    // @ApiOperation("获取支付方式提示语")
    @Login
    @GetMapping("/pc/paymethod/tips/get")
    public Result<List<I18nItemDTO>> payMethodTips(@RequestParam(name = "tenantId") Long tenantId, @RequestParam(name = "payway") Integer payway, @RequestParam(name = "methodId") Integer methodId) {
        return template.execute(new ControllerCallback<List<I18nItemDTO>>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(tenantId)) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if (Boolean.FALSE.equals(agentService.levelOneAgent(tenantId))) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public List<I18nItemDTO> execute() {
                return paywayService.getTips(tenantId, payway, methodId);
            }
        });
    }

    @Data
    private static class PayMethodTipsUpdateRequest {
        private Long tenantId;
        private Integer methodId;
        private Integer payway;
        private List<I18nItemDTO> items;
    }

    @Login
    @PostMapping("/pc/paymethod/tips/update")
    public Result<Boolean> payMethodTipsUpdate(@RequestBody PayMethodTipsUpdateRequest request) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(request.getTenantId())) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if (Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))) {
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }

            @Override
            public Boolean execute() {
                paywayService.setTips(request.getTenantId(), request.getPayway(), request.getMethodId(), request.getItems());
                return true;
            }
        });
    }
}