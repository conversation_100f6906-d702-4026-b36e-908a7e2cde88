/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.controller;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.commons.constant.SkuConstant;
import com.chargebolt.ezreal.constant.TenantConfigExtConstant;
import com.chargebolt.commons.exception.AgentExceptionEnum;
import com.chargebolt.ezreal.response.tenant.PriceResponse;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.template.BaseChargeboltController;
import com.chargebolt.tenant.commons.enums.TenantExceptionEnum;
import com.chargebolt.tenant.dao.model.TenantAgentRelated;
import com.chargebolt.tenant.service.TenantAgentRelatedService;
import com.chargebolt.tenant.service.TenantExtConfigService;
import com.chargebolt.tenant.service.TenantPriceService;
import com.chargebolt.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.biz.facade.SkuFacade;
import so.dian.eros.common.exception.AresErrorEnum;
import so.dian.eros.pojo.param.AddBoxPriceParam;
import so.dian.eros.pojo.param.AddBoxPriceRequest;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PriceController.java, v 1.0 2024-09-10 上午10:11 Exp $
 */
@Slf4j
@RestController
public class PriceController extends BaseChargeboltController {
    @Resource
    private TenantPriceService tenantPriceService;
    @Resource
    private AgentService agentService;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantExtConfigService tenantExtConfigService;
    @Resource
    private TenantAgentRelatedService tenantAgentRelatedService;

    /**
     * 默认价格配置
     *
     * @return
     */
    @Login
    @GetMapping(value = "/pc/tenant/price/get")
    public Result<PriceResponse> getPrice(@RequestParam(name = "tenantId", required = false) Long tenantId) {
        return template.execute(new ControllerCallback<PriceResponse>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(tenantId)){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if(Boolean.FALSE.equals(agentService.levelOneAgent(tenantId))){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }
            @Override
            public PriceResponse execute() {
                return tenantPriceService.getPrice(tenantId);
            }
        });
    }

    @Login
    @PostMapping(value = "/pc/tenant/price/update")
    public Result<Boolean> priceUpdate(@RequestBody AddBoxPriceRequest request) {
        AddBoxPriceParam addBoxPriceParam= new AddBoxPriceParam();
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(request.getTenantId())){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                if(request.getFreeTime()> SkuConstant.MAX_FREE_TIME){
                    throw new I18nMessageException(AresErrorEnum.SKU_ADD_MAX_FREE_TIME_ERROR.getCode().toString(), "免费时间不大于"+SkuConstant.MAX_FREE_TIME);
                }
                // 每天计价周期
                if(request.getPeriodMinute()>SkuConstant.MAX_DAY_PERIOD_MINUTE){
                    throw new I18nMessageException(AresErrorEnum.SKU_ADD_MAX_DAY_PERIOD_TIME_ERROR.getCode().toString(), "单价时间最大不能超过1天");
                }
                if(request.getPeriodMinute()<= SkuConstant.MIN_VALUE){
                    throw new I18nMessageException(AresErrorEnum.SKU_ADD_PERIOD_MINUTE_ZERO_ERROR.getCode().toString(), "单价时间不能为0");
                }
                // 单价最小值
                if(request.getUnitPrice()<= SkuConstant.MIN_VALUE){
                    throw new I18nMessageException(AresErrorEnum.SKU_ADD_UNIT_PRICE_ZERO_ERROR.getCode().toString(), "单价不能为0");
                }
                // 单价最大值
                if(request.getUnitPrice()>SkuConstant.MAX_UNIT_PRICE){
                    throw new I18nMessageException(AresErrorEnum.SKU_ADD_MAX_UNIT_PRICE_ERROR.getCode().toString(), "单价不能大于10000000");
                }
                // 24小时封顶价
                if(request.getMaxCostPerDay()<= SkuConstant.MIN_VALUE){
                    throw new I18nMessageException(AresErrorEnum.SKU_ADD_MAX_COST_PER_DAY_107_ERROR.getCode().toString(),"每24小时封顶价不能为0");
                }
                // 24小时价格计算（计费周期单价➗ 计费周期秒）✖️ 每天分钟数
                double maxCostPerDay= (request.getUnitPrice()/ request.getPeriodMinute())* SkuConstant.MAX_DAY_PERIOD_MINUTE;
                if(request.getMaxCostPerDay()>maxCostPerDay){
                    throw new I18nMessageException(AresErrorEnum.SKU_ADD_MAX_COST_PER_DAY_106_ERROR.getCode().toString(), "每24小时封顶价最大不能超过单价*24");
                }

                // 二代及以下不允许操作
                if(Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
                log.info("代理商充电价格设置，请求参数：{}", JsonUtil.beanToJson(request));

                TenantAgentRelated tenantAgentRelated =new TenantAgentRelated();
                tenantAgentRelated.setTenantId(request.getTenantId());
                tenantAgentRelated = tenantAgentRelatedService.getRecord(tenantAgentRelated);
                if(Objects.isNull(tenantAgentRelated)){
                    throw new I18nMessageException(AgentExceptionEnum.TENANT_AGENT_RELATED_IS_NULL.getCode(), AgentExceptionEnum.TENANT_AGENT_RELATED_IS_NULL.getDesc());
                }
                TenantAgentInfoResponse tenantAgentInfoResponse= tenantService.getTopTenantInfo(tenantAgentRelated.getAgentId());
                log.info("代理商充电价格设置，一级代理商信息获取：{}", JsonUtil.beanToJson(tenantAgentInfoResponse));
                String currencyCode= tenantAgentInfoResponse.getCurrencyCode();
                addBoxPriceParam.setShopIdList(Arrays.asList(tenantAgentInfoResponse.getDefaultShopId()));
                addBoxPriceParam.setFreeTime(request.getFreeTime()*60);
                MultiCurrencyMoney maxCostPerDayMoney= new MultiCurrencyMoney(request.getMaxCostPerDay(), currencyCode);
                addBoxPriceParam.setMaxCostPerDay(maxCostPerDayMoney.getCent());

                addBoxPriceParam.setPeriod(request.getPeriodMinute()*60);
                MultiCurrencyMoney unitPriceMoney= new MultiCurrencyMoney(request.getUnitPrice(), currencyCode);
                addBoxPriceParam.setUnitPrice(unitPriceMoney.getCent());

                MultiCurrencyMoney maxCostMoney= new MultiCurrencyMoney(request.getMaxCost(), currencyCode);
                addBoxPriceParam.setMaxCost(maxCostMoney.getCent());

                // TODO 使用和约定规范有冲突，先这样吧
                MultiCurrencyMoney depositAmountMoney= new MultiCurrencyMoney(request.getMaxCost(), currencyCode);
                // 2025-02-11 价格绑定到门店，将封顶价和押金设置一致
                tenantExtConfigService.saveTenantExtConfig(tenantAgentRelated.getTenantId(),
                        TenantConfigExtConstant.TENANT_CHARGE_DEPOSIT_AMOUNT, String.valueOf(depositAmountMoney.getCent()));

            }
            @Override
            public Boolean execute() {
                BizResult result= skuFacade.createBoxPrice(addBoxPriceParam, null, getUser().getName(), null);
                if(Boolean.FALSE.equals(result.isSuccess())){
                    throw new I18nMessageException(result.getCode().toString(), result.getMsg());
                }
                return Boolean.TRUE;
            }
        });
    }

}