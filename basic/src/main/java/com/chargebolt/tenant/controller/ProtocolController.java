/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.controller;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.commons.constant.BizConstant;
import com.chargebolt.commons.enums.MultilingualEnum;
import com.chargebolt.commons.enums.language.ProtocolTypeLanguageEnum;
import com.chargebolt.commons.exception.CommonExceptionEnum;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.template.BaseChargeboltController;
import com.chargebolt.tenant.commons.enums.TenantExceptionEnum;
import com.chargebolt.tenant.dao.model.Protocol;
import com.chargebolt.tenant.request.protocol.AddProtocolRequest;
import com.chargebolt.tenant.request.protocol.EditProtocolRequest;
import com.chargebolt.ezreal.response.protocol.ProtocolCodeResponse;
import com.chargebolt.ezreal.response.protocol.ProtocolDetailResponse;
import com.chargebolt.ezreal.response.protocol.ProtocolSimpleResponse;
import com.chargebolt.tenant.service.ProtocolService;
import com.chargebolt.tenant.service.TenantAgentRelatedService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ProtocolController.java, v 1.0 2024-09-10 上午10:11 Exp $
 */
@RestController
public class ProtocolController extends BaseChargeboltController {
    @Resource
    private ProtocolService protocolService;
    @Resource
    private AgentService agentService;
    /**
     * 平台管理，协议新增
     *
     * @return
     */
    @Login
    @PostMapping(value = "/pc/protocol/add")
    public Result<Integer> protocolAdd(@RequestBody AddProtocolRequest request) {
        Protocol model= new Protocol();
        return template.execute(new ControllerCallback<Integer>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(request.getTenantId())){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getCode())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_CODE_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_CODE_NOT_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getTitle())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_TITLE_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_TITLE_NOT_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getLang())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_LANG_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_LANG_NOT_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getContent())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_CONTENT_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_CONTENT_NOT_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if(Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
                model.setTenantId(request.getTenantId());
                model.setCode(request.getCode());
                model.setTitle(request.getTitle());
                model.setLang(request.getLang());
                model.setContent(request.getContent());
                model.setClientType(1);
            }
            @Override
            public Integer execute() {
                return protocolService.saveRecord(model);
            }
        });
    }

    @Login
    @PostMapping(value = "/pc/protocol/update")
    public Result<Integer> protocolUpdate(@RequestBody EditProtocolRequest request) {
        Protocol model= new Protocol();
        return template.execute(new ControllerCallback<Integer>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(request.getId())){
                    throw new I18nMessageException(CommonExceptionEnum.ID_NOT_NULL.getCode(), CommonExceptionEnum.ID_NOT_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getCode())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_CODE_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_CODE_NOT_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getTitle())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_TITLE_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_TITLE_NOT_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getLang())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_LANG_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_LANG_NOT_NULL.getDesc());
                }
                if(StringUtils.isBlank(request.getContent())){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_CONTENT_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_CONTENT_NOT_NULL.getDesc());
                }
                // fixme 二代及以下不允许操作
                if(Boolean.FALSE.equals(agentService.levelOneAgent(request.getTenantId()))){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getCode(), TenantExceptionEnum.TENANT_AGENT_LEVEL_GT2_NO_PERMISSION.getDesc());
                }
            }

            @Override
            public void buildContext() {
                model.setId(request.getId());
                model.setCode(request.getCode());
                model.setTitle(request.getTitle());
                model.setLang(request.getLang());
                model.setContent(request.getContent());
            }
            @Override
            public Integer execute() {
                return protocolService.updateRecord(model);
            }
        });
    }
    /**
     * 协议code
     *
     * @return
     */
    @GetMapping(value = "/pc/protocol/protocolCodes")
    public Result<List<ProtocolCodeResponse>> protocolCodes() {
        return template.execute(new ControllerCallback<List<ProtocolCodeResponse>>() {
            @Override
            public void checkParam() {

            }

            @Override
            public void checkConcurrent() {

            }

            @Override
            public void buildContext() {

            }
            @Override
            public List<ProtocolCodeResponse> execute() {
                List<ProtocolCodeResponse> response= new ArrayList<>();
                for (ProtocolTypeLanguageEnum protocolTypeLanguageEnum : ProtocolTypeLanguageEnum.values()) {
                    ProtocolCodeResponse protocolCodeResponse = new ProtocolCodeResponse();
                    protocolCodeResponse.setProtocolCode(protocolTypeLanguageEnum.getCode());
                    protocolCodeResponse.setProtocolName(protocolTypeLanguageEnum.getEnumValue());
                    response.add(protocolCodeResponse);
                }
                return response;
            }
        });
    }

    /**
     * 平台管理，协议列表
     *
     * @param pageNo
     * @param pageSize
     * @param tenantId
     * @return
     */
    @Login
    @GetMapping(value = "/pc/protocol/list")
    public Result<List<ProtocolSimpleResponse>> protocolList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "50") Integer pageSize,
                                                             @RequestParam(name = "tenantId") Long tenantId
//                                                             @RequestParam(name = "protocCode", required = false) String protocCode ,
//                                                             @RequestParam(name = "title", required = false) String title,
//                                                             @RequestParam(name = "lang", required = false) String lang
                                                             ) {
        Protocol query= new Protocol();
        return template.execute(new ControllerCallback<List<ProtocolSimpleResponse>>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(tenantId)){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
//                query.setCode(protocCode);
//                query.setTitle(title);
//                query.setLang(lang);
                query.setTenantId(tenantId);
            }
            @Override
            public List<ProtocolSimpleResponse> execute() {
                return protocolService.protocolSimpleList(pageNo, pageSize, query);
            }
        });
    }
    /**
     * 平台管理，协议详情
     *
     * @return
     */
    @Login
    @GetMapping(value = "/pc/protocol/detail")
    public Result<ProtocolDetailResponse> protocolDetail(@RequestParam(name = "id") Long id) {
        return template.execute(new ControllerCallback<ProtocolDetailResponse>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(id)){
                    throw new I18nMessageException(CommonExceptionEnum.ID_NOT_NULL.getCode(), CommonExceptionEnum.ID_NOT_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }
            @Override
            public ProtocolDetailResponse execute() {
                return protocolService.getProtocolDetail(id);
            }
        });
    }

    /**
     * 平台管理，协议列表
     *
     * @param tenantId
     * @return
     */
    @GetMapping(value = "/1.0/protocol/list")
    public Result<List<ProtocolSimpleResponse>> cProtocolList(
                                                             @RequestParam(name = "tenantId", required = false) Long tenantId,
                                                             @RequestParam(name = "codes", required = false) String codes,
                                                             @RequestParam(name = "language", required = false) String language
    ) {
        Protocol query= new Protocol();
        return template.execute(new ControllerCallback<List<ProtocolSimpleResponse>>() {
            @Override
            public void checkParam() {
                if(StringUtils.isBlank(codes)){
                    throw new I18nMessageException(TenantExceptionEnum.PROTOCOL_CODE_NOT_NULL.getCode(), TenantExceptionEnum.PROTOCOL_CODE_NOT_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
                String lang= language;
                if(Objects.isNull(language)){
                    lang= ThreadLanguageHolder.getCurrentLang();
                }
                query.setCodes(Arrays.stream(codes.split(","))
                        .collect(Collectors.toList()));
                query.setLang(MultilingualEnum.findLanguageEnum(lang).getLocale().toLanguageTag());
                if(Objects.isNull(tenantId)){
                    query.setTenantId(BizConstant.DEFAULT_TENANT_ID);
                }else{
                    query.setTenantId(tenantId);
                }
            }
            @Override
            public List<ProtocolSimpleResponse> execute() {
                return protocolService.protocolTenantSimpleList(query);
            }
        });
    }

    /**
     * 协议详情
     *
     * @return
     */
    @GetMapping(value = "/1.0/protocol/detail")
    public Result<ProtocolDetailResponse> cProtocolDetail(@RequestParam(name = "id") Long id) {
        return template.execute(new ControllerCallback<ProtocolDetailResponse>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(id)){
                    throw new I18nMessageException(CommonExceptionEnum.ID_NOT_NULL.getCode(), CommonExceptionEnum.ID_NOT_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {
            }
            @Override
            public ProtocolDetailResponse execute() {
                return protocolService.getProtocolDetail(id);
            }
        });
    }
}