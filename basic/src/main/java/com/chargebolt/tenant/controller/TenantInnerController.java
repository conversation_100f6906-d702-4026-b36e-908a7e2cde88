/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.controller;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.commons.exception.AgentExceptionEnum;
import com.chargebolt.commons.exception.CommonExceptionEnum;
import com.chargebolt.ezreal.common.PaymentClientTypeEnum;
import com.chargebolt.ezreal.response.tenant.CurrencyExchangeResponse;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.template.BaseChargeboltController;
import com.chargebolt.tenant.commons.enums.TenantExceptionEnum;
import com.chargebolt.tenant.dao.model.PaymentMethod;
import com.chargebolt.tenant.service.AgentTenantConfigService;
import com.chargebolt.tenant.service.CurrencyExchangeService;
import com.chargebolt.tenant.service.PaymentMethodService;
import com.chargebolt.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.template.controller.ControllerCallback;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TenantController.java, v 1.0 2024-10-14 下午6:30 Exp $
 */
@Slf4j
@RestController
public class TenantInnerController extends BaseChargeboltController {
    @Resource
    private TenantService tenantService;
    @Resource
    private AgentTenantConfigService agentTenantConfigService;
    @Resource
    private PaymentMethodService paymentMethodService;
    @Resource
    private CurrencyExchangeService currencyExchangeService;

    /**
     * 获取代理商绑定的租户
     *
     * @param agentId
     * @return
     */
    @GetMapping(value = "/inner/tenant/getTenantByAgentId")
    public Result<TenantAgentInfoResponse> getTenantByAgentId(@RequestParam(name = "agentId", required = false) Long agentId) {
        return template.execute(new ControllerCallback<TenantAgentInfoResponse>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(agentId)){
                    throw new I18nMessageException(AgentExceptionEnum.AGENT_ID_NULL.getCode(), AgentExceptionEnum.AGENT_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public TenantAgentInfoResponse execute() {
                return tenantService.getTopTenantInfo(agentId);
            }
        });
    }

    /**
     * 获取是否自动退款
     * C端有轮询逻辑，增加1分钟缓存
     *
     * @param agentId
     * @return
     */
    @GetMapping(value = "/inner/tenant/getDepositAutoRefund")
    public Result<Boolean> getDepositAutoRefund(@RequestParam(name = "agentId", required = false) Long agentId) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(agentId)){
                    throw new I18nMessageException(AgentExceptionEnum.AGENT_ID_NULL.getCode(), AgentExceptionEnum.AGENT_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public Boolean execute() {
                return agentTenantConfigService.getDepositAutoRefund(agentId);
            }
        });
    }

    @GetMapping(value = "/inner/tenant/getOrderAutoPayWithDeposit")
    public Result<Boolean> getOrderAutoPayWithDeposit(@RequestParam(name = "agentId", required = false) Long agentId) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(agentId)){
                    throw new I18nMessageException(AgentExceptionEnum.AGENT_ID_NULL.getCode(), AgentExceptionEnum.AGENT_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public Boolean execute() {
                return agentTenantConfigService.getOrderAutoPayWithDeposit(agentId);
            }
        });
    }

    @GetMapping(value = "/inner/tenant/getAgentTenantConfig")
    public Result<String> getAgentTenantConfig(@RequestParam(name = "agentId", required = false) Long agentId,
                                               @RequestParam(name = "configKey", required = false) String configKey) {
        return template.execute(new ControllerCallback<String>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(agentId)){
                    throw new I18nMessageException(AgentExceptionEnum.AGENT_ID_NULL.getCode(), AgentExceptionEnum.AGENT_ID_NULL.getDesc());
                }if(StringUtils.isBlank(configKey)){
                    throw new I18nMessageException(AgentExceptionEnum.AGENT_ID_NULL.getCode(), AgentExceptionEnum.AGENT_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public String execute() {
                return agentTenantConfigService.getAgentTenantConfig(agentId, configKey);
            }
        });
    }

    @GetMapping(value = "/inner/tenant/getAgentDefaultShopId")
    public Result<Long> getAgentDefaultShopId(@RequestParam(name = "agentId", required = false) Long agentId) {
        return template.execute(new ControllerCallback<Long>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(agentId)){
                    throw new I18nMessageException(AgentExceptionEnum.AGENT_ID_NULL.getCode(), AgentExceptionEnum.AGENT_ID_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public Long execute() {
                return agentTenantConfigService.getDefaultShopId(agentId);
            }
        });
    }

    @GetMapping(value = "/inner/tenant/getTenantPayMethod")
    public Result<String> getTenantPayMethod(@RequestParam(name = "tenantId", required = false) Long tenantId) {
        return template.execute(new ControllerCallback<String>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(tenantId)){
                    throw new I18nMessageException(TenantExceptionEnum.TENANT_ID_IS_NULL.getCode(), TenantExceptionEnum.TENANT_ID_IS_NULL.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public String execute() {
                PaymentMethod query= new PaymentMethod();
                query.setTenantId(tenantId);
                List<PaymentMethod> list= paymentMethodService.listRecord(query);
                if(CollectionUtils.isEmpty(list)){
                    return "";
                }
                return list.get(0).getPaymentMethod();
            }
        });
    }

    @GetMapping(value = "/inner/tenant/getAgentCurrencyExchange")
    public Result<CurrencyExchangeResponse> getAgentCurrencyExchange(@RequestParam(name = "agentId", required = false) Long agentId,
                                                 @RequestParam(name = "clientCode", required = false) String clientCode) {
        return template.execute(new ControllerCallback<CurrencyExchangeResponse>() {
            @Override
            public void checkParam() {
                if(Objects.isNull(agentId)){
                    throw new I18nMessageException(AgentExceptionEnum.AGENT_ID_NULL.getCode(), AgentExceptionEnum.AGENT_ID_NULL.getDesc());
                }
                if(StringUtils.isBlank(clientCode)){
                    throw new I18nMessageException(CommonExceptionEnum.TYPE_NOT_SUPPORTED.getCode(),CommonExceptionEnum.TYPE_NOT_SUPPORTED.getDesc());
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public CurrencyExchangeResponse execute() {
                return currencyExchangeService.getCurrencyExchange(agentId, PaymentClientTypeEnum.getByCode(clientCode));
            }
        });
    }
}