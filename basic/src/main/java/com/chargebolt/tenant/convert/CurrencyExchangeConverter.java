/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.convert;

import com.chargebolt.tenant.dao.model.CurrencyExchange;
import com.chargebolt.tenant.request.currency.AddCurrencyExchangeRequest;
import com.chargebolt.tenant.request.currency.EditCurrencyExchangeRequest;
import com.chargebolt.tenant.currency.CurrencyExchangeDetailResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: CurrencyExchangeConverter.java, v 1.0 2024-09-11 上午10:33 Exp $
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CurrencyExchangeConverter {
    CurrencyExchangeConverter INSTANCE = Mappers.getMapper(CurrencyExchangeConverter.class);

    CurrencyExchange currencyExchangeAddConvert(AddCurrencyExchangeRequest model);
    CurrencyExchange currencyExchangeEditConvert(EditCurrencyExchangeRequest model);

    @Mapping(target = "clientName", expression = "java(com.chargebolt.commons.enums.language.PaymentClientTypeLanguageEnum.valueOf(model.getClientCode()).getChinese())")
    CurrencyExchangeDetailResponse currencyExchangeDetailResponseConvert(CurrencyExchange model);
}