/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.convert;

import com.chargebolt.tenant.dao.model.Protocol;
import com.chargebolt.ezreal.response.protocol.ProtocolDetailResponse;
import com.chargebolt.ezreal.response.protocol.ProtocolSimpleResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ProtocolConvert.java, v 1.0 2024-09-10 上午11:40 Exp $
 */
@Mapper
        (componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProtocolConverter {
    ProtocolConverter INSTANCE = Mappers.getMapper(ProtocolConverter.class);

    @Mapping(target = "codeName", expression = "java(com.chargebolt.commons.enums.language.ProtocolTypeLanguageEnum.valueOf(model.getCode()).getChinese())")
    ProtocolSimpleResponse protocolSimpleResponseConvert(Protocol model);
    @Mapping(target = "codeName", expression = "java(com.chargebolt.commons.enums.language.ProtocolTypeLanguageEnum.valueOf(model.getCode()).getChinese())")
    ProtocolDetailResponse protocolDetailResponseConvert(Protocol model);
}