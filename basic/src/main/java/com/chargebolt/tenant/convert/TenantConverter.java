/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.convert;

import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.tenant.dao.model.Tenant;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TenantConverter.java, v 1.0 2024-09-12 下午4:08 Exp $
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TenantConverter {
    TenantConverter INSTANCE = Mappers.getMapper(TenantConverter.class);

    @Mapping(source = "tenant.id", target = "tenantId")
    @Mapping(source = "agent.id", target = "agentId")
    @Mapping(source = "agent.agentName", target = "agentName")
    @Mapping(source = "config.regionalCode", target = "regionalCode")
    @Mapping(source = "config.defaultLang", target = "defaultLang")
    @Mapping(source = "config.currencyCode", target = "currencyCode")
    @Mapping(source = "config.zoneUtc", target = "zoneUtc")
    @Mapping(source = "config.dateFormat", target = "dateFormat")
    @Mapping(source = "config.sysVersion", target = "sysVersion")
    @Mapping(source = "depositAmount", target = "depositAmount")
    @Mapping(source = "defaultShopId", target = "defaultShopId")
    @Mapping(target = "timeZone", expression = "java(com.chargebolt.commons.enums.UtcZoneEnum.getTimeZoneByUtc(config.getZoneUtc()))")
    TenantAgentInfoResponse tenantAgentInfoResponseConvert(Tenant tenant, Agent agent, AgentBaseConfig config,
                                                           Long depositAmount, Long defaultShopId);

}