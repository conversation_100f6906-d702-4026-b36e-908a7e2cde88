/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.currency;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: CurrencyExchangeDetailResponse.java, v 1.0 2024-09-11 下午1:57 Exp $
 */
@Data
public class CurrencyExchangeDetailResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409255135724L;


    /**
     * 主键
     */
    private Long id;

    /**
     * 端code，微信、支付宝
     */
    private String clientCode;
    private String clientName;

    /**
     * 当前币种
     */
    private String currentCurrency;

    /**
     * 目标币种
     */
    private String targetCurrency;
    private String regionalCurrency;
    /**
     * 币种兑换倍率
     */
    private Double exchangeRate;

    /**
     * 租户ID
     */
    private Long tenantId;

}