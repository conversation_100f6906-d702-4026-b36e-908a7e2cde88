/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.dao.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * currency_exchange
 *
 * <AUTHOR>
 * @version $Id: CurrencyExchange.java, v 0.1 2024-08-30 15:08:29 Exp $
 */
@Data
public class CurrencyExchange {
    /** serialVersionUID */
    private static final long serialVersionUID = 174474981601447073L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 端code，微信、支付宝
     */
    private String clientCode;

    /**
     * 当前币种
     */
    private String currentCurrency;

    /**
     * 目标币种
     */
    private String targetCurrency;
    private String regionalCurrency;

    /**
     * 币种兑换倍率
     */
    private Double exchangeRate;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}