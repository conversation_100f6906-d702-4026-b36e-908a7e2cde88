/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.dao.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * payment_method
 *
 * <AUTHOR>
 * @version $Id: PaymentMethod.java, v 0.1 2024-08-30 15:10:00 Exp $
 */
@Data
public class PaymentMethod {
    /** serialVersionUID */
    private static final long serialVersionUID = 174795241655522905L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 支付方式Json
     */
    private String paymentMethod;

//    /**
//     * 排序
//     */
//    private Integer sort;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}