/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.dao.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * protocol
 *
 * <AUTHOR>
 * @version $Id: Protocol.java, v 0.1 2024-08-30 15:10:06 Exp $
 */
@Data
public class Protocol {
    /** serialVersionUID */
    private static final long serialVersionUID = 175728548685085099L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 协议标题
     */
    private String title;

    /**
     * 协议code，对应有协议类型
     */
    private String code;

    /**
     * 适配端 1 H5
     */
    private Integer clientType;

    /**
     * 语言，BCP 47值
     */
    private String lang;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;

    /**
     * 协议内容，富文本
     */
    private String content;

    //  ******** 扩展查询 ********
    /**
     * 协议code，对应有协议类型
     */
    private List<String> codes;
}