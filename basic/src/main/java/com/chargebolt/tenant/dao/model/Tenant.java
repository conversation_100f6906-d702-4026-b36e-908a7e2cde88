/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.dao.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * tenant
 *
 * <AUTHOR>
 * @version $Id: Tenant.java, v 0.1 2024-08-30 15:10:13 Exp $
 */
@Data
public class Tenant {
    /** serialVersionUID */
    private static final long serialVersionUID = 174858409835935553L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 来源：1平台签约 2多级代理
     */
    private Integer sourceType;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}