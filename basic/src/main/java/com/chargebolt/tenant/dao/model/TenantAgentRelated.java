/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.dao.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * tenant_agent_related
 *
 * <AUTHOR>
 * @version $Id: TenantAgentRelated.java, v 0.1 2024-08-30 15:10:21 Exp $
 */
@Data
public class TenantAgentRelated {
    /** serialVersionUID */
    private static final long serialVersionUID = 175025654793035130L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}