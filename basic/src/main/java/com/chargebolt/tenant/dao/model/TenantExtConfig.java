/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.dao.model;

import java.io.Serializable;
import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * tenant_ext_config
 *
 * <AUTHOR>
 * @version $Id: TenantExtConfig.java, v 0.1 2024-08-30 15:10:29 Exp $
 */
@Data
public class TenantExtConfig {
    /** serialVersionUID */
    private static final long serialVersionUID = 173843937466395006L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 配置key
     */
    private String configKey;

    /**
     * 配置值，复杂对象使用json字符串，注意长度
     */
    private String configValue;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
}