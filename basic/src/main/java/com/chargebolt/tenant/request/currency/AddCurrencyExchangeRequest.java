/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.request.currency;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AddCurrencyExchangeRequest.java, v 1.0 2024-09-11 上午10:04 Exp $
 */
@Data
public class AddCurrencyExchangeRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409255100410L;

    /**
     * 端code，微信、支付宝
     * 枚举：
     * @see com.chargebolt.commons.enums.language.PaymentClientTypeLanguageEnum
     */
    private String clientCode;

    /**
     * 当前币种
     */
    private String currentCurrency;

    /**
     * 目标币种
     */
    private String targetCurrency;
    private String regionalCurrency;

    /**
     * 币种兑换倍率
     */
    private Double exchangeRate;

    /**
     * 租户ID
     */
    private Long tenantId;
}