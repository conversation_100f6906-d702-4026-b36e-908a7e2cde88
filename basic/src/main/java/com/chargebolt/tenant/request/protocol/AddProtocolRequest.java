/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.request.protocol;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AddProtocolRequest.java, v 1.0 2024-09-10 上午10:38 Exp $
 */
@Data
public class AddProtocolRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409254103846L;

    /**
     * 协议标题
     */
    private String title;

    /**
     * 协议code，对应有协议类型
     */
    private String code;

    /**
     * 适配端 1 H5
     */
    private Integer clientType;

    /**
     * 语言，BCP 47值
     */
    private String lang;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 协议内容，富文本
     */
    private String content;
}