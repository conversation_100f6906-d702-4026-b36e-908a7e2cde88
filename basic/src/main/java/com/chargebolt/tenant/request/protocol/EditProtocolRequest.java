/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.request.protocol;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AddProtocolRequest.java, v 1.0 2024-09-10 上午10:38 Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EditProtocolRequest extends AddProtocolRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202409255735422L;

    private Long id;

}