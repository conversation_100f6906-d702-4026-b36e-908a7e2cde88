/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentTenantConfigService.java, v 1.0 2024-10-18 下午2:16 Exp $
 */
public interface AgentTenantConfigService {
    /**
     * 获取一级代理商自动退押金配置
     * 如果代理商未配置，默认返回true
     *
     * C端有轮询逻辑，增加1分钟缓存
     * @param agentId
     * @return
     */
    Boolean getDepositAutoRefund(Long agentId);

    /**
     * 获取一级代理商订单支付，押金自动抵扣
     * 如果代理商未配置，默认返回true
     *
     * C端有轮询逻辑，增加1分钟缓存
     * @param agentId
     * @return
     */
    Boolean getOrderAutoPayWithDeposit(Long agentId);

    /**
     * 获取一级代理商下扩展配置
     *
     * @param agentId
     * @param configKey
     * @return
     */
    String getAgentTenantConfig(Long agentId, String configKey);

    /**
     * 获取代理商默认门店id
     *
     * @param agentId
     * @return
     */
    Long getDefaultShopId(Long agentId);
}