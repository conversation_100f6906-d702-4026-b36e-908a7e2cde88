/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.commons.constant.CacheKeyConstant;
import com.chargebolt.ezreal.constant.TenantConfigExtConstant;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.tenant.dao.model.TenantExtConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AgentTenantConfigServiceImpl.java, v 1.0 2024-10-18 下午2:19 Exp $
 */
@Slf4j
@Service
public class AgentTenantConfigServiceImpl implements AgentTenantConfigService {
    private final TenantExtConfigService tenantExtConfigService;
    private final TenantService tenantService;
    private final RedissonClient redissonClient;

    public AgentTenantConfigServiceImpl(ObjectProvider<TenantExtConfigService> tenantExtConfigServiceProvider,
                                        ObjectProvider<TenantService> tenantServiceProvider,
                                        ObjectProvider<RedissonClient> redissonClientProvider){
        this.tenantExtConfigService= tenantExtConfigServiceProvider.getIfUnique();
        this.tenantService= tenantServiceProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
    }
    @Override
    public Boolean getDepositAutoRefund(final Long agentId) {
        String cacheKey= CacheKeyConstant.DEPOSIT_AUTO_REFUND_KEY+agentId;
        RBucket<Boolean> bucket= redissonClient.getBucket(cacheKey);
        if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
            return bucket.get();
        }
        TenantAgentInfoResponse tenantAgentInfoResponse= getTopTenantInfo(agentId);
        TenantExtConfig tenantExtConfig= tenantExtConfigService.getTenantExtConfig(tenantAgentInfoResponse.getTenantId(), TenantConfigExtConstant.DEPOSIT_AUTO_REFUND);
        if(Objects.nonNull(tenantExtConfig)&& tenantExtConfig.getConfigValue().equalsIgnoreCase("false")){
            bucket.set(Boolean.FALSE, 30, TimeUnit.SECONDS);
            return Boolean.FALSE;
        }
        bucket.set(Boolean.TRUE, 30, TimeUnit.SECONDS);
        return Boolean.TRUE;
    }

    @Override
    public Boolean getOrderAutoPayWithDeposit(final Long agentId) {
        String cacheKey= CacheKeyConstant.ORDER_AUTO_PAY_BY_DEPOSIT_KEY+agentId;
        RBucket<Boolean> bucket= redissonClient.getBucket(cacheKey);
        if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
            return bucket.get();
        }
        TenantAgentInfoResponse tenantAgentInfoResponse= getTopTenantInfo(agentId);
        TenantExtConfig tenantExtConfig= tenantExtConfigService.getTenantExtConfig(tenantAgentInfoResponse.getTenantId(), TenantConfigExtConstant.ORDER_AUTO_PAY_BY_DEPOSIT);
        if(Objects.nonNull(tenantExtConfig)&& tenantExtConfig.getConfigValue().equalsIgnoreCase("false")){
            bucket.set(Boolean.FALSE, 60, TimeUnit.SECONDS);
            return Boolean.FALSE;
        }
        bucket.set(Boolean.TRUE, 60, TimeUnit.SECONDS);
        return Boolean.TRUE;
    }

    @Override
    public String getAgentTenantConfig(final Long agentId, final String configKey) {
        TenantAgentInfoResponse tenantAgentInfoResponse= getTopTenantInfo(agentId);
        TenantExtConfig tenantExtConfig= tenantExtConfigService.getTenantExtConfig(tenantAgentInfoResponse.getTenantId(), configKey);
        if(Objects.isNull(tenantExtConfig)){
            return "";
        }
        return tenantExtConfig.getConfigValue();
    }

    @Override
    public Long getDefaultShopId(final Long agentId) {
        TenantAgentInfoResponse tenantAgentInfoResponse= getTopTenantInfo(agentId);
        TenantExtConfig tenantExtConfig= tenantExtConfigService.getTenantExtConfig(tenantAgentInfoResponse.getTenantId(), TenantConfigExtConstant.TENANT_DEFAULT_SHOP_ID);
        return Long.valueOf(tenantExtConfig.getConfigValue());
    }

    public TenantAgentInfoResponse getTopTenantInfo(Long agentId){
        return tenantService.getTopTenantInfo(agentId);
    }
}