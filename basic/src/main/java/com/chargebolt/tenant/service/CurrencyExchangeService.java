/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.ezreal.common.PaymentClientTypeEnum;
import com.chargebolt.ezreal.response.tenant.CurrencyExchangeResponse;
import com.chargebolt.tenant.dao.model.CurrencyExchange;
import com.chargebolt.tenant.request.currency.AddCurrencyExchangeRequest;
import com.chargebolt.tenant.request.currency.EditCurrencyExchangeRequest;
import com.chargebolt.tenant.currency.CurrencyExchangeDetailResponse;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: CurrencyExchangeService.java, v 0.1 2024-08-30 15:08:29 Exp $
 */

public interface CurrencyExchangeService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<CurrencyExchange>     返回结果
     */
    List<CurrencyExchange> listRecord(CurrencyExchange model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return CurrencyExchange     返回结果
     */
    CurrencyExchange getRecord(CurrencyExchange model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(CurrencyExchange model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(CurrencyExchange model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(CurrencyExchange model);

    int addCurrencyExchange(AddCurrencyExchangeRequest request);
    int editCurrencyExchange(EditCurrencyExchangeRequest request);
    List<CurrencyExchangeDetailResponse> currencyExchangeList(Integer pageNo, Integer pageSize, Long tenantId);

    CurrencyExchangeResponse getCurrencyExchange(Long agentId, PaymentClientTypeEnum clientType);
}