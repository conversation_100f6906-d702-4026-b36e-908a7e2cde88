/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.ezreal.common.PaymentClientTypeEnum;
import com.chargebolt.ezreal.response.tenant.CurrencyExchangeResponse;
import com.chargebolt.tenant.commons.enums.TenantExceptionEnum;
import com.chargebolt.tenant.convert.CurrencyExchangeConverter;
import com.chargebolt.tenant.dao.CurrencyExchangeDAO;
import com.chargebolt.tenant.dao.model.CurrencyExchange;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.chargebolt.tenant.request.currency.AddCurrencyExchangeRequest;
import com.chargebolt.tenant.request.currency.EditCurrencyExchangeRequest;
import com.chargebolt.tenant.currency.CurrencyExchangeDetailResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.exception.BizProcessException;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: CurrencyExchangeServiceImpl.java, v 0.1 2024-08-30 15:08:29 Exp $
 */
@Service
public class CurrencyExchangeServiceImpl implements CurrencyExchangeService {
    @Override
    public List<CurrencyExchange> listRecord(CurrencyExchange model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return currencyExchangeDAO.listRecord(model);
    }

    @Override
    public CurrencyExchange getRecord(CurrencyExchange model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return currencyExchangeDAO.getRecord(model);
    }

    @Override
    public int saveRecord(CurrencyExchange model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        Date date = new DateBuild().toDate();
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return currencyExchangeDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(CurrencyExchange model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return currencyExchangeDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(CurrencyExchange model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return currencyExchangeDAO.updateRecord(model);
    }

    @Override
    public int addCurrencyExchange(final AddCurrencyExchangeRequest request) {
        CurrencyExchange query= new CurrencyExchange();
        query.setTenantId(request.getTenantId());
        query.setClientCode(request.getClientCode());
        query.setCurrentCurrency(request.getCurrentCurrency());
        query.setTargetCurrency(request.getTargetCurrency());
        query.setRegionalCurrency(request.getRegionalCurrency());
        query= getRecord(query);
        if(Objects.nonNull(query)){
            throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_EXCHANGE_EXISTED.getCode(), TenantExceptionEnum.EXCHANGE_EXCHANGE_EXISTED.getDesc());
        }
        CurrencyExchange model= CurrencyExchangeConverter.INSTANCE.currencyExchangeAddConvert(request);
        return saveRecord(model);
    }

    @Override
    public int editCurrencyExchange(final EditCurrencyExchangeRequest request) {
        CurrencyExchange query= new CurrencyExchange();
        query.setTenantId(request.getTenantId());
        query.setClientCode(request.getClientCode());
        query.setCurrentCurrency(request.getCurrentCurrency());
        query.setTargetCurrency(request.getTargetCurrency());
        query= getRecord(query);
        if(Objects.nonNull(query) && !query.getId().equals(request.getId())){
            throw new I18nMessageException(TenantExceptionEnum.EXCHANGE_EXCHANGE_EXISTED.getCode(), TenantExceptionEnum.EXCHANGE_EXCHANGE_EXISTED.getDesc());
        }
        return updateRecord(CurrencyExchangeConverter.INSTANCE.currencyExchangeEditConvert(request));
    }

    @Override
    public List<CurrencyExchangeDetailResponse> currencyExchangeList(final Integer pageNo, final Integer pageSize, final Long tenantId) {
        CurrencyExchange query= new CurrencyExchange();
        query.setTenantId(tenantId);
        Page<CurrencyExchange> page = PageHelper.startPage(pageNo, pageSize, Boolean.FALSE).doSelectPage(() -> listRecord(query));
        return page.stream().map(CurrencyExchangeConverter.INSTANCE::currencyExchangeDetailResponseConvert).collect(Collectors.toList());
    }

    @Override
    public CurrencyExchangeResponse getCurrencyExchange(final Long agentId, final PaymentClientTypeEnum clientType) {
        Long tenantId= tenantAgentRelatedService.getTenantId(agentId);
        if(Objects.isNull(tenantId)){
            throw new BizProcessException("Tenant does not exist");
        }
        CurrencyExchange query= new CurrencyExchange();
        query.setTenantId(tenantId);
        query.setClientCode(clientType.getCode());
        List<CurrencyExchange> listRecord =listRecord(query);
        if(CollectionUtils.isNotEmpty(listRecord)){
            CurrencyExchangeResponse response= new CurrencyExchangeResponse();
            BeanUtils.copyProperties(listRecord.get(0),response);
            return response;
        }
        return null;
    }


    /**
     * 推荐使用构造器注入
     */
    private final CurrencyExchangeDAO currencyExchangeDAO;
    private final TenantAgentRelatedService tenantAgentRelatedService;
    public CurrencyExchangeServiceImpl(ObjectProvider<CurrencyExchangeDAO> currencyExchangeProvider,
                                       ObjectProvider<TenantAgentRelatedService> tenantAgentRelatedServiceProvider) {
        this.currencyExchangeDAO= currencyExchangeProvider.getIfUnique();
        this.tenantAgentRelatedService= tenantAgentRelatedServiceProvider.getIfUnique();
    }
}