/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.tenant.dao.PaymentMethodDAO;
import com.chargebolt.tenant.dao.model.PaymentMethod;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: PaymentMethodServiceImpl.java, v 0.1 2024-08-30 15:10:00 Exp $
 */
@Service
public class PaymentMethodServiceImpl implements PaymentMethodService {
    @Override
    public List<PaymentMethod> listRecord(PaymentMethod model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return paymentMethodDAO.listRecord(model);
    }

    @Override
    public PaymentMethod getRecord(PaymentMethod model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return paymentMethodDAO.getRecord(model);
    }

    @Override
    public int saveRecord(PaymentMethod model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        Date date = new DateBuild().toDate();
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return paymentMethodDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(PaymentMethod model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return paymentMethodDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(PaymentMethod model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return paymentMethodDAO.updateRecord(model);
    }


    /**
     * 推荐使用构造器注入
     */
    private final PaymentMethodDAO paymentMethodDAO;
    public PaymentMethodServiceImpl(ObjectProvider<PaymentMethodDAO> paymentMethodProvider) {
        this.paymentMethodDAO= paymentMethodProvider.getIfUnique();
    }
}