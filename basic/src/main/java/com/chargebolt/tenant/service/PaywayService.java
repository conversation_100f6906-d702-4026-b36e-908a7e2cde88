package com.chargebolt.tenant.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.request.tenant.PaywayUpdateRequest;
import com.chargebolt.tenant.dao.model.PaymentMethod;
import com.chargebolt.theseus.common.enums.ConfigKeyEnum;
import com.chargebolt.theseus.dto.*;
import com.chargebolt.zeus.dto.PayMethodLogoDTO;
import com.chargebolt.zeus.enums.PaywayGroupEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.eros.common.exception.SystemErrorEnum;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.eros.manager.I18nBizManager;
import so.dian.eros.manager.I18nLanguageManager;
import so.dian.eros.pojo.dto.theseus.StatusUpdateDTO;
import so.dian.eros.remote.theseus.ConfigClient;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PaywayService {

    @Resource
    private ConfigClient configClient;
    @Resource
    private PaymentMethodService paymentMethodService;
    @Resource
    private I18nBizManager i18nBizManager;
    @Resource
    private I18nLanguageManager i18nLanguageManager;


    @Data
    public static class PayToolGroupDTO {
        private String uid;
        private Integer id;
        private String name;
        private List<PayMethodDTO> methods;
    }

    @Data
    public static class PayMethodDTO extends PayMethodOrderItemDTO {
        private String uid;
        private String name;
        private String paywayName;
        private PayMethodLogoDTO logo;
        // 提示文案
        private String tips;
    }

    private static Map<PayMethodEnum, PayMethodLogoDTO> methodLogoMap;

    private ConfigNormalDTO getConfigNormalDTO() {
        BizResult<ConfigNormalDTO> bizResult = configClient.getConfigInString(ConfigKeyEnum.PAY_METHOD_ORDER);
        if (bizResult == null || !bizResult.isSuccess() || bizResult.getData() == null) {
            // tag 未国际化
            throw BizException.create(SystemErrorEnum.REMOTE_FALLBACK);
        }
        ConfigNormalDTO configNormalDTO = bizResult.getData();
        return configNormalDTO;
    }

    public Boolean updateStatus(StatusUpdateDTO statusUpdateDTO) {

        PaymentMethod query = new PaymentMethod();
        query.setTenantId(statusUpdateDTO.getTenantId());
        List<PaymentMethod> paymentMethodList = paymentMethodService.listRecord(query);
        List<PayMethodOrderDTO> payMethodGroupDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(paymentMethodList)) {
            payMethodGroupDTOS = JsonUtil.jsonToArray(paymentMethodList.get(0).getPaymentMethod(), PayMethodOrderDTO.class);
        } else {
            throw new I18nMessageException("-9999", "代理商需要初始化支付方法");
        }

//        ConfigNormalDTO configNormalDTO = getConfigNormalDTO();
//        List<PayMethodOrderDTO> payMethodGroupDTOS = JSON.parseArray(configNormalDTO.getValue(), PayMethodOrderDTO.class);
        for (PayMethodOrderDTO payMethodGroupDTO : payMethodGroupDTOS) {
            if (payMethodGroupDTO.getId().equals(statusUpdateDTO.getTypeId())) {
                Integer status = 0;
                for (PayMethodOrderItemDTO methodsDTO : payMethodGroupDTO.getMethods()) {
                    if (methodsDTO.getId().equals(statusUpdateDTO.getToolId()) && methodsDTO.getPayway().equals(statusUpdateDTO.getPayway())) {
                        methodsDTO.setStatus(statusUpdateDTO.getStatus());
                    }
                    if (methodsDTO.getStatus() == 1) {
                        status = 1;
                    }
                }
                payMethodGroupDTO.setStatus(status);
            }
        }
        PaymentMethod paymentMethod = paymentMethodList.get(0);
        paymentMethod.setPaymentMethod(JsonUtil.beanToJson(payMethodGroupDTOS));
        paymentMethodService.updateRecord(paymentMethod);
        return Boolean.TRUE;
    }

    public Boolean update(PaywayUpdateRequest request) {
        PaymentMethod query = new PaymentMethod();
        query.setTenantId(request.getTenantId());
        List<PaymentMethod> paymentMethodList = paymentMethodService.listRecord(query);
        List<PayMethodOrderDTO> payToolOrderDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(paymentMethodList)) {
            payToolOrderDTOS = JsonUtil.jsonToArray(paymentMethodList.get(0).getPaymentMethod(), PayMethodOrderDTO.class);
        } else {
            ConfigNormalDTO configNormalDTO = getConfigNormalDTO();
            payToolOrderDTOS = JSON.parseArray(configNormalDTO.getValue(), PayMethodOrderDTO.class);
        }
        Map<String, Map<String, PayMethodOrderItemDTO>> originalPayTypeMap = Maps.newHashMap();
        for (PayMethodOrderDTO payMethodOrderDTO : payToolOrderDTOS) {
            Map<String, PayMethodOrderItemDTO> map = originalPayTypeMap.computeIfAbsent(String.valueOf(payMethodOrderDTO.getId()), k -> Maps.newHashMap());
            for (PayMethodOrderItemDTO payMethodOrderItemDTO : payMethodOrderDTO.getMethods()) {
                map.put(String.format("%s-%s", payMethodOrderItemDTO.getPayway(), payMethodOrderItemDTO.getId()), payMethodOrderItemDTO);
            }
        }
        List<PayMethodOrderDTO> save = Lists.newArrayList();
        if (request.getList().size() != originalPayTypeMap.size()) {
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "参数有误");
        }
        for (PayMethodOrderDTO payMethodOrderDTO : request.getList()) {
            Integer status = 0;
            PayMethodOrderDTO newPayMethodOrderDTO = new PayMethodOrderDTO();
            Map<String, PayMethodOrderItemDTO> map = originalPayTypeMap.get(String.valueOf(payMethodOrderDTO.getId()));
            if (map == null) {
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "参数有误");
            }
            newPayMethodOrderDTO.setId(payMethodOrderDTO.getId());
            newPayMethodOrderDTO.setMethods(Lists.newArrayList());
            if (payMethodOrderDTO.getMethods().size() != map.size()) {
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "参数有误");
            }
            for (PayMethodOrderItemDTO methodsDTO : payMethodOrderDTO.getMethods()) {
                PayMethodOrderItemDTO originalMethodsDTO = map.get(String.format("%s-%s", methodsDTO.getPayway(), methodsDTO.getId()));
                if (originalMethodsDTO == null) {
                    // tag 未国际化
                    throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "参数有误");
                }
                PayMethodOrderItemDTO newMethodsDTO = new PayMethodOrderItemDTO();
                newMethodsDTO.setId(originalMethodsDTO.getId());
                newMethodsDTO.setStatus(originalMethodsDTO.getStatus());
                newMethodsDTO.setPayway(originalMethodsDTO.getPayway());
                newPayMethodOrderDTO.getMethods().add(newMethodsDTO);
                if (newMethodsDTO.getStatus() == 1) {
                    status = 1;
                }
            }
            newPayMethodOrderDTO.setStatus(status);
            save.add(newPayMethodOrderDTO);
        }
        if (CollectionUtils.isNotEmpty(paymentMethodList)) {
            PaymentMethod paymentMethod = paymentMethodList.get(0);
            paymentMethod.setPaymentMethod(JsonUtil.beanToJson(save));
            paymentMethodService.updateRecord(paymentMethod);
        } else {
            PaymentMethod savePaymentMethod = new PaymentMethod();
            savePaymentMethod.setPaymentMethod(JsonUtil.beanToJson(save));
            savePaymentMethod.setTenantId(request.getTenantId());
            paymentMethodService.saveRecord(savePaymentMethod);
        }
        return true;
    }

    private Map<String, String> mapTips(List<I18nItemDTO> items) {
        return items.stream().collect(Collectors.toMap(I18nItemDTO::getLang, I18nItemDTO::getValue));
    }

    public List<PayToolGroupDTO> get(Long tenantId) {
        PaymentMethod query = new PaymentMethod();
        query.setTenantId(tenantId);
        List<PaymentMethod> list = paymentMethodService.listRecord(query);
        List<PayMethodOrderDTO> payToolOrderDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            payToolOrderDTOS = JsonUtil.jsonToArray(list.get(0).getPaymentMethod(), PayMethodOrderDTO.class);
        } else {
            ConfigNormalDTO configNormalDTO = getConfigNormalDTO();
            payToolOrderDTOS = JSON.parseArray(configNormalDTO.getValue(), PayMethodOrderDTO.class);
        }
        return payToolOrderDTOS.stream().map(payMethodOrderDTO -> {
            PayToolGroupDTO payToolGroupDTO = new PayToolGroupDTO();
            payToolGroupDTO.setUid(String.valueOf(payMethodOrderDTO.getId()));
            payToolGroupDTO.setId(payMethodOrderDTO.getId());
            payToolGroupDTO.setName(PaywayGroupEnum.explain(payMethodOrderDTO.getId()).name());
            payToolGroupDTO.setMethods(payMethodOrderDTO.getMethods().stream().map(payMethodOrderItemDTO -> {
                PayMethodDTO payMethodDTO = new PayMethodDTO();
                payMethodDTO.setId(payMethodOrderItemDTO.getId());
                payMethodDTO.setPayway(payMethodOrderItemDTO.getPayway());
                payMethodDTO.setUid(String.format("%s-%s", payMethodDTO.getId(), payMethodDTO.getPayway()));
                payMethodDTO.setStatus(payMethodOrderItemDTO.getStatus());

                I18nDTO i18nDTO = i18nBizManager.getI18nDTO(String.format("paywayConfig|methodTips-%s-%s-%s", tenantId, payMethodDTO.getId(), payMethodDTO.getPayway()));
                if (i18nDTO != null) {
                    Map<String, String> topsMap = mapTips(i18nDTO.getItems());
                    String lang = ThreadLanguageHolder.getCurrentLang();
                    String value = topsMap.get(lang);
                    if (StringUtils.isEmpty(value)) {
                        value = topsMap.get("en-GB");
                    }
                    payMethodDTO.setTips(value);
                }

                PayMethodEnum methodEnum = PayMethodEnum.explain(payMethodOrderItemDTO.getId());
                if (Objects.nonNull(methodEnum)) {
                    payMethodDTO.setName(methodEnum.name());
                }
                PaywayEnum paywayEnum = PaywayEnum.explain(payMethodOrderItemDTO.getPayway());
                if (Objects.nonNull(paywayEnum)) {
                    payMethodDTO.setPaywayName(PaywayEnum.explain(payMethodOrderItemDTO.getPayway()).getName());
                }
                payMethodDTO.setLogo(getMethodLogo(payMethodOrderItemDTO.getId()));
                return payMethodDTO;
            }).collect(Collectors.toList()));
            return payToolGroupDTO;
        }).collect(Collectors.toList());
    }

    private PayMethodLogoDTO getMethodLogo(Integer id) {
        if (methodLogoMap == null) {
            methodLogoMap = JSON.parseObject(configClient.getConfigInString(ConfigKeyEnum.PAY_METHOD_LOGO).getData().getValue(), new TypeReference<Map<PayMethodEnum, PayMethodLogoDTO>>() {
            });
            if (org.springframework.util.CollectionUtils.isEmpty(methodLogoMap)) {
                log.error("获取支付方式logo列表失败");
                // tag 未国际化
                throw BizException.create(SystemErrorEnum.REMOTE_FALLBACK);
            }
        }
        return methodLogoMap.get(PayMethodEnum.explain(id));
    }

    private List<Integer> methodIdList(Long tenantId) {
        PaymentMethod query = new PaymentMethod();
        query.setTenantId(tenantId);
        List<PaymentMethod> paymentMethodList = paymentMethodService.listRecord(query);
        List<PayMethodOrderDTO> payToolOrderDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(paymentMethodList)) {
            payToolOrderDTOS = JsonUtil.jsonToArray(paymentMethodList.get(0).getPaymentMethod(), PayMethodOrderDTO.class);
        } else {
            ConfigNormalDTO configNormalDTO = getConfigNormalDTO();
            payToolOrderDTOS = JSON.parseArray(configNormalDTO.getValue(), PayMethodOrderDTO.class);
        }
        return payToolOrderDTOS.stream().map(payMethodOrderDTO -> payMethodOrderDTO.getMethods().stream().map(payMethodOrderItemDTO -> payMethodOrderItemDTO.getId()).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<I18nItemDTO> getTips(Long tenantId, Integer payway, Integer methodId) {
        List<Integer> methodIdList = methodIdList(tenantId);
        if (!methodIdList.contains(methodId)) {
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "支付方式不存在");
        }
        I18nDTO i18nDTO = i18nBizManager.getI18nDTO(String.format("paywayConfig|methodTips-%s-%s-%s", tenantId, methodId, payway));
        if (i18nDTO == null) {
            return Collections.emptyList();
        }
        return i18nDTO.getItems();
    }

    public void setTips(Long tenantId, Integer payway, Integer methodId, List<I18nItemDTO> items) {
        List<Integer> methodIdList = methodIdList(tenantId);
        if (!methodIdList.contains(methodId)) {
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.PARAMS_ERROR, "支付方式不存在");
        }
        String key = String.format("paywayConfig|methodTips-%s-%s-%s", tenantId, methodId, payway);
        I18nDTO i18nDTO = i18nBizManager.getI18nDTO(key);
        if (i18nDTO == null) {
            i18nDTO = new I18nDTO();
            i18nDTO.setKey(key);
        }
        items.forEach(i18nItemDTO -> {
            String lang = i18nItemDTO.getLang();
            if (i18nItemDTO.getValue() == null) {
                i18nItemDTO.setValue("");
            }
            if (i18nLanguageManager.validateLang(lang)) {
                // 标准语言标识
            } else {
                LangDTO langDTO = i18nLanguageManager.getPatternLangDTO(i18nItemDTO.getLang());
                i18nItemDTO.setLang(langDTO.getLang());
            }
        });
        i18nDTO.setItems(items);
        i18nBizManager.setI18nDTO(i18nDTO);
    }
}
