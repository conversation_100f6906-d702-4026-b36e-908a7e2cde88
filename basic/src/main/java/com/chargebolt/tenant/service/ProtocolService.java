/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.tenant.dao.model.Protocol;
import com.chargebolt.ezreal.response.protocol.ProtocolDetailResponse;
import com.chargebolt.ezreal.response.protocol.ProtocolSimpleResponse;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: ProtocolService.java, v 0.1 2024-08-30 15:10:06 Exp $
 */

public interface ProtocolService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<Protocol>     返回结果
     */
    List<Protocol> listRecord(Protocol model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return Protocol     返回结果
     */
    Protocol getRecord(Protocol model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(Protocol model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(Protocol model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(Protocol model);

    List<ProtocolSimpleResponse> protocolSimpleList(Integer pageNo, Integer pageSize, Protocol model);

    ProtocolDetailResponse getProtocolDetail(Long id);

    /**
     * 获取租户下的协议列表，如果租户没配置协议，获取顶层租户的默认协议
     *
     * @param model
     * @return
     */
    List<ProtocolSimpleResponse> protocolTenantSimpleList(Protocol model);
}