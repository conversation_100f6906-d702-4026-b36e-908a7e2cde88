/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.commons.constant.BizConstant;
import com.chargebolt.tenant.convert.ProtocolConverter;
import com.chargebolt.tenant.dao.ProtocolDAO;
import com.chargebolt.tenant.dao.model.Protocol;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

import com.chargebolt.ezreal.response.protocol.ProtocolDetailResponse;
import com.chargebolt.ezreal.response.protocol.ProtocolSimpleResponse;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: ProtocolServiceImpl.java, v 0.1 2024-08-30 15:10:06 Exp $
 */
@Service
public class ProtocolServiceImpl implements ProtocolService {
    @Override
    public List<Protocol> listRecord(Protocol model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return protocolDAO.listRecord(model);
    }

    @Override
    public Protocol getRecord(Protocol model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return protocolDAO.getRecord(model);
    }

    @Override
    public int saveRecord(Protocol model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return protocolDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(Protocol model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return protocolDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(Protocol model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return protocolDAO.updateRecord(model);
    }

    @Override
    public List<ProtocolSimpleResponse> protocolSimpleList(final Integer pageNo, final Integer pageSize, final Protocol model) {
        Page<Protocol> protocolPage = PageHelper.startPage(pageNo, pageSize, Boolean.FALSE)
                .setOrderBy(" id DESC").doSelectPage(new ISelect(){
                    @Override
                    public void doSelect() {
                        listRecord(model);
                    }
                });
        if (protocolPage != null && protocolPage.getResult() != null && !protocolPage.getResult().isEmpty()) {
            return protocolPage.getResult().stream().map(ProtocolConverter.INSTANCE::protocolSimpleResponseConvert)
                    .collect(java.util.stream.Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public ProtocolDetailResponse getProtocolDetail(final Long id) {
        Protocol protocol = new Protocol();
        protocol.setId(id);
        protocol= getRecord(protocol);
        if(Objects.isNull(protocol)){
            return null;
        }
        return ProtocolConverter.INSTANCE.protocolDetailResponseConvert(protocol);
    }

    @Override
    public List<ProtocolSimpleResponse> protocolTenantSimpleList(final Protocol model) {
        List<ProtocolSimpleResponse> responses= new ArrayList<>();
        for (String code : model.getCodes()){
            Protocol query= new Protocol();
            query.setCode(code);
            query.setTenantId(model.getTenantId());
            query.setLang(model.getLang());
            List<ProtocolSimpleResponse> list = protocolSimpleList(1, 20, query);
            if(CollectionUtils.isEmpty(list)){
                query.setLang(null);
                list= protocolSimpleList(1, 20, query);
                if(CollectionUtils.isEmpty(list)){
                    query.setTenantId(BizConstant.DEFAULT_TENANT_ID);
                    query.setLang(Locale.UK.toLanguageTag());
                    list= protocolSimpleList(1, 20, query);
                }
            }
            if(CollectionUtils.isNotEmpty(list)){
                responses.add(list.get(0));
            }
        }
        return responses;
    }


    /**
     * 推荐使用构造器注入
     */
    private final ProtocolDAO protocolDAO;
    public ProtocolServiceImpl(ObjectProvider<ProtocolDAO> protocolProvider) {
        this.protocolDAO= protocolProvider.getIfUnique();
    }
}