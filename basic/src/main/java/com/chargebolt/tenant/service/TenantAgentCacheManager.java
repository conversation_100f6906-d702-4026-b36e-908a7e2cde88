/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.tenant.dao.model.TenantAgentRelated;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TenantAgentCacheManager.java, v 1.0 2024-11-01 下午2:50 Exp $
 */
@Component
public class TenantAgentCacheManager {
    private static final String REMOTE_CACHE_TENANT_TOP_LEVEL_KEY="__CACHE_TENANT_TOP_LEVEL_";


    private final RedissonClient redissonClient;
    private final TenantAgentRelatedService tenantAgentRelatedService;
    public TenantAgentCacheManager(ObjectProvider<RedissonClient> redissonClientProvider,
                                   ObjectProvider<TenantAgentRelatedService> tenantAgentRelatedServiceProvider){
        this.redissonClient = redissonClientProvider.getIfUnique();
        this.tenantAgentRelatedService= tenantAgentRelatedServiceProvider.getIfUnique();
    }

    public TenantAgentInfoResponse getTenantAgentInfoCache(final Long agentId) {
        RBucket<TenantAgentInfoResponse> rBucket= redissonClient.getBucket(REMOTE_CACHE_TENANT_TOP_LEVEL_KEY+agentId);
        if(Objects.nonNull(rBucket)&& Objects.nonNull(rBucket.get())){
            return rBucket.get();
        }
        return null;
    }

    public TenantAgentInfoResponse setTenantAgentInfoCache(final Long agentId, TenantAgentInfoResponse tenantAgentInfoResponse) {
        RBucket<TenantAgentInfoResponse> rBucket= redissonClient.getBucket(REMOTE_CACHE_TENANT_TOP_LEVEL_KEY+agentId);
        rBucket.set(tenantAgentInfoResponse, 10, TimeUnit.MINUTES);
        return tenantAgentInfoResponse;
    }
    public void refreshTenantCache(final Long tenantId) {
        TenantAgentRelated query= new TenantAgentRelated();
        query.setTenantId(tenantId);
        query= tenantAgentRelatedService.getRecord(query);
        if(Objects.nonNull(query)){
            redissonClient.getBucket(REMOTE_CACHE_TENANT_TOP_LEVEL_KEY+query.getAgentId()).delete();
        }
    }

    public void refreshAgentCache(final Long agentId) {
        redissonClient.getBucket(REMOTE_CACHE_TENANT_TOP_LEVEL_KEY+ agentId).delete();
    }

    public void refreshCacheBatch(final List<Long> agentIds) {
        for(Long id:agentIds){
            refreshAgentCache(id);
        }
    }
}