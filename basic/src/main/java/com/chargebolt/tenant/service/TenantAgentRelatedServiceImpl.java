/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.commons.constant.BizConstant;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.service.agent.AgentBaseConfigService;
import com.chargebolt.tenant.dao.TenantAgentRelatedDAO;
import com.chargebolt.tenant.dao.model.TenantAgentRelated;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;

import java.util.List;
import java.util.Objects;



/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: TenantAgentRelatedServiceImpl.java, v 0.1 2024-08-30 15:10:21 Exp $
 */
@Service
public class TenantAgentRelatedServiceImpl implements TenantAgentRelatedService {
    @Override
    public List<TenantAgentRelated> listRecord(TenantAgentRelated model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return tenantAgentRelatedDAO.listRecord(model);
    }

    @Override
    public TenantAgentRelated getRecord(TenantAgentRelated model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return tenantAgentRelatedDAO.getRecord(model);
    }

    @Override
    public int saveRecord(TenantAgentRelated model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return tenantAgentRelatedDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(TenantAgentRelated model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return tenantAgentRelatedDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(TenantAgentRelated model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return tenantAgentRelatedDAO.updateRecord(model);
    }

    @Override
    public Long getTenantId(final Long agentId) {
        if(Objects.isNull(agentId)){
            return BizConstant.DEFAULT_TENANT_ID;
        }
        TenantAgentRelated query= new TenantAgentRelated();
        query.setAgentId(agentId);
        query= getRecord(query);
        return Objects.isNull(query)? null : query.getTenantId();
    }



    /**
     * 推荐使用构造器注入
     */
    private final TenantAgentRelatedDAO tenantAgentRelatedDAO;
    public TenantAgentRelatedServiceImpl(ObjectProvider<TenantAgentRelatedDAO> tenantAgentRelatedProvider) {
        this.tenantAgentRelatedDAO= tenantAgentRelatedProvider.getIfUnique();
    }
}