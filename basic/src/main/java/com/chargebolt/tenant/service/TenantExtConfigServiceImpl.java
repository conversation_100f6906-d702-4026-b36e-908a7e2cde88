/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.tenant.dao.TenantExtConfigDAO;
import com.chargebolt.tenant.dao.model.TenantExtConfig;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: TenantExtConfigServiceImpl.java, v 0.1 2024-08-30 15:10:29 Exp $
 */
@Service
public class TenantExtConfigServiceImpl implements TenantExtConfigService {
    @Override
    public List<TenantExtConfig> listRecord(TenantExtConfig model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return tenantExtConfigDAO.listRecord(model);
    }

    @Override
    public TenantExtConfig getRecord(TenantExtConfig model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return tenantExtConfigDAO.getRecord(model);
    }

    @Override
    public int saveRecord(TenantExtConfig model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return tenantExtConfigDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(TenantExtConfig model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        tenantAgentCacheManager.refreshTenantCache(model.getTenantId());
        return tenantExtConfigDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(TenantExtConfig model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return tenantExtConfigDAO.updateRecord(model);
    }

    @Override
    public int saveOrUpdate(final TenantExtConfig model) {
        TenantExtConfig query= new TenantExtConfig();
        query.setTenantId(model.getTenantId());
        query.setConfigKey(model.getConfigKey());
        query= getRecord(query);
        if(Objects.isNull(query)){
            saveRecord(model);
        }else{
            query.setConfigKey(model.getConfigKey());
            query.setConfigValue(model.getConfigValue());
            updateRecord(query);
        }
        tenantAgentCacheManager.refreshTenantCache(model.getTenantId());
        return 1;
    }

    @Override
    public int saveTenantExtConfig(final Long tenantId, final String configKey, final String configValue) {
        TenantExtConfig tenantExtConfig= new TenantExtConfig();
        tenantExtConfig.setTenantId(tenantId);
        tenantExtConfig.setConfigKey(configKey);
        tenantExtConfig.setConfigValue(configValue);
        return saveOrUpdate(tenantExtConfig);
    }

    @Override
    public TenantExtConfig getTenantExtConfig(final Long tenantId, final String configKey) {
        TenantExtConfig query= new TenantExtConfig();
        query.setTenantId(tenantId);
        query.setConfigKey(configKey);
        return getRecord(query);
    }


    /**
     * 推荐使用构造器注入
     */
    private final TenantExtConfigDAO tenantExtConfigDAO;
    private final TenantAgentCacheManager tenantAgentCacheManager;
    public TenantExtConfigServiceImpl(ObjectProvider<TenantExtConfigDAO> tenantExtConfigProvider,
                                      ObjectProvider<TenantAgentCacheManager> tenantAgentCacheManagerProvider
                                      ) {
        this.tenantExtConfigDAO= tenantExtConfigProvider.getIfUnique();
        this.tenantAgentCacheManager= tenantAgentCacheManagerProvider.getIfUnique();
    }
}