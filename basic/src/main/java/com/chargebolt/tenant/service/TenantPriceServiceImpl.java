/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.ezreal.constant.TenantConfigExtConstant;
import com.chargebolt.commons.exception.AgentExceptionEnum;
import com.chargebolt.ezreal.response.tenant.PriceResponse;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.tenant.dao.model.TenantAgentRelated;
import com.chargebolt.tenant.dao.model.TenantExtConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import so.dian.ares.dto.shop.QueryBoxCustomPriceDTO;
import so.dian.eros.biz.service.AresService;
import so.dian.eros.common.util.LocationUtil;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.eros.manager.hermes.convert.OrderDtoVoConverter;
import so.dian.mofa3.lang.money.CurrencyEnum;
import so.dian.mofa3.lang.money.MoneyFormatter;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;

import javax.annotation.Resource;
import java.util.Locale;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TenantPriceServiceImpl.java, v 1.0 2024-10-16 下午8:54 Exp $
 */
@Slf4j
@Service
public class TenantPriceServiceImpl implements TenantPriceService {
    private final AresService aresService;
    private final TenantExtConfigService tenantExtConfigService;
    private final TenantAgentRelatedService tenantAgentRelatedService;
    private final TenantService tenantService;
    private final ApplicationContext ctx;
    public TenantPriceServiceImpl(ObjectProvider<AresService> aresServiceProvider,
                                  ObjectProvider<TenantExtConfigService> tenantExtConfigProvider,
                                  ObjectProvider<TenantAgentRelatedService> tenantAgentRelatedProvider,
                                  ObjectProvider<TenantService> tenantServiceProvider,
                                  ObjectProvider<ApplicationContext> applicationContextProvider){
        this.aresService = aresServiceProvider.getIfUnique();
        this.tenantExtConfigService = tenantExtConfigProvider.getIfUnique();
        this.tenantAgentRelatedService= tenantAgentRelatedProvider.getIfUnique();
        this.tenantService = tenantServiceProvider.getIfUnique();
        this.ctx = applicationContextProvider.getIfUnique();
    }
    @Override
    public PriceResponse getPrice(final Long tenantId) {
        TenantExtConfig defaultShop = tenantExtConfigService.getTenantExtConfig(tenantId, TenantConfigExtConstant.TENANT_DEFAULT_SHOP_ID);
        if(Objects.isNull(defaultShop)){
            log.error("租户：{} 兜底门店未配置", tenantId);
            throw new I18nMessageException(AgentExceptionEnum.TENANT_DEFAULT_SHOP_IS_NULL.getCode(), AgentExceptionEnum.TENANT_DEFAULT_SHOP_IS_NULL.getDesc());
        }
        TenantAgentRelated tenantAgentRelated =new TenantAgentRelated();
        tenantAgentRelated.setTenantId(tenantId);
        tenantAgentRelated = tenantAgentRelatedService.getRecord(tenantAgentRelated);
        if(Objects.isNull(tenantAgentRelated)){
            throw new I18nMessageException(AgentExceptionEnum.TENANT_AGENT_RELATED_IS_NULL.getCode(), AgentExceptionEnum.TENANT_AGENT_RELATED_IS_NULL.getDesc());
        }
        TenantAgentInfoResponse tenantAgentInfoResponse= tenantService.getTopTenantInfo(tenantAgentRelated.getAgentId());
        QueryBoxCustomPriceDTO price = aresService.getSkuByShopId(Long.valueOf(defaultShop.getConfigValue()));
        return buildPriceResponse(price, tenantAgentInfoResponse);
    }

    private PriceResponse buildPriceResponse(final QueryBoxCustomPriceDTO price, final TenantAgentInfoResponse tenantAgentInfoResponse) {
        PriceResponse response= new PriceResponse();
        response.setPeriodMinute(Objects.isNull(price)||Objects.isNull(price.getPeriod())?0:price.getPeriod()/60);
        response.setFreeTime(Objects.isNull(price)||Objects.isNull(price.getFreeTime())?0:price.getFreeTime()/60);

        String lang= ThreadLanguageHolder.getCurrentLang();
        Locale locale= LocationUtil.getLocale(lang);
        MultiCurrencyMoney unitPriceMoney = new MultiCurrencyMoney(0, tenantAgentInfoResponse.getCurrencyCode());
        unitPriceMoney.setCent(Objects.isNull(price)||Objects.isNull(price.getCurrentPrice())?0:price.getCurrentPrice());
        response.setUnitPrice(unitPriceMoney.getAmount().toString());
        response.setUnitPriceDesc(MoneyFormatter.format(unitPriceMoney)+OrderDtoVoConverter.buildPriceCycle(price.getPeriod(), ctx, locale));

        response.setFreeTimeDesc(OrderDtoVoConverter.buildFreeTimeStr(price.getFreeTime(), ctx, locale));
        CurrencyEnum currencyEnum = CurrencyEnum.getByCurrencyCode(tenantAgentInfoResponse.getCurrencyCode());
        response.setCurrencyCode(currencyEnum.getCurrencyCode());
        response.setCurrencyLabel(currencyEnum.getCurrencyLabel());

        MultiCurrencyMoney maxCostPerDayMoney= new MultiCurrencyMoney(0, tenantAgentInfoResponse.getCurrencyCode());
        maxCostPerDayMoney.setCent(Objects.isNull(price)||Objects.isNull(price.getMaxCostPerDay())?0:price.getMaxCostPerDay());
        response.setMaxCostPerDay(maxCostPerDayMoney.getAmount().toString());
        response.setMaxCostPerDayFormatted(MoneyFormatter.format(maxCostPerDayMoney));

        MultiCurrencyMoney maxCostMoney= new MultiCurrencyMoney(0, tenantAgentInfoResponse.getCurrencyCode());
        maxCostMoney.setCent(Objects.isNull(price)||Objects.isNull(price.getMaxCost())?0:price.getMaxCost());
        response.setMaxCost(maxCostMoney.getAmount().toString());
        response.setMaxCostFormatted(MoneyFormatter.format(maxCostMoney));
        if(Objects.nonNull(tenantAgentInfoResponse.getDepositAmount())){
            MultiCurrencyMoney depositAmountMoney= new MultiCurrencyMoney(0, tenantAgentInfoResponse.getCurrencyCode());
            depositAmountMoney.setCent(tenantAgentInfoResponse.getDepositAmount());
            response.setDepositAmount(depositAmountMoney.getAmount().toString());
            response.setDepositAmountFormatted(MoneyFormatter.format(depositAmountMoney));
        }
        response.setLabelLocation(unitPriceMoney.getCurrencyLabelLocation());
        response.setFractionDigits(unitPriceMoney.getFractionDigits());
        return response;
    }
}