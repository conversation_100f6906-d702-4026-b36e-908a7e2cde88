/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.tenant.dao.model.Tenant;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: TenantService.java, v 0.1 2024-08-30 15:10:13 Exp $
 */

public interface TenantService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<Tenant>     返回结果
     */
    List<Tenant> listRecord(Tenant model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return Tenant     返回结果
     */
    Tenant getRecord(Tenant model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   数据ID
     */
    Long saveRecord(Tenant model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(Tenant model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(Tenant model);

    /**
     * 通过代理商，获取顶层的租户信息
     *
     * @param agentId
     * @return
     */
    TenantAgentInfoResponse getTopTenantInfo(Long agentId);

    TenantAgentInfoResponse getTopTenantInfoByShopId(Long shopId);

}