/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.tenant.service;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.ezreal.constant.TenantConfigExtConstant;
import com.chargebolt.commons.exception.AgentExceptionEnum;
import com.chargebolt.dao.agent.model.Agent;
import com.chargebolt.dao.agent.model.AgentBaseConfig;
import com.chargebolt.service.agent.AgentBaseConfigService;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.tenant.convert.TenantConverter;
import com.chargebolt.tenant.dao.TenantDAO;
import com.chargebolt.tenant.dao.model.Tenant;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.chargebolt.tenant.dao.model.TenantAgentRelated;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.tenant.dao.model.TenantExtConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.eros.common.exception.ShopErrorEnum;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateUtil;
import so.dian.talos.biz.facade.ShopFacade;
import so.dian.talos.client.dto.ShopDTO;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: TenantServiceImpl.java, v 0.1 2024-08-30 15:10:13 Exp $
 */
@Slf4j
@Service
public class TenantServiceImpl implements TenantService {

    @Override
    public List<Tenant> listRecord(Tenant model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return tenantDAO.listRecord(model);
    }

    @Override
    public Tenant getRecord(Tenant model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return tenantDAO.getRecord(model);
    }

    @Override
    public Long saveRecord(Tenant model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return tenantDAO.saveRecord(model);
    }

    @Override
    public int removeRecord(Tenant model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        tenantAgentCacheManager.refreshTenantCache(model.getId());
        return tenantDAO.removeRecord(model);
    }

    @Override
    public int updateRecord(Tenant model) {
        model.setGmtUpdate(DateUtil.timeStampMilli());
        tenantAgentCacheManager.refreshTenantCache(model.getId());
        return tenantDAO.updateRecord(model);
    }

    @Override
    public TenantAgentInfoResponse getTopTenantInfo(final Long agentId) {
        Agent agent = agentService.getTopLevelAgent(agentId);
        TenantAgentInfoResponse rBucket= tenantAgentCacheManager.getTenantAgentInfoCache(agentId);
        if(Objects.nonNull(rBucket)){
            return rBucket;
        }
        if(Objects.nonNull(agent.getId())){
            // 关联关系
            TenantAgentRelated tenantAgentRelated= new TenantAgentRelated();
            tenantAgentRelated.setAgentId(agent.getId());
            tenantAgentRelated= tenantAgentRelatedService.getRecord(tenantAgentRelated);
            // 租户信息
            Tenant tenant = new Tenant();
            tenant.setId(tenantAgentRelated.getTenantId());
            tenant= getRecord(tenant);
            // 代理商基础信息
            AgentBaseConfig config = new AgentBaseConfig();
            config.setAgentId(agent.getId());
            config= agentBaseConfigService.getRecord(config);

            // 押金
            TenantExtConfig depositAmount = tenantExtConfigService.getTenantExtConfig(tenant.getId(), TenantConfigExtConstant.TENANT_CHARGE_DEPOSIT_AMOUNT);
            if(Objects.isNull(depositAmount)){
                log.error("租户：{}押金未配置", tenant.getId());
                throw new I18nMessageException(AgentExceptionEnum.TENANT_DEPOSIT_AMOUNT_IS_NULL.getCode(), AgentExceptionEnum.TENANT_DEPOSIT_AMOUNT_IS_NULL.getDesc());
            }
            // 兜底门店
            TenantExtConfig defaultShop = tenantExtConfigService.getTenantExtConfig(tenant.getId(), TenantConfigExtConstant.TENANT_DEFAULT_SHOP_ID);
            if(Objects.isNull(defaultShop)){
                log.error("租户：{} 兜底门店未配置", tenant.getId());
                throw new I18nMessageException(AgentExceptionEnum.TENANT_DEFAULT_SHOP_IS_NULL.getCode(), AgentExceptionEnum.TENANT_DEFAULT_SHOP_IS_NULL.getDesc());
            }
            TenantAgentInfoResponse response = TenantConverter.INSTANCE.tenantAgentInfoResponseConvert(tenant, agent, config,
                    Long.valueOf(depositAmount.getConfigValue()), Long.valueOf(defaultShop.getConfigValue()));
            tenantAgentCacheManager.setTenantAgentInfoCache(agent.getId(), response);
            return response;
        }
        return null;
    }



    @Override
    public TenantAgentInfoResponse getTopTenantInfoByShopId(final Long shopId) {
        ShopDTO shopDTO= shopFacade.queryById(shopId);
        if(Objects.isNull(shopDTO)){
            throw new I18nMessageException(ShopErrorEnum.SHOP_NOT_EXIST.getCode().toString(), ShopErrorEnum.SHOP_NOT_EXIST.getDesc());
        }
        return getTopTenantInfo(shopDTO.getAgentId());
    }



    /**
     * 推荐使用构造器注入
     */
    private final TenantDAO tenantDAO;
    private final RedissonClient redissonClient;
    private final AgentService agentService;
    private final AgentBaseConfigService agentBaseConfigService;
    private final TenantAgentRelatedService tenantAgentRelatedService;
    private final TenantExtConfigService tenantExtConfigService;
    private final ShopFacade shopFacade;
    private final TenantAgentCacheManager tenantAgentCacheManager;
    public TenantServiceImpl(ObjectProvider<TenantDAO> tenantProvider,
                             ObjectProvider<RedissonClient> redissonProvider,
                             ObjectProvider<AgentService> agentServiceProvider,
                             ObjectProvider<AgentBaseConfigService> agentBaseConfigProvider,
                             ObjectProvider<TenantAgentRelatedService> tenantAgentRelatedProvider,
                             ObjectProvider<TenantExtConfigService> tenantExtConfigProvider,
                             ObjectProvider<ShopFacade> shopFacadeProvider,
                             ObjectProvider<TenantAgentCacheManager> tenantAgentCacheManagerProvider
                             ) {
        this.tenantDAO= tenantProvider.getIfUnique();
        this.redissonClient= redissonProvider.getIfUnique();
        this.agentService= agentServiceProvider.getIfUnique();
        this.agentBaseConfigService= agentBaseConfigProvider.getIfUnique();
        this.tenantAgentRelatedService= tenantAgentRelatedProvider.getIfUnique();
        this.tenantExtConfigService= tenantExtConfigProvider.getIfUnique();
        this.shopFacade= shopFacadeProvider.getIfUnique();
        this.tenantAgentCacheManager= tenantAgentCacheManagerProvider.getIfUnique();
    }
}