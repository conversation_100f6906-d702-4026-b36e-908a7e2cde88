package so.dian.demeter.biz.service;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.commons.eden.util.LocalNumberUtils;
import so.dian.demeter.client.enu.AccountStorageApplyStatusEnum;
import so.dian.demeter.client.param.AccountStorageApplyLostParam;
import so.dian.demeter.client.param.AccountStorageApplyParam;
import so.dian.demeter.client.param.AccountStorageApplyBackParam;
import so.dian.demeter.common.enums.AccountStorageApplyTypeEnum;
import so.dian.demeter.common.util.LocalDateUtils;
import so.dian.demeter.dao.rds.AccountStorageApplyCollectMapper;
import so.dian.demeter.dao.rds.AccountStorageApplyDeviceMapper;
import so.dian.demeter.dao.rds.AccountStorageApplyMapper;
import so.dian.demeter.pojo.bo.AccountStorageApplyBO;
import so.dian.demeter.pojo.converter.AccountStorageApplyConverter;
import so.dian.demeter.pojo.entity.AccountStorageApplyCollectDO;
import so.dian.demeter.pojo.entity.AccountStorageApplyDO;
import so.dian.demeter.pojo.entity.AccountStorageApplyDeviceDO;
import so.dian.demeter.pojo.param.AccountStorageApplyPageQuery;

/**
 * AccountStorageApplyService
 *
 * <AUTHOR>
 */
@Service
public class AccountStorageApplyService {

    @Resource
    private AccountStorageApplyMapper accountStorageApplyMapper;
    @Resource
    private AccountStorageApplyCollectMapper accountStorageApplyCollectMapper;
    @Resource
    private AccountStorageApplyDeviceMapper accountStorageApplyDeviceMapper;

    /**
     * 是否存在未终结的备件申领申请
     * return true:表示存在，false:表示不存在
     */
    public Boolean existedNotFinishedApply(Long cooperatorId, Long accountId) {
        AccountStorageApplyDO storageApplyDO = accountStorageApplyMapper
                .getNotFinishedApplyByAccountId(cooperatorId, accountId);
        return Objects.nonNull(storageApplyDO);
    }

    public List<AccountStorageApplyBO> listByApplyTypeAndStatus(Long cooperatorId, Long accountId,
            AccountStorageApplyTypeEnum applyType, AccountStorageApplyStatusEnum status) {
        List<AccountStorageApplyDO> storageApplyDOList = accountStorageApplyMapper
                .listByApplyTypeAndStatus(cooperatorId, accountId, applyType.getCode(), status.getCode());
        return AccountStorageApplyConverter.convertDO2BO(storageApplyDOList);
    }

    public AccountStorageApplyBO addApply(AccountStorageApplyParam param, Long accountStorageId) {
        AccountStorageApplyDO applyDO = AccountStorageApplyConverter
                .buildApplyForInsert(param, param.getFromBizId(), accountStorageId);
        accountStorageApplyMapper.insert(applyDO);
        return AccountStorageApplyConverter.convertDO2BO(applyDO);
    }

    public AccountStorageApplyBO addApplyLost(AccountStorageApplyLostParam param, Long accountStorageId) {
        AccountStorageApplyDO applyDO = AccountStorageApplyConverter.buildApplyLostForInsert(param, accountStorageId);
        accountStorageApplyMapper.insert(applyDO);
        return AccountStorageApplyConverter.convertDO2BO(applyDO);
    }

    public AccountStorageApplyBO addApplyBackToWarehouse(AccountStorageApplyBackParam param, Long warehouseId,
            Long accountStorageId) {
        AccountStorageApplyDO applyDO = AccountStorageApplyConverter
                .buildApplyBackToWarehouseForInsert(param, warehouseId, accountStorageId);
        accountStorageApplyMapper.insert(applyDO);
        return AccountStorageApplyConverter.convertDO2BO(applyDO);
    }

    public Long countByApplyPageQuery(AccountStorageApplyPageQuery query) {
        if (Objects.isNull(query)) {
            return 0L;
        }
        return accountStorageApplyMapper.countByApplyPageQuery(query);
    }

    public List<AccountStorageApplyBO> pageByApplyPageQuery(AccountStorageApplyPageQuery query) {
        if (Objects.isNull(query)) {
            return Lists.newArrayList();
        }
        List<AccountStorageApplyDO> applyDOList = accountStorageApplyMapper.pageByApplyPageQuery(query);
        return AccountStorageApplyConverter.convertDO2BO(applyDOList);
    }

    public AccountStorageApplyBO getByApplyNo(Long cooperatorId, String applyNo) {
        if (StringUtils.isBlank(applyNo) || !LocalNumberUtils.isPositive(cooperatorId)) {
            return null;
        }
        AccountStorageApplyDO applyDO = accountStorageApplyMapper.getByApplyNo(applyNo, cooperatorId);
        return AccountStorageApplyConverter.convertDO2BO(applyDO);
    }

    public Boolean rejectApplyByApplyNo(Long cooperatorId, String applyNo) {
        if (StringUtils.isBlank(applyNo) || !LocalNumberUtils.isPositive(cooperatorId)) {
            return false;
        }
        int rejectResult = accountStorageApplyMapper.rejcectByApplyNo(applyNo, cooperatorId, LocalDateUtils.now());
        return rejectResult > 0;
    }

    public Boolean outcomeApplyByApplyNo(Long cooperatorId, String applyNo) {
        if (StringUtils.isBlank(applyNo) || !LocalNumberUtils.isPositive(cooperatorId)) {
            return false;
        }
        int result = accountStorageApplyMapper.outcomeApplyByApplyNo(applyNo, cooperatorId, LocalDateUtils.now());
        return result > 0;
    }

    public Boolean confirmBackToWarehouse(Long cooperatorId, String applyNo) {
        if (StringUtils.isBlank(applyNo) || !LocalNumberUtils.isPositive(cooperatorId)) {
            return false;
        }
        int result = accountStorageApplyMapper.confirmBackToWarehouse(applyNo, cooperatorId, LocalDateUtils.now());
        return result > 0;
    }

    public List<String> listNotFinishBackDeviceNo(Long cooperatorId, Long accountId) {
        List<AccountStorageApplyDO> storageApplyDOList = accountStorageApplyMapper
                .listByApplyTypeAndStatus(cooperatorId, accountId, AccountStorageApplyTypeEnum.BACK.getCode(),
                        AccountStorageApplyStatusEnum.APPLIED.getCode());
        if (CollectionUtils.isEmpty(storageApplyDOList)) {
            return Lists.newArrayList();
        }
        List<String> applyNoList = LocalListUtils.transferList(storageApplyDOList, AccountStorageApplyDO::getApplyNo);
        List<AccountStorageApplyCollectDO> applyCollectDOList = accountStorageApplyCollectMapper
                .listByApplyNoList(applyNoList);
        if (CollectionUtils.isEmpty(applyCollectDOList)) {
            return Lists.newArrayList();
        }
        List<Long> applyCollectIdList = LocalListUtils
                .transferList(applyCollectDOList, AccountStorageApplyCollectDO::getId);
        List<AccountStorageApplyDeviceDO> applyDeviceDOList = accountStorageApplyDeviceMapper
                .listByApplyCollectIdList(applyCollectIdList);
        if (CollectionUtils.isEmpty(applyDeviceDOList)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(applyDeviceDOList, AccountStorageApplyDeviceDO::getDeviceNo);
    }
}
