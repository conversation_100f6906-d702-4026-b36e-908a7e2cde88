package so.dian.demeter.biz.service;

import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import so.dian.demeter.common.util.cache.LockEnum;
import so.dian.demeter.common.util.cache.RedisLockUtils;
import so.dian.demeter.dao.rds.AccountStorageInfoMapper;
import so.dian.demeter.pojo.bo.AccountStorageInfoBO;
import so.dian.demeter.pojo.converter.AccountStorageInfoConverter;
import so.dian.demeter.pojo.entity.AccountStorageInfoDO;

/**
 * AccountStorageInfoService
 *
 * <AUTHOR>
 */
@Service
public class AccountStorageInfoService {

    @Value("${biz.account.totalAmount}")
    private Integer initAccountTotalAmount;
    @Resource
    private AccountStorageInfoMapper accountStorageInfoMapper;
    @Resource
    private RedisLockUtils redisLockUtils;

    public AccountStorageInfoBO getByCooperatorIdAndAccountId(Long cooperatorId, Long accountId,
            Boolean needInitIfNotExisted) {
        AccountStorageInfoDO storageInfoDO = accountStorageInfoMapper
                .getByCooperatorIdAndAccountId(cooperatorId, accountId);
        if (Objects.nonNull(storageInfoDO)) {
            return AccountStorageInfoConverter.convertDO2BO(storageInfoDO);
        }
        if(!needInitIfNotExisted) {
            return null;
        }
        return initAccountStorageInfo(cooperatorId, accountId);

    }

    public AccountStorageInfoBO initAccountStorageInfo(Long cooperatorId, Long accountId) {
        //redis 锁
        String key = cooperatorId + "_" + accountId;
        boolean lockResult = redisLockUtils.lock(LockEnum.ACCOUNT_STORAGE_INIT_LOCK, key);
        AccountStorageInfoDO storageInfoDO = accountStorageInfoMapper
                .getByCooperatorIdAndAccountId(cooperatorId, accountId);
        if (!lockResult) {
            return AccountStorageInfoConverter.convertDO2BO(storageInfoDO);
        }
        try {
            if (Objects.nonNull(storageInfoDO)) {
                return AccountStorageInfoConverter.convertDO2BO(storageInfoDO);
            }
            storageInfoDO = AccountStorageInfoConverter.buildForInsert(cooperatorId, accountId, initAccountTotalAmount);
            accountStorageInfoMapper.insert(storageInfoDO);
            return AccountStorageInfoConverter.convertDO2BO(storageInfoDO);
        } finally {
            redisLockUtils.unlock(LockEnum.ACCOUNT_STORAGE_INIT_LOCK, key);
        }
    }

}
