/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package so.dian.demeter.biz.service;

import com.chargebolt.commons.constant.CacheKeyConstant;
import com.chargebolt.commons.enums.MqttStatusEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.device.dto.DeviceOnlineStateDTO;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.apollo.dto.result.box.ApolloBoxDTO;
import so.dian.demeter.common.enums.DeviceStorageTypeEnum;
import so.dian.demeter.dao.rds.DeviceMapper;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.demeter.pojo.bo.DeviceQueryBO;
import so.dian.demeter.pojo.entity.DeviceDO;
import so.dian.demeter.response.DeviceStatisticResponse;
import so.dian.demeter.response.DeviceUsageResponse;
import so.dian.eros.common.util.DeviceUtil;
import so.dian.eros.manager.apollo.ApolloBoxManager;
import so.dian.mofa3.lang.util.DateUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DeviceStatisticServiceImpl.java, v 1.0 2023-11-28 3:56 PM Exp $
 */
@Service
@Slf4j
public class DeviceStatisticServiceImpl implements DeviceStatisticService {
    private static final String DEVICE_STATE_KEY= "__CACHE_DEVICE_STATE_";
    private static final String DEVICE_STATE_UPDATE_TIME_KEY= "__CACHE_DEVICE_STATE_UPDATE_TIME";
    /**
     * 数据同步锁
     */
    private static final String DEVICE_STATE_SYNC_LOCK= "__CACHE_DEVICE_STATE_SYNC_LOCK";
    /**
     * 设备状态缓存数据
     */
    private static final String DEVICE_STATE_DATA_KEY = "__CACHE_DEVICE_STATE_DATA_KEY_";
    /**
     * 设备统计汇总数据缓存key
     */
    private static final String DEVICE_STATISTIC_DATA_KEY = "__CACHE_INSTALLED_DEVICE_STATISTIC_DATA_KEY_";
    /**
     * 设备在线、离线缓存
     */
    private static final String DEVICE_USAGE_STATISTIC_DATA_KEY = "__CACHE_USAGE_DEVICE_STATISTIC_DATA_KEY_";

    private final DeviceInfoService deviceInfoService;
    private final DeviceMapper deviceMapper;
    private final ApolloBoxManager apolloBoxManager;
    private final RedissonClient redissonClient;
    public DeviceStatisticServiceImpl(ObjectProvider<DeviceInfoService> deviceInfoServiceProvider,
                                      ObjectProvider<DeviceMapper> deviceMapperProvider,
                                      ObjectProvider<ApolloBoxManager> apolloBoxManagerProvider,
                                      ObjectProvider<RedissonClient> redissonClientProvider){
        this.deviceInfoService= deviceInfoServiceProvider.getIfUnique();
        this.deviceMapper= deviceMapperProvider.getIfUnique();
        this.apolloBoxManager= apolloBoxManagerProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
    }
    @Override
    public DeviceStatisticResponse getDeviceStatistic(final UserDataAuthorityContext userDataAuthorityContext) {
        DeviceStatisticResponse response= new DeviceStatisticResponse();
        String cacheKey= DEVICE_STATISTIC_DATA_KEY+ userDataAuthorityContext.getUserId();
        RBucket<DeviceStatisticResponse> bucket= redissonClient.getBucket(cacheKey);
        if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
            return bucket.get();
        }
        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
        // 设备
        DeviceQueryBO deviceQueryBO= new DeviceQueryBO();
        deviceQueryBO.setStorageTypes(Arrays.asList(DeviceStorageTypeEnum.SHOP.getCode(), DeviceStorageTypeEnum.USER.getCode()));
        deviceQueryBO.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));

        Long deviceCount= deviceMapper.countByPageParam(deviceQueryBO);

        // 充电宝
        DeviceQueryBO powerBankQueryBO= new DeviceQueryBO();
        powerBankQueryBO.setStorageTypes(Arrays.asList(DeviceStorageTypeEnum.SHOP.getCode(), DeviceStorageTypeEnum.USER.getCode()));
        powerBankQueryBO.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));

        Long powerBankCount= deviceMapper.countByPageParam(powerBankQueryBO);
        response.setDeviceCount(deviceCount.intValue());
        response.setPowerBankCount(powerBankCount.intValue());
        response.setLastUpdateTime(System.currentTimeMillis());
        bucket.set(response, CacheKeyConstant.DATA_PANEL_CACHE_TIME, TimeUnit.SECONDS);
        return response;
    }

    @Override
    public void syncDeviceState() {
        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
        // 设备
        DeviceQueryBO deviceQueryBO= new DeviceQueryBO();
        deviceQueryBO.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
        deviceQueryBO.setStorageType(DeviceStorageTypeEnum.SHOP.getCode());
        Long shopDeviceCount = deviceMapper.countByPageParam(deviceQueryBO);
        boolean flag= Boolean.TRUE;
        int pageSize= 50;
        int pageNum= 0;
        final AtomicInteger onLineCount = new AtomicInteger();
        final AtomicInteger offLineCount = new AtomicInteger();
        RLock lock = redissonClient.getLock(DEVICE_STATE_SYNC_LOCK);
        try {
            if (lock.tryLock()) {
                while(flag){
                    log.info("设备同步页码：{}", pageNum);
                    Page<DeviceDO> deviceDOPage= PageHelper.startPage(pageNum, pageSize, Boolean.FALSE)
                            .setOrderBy(" id DESC").doSelectPage(new ISelect(){
                                @Override
                                public void doSelect() {
                                    deviceMapper.listRecord(deviceQueryBO);
                                }
                            });
                    if(CollectionUtils.isEmpty(deviceDOPage.getResult())){
                        flag= Boolean.FALSE;
                        continue;
                    }

                    List<String> deviceNos = deviceDOPage.getResult().stream()
                            .map(DeviceDO::getDeviceNo)
                            .collect(Collectors.toList());
                    Map<String, ApolloBoxDTO> deviceMap= apolloBoxManager.getBoxFromApolloWithBoxNos(deviceNos);

                    deviceMap.entrySet().forEach(entry -> {
                        ApolloBoxDTO apolloBoxDTO = entry.getValue();
                        if(Objects.equals(apolloBoxDTO.getMqttStatus(), MqttStatusEnum.ONLINE.getCode())){
                            redissonClient.getBucket(DEVICE_STATE_KEY+apolloBoxDTO.getDeviceNo()).set(Boolean.TRUE, 30, TimeUnit.DAYS);
                            onLineCount.getAndIncrement();
                        }else{
                            redissonClient.getBucket(DEVICE_STATE_KEY+apolloBoxDTO.getDeviceNo()).set(Boolean.FALSE, 30, TimeUnit.DAYS);
                            offLineCount.getAndIncrement();
                        }
                    });

                    pageNum++;
                }
                redissonClient.getBucket(DEVICE_STATE_UPDATE_TIME_KEY).set(DateUtil.timeStampMilli(), 30, TimeUnit.DAYS);
                // 缓存所有设备在线、离线数据
                DeviceUsageResponse deviceUsageResponse= new DeviceUsageResponse();
                deviceUsageResponse.setOnlineCount(onLineCount.get());
                deviceUsageResponse.setOfflineCount(offLineCount.get());
                deviceUsageResponse.setShopDeviceCount(shopDeviceCount.intValue());
                deviceUsageResponse.setLastUpdateTime(DateUtil.timeStampMilli());
                redissonClient.getBucket(DEVICE_STATE_DATA_KEY+"admin").set(deviceUsageResponse, 30, TimeUnit.DAYS);

            }else{
                log.info("设备状态同步任务处理中....");
            }
        }finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("设备状态同步完成....");
    }

    @Override
    public DeviceUsageResponse getDeviceUsage(final UserDataAuthorityContext userDataAuthorityContext) {
        DeviceUsageResponse response= new DeviceUsageResponse();
        String cacheKey= DEVICE_USAGE_STATISTIC_DATA_KEY+ userDataAuthorityContext.getUserId();
        RBucket<DeviceUsageResponse> bucket= redissonClient.getBucket(cacheKey);
        if(Objects.nonNull(bucket)&& Objects.nonNull(bucket.get())){
            return bucket.get();
        }
        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
        List<Long> deviceTypeIds= deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList());

        // 设备
        DeviceOnlineStateDTO deviceOnlineStateDTO= deviceMapper.deviceOnlineStateStatistics(deviceTypeIds);
        response.setOnlineCount(deviceOnlineStateDTO.getDeviceOnlineCount());
        response.setOfflineCount(deviceOnlineStateDTO.getDeviceOfflineCount());
        response.setShopDeviceCount(deviceOnlineStateDTO.getDeviceOnlineCount()+deviceOnlineStateDTO.getDeviceOfflineCount());
        response.setLastUpdateTime(DateUtil.timeStampMilli());
        bucket.set(response, CacheKeyConstant.DATA_PANEL_CACHE_TIME, TimeUnit.SECONDS);
        return response;
    }



}