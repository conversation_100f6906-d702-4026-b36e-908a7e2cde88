package so.dian.demeter.biz.service;

import com.chargebolt.aeacus.controller.UserController;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.util.LocalMapUtils;
import so.dian.commons.eden.util.LocalNumberUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * UserService
 *
 * <AUTHOR>
 */
@Service
public class UserService {

    @Resource
    private UserController userController;

    public Long getUserIdByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        BizResult<OssUserDTO> userResult = userController.queryByName("1.0", name);
        if (!userResult.isSuccess()) {
            return null;
        }
        OssUserDTO ossUserDTO = userResult.getData();
        if (Objects.isNull(ossUserDTO)) {
            return null;
        }
        return ossUserDTO.getUserId();
    }

    public String getNameById(Long userId) {
        if (!LocalNumberUtils.isPositive(userId)) {
            return null;
        }
        BizResult<List<OssUserDTO>> userListResult = userController.queryByIds("1.0", Lists.newArrayList(userId));
        if (!userListResult.isSuccess()) {
            return null;
        }
        List<OssUserDTO> userList = userListResult.getData();
        if (CollectionUtils.isEmpty(userList)) {
            return null;
        }
        return userList.get(0).getName();
    }

    public Map<Long, String> getIdNameMapByIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Maps.newHashMap();
        }
        Set<Long> userIdSet = Sets.newHashSet(userIdList);
        userIdList = Lists.newArrayList(userIdSet);
        BizResult<List<OssUserDTO>> userListResult = userController.queryByIds("1.0", userIdList);
        if (!userListResult.isSuccess()) {
            return Maps.newHashMap();
        }
        List<OssUserDTO> userList = userListResult.getData();
        if (CollectionUtils.isEmpty(userList)) {
            return Maps.newHashMap();
        }
        return LocalMapUtils.listAsHashMap(userList, OssUserDTO::getUserId, OssUserDTO::getName);
    }
}
