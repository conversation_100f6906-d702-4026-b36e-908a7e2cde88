package so.dian.demeter.biz.service;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.demeter.client.param.WarehouseIncomeApplyRemoteParam;
import so.dian.demeter.dao.rds.WarehouseIncomeCollectMapper;
import so.dian.demeter.pojo.bo.WarehouseIncomeCollectBO;
import so.dian.demeter.pojo.converter.WarehouseIncomeCollectConverter;
import so.dian.demeter.pojo.entity.WarehouseIncomeCollectDO;

/**
 * WarehouseIncomeCollectService
 *
 * <AUTHOR>
 */
@Service
public class WarehouseIncomeCollectService {

    @Resource
    private WarehouseIncomeCollectMapper warehouseIncomeCollectMapper;

    public List<String> getApplyNoByDeviceNo(String deviceNo) {
        if (StringUtils.isBlank(deviceNo)) {
            return null;
        }
        return warehouseIncomeCollectMapper.getApplyNoByDeviceNo(deviceNo);
    }

    public List<WarehouseIncomeCollectBO> listByApplyNoList(List<String> applyNoList) {
        if (CollectionUtils.isEmpty(applyNoList)) {
            return Lists.newArrayList();
        }
        List<WarehouseIncomeCollectDO> collectDOList = warehouseIncomeCollectMapper.listByApplyNoList(applyNoList);
        return WarehouseIncomeCollectConverter.convertDO2BO(collectDOList);
    }

    public List<WarehouseIncomeCollectBO> listByApplyNo(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            return Lists.newArrayList();
        }
        return listByApplyNoList(Lists.newArrayList(applyNo));
    }

    public List<WarehouseIncomeCollectBO> add(String applyNo, WarehouseIncomeApplyRemoteParam param) {
        if (StringUtils.isBlank(applyNo) || Objects.isNull(param)) {
            return Lists.newArrayList();
        }
        List<WarehouseIncomeCollectDO> incomeCollectForInsertList = WarehouseIncomeCollectConverter
                .buildForInsert(applyNo, param);
        if (CollectionUtils.isEmpty(incomeCollectForInsertList)) {
            return Lists.newArrayList();
        }
        warehouseIncomeCollectMapper.batchInsert(incomeCollectForInsertList);
        return WarehouseIncomeCollectConverter.convertDO2BO(incomeCollectForInsertList);
    }

}
