package so.dian.demeter.cache;


import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class RedisCacheConfig {
    @Autowired
    private RedissonClient redissonClient;

    @Bean(value = "redisCacheManager")
    @Primary
    @RefreshScope
    public CacheManager redisCacheManager(){
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setTimeout(60*60*1000L);
        redisCacheManager.setRedissonClient(redissonClient);
        return redisCacheManager;
    }

    @Bean(value = "redisCache1Min")
    @RefreshScope
    public CacheManager redisCache1Min(){
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setTimeout(1*60*1000L);
        redisCacheManager.setRedissonClient(redissonClient);
        return redisCacheManager;
    }


    @Bean(value = "redisCache5Min")
    @RefreshScope
    public CacheManager redisCache5Min(){
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setTimeout(5*60*1000L);
        redisCacheManager.setRedissonClient(redissonClient);
        return redisCacheManager;
    }

    @Bean(value = "redisCache1Days")
    @RefreshScope
    public CacheManager redisCache1Days(){
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setTimeout(24*60*60*1000L);
        redisCacheManager.setRedissonClient(redissonClient);
        return redisCacheManager;
    }

    @Bean(value = "redisCache7Days")
    @RefreshScope
    public CacheManager redisCache7Days(){
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setTimeout(7*24*60*60*1000L);
        redisCacheManager.setRedissonClient(redissonClient);
        return redisCacheManager;
    }

    @Bean(value = "redisCache1Hours")
    @RefreshScope
    public CacheManager redisCache1Hours(){
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setTimeout(60*60*1000L);
        redisCacheManager.setRedissonClient(redissonClient);
        return redisCacheManager;
    }
}
