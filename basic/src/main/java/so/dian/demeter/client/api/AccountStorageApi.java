package so.dian.demeter.client.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.client.dto.AccountStorageInfoDTO;

/**
 * WarehouseIncomeApi
 *
 * <AUTHOR>
 */
public interface AccountStorageApi {

    /**
     * 查询备件库信息
     */
    @GetMapping(value = "/accountStorageInfo/getByAccountId")
    BizResult<AccountStorageInfoDTO> getByAccountId(@RequestParam("cooperatorId") Long cooperatorId,
            @RequestParam("accountId") Long accountId);

}
