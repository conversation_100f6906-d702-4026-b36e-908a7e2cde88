package so.dian.demeter.client.dto;

import lombok.Data;

/**
 * @Description 检修设备信息
 * <AUTHOR>
 * @Date 2023/8/9
 * @Version 1.0
 **/
@Data
public class DeviceMaintenanceDTO {
    // 设备编码
    private String deviceNo;
    // 设备名称
    private String deviceName;
    // 位置信息
    private DeviceBelongsDTO belongs;
    // 是否是盒子
    private Boolean isBox;
    // 是否在线 true在线/false离线 机架/充电宝为null
    private Boolean online;
    // 设备在线类型 1流量 2wifi
    private Integer onlineType;
    // 最后一次离线时间
    private String lastOfflineTime;
    // 设备近期信号是否稳定，true稳定 false不稳定
    private Boolean signalStable;
    // 信号值 0-100
    private Integer signalValue;
    // 信号量 1-5 由signalValue转化
    private Integer semaphore;
    // 是否首次强制开仓
    private Boolean firstForceOpen;
    // 是否支持wifi
    private Boolean supportWifi;
    // 是否可以检修
    private Boolean canMaintenance;
    // 是否可以蓝牙检修
    private Boolean canBleMaintenance;
    // 检修其他信息
    private String maintenanceMessage;
}
