package so.dian.demeter.client.dto;

import lombok.Data;

import java.util.Date;

@Data
public class PowerBankLogDTO {
    // 正常、归还流入、归还流出、使用中、购买流出
    private String flowSituation;
    // 借出盒子编号
    private String loanBoxNo;
    // 借出城市
    private String loanCity;
    // 借出人ID
    private Long loanId;
    // 借出时间
    private Date loanTime;
    // 借出城市
    private String locaShopName;
    // 借出城市ID
    private Long locaShopId;
    // 归还盒子编号
    private String returnBoxNo;
    // 归还城市
    private String returnCity;
    // 归还门店ID
    private Long returnShopId;
    // 归还城市
    private String returnShopName;
    // 归还时间
    private Date returnTime;
    // 自行判断的订单状态，1:订单已完成,充电宝已归还
    private Integer orderStatus;
}
