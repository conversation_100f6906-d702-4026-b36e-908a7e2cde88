package so.dian.demeter.client.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * DeviceStatusEnum
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeviceClassifyEnum implements EnumInterface<DeviceClassifyEnum> {
    BOX(1, "盒子"),
    POWER_BANK(2, "充电宝"),
    ;

    private Integer code;
    private String desc;

    @Override
    public DeviceClassifyEnum getDefault() {
        return null;
    }
}
