package so.dian.demeter.common.constant;

import com.google.common.collect.Lists;
import java.util.List;

/**
 * CommonConstants
 *
 * <AUTHOR>
 */
public class CommonConstants {

    public static final String APPLICATION_NAME = "demeter";
    public static final String BASE_DAO_PACKAGE_NAME = "so.dian.demeter.dao.rds";
    public static final String POWER_BANK_SUB_DEVICE_TYPE = "Power bank";
    public static final String INVALID_POWER_BANK_NO = "****************";
    public static final Long OPEN_USE_NO_INSTALL_OPERATE_LOG = 1L;
    public static final Long OPEN_USE_HAS_INSTALL_OPERATE_LOG = 2L;
    public static final List<Integer> CAN_INSTALL_SHOP_STATUS = Lists.newArrayList(1,2);
    public static final Integer INTEGER_ONE = 1;
    public static final Integer INTEGER_TWO = 2;
    public static final Integer INTEGER_THREE = 3;
    public static final Integer INTEGER_FOUR = 4;
    public static final Integer INTEGER_ZERO = 0;
    public static final Integer INTEGER_FIVE = 5;
    public static final Integer INTEGER_TEN = 10;
    public static final Integer INTEGER_FIFTEEN = 15;
    public static final Integer INTEGER_FIFTY = 50;
    public static final Integer INTEGER_THREE_HUNDRED_SIXTY = 360;
    public final static String REDIS_KEY_BOX_ORDER_INFO = "BOX_ORDER_INFO:";
    public final static String REDIS_KEY_BOX_ONLINE_INFO = "BOX_ONLINE_INFO:";
    public final static String REDIS_KEY_SHOP_BOX_INFO = "SHOP_BOX_INFO:";
    public final static String REDIS_KEY_SHOP_BOX_HISTORY = "SHOP_BOX_HISTORY:";
    public final static String LOCATION_UK = "UK";

    public final static Long SYSTEM_ID = 0L;
    public final static String SYSTEM_NAME = "system";
    public final static String CONSUMER = "consumer";

}
