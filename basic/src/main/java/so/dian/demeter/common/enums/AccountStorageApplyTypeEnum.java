package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * AccountStorageApplyTypeEnum
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AccountStorageApplyTypeEnum implements EnumInterface<AccountStorageApplyTypeEnum> {
    APPLY(1, "申领"),
    BACK(2, "退回"),
    LOST(3, "遗失"),;

    private Integer code;
    private String desc;

    @Override
    public AccountStorageApplyTypeEnum getDefault() {
        return null;
    }
}
