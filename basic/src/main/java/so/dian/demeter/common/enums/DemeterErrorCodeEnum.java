package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * LeoErrorCodeEnum
 *
 * <AUTHOR>
 * @desc 描述信息
 * @date 18/7/11
 */
@Getter
@AllArgsConstructor
public enum DemeterErrorCodeEnum implements EnumInterface<DemeterErrorCodeEnum> {
    KAFKA_RUNNING(0, "kafka处于运行状态");

    private Integer code;
    private String desc;

    @Override
    public DemeterErrorCodeEnum getDefault() {
        return null;
    }
}