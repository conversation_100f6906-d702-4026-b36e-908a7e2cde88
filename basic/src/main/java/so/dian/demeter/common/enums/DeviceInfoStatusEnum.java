package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * DeviceInfoStatusEnum
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeviceInfoStatusEnum implements EnumInterface<DeviceInfoStatusEnum> {
    CAN_NOT_APPLY(0, "不可申领"),
    CAN_APPLY(1, "可申领"),
    ;

    private Integer code;
    private String desc;

    @Override
    public DeviceInfoStatusEnum getDefault() {
        return CAN_APPLY;
    }
}
