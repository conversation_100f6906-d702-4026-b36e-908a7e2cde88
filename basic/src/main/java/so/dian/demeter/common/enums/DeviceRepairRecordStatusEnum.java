package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * <AUTHOR>
 * @create 2019-03-11 3:16 PM
 * @desc 充电宝放入取出异常状态枚举
 */
@Getter
@AllArgsConstructor
public enum DeviceRepairRecordStatusEnum implements EnumInterface<DeviceRepairRecordStatusEnum> {

    STATUS_1(1, "正常"),
    STATUS_2(2, "异常");
    private Integer code;
    private String desc;

    @Override
    public DeviceRepairRecordStatusEnum getDefault() {
        return STATUS_1;
    }
}
