package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * DeviceInfoStatusEnum
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeviceStorageTypeEnum implements EnumInterface<DeviceStorageTypeEnum> {
    UNKNOW(0,"未知"),
    WAREHOUSE(1, "仓库"),
    ACCOUNT_STORAGE(2, "小二备件库"),
    SHOP(3, "门店"),
    USER(4, "用户"),
    ;

    private Integer code;
    private String desc;

    @Override
    public DeviceStorageTypeEnum getDefault() {
        return null;
    }
}
