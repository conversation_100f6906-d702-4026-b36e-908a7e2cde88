package so.dian.demeter.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.apollo.dto.PowerBankEventMsgDTO;

import java.util.List;

/**
 * @Description 检修权限
 * <AUTHOR>
 * @Date 2022/7/22
 * @Version 1.0
 **/
public class MaintenanceOpenConst {
    // 检修充电宝取出放入消息处理
    public static final List<Integer> HANDLE_EVENT_IDS = Lists.newArrayList(
            PowerBankEventMsgDTO.Event.POWERBANK_TAKEAWAY_SUCCESS.getId(), PowerBankEventMsgDTO.Event.POWERBANK_RETURN_SUCCESS.getId(),
            PowerBankEventMsgDTO.Event.LOAN_CHECK_FAIL.getId(), PowerBankEventMsgDTO.Event.SLOT_OPEN_FAIL.getId(),
            PowerBankEventMsgDTO.Event.POWERBANK_SWITCH_OPEN_FAIL.getId(), PowerBankEventMsgDTO.Event.POWERBANK_POAN_PROCESS_INTERRUPT.getId());

    @Getter
    @AllArgsConstructor
    public enum OpenTypeEnum {
        INIT(0, "初始"),
        OPEN(1, "开仓"),
        FORCE_OPEN(2, "强制开仓"),

        ;
        private Integer code;
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum OpenStatusEnum {
        INIT(0, "", "", "", ""), // 初始
        OPEN(1, "", "", "", ""), // 开仓中
        OPEN_SUCCESS(2, "充电宝已解锁，请及时拿出", "充电宝已解锁，请及时拿出",
                "The power bank has been unlocked, please take it out in time", "The power bank has been unlocked, please take it out in time"),
        OPEN_FAIL(3, "充电宝解锁失败，请重试或尝试强制推出", "充电宝解锁失败，请重试或尝试强制推出",
                "The power bank unlock failed, please try again or try to force it out", "The power bank unlock failed, please try again or try to force it out"),
        OPEN_TIMEOUT(4, "充电宝解锁超时，请重试", "充电宝解锁超时，请重试",
                "The power bank unlocking timed out, please try again", "The power bank unlocking timed out, please try again"),
        FORCE_OPEN_FAIL(5, "强制弹出失败，请重试，若多次重复后依旧无法弹出，请将仓位禁用", "强制弹出失败，请重试，若多次重复后依旧无法弹出，请将仓位禁用",
                "If the forced ejection fails, please try again, if it still fails to eject after repeated repeats, please disable the slot",
                "If the forced ejection fails, please try again, if it still fails to eject after repeated repeats, please disable the slo"),
        TAKE_SUCCESS(6, "充电宝已成功取出", "充电宝已成功弹出",
                "The power bank has been successfully removed", "The power bank has been successfully ejected"),


        ;
        private Integer code;
        private String zhDesc;
        private String zhDesc2;
        private String enDesc;
        private String enDesc2;

        public static OpenStatusEnum getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (OpenStatusEnum openStatusEnum: OpenStatusEnum.values()) {
                if (code.equals(openStatusEnum.getCode())) {
                    return openStatusEnum;
                }
            }
            return null;
        }
    }
}
