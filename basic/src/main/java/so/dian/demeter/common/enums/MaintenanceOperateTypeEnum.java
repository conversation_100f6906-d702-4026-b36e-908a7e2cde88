package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * <AUTHOR>
 * @create 2019-03-11 3:14 PM
 * @desc 检修操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum MaintenanceOperateTypeEnum implements EnumInterface<MaintenanceOperateTypeEnum> {

    OPERATE_TYPE_1(1, "BD检修"),
    OPERATE_TYPE_2(2, "仓管检修");

    private Integer code;
    private String desc;

    @Override
    public MaintenanceOperateTypeEnum getDefault() {
        return OPERATE_TYPE_1;
    }
}
