package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * <AUTHOR>
 * @create 2019-03-11 3:13 PM
 * @desc 检修状态枚举
 */
@Getter
@AllArgsConstructor
public enum MaintenanceStatusEnum implements EnumInterface<MaintenanceStatusEnum> {
    REPAIRING(1, "检修中"),
    REPAIRED(2, "已完成"),;

    private Integer code;
    private String desc;

    @Override
    public MaintenanceStatusEnum getDefault() {
        return REPAIRING;
    }
}
