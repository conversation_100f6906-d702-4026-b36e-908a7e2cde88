package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Description 在线状态
 * <AUTHOR>
 * @Date 2023/8/10
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum OnlineStatusEnum implements EnumInterface<OnlineStatusEnum> {

    OFFLINE(0, "离线"),
    ONLINE(1, "在线"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OnlineStatusEnum getDefault() {
        return OFFLINE;
    }
}
