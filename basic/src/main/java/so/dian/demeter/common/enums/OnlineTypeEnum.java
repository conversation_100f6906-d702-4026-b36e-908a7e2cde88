package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * <AUTHOR>
 * @create 2023/08/10
 * @desc 设备在线类型
 */
@Getter
@AllArgsConstructor
public enum OnlineTypeEnum implements EnumInterface<OnlineTypeEnum> {

    FLOW_RATE(1, "流量"),
    WIFI(2, "wifi"),
    BLE(3, "蓝牙"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OnlineTypeEnum getDefault() {
        return FLOW_RATE;
    }
}
