package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * OperateLogBizTypeEnum
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OperateLogBizTypeEnum implements EnumInterface<OperateLogBizTypeEnum> {
    WAREHOUSE_INCOME(1, "仓库入库"),
    ACCOUNT_STORAGE_APPLY(2, "小二备件申领"),
    ACCOUNT_STORAGE_APPLY_LOST(3, "小二备件遗失申报"),
    ACCOUNT_STORAGE_APPLY_BACK_TO_WAREHOUSE(4, "小二备件退回仓库申请"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OperateLogBizTypeEnum getDefault() {
        return null;
    }
}
