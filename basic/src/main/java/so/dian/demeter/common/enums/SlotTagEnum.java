package so.dian.demeter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * @Description 仓位枚举
 * <AUTHOR>
 * @Date 2023/8/11
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum SlotTagEnum implements EnumInterface<SlotTagEnum> {
    AVAIABLE_RENT(1, "可租借"),
    NOT_AVAIABLE_RENT(2, "不可租借"),
    CHARGING(3, "充电中"),
    ;

    private Integer code;
    private String desc;

    @Override
    public SlotTagEnum getDefault() {
        return null;
    }
}
