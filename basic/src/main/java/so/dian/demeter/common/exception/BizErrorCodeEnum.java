package so.dian.demeter.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.exception.ErrorCodeEnum;

/**
 * 业务异常 <br/>
 *
 * <AUTHOR>
 * @date 2018/10/24 11:38 AM
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Getter
@AllArgsConstructor
public enum BizErrorCodeEnum implements EnumInterface {

    //---------common code---------------
    PARAMS_ERROR(70000, "param error"),
    RECORD_NOT_EXISTED(70001, "record not existed"),
    REDIS_LOCK_FAILED(70002, "redis lock failed"),
    REDIS_CACHE_FAILED(70003, "redis cache failed"),
    //------device----------
    DEVICE_NOT_EXISTED(70100, "Device not existed"),
    DEVICE_TYPE_NOT_DEFINITION(70101, "Device type not definition"),
    DEVICE_IN_OTHER_MAINTENANCE(70102,"Others are under maintenance"), // 无权限维修
    DEVICE_NOT_IN_MAINTENANCE(70103,"The device is not under maintenance"), // 设备未在检修
    DEVICE_OFFLINE(70104,"Device offline"),
    DEVICE_IN_UPGRADE(70105,"Device in upgrade"),
    DEVICE_CANNOT_MAINTENANCE(70106, "device can not maintenance"),

    //------warehouse---------
    DEVICE_HAS_INCOME_WAREHOUSE(70200, "device has income warehouse"),
    WAREHOUSE_NOT_EXISTED(70201, "warehouse not existed"),
    DEVICE_HAS_EXISTED(70202, "device has existed"),
    //-----account storage--------------
    ACCOUNT_STORAGE_NOT_EXISTED(70300, "account storage not existed"),
    ACCOUNT_STORAGE_INVALID(70301, "account storage invalid"),
    //----account storage apply---------
    ACCOUNT_EXISTED_NOT_FINISHED_APPLY(70400, "account existed not existed finished apply"),
    ACCOUNT_APPLY_MORE_THAN_TOTAL_AMOUNT(70401, "account more than total amount"),
    ACCOUNT_APPLY_NOT_EXISTED(70402, "account apply not existed"),
    ACCOUNT_APPLY_STATUS_INVALID(70403, "account apply status invalid"),
    ACCOUNT_APPLY_OUTCOME_NOT_MATCH(70404, "account apply outcome not match"),
    ACCOUNT_APPLY_DEVICE_NOT_IN_WAREHOUSE(70405, "account apply device not in warehouse"),
    ACCOUNT_APPLY_DEVICE_NOT_TEST_OK(70406, "account apply device not test ok"),
    ACCOUNT_APPLY_DEVICE_NOT_IN_ACCOUNT_STORAGE(70407, "account apply device not in account storage"),
    ACCOUNT_APPLY_BACK_SAME_DEVICE(70408, "account apply back same device"),
    ACCOUNT_APPLY_BACK_DEVICE_LOST(70409, "account apply back device lost"),
    ACCOUNT_RECYCLE_MORE_THAN_TOTAL_AMOUNT(70410, "account recycle more than total amount"),
    //----------设备安装与回收-------------
    SHOP_NO_PERMISSION(70500, "shop no permission"),
    DEVICE_NOT_IN_SHOP(70501, "device not in shop"),
    DEVICE_INSTALL_TO_NOT_SIGN_SHOP(70502, "device install to not sign shop"),

    // 蓝牙
    BLE_GET_TOKEN_FAIL(70600, "Failed to obtain Bluetooth initialization command"),
    BLE_RESPONSE_DECRYPTION_FAIL(70601, "Failed to parse the data reported by Bluetooth"),
    BLE_CONNECT_WIFI_FAIL(70602, "Failed to obtain Bluetooth networking commands"),
    BLE_OPEN_SLOT_FAIL(70603, "Failed to obtain Bluetooth opening instructions"),
    BLE_REPORT_ALL_SLOT_FAIL(70604, "Failed to obtain the Bluetooth reporting command"),
    BLE_FINISH_FAIL(70605,"Disconnecting Bluetooth failed"),
    DEVICE_NOT_SUPPORT_BLE(70606, "device not support Bluetooth"),
    DEVICE_BLE_OTHER_USER(70607, "Others are using Bluetooth"),
    DEVICE_BLE_CONNECT(70608, "Please connect to Bluetooth first"),
    DEVICE_USER_NO_PERMISSIONS(70608, "The current user has no permissions"),
    ;

    private Integer code;
    private String desc;


    @Override
    public EnumInterface getDefault() {
        return ErrorCodeEnum.UNKNOWN_ERROR;
    }
}
