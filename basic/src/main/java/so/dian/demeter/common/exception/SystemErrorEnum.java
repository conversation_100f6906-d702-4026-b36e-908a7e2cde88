package so.dian.demeter.common.exception;

import so.dian.commons.eden.enums.EnumInterface;

/**
 * Created by 希罗 on 2018/8/16
 */
public enum SystemErrorEnum implements EnumInterface {

    SYSTEM_ERROR(500, "服务器内部异常");

    private Integer code;
    private String desc;

    SystemErrorEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public EnumInterface getDefault() {
        return null;
    }

    @Override
    public String getDesc() {
        return null;
    }
}
