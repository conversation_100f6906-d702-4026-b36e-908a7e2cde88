package so.dian.demeter.common.thread;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import so.dian.demeter.biz.facade.DeviceFacade;
import so.dian.demeter.client.dto.BoxComprehensiveDTO;
import so.dian.demeter.client.param.ExportShopParam;
import so.dian.demeter.common.constant.CommonConstants;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
public class HandleExcelDataThread {
    @Resource
    private DeviceFacade deviceFacade;
    // 创建 5 个线程
    private ExecutorService executorService = Executors.newFixedThreadPool(CommonConstants.INTEGER_FIVE);

    public Map<Long, Map<String, BoxComprehensiveDTO>> handleExcelData (ExportShopParam exportShopParam) {
        log.info("获取数据开始时间:{}", new Date());
        List<Future<Map<Long, Map<String, BoxComprehensiveDTO>>>> list = new ArrayList<>(CommonConstants.INTEGER_FIVE);
        Map<Long, Map<String, BoxComprehensiveDTO>> result = new HashMap<>();
        List<Long> shopIds = exportShopParam.getShopIds();
        if (CollectionUtils.isEmpty(shopIds)) {
            return null;
        }
        int startNum = 0;
        int endNum = 10;
        boolean flag = false;
        for (int i = 0; i < CommonConstants.INTEGER_FIVE; i++) {
            if (endNum > shopIds.size()) {
                endNum = shopIds.size();
                flag = true;
            }
            List<Long> tempShopIds = new ArrayList<>(shopIds.subList(startNum, endNum));
            endNum = endNum + CommonConstants.INTEGER_TEN;
            startNum = startNum + CommonConstants.INTEGER_TEN;
            ExportShopParam param = new ExportShopParam();
            param.setEndTime(exportShopParam.getEndTime());
            param.setStartTime(exportShopParam.getStartTime());
            param.setShopIds(tempShopIds);
            Future<Map<Long, Map<String, BoxComprehensiveDTO>>> future = executorService.submit(() -> deviceFacade.getBoxExcelData(param));
            list.add(future);
            if (flag) {
                break;
            }
        }
        // 打印for (Future<Map<Long, Map<String, BoxComprehensiveDTO>>> future : list) 结果
        for (Future<Map<Long, Map<String, BoxComprehensiveDTO>>> future : list) {
            try {
                result.putAll(future.get());
            } catch (InterruptedException e) {
                log.error("handleExcelData InterruptedException, e:{}", e.getMessage());
            } catch (ExecutionException e) {
                log.error("handleExcelData ExecutionException, e:{}", e.getMessage());
            }
        }
        log.info("获取数据结束时间:{}", new Date());
        return result;
    }
}
