package so.dian.demeter.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import so.dian.commons.eden.util.LocalNumberUtils;

/**
 * VegaDateUtils
 *
 * <AUTHOR>
 * @date 2018/1/4
 */
@Slf4j
public class LocalDateUtils {

    public static final String DATE_DAY_PATTERN = "YYYY-MM-dd";
    public static final String DATE_DAY_LONG_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final Long DAY_MILLISECONDS = 86400000L;

    public static Date date(Long timestamp) {
        if(!LocalNumberUtils.isPositive(timestamp)) {
            return null;
        }
        return new Date(timestamp);
    }



    public static Date now() {
        DateTime dateTime = DateTime.now();
        return dateTime.toDate();
    }

    /**
     * 获取指定天的开始时间
     */
    public static Date getDateDayStart(Date date) {
        if (Objects.equals(null, date)) {
            return null;
        }
        return new DateTime(date).millisOfDay().withMinimumValue().toDate();
    }

    /**
     * 获取指定天的结束时间
     */
    public static Date getDateDayEnd(Date date) {
        if (Objects.equals(null, date)) {
            return null;
        }
        return new DateTime(date).millisOfDay().withMaximumValue().toDate();
    }

    public static Date getTodayStart() {
        return getDateDayStart(now());
    }

    public static Date getDateSecondEnd(Date date) {
        return new DateTime(date).secondOfDay().withMaximumValue().toDate();
    }

    public static Date getTodaySecondEnd() {
        return getDateSecondEnd(now());
    }

    public static Date getTodayEnd() {
        return getDateDayEnd(now());
    }

    public static Date getBeforeDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return new DateTime(date).minusDays(1).toDate();
    }

    public static Date getTomorrowDate() {
        return DateTime.now().plusDays(1).toDate();
    }

    public static Date getBeforeDateStart(Date date) {
        return getDateDayStart(getBeforeDate(date));
    }

    public static Date getBeforeDateEnd(Date date) {
        return getDateDayEnd(getBeforeDate(date));
    }

    public static Date getBeforeDateSecondEnd(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return new DateTime(date).millisOfDay().withMinimumValue().minusSeconds(1).toDate();
    }

    public static Date parse(String dateStr, String pattern) {
        if (StringUtils.isEmpty(dateStr) || StringUtils.isEmpty(pattern)) {
            return null;
        }
        DateTimeFormatter format = DateTimeFormat.forPattern(pattern);
        DateTime dateTime = null;
        try {
            dateTime = DateTime.parse(dateStr, format);
        } catch (Exception e) {

        }
        if (Objects.isNull(dateTime)) {
            return null;
        }
        return dateTime.toDate();
    }


    // 得到指定日期（几天前/几天后）整数往后推,负数往前移动
    public static Date getAppointDay(int num) {
        DateFormat dateFormat = new SimpleDateFormat();
        //格式化当前时间
        String format = dateFormat.format(new Date());
        Date resultDate;
        try {
            resultDate = dateFormat.parse(format);
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(resultDate);
            //整数往后推,负数往前移动
            calendar.add(Calendar.DATE, num);
            //指定的日期
            return calendar.getTime();
        } catch (ParseException e) {
            log.error("LocalDateUtils getAppointDay, exception:{}", e.getMessage());
        }
         return null;
    }


    public static String getNowStr() {
        return DateTime.now().toString("YYMMddHHmmss");
    }

    public static String getNowStrWithMillis() {
        return DateTime.now().toString("YYMMddHHmmssSSS");
    }

    public static String getDateStr(Date date, String pattern) {
        return new DateTime(date).toString(pattern);
    }

    public static Date add(Date originDate, int step, PeriodUnit periodUnit) {
        if (Objects.equals(PeriodUnit.DAY, periodUnit)) {
            return new DateTime(originDate).plusDays(step).toDate();
        }
        if (Objects.equals(PeriodUnit.MONTH, periodUnit)) {
            return new DateTime(originDate).plusMonths(step).toDate();
        }
        return null;
    }

    public static enum PeriodUnit {
        DAY, MONTH;
    }

    public static Integer calcDaysDuration(Date date1, Date date2) {
        Long temp = date2.getTime() - date1.getTime();
        Long days = temp / DAY_MILLISECONDS;
        if(temp % DAY_MILLISECONDS > 0) {
            days = days + 1;
        }
        return Integer.parseInt(String.valueOf(days)) ;
    }

    /**
     * date2比date1多的天数
     * @param date1 Date
     * @param date2 Date
     * @return int
     */
    public static int differentDays(Date date1,Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1= cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if(year1 != year2) {
            //同一年
            int timeDistance = 0 ;
            for(int i = year1 ; i < year2 ; i ++) {
                if(i%4==0 && i%100!=0 || i%400==0)    //闰年
                {
                    timeDistance += 366;
                }
                else    //不是闰年
                {
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2-day1) ;
        } else {
            //不同年
            return day2-day1;
        }
    }
}
