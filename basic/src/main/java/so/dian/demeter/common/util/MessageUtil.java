package so.dian.demeter.common.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import so.dian.demeter.mq.device.dto.PowerBankEventMonitorDTO;

/**
 * @Description 消息相关工具
 * <AUTHOR>
 * @Date 2023/12/26
 * @Version 1.0
 **/
public class MessageUtil {

    private static final String XB3_TRACE_ID = "X-B3-TraceId";

    public static final String PB_MSG_TRACE = "pbMsg:";

    public static void addTid(String topic, PowerBankEventMonitorDTO eventMonitorDTO) {
        String oldTid = MDC.get(XB3_TRACE_ID);
        if (StringUtils.isBlank(oldTid) || oldTid.startsWith(PB_MSG_TRACE)) {
            String tid = PB_MSG_TRACE + topic + ":" + eventMonitorDTO.getMsgId() + ":" + eventMonitorDTO.getPowerBankNo();
            MDC.put(XB3_TRACE_ID, tid);
        }
    }
}
