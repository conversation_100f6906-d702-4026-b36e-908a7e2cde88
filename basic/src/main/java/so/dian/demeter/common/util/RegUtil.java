package so.dian.demeter.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created with lhc
 *
 * @Author: bailong
 * Date: 2017/7/10
 * Time: 下午4:57
 * 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备********号
 */
public class RegUtil {
    //这里因为前期都是00，所以兼容之
    public static final String POWER_BANK_DEVICE_NO = "^1[3-9][0-9]{9}$";
    public static boolean isMobile(String mobile){
        Pattern pattern = Pattern.compile(POWER_BANK_DEVICE_NO);
        return pattern.matcher(mobile).matches();
    }


}
