package so.dian.demeter.common.util.biz;

import so.dian.demeter.client.enu.DeviceOperateTypeEnum;
import so.dian.demeter.common.enums.AccountStorageApplyTypeEnum;
import so.dian.demeter.common.util.LocalDateUtils;

import java.text.DecimalFormat;
import java.util.Objects;
import java.util.Random;

/**
 * ApplyNoUtils：单号生成工具类
 *
 * <AUTHOR>
 */
public class BizNoUtils {

    private static final String WAREHOUSE_INCOME_APPLY_NO_PREFIX = "WI";
    private static final String ACCOUNT_STORAGE_APPLY_APPLY_NO_PREFIX = "AS";
    private static final String DEVICE_INSTALL_APPLY_NO_PREFIX = "DI";
    private static final String DEVICE_RECYCLE_APPLY_NO_PREFIX = "DR";
    private static final String DEVICE_MAINTENANCE_NO_PREFIX = "DM";

    public static String buildWarehouseIncomeApplyNo(Long warehouseId, Long operatorId) {
        StringBuilder stringBuilder = new StringBuilder(WAREHOUSE_INCOME_APPLY_NO_PREFIX);
        DecimalFormat warehouseIdFormatter = new java.text.DecimalFormat("0000000");
        DecimalFormat operatorIdFormatter = new java.text.DecimalFormat("0000000");
        DecimalFormat randomFormatter = new java.text.DecimalFormat("000");
        Random random = new Random();
        String dateString = LocalDateUtils.getDateStr(LocalDateUtils.now(), "yyyyMMddHHmmssSSS");
        return stringBuilder.append(warehouseIdFormatter.format(warehouseId))
                .append(operatorIdFormatter.format(operatorId)).append(dateString)
                .append(randomFormatter.format(random.nextInt(100))).toString();
    }

    public static String buildAccountStorageApplyNo(AccountStorageApplyTypeEnum applyType, Long accountStorageId) {
        StringBuilder stringBuilder = new StringBuilder(ACCOUNT_STORAGE_APPLY_APPLY_NO_PREFIX);
        DecimalFormat accountStorageIdFormatter = new java.text.DecimalFormat("********");
        DecimalFormat applyTypeFormatter = new java.text.DecimalFormat("00");
        DecimalFormat randomFormatter = new java.text.DecimalFormat("000");
        Random random = new Random();
        String dateString = LocalDateUtils.getDateStr(LocalDateUtils.now(), "yyyyMMddHHmmssSSS");
        return stringBuilder.append(accountStorageIdFormatter.format(accountStorageId))
                .append(applyTypeFormatter.format(applyType.getCode())).append(dateString)
                .append(randomFormatter.format(random.nextInt(100))).toString();
    }

    public static String buildDeviceOperateApplyNo(DeviceOperateTypeEnum operateTypeEnum, Long operateId) {
        String prefix = Objects.equals(operateTypeEnum, DeviceOperateTypeEnum.INSTALL) ? DEVICE_INSTALL_APPLY_NO_PREFIX
                : (Objects.equals(operateTypeEnum, DeviceOperateTypeEnum.TEST)
                    ? DEVICE_MAINTENANCE_NO_PREFIX : DEVICE_RECYCLE_APPLY_NO_PREFIX);
        StringBuilder stringBuilder = new StringBuilder(prefix);
        DecimalFormat accountStorageIdFormatter = new java.text.DecimalFormat("000000");
        DecimalFormat applyTypeFormatter = new java.text.DecimalFormat("00");
        DecimalFormat randomFormatter = new java.text.DecimalFormat("000");
        Random random = new Random();
        String dateString = LocalDateUtils.getDateStr(LocalDateUtils.now(), "yyyyMMddHHmmssSSS");
        return stringBuilder.append(accountStorageIdFormatter.format(operateId))
                .append(applyTypeFormatter.format(operateTypeEnum.getCode())).append(dateString)
                .append(randomFormatter.format(random.nextInt(10000))).toString();
    }

    public static String buildPercentageBillNo(Long accountStorageId) {
        StringBuilder stringBuilder = new StringBuilder("301");
        DecimalFormat warehouseIdFormatter = new java.text.DecimalFormat("000000");
        DecimalFormat randomFormatter = new java.text.DecimalFormat("000");
        Random random = new Random();
        String dateString = LocalDateUtils.getDateStr(LocalDateUtils.now(), "yyMMddHHmmssSSS");
        return stringBuilder.append(warehouseIdFormatter.format(accountStorageId))
                .append(dateString)
                .append(randomFormatter.format(random.nextInt(999))).toString();
    }


}
