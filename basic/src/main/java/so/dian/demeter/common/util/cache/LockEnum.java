package so.dian.demeter.common.util.cache;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.CacheInterface;
import so.dian.demeter.common.constant.CommonConstants;

import java.util.concurrent.TimeUnit;

@Getter
@AllArgsConstructor
public enum LockEnum implements CacheInterface<LockEnum> {
    ACCOUNT_STORAGE_INIT_LOCK("ACCOUNT_STORAGE_INIT_LOCK_", "小二备件库信息初始化锁", 1L, TimeUnit.SECONDS),
    ACCOUNT_STORAGE_APPLY_LOCK("ACCOUNT_STORAGE_APPLY_LOCK_", "小二备件申领锁", 2L, TimeUnit.SECONDS),
    ACCOUNT_STORAGE_APPLY_BACK_LOCK("ACCOUNT_STORAGE_APPLY_BACK_LOCK_", "小二备件退回仓库锁", 2L, TimeUnit.SECONDS),
    ACCOUNT_STORAGE_LOST_LOCK("ACCOUNT_STORAGE_LOST_LOCK_", "小二备件遗失申报锁", 2L, TimeUnit.SECONDS),
    DEVICE_MAINTENANCE_LOCK("DEVICE_MAINTENANCE_LOCK_", "设备检修锁", 2L, TimeUnit.SECONDS),
    DEVICE_MAINTENANCE_SLOT_CACHE_LOCK("DEVICE_MAINTENANCE_SLOT_CACHE_LOCK_", "设备检修仓位缓存锁", 2L, TimeUnit.SECONDS),
    PB_PUT_TAKE_LOCK("PB_PUT_TAKE_LOCK", "设备检修仓位缓存锁", 2L, TimeUnit.SECONDS),

    ;

    private String key;
    private String desc;
    private Long expire;
    private TimeUnit unit;

    @Override
    public String getKey() {
        return getKey(CommonConstants.APPLICATION_NAME);
    }
    @Override
    public String getKey(String namespace) {
        return namespace + "$" + this.key;
    }

}
