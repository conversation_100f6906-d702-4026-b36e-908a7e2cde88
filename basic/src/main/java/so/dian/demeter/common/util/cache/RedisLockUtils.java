package so.dian.demeter.common.util.cache;

import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * redis并发锁
 *
 * <AUTHOR>
 * @date 2018/4/16 上午12:15
 * @Copyright 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
@Component
@Slf4j(topic = "error")
public class RedisLockUtils {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public boolean lock(LockEnum prefix, String field) {
        String key = buildCacheKey(prefix, field);
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        try {
            String value = redisTemplate.opsForValue().get(key);
            if (StringUtils.isNotBlank(value)) {
                Long expireLong = redisTemplate.getExpire(key);
                if (Objects.equals(-1L, expireLong)) {
                    redisTemplate.expire(key, prefix.getExpire(), prefix.getUnit());
                    return true;
                }
            }
            boolean lockResult = redisTemplate.opsForValue().setIfAbsent(key, "1");
            if (lockResult) {
                redisTemplate.expire(key, prefix.getExpire(), prefix.getUnit());
            }
            return lockResult;
        } catch (Exception e) {
            log.error("lock fail,key:{}-{}", key, e);
        }
        return true;
    }

    public boolean unlock(LockEnum prefix, String field) {

        String key = buildCacheKey(prefix, field);
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("clear lock fail,key:{}-{}", key, e);
        }
        return true;
    }

    private String buildCacheKey(LockEnum prefix, String field) {
        if (StringUtils.isBlank(field) || Objects.isNull(prefix)) {
            return null;
        }
        return prefix.getKey() + field;
    }
}
