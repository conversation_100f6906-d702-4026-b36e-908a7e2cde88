package so.dian.demeter.common.util.cache.biz;

import com.alibaba.fastjson.JSON;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.demeter.common.constant.CacheKeyConstants;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.eros.cache.RedisClient;

/**
 * DeviceInfoCacheUtils
 *
 * <AUTHOR>
 */
@Service
@Slf4j(topic = "error")
public class DeviceInfoCacheUtils {

    @Resource
    private RedisClient redisClient;

    public void setDeviceInfo(List<DeviceInfoBO> deviceInfoBOList) {
        if (Objects.isNull(deviceInfoBOList)) {
            return;
        }
        try {
            redisClient.set(CacheKeyConstants.DEVICE_INFO_ALL_KEY, deviceInfoBOList);
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils set all deviceInfo cache error, deviceInfoList:{}",
                    JSON.toJSONString(deviceInfoBOList), e);
        }
    }

    public List<DeviceInfoBO> getAll() {
        try {
            String cacheResult = redisClient.get(CacheKeyConstants.DEVICE_INFO_ALL_KEY, String.class);
            return JSON.parseArray(cacheResult, DeviceInfoBO.class);
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils get all deviceInfo cache error", e);
        }
        return null;
    }

    public void removeDeviceInfoCache() {
        try {
            redisClient.remove(CacheKeyConstants.DEVICE_INFO_ALL_KEY);
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils remove deviceInfo cache error", e);
        }
    }

    public void setDeviceInfoByCooperatorId(Long cooperatorId, List<DeviceInfoBO> deviceInfoBOList) {
        if (Objects.isNull(deviceInfoBOList)) {
            return;
        }
        try {
            redisClient.set(buildKey(CacheKeyConstants.DEVICE_INFO_COOPERATOR_ID_KEY_PREFIX, cooperatorId),
                    deviceInfoBOList);
        } catch (Exception e) {
            log.error(
                    "DeviceInfoCacheUtils set deviceInfo by cooperatorId cache error, cooperatorId:{}, deviceInfoList:{}",
                    cooperatorId, deviceInfoBOList, e);
        }
    }

    public List<DeviceInfoBO> getByCooperatorId(Long cooperatorId) {
        if (Objects.isNull(cooperatorId)) {
            return null;
        }
        try {
            String cacheResult = redisClient
                    .get(buildKey(CacheKeyConstants.DEVICE_INFO_COOPERATOR_ID_KEY_PREFIX, cooperatorId), String.class);
            return JSON.parseArray(cacheResult, DeviceInfoBO.class);
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils get deviceInfo by cooperatorId cache error, cooperatorId:{}", cooperatorId,
                    e);
        }
        return null;
    }

    public void removeByCooperatorId(Long cooperatorId) {
        try {
            redisClient.remove(buildKey(CacheKeyConstants.DEVICE_INFO_COOPERATOR_ID_KEY_PREFIX, cooperatorId));
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils remove deviceInfo by cooperatorId cache error, cooperatorId:{}",
                    cooperatorId, e);
        }
    }

    public DeviceInfoBO getById(Long id) {
        try {
            return redisClient.get(buildKey(CacheKeyConstants.DEVICE_INFO_ID_KEY_PREFIX, id), DeviceInfoBO.class);
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils get deviceInfo by id cache error, id:{}", id, e);
        }
        return null;
    }

    public void setById(DeviceInfoBO deviceInfoBO) {
        if (Objects.isNull(deviceInfoBO)) {
            return;
        }
        try {
            redisClient
                    .set(buildKey(CacheKeyConstants.DEVICE_INFO_ID_KEY_PREFIX, deviceInfoBO.getId()), deviceInfoBO);
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils set deviceInfo by id cache error, deviceInfoBO:{}", deviceInfoBO, e);
        }
    }

    public void removeById(Long id) {
        try {
            redisClient.remove(buildKey(CacheKeyConstants.DEVICE_INFO_ID_KEY_PREFIX, id));
        } catch (Exception e) {
            log.error("DeviceInfoCacheUtils remove deviceInfo by id cache error, id:{}", id, e);
        }
    }

    private String buildKey(String keyPrefix, Long id) {
        return keyPrefix + id;
    }

}
