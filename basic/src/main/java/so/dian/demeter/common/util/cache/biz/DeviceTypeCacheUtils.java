package so.dian.demeter.common.util.cache.biz;

import com.alibaba.fastjson.JSON;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.demeter.common.constant.CacheKeyConstants;
import so.dian.demeter.pojo.bo.DeviceTypeBO;
import so.dian.eros.cache.RedisClient;

/**
 * DeviceTypeCacheUtils
 *
 * <AUTHOR>
 */
@Service
@Slf4j(topic = "error")
public class DeviceTypeCacheUtils {

    @Resource
    private RedisClient redisClient;

    public void setDeviceType(List<DeviceTypeBO> deviceTypeBOList) {
        if (Objects.isNull(deviceTypeBOList)) {
            return;
        }
        try {
            redisClient.set(CacheKeyConstants.DEVICE_TYPE_ALL_KEY, deviceTypeBOList);
        } catch (Exception e) {
            log.error("DeviceTypeCacheUtils set all deviceType cache error, deviceTypeList:{}",
                    JSON.toJSONString(deviceTypeBOList), e);
        }
    }

    public List<DeviceTypeBO> getDeviceType() {
        try {
            String cacheResult = redisClient.get(CacheKeyConstants.DEVICE_TYPE_ALL_KEY, String.class);
            return JSON.parseArray(cacheResult, DeviceTypeBO.class);
        } catch (Exception e) {
            log.error("DeviceTypeCacheUtils get all deviceType cache error", e);
        }
        return null;
    }

    public void removeDeviceTypeCache() {
        try {
            redisClient.remove(CacheKeyConstants.DEVICE_TYPE_ALL_KEY);
        } catch (Exception e) {
            log.error("DeviceTypeCacheUtils remove deviceType cache error", e);
        }
    }

}
