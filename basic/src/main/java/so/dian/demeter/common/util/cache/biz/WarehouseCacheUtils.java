package so.dian.demeter.common.util.cache.biz;

import com.alibaba.fastjson.JSON;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.demeter.common.constant.CacheKeyConstants;
import so.dian.demeter.pojo.bo.WarehouseBO;
import so.dian.eros.cache.RedisClient;

/**
 * WarehouseCacheUtils
 *
 * <AUTHOR>
 */
@Component
@Slf4j(topic = "error")
public class WarehouseCacheUtils {

    @Resource
    private RedisClient redisClient;

    public List<WarehouseBO> getByCooperatorId(Long cooperatorId) {
        try {
            String cacheResult = redisClient
                    .get(buildKey(CacheKeyConstants.WAREHOUSE_COOPERATOR_ID_KEY_PREFIX, cooperatorId), String.class);
            return JSON.parseArray(cacheResult, WarehouseBO.class);
        } catch (Exception e) {
            log.error("WarehouseCacheUtils get warehouse by cooperatorId cache error", e);
        }
        return null;
    }

    public void setByCooperatorId(Long cooperatorId, List<WarehouseBO> warehouseBOList) {
        if (Objects.isNull(warehouseBOList)) {
            return;
        }
        try {
            redisClient
                    .set(buildKey(CacheKeyConstants.WAREHOUSE_COOPERATOR_ID_KEY_PREFIX, cooperatorId), warehouseBOList);
        } catch (Exception e) {
            log.error(
                    "WarehouseCacheUtils set deviceType by cooperatorId cache error, cooperatorId:{}, warehouseList:{}",
                    cooperatorId, JSON.toJSONString(warehouseBOList), e);
        }
    }

    public void removeByCooperatorId(Long cooperatorId) {
        try {
            redisClient.remove(buildKey(CacheKeyConstants.WAREHOUSE_COOPERATOR_ID_KEY_PREFIX, cooperatorId));
        } catch (Exception e) {
            log.error("WarehouseCacheUtils remove warehouse by cooperatorId cache error, cooperatorId:{}", cooperatorId,
                    e);
        }
    }

    public WarehouseBO getById(Long id) {
        try {
            return redisClient.get(buildKey(CacheKeyConstants.WAREHOUSE_ID_CACHE_PREFIX, id), WarehouseBO.class);
        } catch (Exception e) {
            log.error("WarehouseCacheUtils get warehouse by cooperatorId cache error", e);
        }
        return null;
    }

    public void setById(WarehouseBO warehouseBO) {
        if (Objects.isNull(warehouseBO)) {
            return;
        }
        try {
            redisClient
                    .set(buildKey(CacheKeyConstants.WAREHOUSE_ID_CACHE_PREFIX, warehouseBO.getId()), warehouseBO);
        } catch (Exception e) {
            log.error("WarehouseCacheUtils set deviceType by id cache error, warehouseBO:{}", warehouseBO, e);
        }
    }

    public void removeById(Long id) {
        try {
            redisClient.remove(buildKey(CacheKeyConstants.WAREHOUSE_ID_CACHE_PREFIX, id));
        } catch (Exception e) {
            log.error("WarehouseCacheUtils remove warehouse by id cache error, id:{}", id, e);
        }
    }

    private String buildKey(String keyPrefix, Long id) {
        return keyPrefix + id;
    }

}
