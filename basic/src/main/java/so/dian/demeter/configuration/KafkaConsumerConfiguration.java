package so.dian.demeter.configuration;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@EnableKafka
public class KafkaConsumerConfiguration implements InitializingBean {

    @Autowired
    private KafkaProperties kafkaProperties;

    @Bean("pbTakeOutPutContainerFactory")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactory() {
        return concurrentFactory(kafkaProperties.getPbTakeoutPutinUri(), kafkaProperties.getPbTakeoutPutinGroup());
    }

    @Bean("deviceOnlineContainerFactory")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> lhckfkListenerContainerFactory() {
        return concurrentFactory(kafkaProperties.getDeviceOnlineUri(), kafkaProperties.getDeviceOnlineGroup());
    }

    private KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> concurrentFactory(String serverUrl, String groupId) {
        if(StringUtils.isBlank(serverUrl)) {
            return null;
        }
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory(serverUrl, groupId));
        factory.setConcurrency(kafkaProperties.getConcurrency());
        factory.getContainerProperties().setPollTimeout(1500);
        return factory;
    }

    private ConsumerFactory<String, Object> consumerFactory(String serverUri, String groupId) {
        Map<String, Object> propsMap = new HashMap<>(8);
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, serverUri);
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, kafkaProperties.isEnableAutoCommit());
        propsMap.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, kafkaProperties.getAutoCommitInterval());
        propsMap.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, kafkaProperties.getSessionTimeout());
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        propsMap.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, kafkaProperties.getAutoOffsetReset());
        return new DefaultKafkaConsumerFactory<>(propsMap);
    }

    @Override
    public void afterPropertiesSet() {
        if(StringUtils.isNotBlank(kafkaProperties.getPbTakeoutPutinTopic())) {
            System.setProperty("pbTakeoutPutinTopic", kafkaProperties.getPbTakeoutPutinTopic());
        } else {
            System.setProperty("pbTakeoutPutinTopic", "chargebolt_repair_biz_dev");
        }
        if(StringUtils.isNotBlank(kafkaProperties.getDeviceOnlineTopic())) {
            System.setProperty("deviceOnlineTopic", kafkaProperties.getDeviceOnlineTopic());
        } else {
            System.setProperty("deviceOnlineTopic", "adela_device_sync_dev");
        }
    }

}
