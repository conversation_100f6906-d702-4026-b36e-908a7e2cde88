package so.dian.demeter.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Description kafka消息配置
 * <AUTHOR>
 * @Date 2022/10/27
 * @Version 1.0
 **/
@Configuration
@Data
@ConfigurationProperties(prefix = "kafka.consume")
public class KafkaProperties {

    private boolean enableAutoCommit;
    private String sessionTimeout;
    private String autoCommitInterval;
    private String autoOffsetReset;
    private int concurrency;

    private String pbTakeoutPutinUri;
    private String pbTakeoutPutinTopic;
    private String pbTakeoutPutinGroup;

    private String deviceOnlineUri;
    private String deviceOnlineTopic;
    private String deviceOnlineGroup;
}
