package so.dian.demeter.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 检修相关配置
 * <AUTHOR>
 * @Date 2023/8/15
 * @Version 1.0
 **/
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "demeter.maintenance")
public class MaintenanceProperties {

    // 开仓超时
    @Value("${demeter.maintenance.slotOpen.timeOut:60}")
    private Integer slotOpenTimeOut;
    // 电量满足x时可以租借
    @Value("${demeter.maintenance.device.batteryRent:3700}")
    private Integer batteryRent;
    // 电量不足x 充电问题
    @Value("${demeter.maintenance.device.batteryChargingFail:3700}")
    private Integer batteryChargingFail;
    // 强制开仓x天提示一次
    @Value("${demeter.maintenance.forceOpen.tipsOnceAFewDays:1}")
    private Integer forceOpenTipsOnceAFewDays;
    @Value("#{'${demeter.maintenance.deviceInfo.supportWifi}'.split(',')}")
    private List<String> supportWifiTypes;
    @Value("#{'${demeter.maintenance.deviceInfo.supportBle}'.split(',')}")
    private List<String> supportBleTypes;
}
