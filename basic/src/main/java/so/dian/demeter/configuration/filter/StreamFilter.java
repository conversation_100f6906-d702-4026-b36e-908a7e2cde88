package so.dian.demeter.configuration.filter;

import java.io.IOException;
import java.util.Objects;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

/**
 * StreamFilter
 * <p>
 * 使用ContentCachingRequestWrapper来包装HttpServletRequest，
 * 使用ContentCachingResponseWrapper来包装HttpServletResponse
 *
 * <AUTHOR>
 * @desc 描述信息
 * @date 17/11/30
 */
//@WebFilter(filterName = "streamFilter", value = "/*")
//@Order(3)
public class StreamFilter implements Filter {

    private static final String FILE_CONTENT_TYPE = "multipart/form-data";
    private static final String METHOD_POST = "POST";
    private static final Logger rtLog = LoggerFactory.getLogger("rt");

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        String contentType = request.getContentType();
        HttpServletResponse resp = (HttpServletResponse) response;
        String method = req.getMethod();
        //传文件时不缓存request
        if (Objects.nonNull(contentType) && contentType.startsWith(FILE_CONTENT_TYPE) && METHOD_POST.equalsIgnoreCase(method)) {
            return;
        } else {
            ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(req);
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(resp);
            try {
                Long startTime = System.currentTimeMillis();
                chain.doFilter(requestWrapper, responseWrapper);
                if(!req.getRequestURI().contains(".")){
                    rtLog.info(req.getRequestURI() + "  " + (System.currentTimeMillis() - startTime) + "ms");
                }
            } finally {
                responseWrapper.copyBodyToResponse();
            }
        }
    }

    @Override
    public void destroy() {
    }
}
