package so.dian.demeter.configuration.interceptor;

import cn.hutool.core.date.DateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import so.dian.demeter.common.constant.CommonConstants;

/**
 * MybatisSqlInterceptor
 *
 * <AUTHOR>
 * @desc 描述信息
 * @date 18/4/10
 */
@Intercepts(
        {
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class,
                        Object.class, RowBounds.class, ResultHandler.class}),
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class,
                        Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
                @Signature(type = Executor.class, method = "update", args = {MappedStatement.class,
                        Object.class})
        }
)
@Slf4j(topic = "sqlRt")
public class SqlRtInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = null;
        if (invocation.getArgs().length > 1) {
            parameter = invocation.getArgs()[1];
        }
        String sqlId = mappedStatement.getId().replace(CommonConstants.BASE_DAO_PACKAGE_NAME, "");
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        Configuration configuration = mappedStatement.getConfiguration();

        long startTime = System.currentTimeMillis();
        Object result = null;
        try {
            result = invocation.proceed();
        } finally {
            long endTime = System.currentTimeMillis();
            long sqlCostTime = endTime - startTime;
            String sql = this.getSql(configuration, boundSql);
            this.formatSqlLog(mappedStatement.getSqlCommandType(), sqlId, sql, sqlCostTime, result);
        }

        return result;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }

    /**
     * 获取完整的sql语句
     */
    private String getSql(Configuration configuration, BoundSql boundSql) {
        // 输入sql字符串空判断
        String sql = boundSql.getSql();
        if (StringUtils.isBlank(sql)) {
            return "";
        }

        //美化sql
        sql = this.beautifySql(sql);

        //填充占位符, 目前基本不用mybatis存储过程调用,故此处不做考虑
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        if (!parameterMappings.isEmpty() && parameterObject != null) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = this.replacePlaceholder(sql, parameterObject);
            } else {
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                for (ParameterMapping parameterMapping : parameterMappings) {
                    String propertyName = parameterMapping.getProperty();
                    if (metaObject.hasGetter(propertyName)) {
                        Object obj = metaObject.getValue(propertyName);
                        sql = this.replacePlaceholder(sql, obj);
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        Object obj = boundSql.getAdditionalParameter(propertyName);
                        sql = this.replacePlaceholder(sql, obj);
                    }
                }
            }
        }
        return sql;
    }

    /**
     * 多空行、空白匹配...
     */
    public final static String EMPTY_FORMAT = "[\\s\n ]+";

    /**
     * 美化Sql
     */
    private String beautifySql(String sql) {
        return sql.replaceAll(EMPTY_FORMAT, " ");
    }

    /**
     * SQL占位符?匹配
     */
    public final static String SQL_PLACEHOLDER_FORMAT = "\\?";

    /**
     * 填充占位符?
     */
    private String replacePlaceholder(String sql, Object parameterObject) {
        String result;
        if (parameterObject instanceof String) {
            result = "'" + parameterObject.toString() + "'";
        } else if (parameterObject instanceof Date) {
            result = "'" + new DateTime((Date) parameterObject).toString() + "'";
        } else if (Objects.isNull(parameterObject)) {
            result = "null";
        } else {
            result = parameterObject.toString();
        }
        return sql.replaceFirst(SQL_PLACEHOLDER_FORMAT, result);
    }

    /**
     * 格式化sql日志
     */
    private void formatSqlLog(SqlCommandType sqlCommandType, String sqlId, String sql, long costTime,
            Object obj) {
        String sqlLog = sqlId + "|" + sql + "|" + costTime;
        if (sqlCommandType == SqlCommandType.UPDATE || sqlCommandType == SqlCommandType.INSERT
                || sqlCommandType == SqlCommandType.DELETE) {
            sqlLog += "|" + (Objects.isNull(obj) ? '0' : Integer.valueOf(obj.toString()));
        }
        if (costTime < 1000L) {
            log.info("INFO|" + sqlLog);
        } else if (costTime < 3000L) {
            log.warn("WARN|" + sqlLog);
        } else {
            log.error("ERROR|" + sqlLog);
        }

    }
}
