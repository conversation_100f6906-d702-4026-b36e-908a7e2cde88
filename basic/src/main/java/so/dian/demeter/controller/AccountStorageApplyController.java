package so.dian.demeter.controller;



import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.AccountStorageApplyFacade;
import so.dian.demeter.client.api.AccountStorageApplyApi;
import so.dian.demeter.client.dto.AccountStorageApplyDetailDTO;
import so.dian.demeter.client.dto.AccountStorageApplyListDTO;
import so.dian.demeter.client.dto.AccountStorageBackApplyDetailDTO;
import so.dian.demeter.client.dto.AccountStorageBackApplyListDTO;
import so.dian.demeter.client.dto.ApplyNoDTO;
import so.dian.demeter.client.dto.PageData;
import so.dian.demeter.client.param.AccountStorageApplyLostParam;
import so.dian.demeter.client.param.AccountStorageApplyOutcomeParam;
import so.dian.demeter.client.param.AccountStorageApplyParam;
import so.dian.demeter.client.param.AccountStorageApplyBackParam;
import so.dian.demeter.client.param.AccountStorageApplyRejectParam;
import so.dian.demeter.client.param.AccountStorageApplyRemoteQuery;
import so.dian.demeter.client.param.AccountStorageBackApplyRemoteQuery;
import so.dian.demeter.client.param.AccountStorageConfirmBackApplyParam;

/**
 * AccountStorageApplyController
 *
 * <AUTHOR>
 */
@Tag(name = "AccountStorageApplyController", description = "account storage apply API")
@RestController
public class AccountStorageApplyController implements AccountStorageApplyApi {

    @Resource
    private AccountStorageApplyFacade accountStorageApplyFacade;

    @Operation(summary = "校验是否可申领备件")
    @Override
    public BizResult<Object> checkCanApply(@RequestParam("cooperatorId") Long cooperatorId,
            @RequestParam("accountId") Long accountId) {
        return accountStorageApplyFacade.checkCanApply(cooperatorId, accountId);
    }

    @Operation(summary = "小二确认申领备件")
    @Override
    public BizResult<ApplyNoDTO> confirmApply(@RequestBody AccountStorageApplyParam param) {
        return accountStorageApplyFacade.confirmApply(param);
    }

    @Operation(summary = "小二申领备件出库")
    @Override
    public BizResult<Object> confirmApplyOutcome(@RequestBody AccountStorageApplyOutcomeParam param) {
        return accountStorageApplyFacade.confirmApplyOutcome(param);
    }

    @Operation(summary = "拒绝小二申领备件申请")
    @Override
    public BizResult<Object> reject(@RequestBody AccountStorageApplyRejectParam param) {
        return accountStorageApplyFacade.reject(param);
    }

    @Operation(summary = "申报遗失")
    @Override
    public BizResult<ApplyNoDTO> applyLost(@RequestBody AccountStorageApplyLostParam param) {
        return accountStorageApplyFacade.applyLost(param);
    }

    @Operation(summary = "退回仓库申请")
    @Override
    public BizResult<ApplyNoDTO> applyBack(@RequestBody AccountStorageApplyBackParam param) {
        return accountStorageApplyFacade.applyBack(param);
    }

    @Operation(summary = "查询小二备件申领工单列表")
    @Override
    public BizResult<PageData<AccountStorageApplyListDTO>> pageApply(
            @RequestBody AccountStorageApplyRemoteQuery query) {
        return accountStorageApplyFacade.pageApply(query);
    }

    @Operation(summary = "查询小二备件申领工单详情")
    @Override
    public BizResult<AccountStorageApplyDetailDTO> getDetailByApplyNo(@RequestParam("cooperatorId") Long cooperatorId,
            @RequestParam("applyNo") String applyNo) {
        return accountStorageApplyFacade.getDetailByApplyNo(cooperatorId, applyNo);
    }

    @Operation(summary = "查询小二备件退回工单列表")
    @Override
    public BizResult<PageData<AccountStorageBackApplyListDTO>> pageBackApply(
            @RequestBody AccountStorageBackApplyRemoteQuery query) {
        return accountStorageApplyFacade.pageBackApply(query);
    }

    @Operation(summary = "查询小二备件退回工单详情")
    @Override
    public BizResult<AccountStorageBackApplyDetailDTO> getBackApplyDetailByApplyNo(
            @RequestParam("cooperatorId") Long cooperatorId, @RequestParam("applyNo") String applyNo) {
        return accountStorageApplyFacade.getBackApplyDetailByApplyNo(cooperatorId, applyNo);
    }

    @Operation(summary = "确认小二退回备件入库")
    @Override
    public BizResult<Object> confirmBackToWarehouse(@RequestBody AccountStorageConfirmBackApplyParam param) {
        return accountStorageApplyFacade.confirmBackToWarehouse(param);
    }
}
