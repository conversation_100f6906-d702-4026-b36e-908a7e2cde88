package so.dian.demeter.controller;



import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.AccountStorageFacade;
import so.dian.demeter.client.api.AccountStorageApi;
import so.dian.demeter.client.dto.AccountStorageInfoDTO;

/**
 * AccountStorageController
 *
 * <AUTHOR>
 */
@Tag(name = "AccountStorageController", description = "account storage API")
@RestController
public class AccountStorageController implements AccountStorageApi {

    @Resource
    private AccountStorageFacade accountStorageFacade;

    @Operation(summary = "查询备件库信息")
    @Override
    public BizResult<AccountStorageInfoDTO> getByAccountId(@RequestParam("cooperatorId") Long cooperatorId,
            @RequestParam("accountId") Long accountId) {
        return accountStorageFacade.getByAccountId(cooperatorId, accountId);
    }
}
