package so.dian.demeter.controller;



import java.util.List;
import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.DeviceFacade;
import so.dian.demeter.biz.service.DeviceInfoService;
import so.dian.demeter.biz.service.DeviceTypeService;
import so.dian.demeter.biz.service.WarehouseService;
import so.dian.demeter.client.param.ExportShopParam;
import so.dian.demeter.common.util.cache.biz.DeviceInfoCacheUtils;
import so.dian.demeter.job.CollectShopBoxInfoJob;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.demeter.pojo.bo.WarehouseBO;
import so.dian.demeter.pojo.param.AddDeviceInfoParam;
import so.dian.demeter.pojo.param.AddDeviceTypeParam;
import so.dian.demeter.pojo.bo.DeviceTypeBO;
import so.dian.demeter.pojo.param.AddWarehouseParam;

/**
 * TestController
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "测试接口", description = "测试接口")
public class DemeterTestController {

    @Resource
    private DeviceTypeService deviceTypeService;
    @Resource
    private DeviceInfoService deviceInfoService;
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private DeviceInfoCacheUtils deviceInfoCacheUtils;
//    @Resource
//    private CalculateBoxOrderJob calculateBoxOrderJob;
    @Resource
    private DeviceFacade deviceFacade;
    @Resource
    private CollectShopBoxInfoJob collectShopBoxInfoJob;


    //-----------------设备类型相关---------------------------
    @Operation(summary = "初始化设备类型配置")
    @PostMapping("/test/deviceType/add")
    public BizResult<Boolean> addDeviceType(@RequestBody List<AddDeviceTypeParam> addDeviceTypeParamList) {
        return BizResult.create(deviceTypeService.addDeviceType(addDeviceTypeParamList));
    }

    @Operation(summary = "获取所有设备类型配置")
    @GetMapping("/test/deviceType/getAll")
    public BizResult<List<DeviceTypeBO>> getAllDeviceType() {
        return BizResult.create(deviceTypeService.getAll());
    }

    @Operation(summary = "根据id删除设备类型配置")
    @PostMapping("/test/deviceType/deleteByIdList")
    public BizResult<Boolean> deleteDeviceTypeByIdList(@RequestBody List<Long> idList) {
        return BizResult.create(deviceTypeService.deleteByIdList(idList));
    }
    //----------------设备信息相关----------------------------
    @Operation(summary = "初始化设备信息")
    @PostMapping("/test/deviceInfo/add")
    public BizResult<Boolean> addDeviceInfo(@RequestBody List<AddDeviceInfoParam> addDeviceInfoParamList) {
        return BizResult.create(deviceInfoService.addDeviceInfo(addDeviceInfoParamList));
    }

    @Operation(summary = "根据id删除设备信息")
    @PostMapping("/test/deviceInfo/deleteByIdList")
    public BizResult<Boolean> deleteDeviceInfoByIdList(@RequestBody List<Long> idList) {
        return BizResult.create(deviceInfoService.deleteByIdList(idList));
    }
    @Operation(summary = "根据合作方id获取设备信息")
    @GetMapping("/test/deviceInfo/getByCooperatorId")
    public BizResult<List<DeviceInfoBO>> getDeviceInfoByCooperatorId(@RequestParam("cooperatorId") Long cooperatorId) {
        return BizResult.create(deviceInfoService.getByCooperatorId(cooperatorId));
    }

    //----------------仓库信息相关----------------------------
    @Operation(summary = "初始化仓库信息")
    @PostMapping("/test/warehouse/add")
    public BizResult<List<WarehouseBO>> addWarehouse(@RequestBody List<AddWarehouseParam> addWarehouseParamList) {
        return BizResult.create(warehouseService.addWarehouse(addWarehouseParamList));
    }

    @Operation(summary = "根据id获取仓库信息")
    @GetMapping("/test/warehouse/getById")
    public BizResult<WarehouseBO> getById(@RequestParam("id") Long id) {
        return BizResult.create(warehouseService.getById(id));
    }

    @Operation(summary = "根据合作方id获取仓库信息")
    @GetMapping("/test/warehouse/getByCooperatorId")
    public BizResult<List<WarehouseBO>> getByCooperatorId(@RequestParam("cooperatorId") Long cooperatorId) {
        WarehouseBO warehouseBO = warehouseService.getOneByCooperatorId(cooperatorId);
        deviceInfoService.getById(1L);
        return BizResult.create(warehouseService.getByCooperatorId(cooperatorId));
    }

    @Operation(summary = "根据id删除仓库信息")
    @PostMapping("/test/warehouse/deleteByIdList")
    public BizResult<Boolean> deleteWarehouseByIdList(@RequestBody List<Long> idList) {
        return BizResult.create(warehouseService.deleteByIdList(idList));
    }

    @Operation(summary = "根据id删除仓库信息")
    @GetMapping("/test/deviceInfo/removeCacheByCooperatorId")
    public BizResult<Boolean> removeCacheByCooperatorId(@RequestParam("cooperatorId") Long cooperatorId) {
        deviceInfoCacheUtils.removeByCooperatorId(cooperatorId);
        deviceInfoCacheUtils.removeDeviceInfoCache();
        return BizResult.create(true);
    }

//    @Operation(summary = "初始化盒子订单数据接口")
//    @GetMapping("/test/device/initBoxOrderInfo")
//    public BizResult initBoxOrderInfo() {
//        try {
//            calculateBoxOrderJob.init();
//        } catch (Exception e) {
//            return BizResult.create(e);
//        }
//        return BizResult.create(true);
//    }

    @Operation(summary = "获取盒子导出数据")
    @GetMapping("/test/device/getExcelData")
    public BizResult getExcelData(@RequestBody ExportShopParam exportShopParam) {
        try {
            return BizResult.create(deviceFacade.export(exportShopParam));
        } catch (Exception e) {
            return BizResult.create(e);
        }
    }

    @GetMapping("/test/device/initShopBoxInfo")
    public BizResult initShopBoxInfo() {
        try {
            collectShopBoxInfoJob.run();
        } catch (Exception e) {
            return BizResult.create(e);
        }
        return BizResult.create(true);
    }

    @Operation(summary = "根据Redis Key获取对应数据")
    @GetMapping("/test/redisList")
    public BizResult getRedisList(@RequestParam("key") String key) {
        return deviceFacade.testRedisList(key);
    }

    @Operation(summary = "根据Redis Key获取对应数据")
    @GetMapping("/test/redisMap")
    public BizResult getRedisMap(@RequestParam("key") String key) {
        return deviceFacade.testRedisMap(key);
    }

    @Operation(summary = "根据Redis Key获取对应数据")
    @GetMapping("/test/redisString")
    public BizResult getRedisString(@RequestParam("key") String key) {
        return deviceFacade.testRedisString(key);
    }

    @Operation(summary = "根据deviceNo获取对应数据")
    @GetMapping("/test/device")
    public BizResult getDeviceByNo(@RequestParam("deviceNo") String deviceNo) {
        return deviceFacade.testGetDeviceByNo(deviceNo);
    }

    @Operation(summary = "根据Redis Key删除对应Map数据")
    @DeleteMapping("/test/deleteMap")
    public BizResult deleteMap(@RequestParam("key") String key) {
        return deviceFacade.testDeleteMap(key);
    }

    @Operation(summary = "根据Redis Key unlock")
    @GetMapping("/test/unlock")
    public BizResult unlock(@RequestParam("key") String key) {
        deviceFacade.testUnlock(key);
        return BizResult.create(null);
    }
}
