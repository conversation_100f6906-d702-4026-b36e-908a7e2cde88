package so.dian.demeter.controller;



import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.DeviceFacade;
import so.dian.demeter.client.dto.BoxComprehensiveDTO;
import so.dian.demeter.client.dto.BoxOrderForShopDetailDTO;
import so.dian.demeter.client.dto.BoxPanelDTO;
import so.dian.demeter.client.dto.BoxPanelPowerBankDTO;
import so.dian.demeter.client.dto.BoxTotalOrderDTO;
import so.dian.demeter.client.dto.DeviceDTO;
import so.dian.demeter.client.dto.DeviceWithStatusDetailDTO;
import so.dian.demeter.client.dto.PageData;
import so.dian.demeter.client.dto.PowerBankDTO;
import so.dian.demeter.client.dto.ShopDeviceInfoWithShopDTO;
import so.dian.demeter.client.dto.SimpleBoxInfoDTO;
import so.dian.demeter.client.param.DevicePageParam;
import so.dian.demeter.client.param.DeviceWithSlotQuery;
import so.dian.demeter.client.param.ExportShopParam;
import so.dian.demeter.client.param.ShopDeviceQuery;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * DeviceController
 *
 * <AUTHOR>
 */
@Tag(name = "DeviceController", description = "Device API")
@RestController
public class DeviceController/* implements DeviceApi*/ {

    @Resource
    private DeviceFacade deviceFacade;

    @Operation(summary = "根据设备编号和操作类型查询盒子信息")
    public BizResult<DeviceWithStatusDetailDTO> getForInstallAndRecycleByDeviceNo(
            @RequestBody DeviceWithSlotQuery query) {
        return deviceFacade.getForInstallAndRecycleByDeviceNo(query);
    }

    @Operation(summary = "获取门店设备信息")
    public BizResult<List<ShopDeviceInfoWithShopDTO>> getShopDeviceInfo(@RequestBody ShopDeviceQuery query) {
        return deviceFacade.getShopDeviceInfo(query);
    }

    @Operation(summary = "获取门店设备状态信息")
    @PostMapping("/demeter/device/getShopDeviceStatusInfo")
    public BizResult<List<SimpleBoxInfoDTO>> getShopSimpleBoxInfo(@RequestBody ShopDeviceQuery query) {
        return deviceFacade.getShopSimpleBoxInfo(query);
    }

    @Operation(summary = "根据设备编号查询门店设备状态信息")
    @GetMapping("/demeter/device/getShopDeviceInfoByDeviceNo")
    public BizResult<SimpleBoxInfoDTO> getShopSimpleBoxInfoByDeviceNo(@RequestParam("deviceNo") String deviceNo) {
        return deviceFacade.getShopSimpleBoxInfoByDeviceNo(deviceNo);
    }

    @Operation(summary = "根据个人权限获取所有的盒子信息")
    public BizResult<List<BoxPanelDTO>> getAllBoxWithAeacus(@RequestParam("shopIds") List<Long> shopIds) {
        return BizResult.create(deviceFacade.getBoxPanelWithAeacus(shopIds));
    }

    @Operation(summary = "根据盒子编号获取所有充电宝信息")
    public BizResult<List<BoxPanelPowerBankDTO>> getPowerBankByDeviceNo(@RequestParam("deviceNo") String deviceNo) {
        return BizResult.create(deviceFacade.getPowerBankByDeviceNo(deviceNo));
    }

    @Operation(summary = "根据充电宝编号获取充电宝信息")
    public BizResult<Map<String, List<BoxPanelPowerBankDTO>>> getPowerBankByDeviceNos(@RequestParam("deviceNos")  List<String> deviceNos) {
        return BizResult.create(deviceFacade.getPowerBankByDeviceNos(deviceNos));
    }

    @Operation(summary = "导出门店数据")
    public BizResult<Map<Long, Map<String, BoxComprehensiveDTO>>> export(@RequestBody ExportShopParam param) {
        return BizResult.create(deviceFacade.export(param));
    }

    @Operation(summary = "根据门店ID获取盒子相关信息")
    public BizResult<List<BoxOrderForShopDetailDTO>> getBoxInfoByShopId(@RequestParam("shopId") Long shopId) {
        return BizResult.create(deviceFacade.getBoxInfoByShopId(shopId));
    }

    @Operation(summary = "根据门店ID、时间间隔获取盒子总订单相关信息")
    public BizResult<BoxTotalOrderDTO> getTotalOrderInfo(@RequestParam("shopId") Long shopId,
                                                               @RequestParam("startTime") String startTime,
                                                               @RequestParam("endTime") String endTime) {
        return BizResult.create(deviceFacade.getTotalOrderInfo(shopId, startTime, endTime));
    }
}
