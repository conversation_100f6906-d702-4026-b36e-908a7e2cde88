package so.dian.demeter.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.service.DeviceFaultInfoService;
import so.dian.demeter.client.api.DeviceFaultInfoApi;
import so.dian.demeter.pojo.entity.DeviceFaultInfoDO;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 故障信息上报模块
 *
 * @Author: Yungu email:<EMAIL>
 * @Date: 2021/5/31 5:45 下午
 */
@Slf4j
@RestController
public class DeviceFaultInfoController /*implements DeviceFaultInfoApi*/ {

    @Resource
    private DeviceFaultInfoService deviceFaultInfoService;
    /**
     * 故障信息上报接口
     * @param deviceNo
     * @param describe
     * @param reportTime
     * @param reportName
     * @param status
     * @return
     */
    @RequestMapping("/demeter/device/fault/add")
    public BizResult<Object> deviceFaultInfoAdd(String deviceNo, String describe, String reportTime, String reportName, String status) {
        DeviceFaultInfoDO deviceFaultInfoDO = new DeviceFaultInfoDO();
        deviceFaultInfoDO.setCreateTime(new Date());
        deviceFaultInfoDO.setDeviceNo(deviceNo);
        deviceFaultInfoDO.setReportName(reportName);
        deviceFaultInfoDO.setReportTime(reportTime);
        deviceFaultInfoDO.setDescribe(describe);
        deviceFaultInfoDO.setStatus(status);
        deviceFaultInfoDO.setGmtCreate(System.currentTimeMillis());
        deviceFaultInfoDO.setGmtUpdate(System.currentTimeMillis());
        return BizResult.create(deviceFaultInfoService.insert(deviceFaultInfoDO));
    }

    public BizResult<Object> deviceFaultInfoQuery(String deviceNo, String beginTime, String endTime, Integer pageNum, Integer pageSize) {
        return BizResult.create(deviceFaultInfoService.queryByParam(deviceNo, beginTime, endTime, pageNum, pageSize));
    }
}
