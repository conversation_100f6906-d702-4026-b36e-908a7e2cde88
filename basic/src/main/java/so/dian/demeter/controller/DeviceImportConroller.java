package so.dian.demeter.controller;


import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.service.DeviceImportService;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class DeviceImportConroller {

    @Resource
    private DeviceImportService deviceImportService;

    @Operation(summary = "批量导入设别")
    @PostMapping("/device/import/device")
    BizResult importDevice(@RequestBody List<String> deviceNos) {
        deviceImportService.importDevices(deviceNos);
        return BizResult.create(null);
    }

    @Operation(summary = "批量导入充电宝")
    @PostMapping("/device/import/charger")
    BizResult importCharger(@RequestBody List<String> chargerNos) {
        deviceImportService.importChargers(chargerNos);
        return BizResult.create(null);
    }
}
