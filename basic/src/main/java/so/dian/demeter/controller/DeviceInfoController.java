package so.dian.demeter.controller;



import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.DeviceInfoFacade;
import so.dian.demeter.client.api.DeviceInfoApi;
import so.dian.demeter.client.dto.DeviceInfoDTO;
import so.dian.demeter.client.dto.PageData;
import so.dian.demeter.client.dto.ProductSelectDTO;

import javax.annotation.Resource;

/**
 * DeviceInfoController
 *
 * <AUTHOR>
 */
@Tag(name = "DeviceInfoController", description = "Device info API")
@RestController
public class DeviceInfoController implements DeviceInfoApi {

    @Resource
    private DeviceInfoFacade deviceInfoFacade;

    @Operation(summary = "根据设备分类和设备编号查询设备产品信息")
    @Override
    public BizResult<DeviceInfoDTO> getByDeviceNo(@RequestParam("operateType") Integer operateType,@RequestParam("cooperatorId") Long cooperatorId,
            @RequestParam("classifyType") Integer classifyType, @RequestParam("deviceNo") String deviceNo) {
        return deviceInfoFacade.getByDeviceNo(operateType, cooperatorId, classifyType, deviceNo);
    }

    @Operation(summary = "根据合作方id查询设备信息用于备件申领选择")
    @Override
    public BizResult<PageData<ProductSelectDTO>> listDeviceInfoForApply(@RequestParam("cooperatorId") Long cooperatorId) {
        return deviceInfoFacade.listDeviceInfoForApply(cooperatorId);
    }
}
