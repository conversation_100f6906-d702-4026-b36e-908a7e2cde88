package so.dian.demeter.controller;

import com.alibaba.fastjson.JSON;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.DeviceLogFacade;
import so.dian.demeter.client.api.DeviceLogApi;
import so.dian.demeter.client.dto.DeviceInfoForLogDTO;
import so.dian.demeter.client.dto.DeviceLogDTO;
import so.dian.demeter.client.dto.PowerBankUseLogDTO;
import so.dian.demeter.client.param.DeviceLogParam;
import so.dian.demeter.client.param.PowerBankUseLogParam;

import javax.annotation.Resource;

/**
 * DeviceInfoController
 */
@Tag(name = "DeviceInfoController", description = "Device info API")
@RestController
@Slf4j
public class DeviceLogController implements DeviceLogApi {

    @Resource
    private DeviceLogFacade deviceLogFacade;

    @Operation(summary = "分页获取设备信息")
    @Override
    public BizResult<DeviceInfoForLogDTO> getDeviceForPage(@RequestBody DeviceLogParam deviceLogParam) {
        log.info("DeviceLogController getDeviceForPage. param:{}", JSON.toJSONString(deviceLogParam));
        return BizResult.create(deviceLogFacade.getDeviceInfoForLog(deviceLogParam));
    }

    @Operation(summary = "分页获取安装、回收记录")
    @Override
    public BizResult<DeviceLogDTO> getLogForPage(@RequestParam("deviceNo") String deviceNo, @RequestParam("type") Integer type,
                                                 @RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum) {
        log.info("DeviceLogController getLogForPage. deviceNo:{}, type:{}, pageSize:{}, pageNum:{}", deviceNo, type, pageSize, pageNum);
        return BizResult.create(deviceLogFacade.getLogForPage(deviceNo, type, pageNum, pageSize));
    }
}
