package so.dian.demeter.controller;



import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.DeviceFacade;
import so.dian.demeter.client.api.DeviceOperateApi;
import so.dian.demeter.client.dto.StorageDeviceOperateDTO;
import so.dian.demeter.client.param.DeviceInstallParam;
import so.dian.demeter.client.param.DeviceRecycleParam;

import java.util.List;

/**
 * DeviceController
 *
 * <AUTHOR>
 */
@Tag(name = "DeviceOperateController", description = "Device operate API")
@RestController
public class DeviceOperateController implements DeviceOperateApi {

    @Resource
    private DeviceFacade deviceFacade;

    @Operation(summary = "设备安装")
    @Override
    public BizResult<Object> install(@RequestBody DeviceInstallParam param) {
        return deviceFacade.install(param);
    }

    @Operation(summary = "设备回收")
    @Override
    public BizResult<Object> recycle(@RequestBody DeviceRecycleParam param) {
        return deviceFacade.recycle(param);
    }

    @Operation(summary = "根据门店ID查询门店安装回收记录")
    @Override
    public BizResult<List<StorageDeviceOperateDTO>> getDeviceOperateByShopId(Integer operateType, Long cooperatorId, Long shopId) {
        return BizResult.create(deviceFacade.queryByShopId(operateType, cooperatorId, shopId));
    }
}
