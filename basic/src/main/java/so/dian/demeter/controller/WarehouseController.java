package so.dian.demeter.controller;



import java.util.List;
import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.biz.facade.WarehouseFacade;
import so.dian.demeter.biz.service.DeviceInfoService;
import so.dian.demeter.biz.service.DeviceTypeService;
import so.dian.demeter.biz.service.WarehouseService;
import so.dian.demeter.client.api.WarehouseApi;
import so.dian.demeter.client.dto.WarehouseInfoDTO;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.demeter.pojo.bo.DeviceTypeBO;
import so.dian.demeter.pojo.bo.WarehouseBO;
import so.dian.demeter.pojo.param.AddDeviceInfoParam;
import so.dian.demeter.pojo.param.AddDeviceTypeParam;
import so.dian.demeter.pojo.param.AddWarehouseParam;

/**
 * WarehouseController
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "仓库接口", description = "仓库接口")
public class WarehouseController implements WarehouseApi {

    @Resource
    private WarehouseFacade warehouseFacade;

    @Operation(summary = "根据合作方id查询仓库")
    @Override
    public BizResult<WarehouseInfoDTO> getByCooperatorId(@RequestParam("cooperatorId") Long cooperatorId) {
        return warehouseFacade.getByCooperatorId(cooperatorId);
    }
}
