package so.dian.demeter.controller;



import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.sf.jsqlparser.expression.operators.relational.OldOracleJoinBinaryExpression;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.demeter.biz.facade.WarehouseIncomeFacade;
import so.dian.demeter.biz.service.DeviceInfoService;
import so.dian.demeter.client.api.WarehouseIncomeApi;
import so.dian.demeter.client.dto.ApplyNoDTO;
import so.dian.demeter.client.dto.DeviceApplyDTO;
import so.dian.demeter.client.dto.PageData;
import so.dian.demeter.client.dto.WarehouseIncomeApplyListDTO;
import so.dian.demeter.client.dto.WarehouseIncomeDetailDTO;
import so.dian.demeter.client.param.WarehouseIncomeApplyRemoteParam;
import so.dian.demeter.client.param.WarehouseIncomeApplyRemoteQuery;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.eros.common.exception.Exec;
import so.dian.mofa3.lang.enums.ResponseCodeEnum;
import so.dian.mofa3.lang.exception.CheckParamException;

import java.util.Arrays;
import java.util.Objects;

/**
 * WarehouseIncomeController
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "仓库入库接口", description = "仓库入库接口")
public class WarehouseIncomeController implements WarehouseIncomeApi {

    @Resource
    private WarehouseIncomeFacade warehouseIncomeFacade;
    @Resource
    private DeviceInfoService deviceInfoService;


    @Operation(summary = "仓库入库列表")
    @Override
    public BizResult<PageData<WarehouseIncomeApplyListDTO>> pageApply(@RequestBody WarehouseIncomeApplyRemoteQuery query) {
        return warehouseIncomeFacade.pageApply(query);
    }

    @Operation(summary = "仓库入库工单详情")
    @Override
    public BizResult<WarehouseIncomeDetailDTO> getApplyDetail(@RequestParam("cooperatorId") Long cooperatorId,
            @RequestParam("applyNo") String applyNo) {
        return warehouseIncomeFacade.getApplyDetail(cooperatorId, applyNo);
    }

    @Operation(summary = "确认仓库入库")
    @Override
    public BizResult<ApplyNoDTO> applyWarehouseIncome(@RequestBody WarehouseIncomeApplyRemoteParam param) {
        return warehouseIncomeFacade.applyWarehouseIncome(param);
    }

    @Override
    public BizResult<ApplyNoDTO> warehouseDeviceImport(final DeviceApplyDTO request) {
        if(Objects.isNull(request.getDeviceInfoId())){
            return BizResult.error(Exec.INVALID_PARAM,"设备类型ID不能为空");
        }
        if(Objects.isNull(request.getAgentId())){
            return BizResult.error(Exec.INVALID_PARAM,"agentID不能为空");
        }
        if(Objects.isNull(request.getSellerId())){
            return BizResult.error(Exec.INVALID_PARAM,"sellerID不能为空");
        }
        DeviceInfoBO deviceInfoBO=deviceInfoService.getById(request.getDeviceInfoId());
        if(Objects.isNull(deviceInfoBO)){
            return BizResult.error(Exec.INVALID_PARAM,"设备类型ID不存在："+request.getDeviceInfoId());
        }
        WarehouseIncomeApplyRemoteParam param= new WarehouseIncomeApplyRemoteParam();
        param.setCooperatorId(1L);
        param.setOperatorId(1L);
        param.setAgentId(request.getAgentId());
        param.setSellerId(request.getSellerId());
        param.setDeviceGroup(Arrays.asList(request));
        return warehouseIncomeFacade.warehouseDeviceImport(param);
    }
}
