package so.dian.demeter.dao.rds;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.demeter.pojo.entity.AccountStorageInfoDO;

/**
 * AccountStorageInfoMapper
 *
 * <AUTHOR>
 * @date 2019/3/9
 */
public interface AccountStorageInfoMapper {

    int insert(AccountStorageInfoDO storageInfoDO);

    AccountStorageInfoDO getById(@Param("id") Long id);

    AccountStorageInfoDO getByCooperatorIdAndAccountId(@Param("cooperatorId") Long cooperatorId,
            @Param("accountId") Long accountId);

    int deleteByIdList(@Param("idList") List<Long> idList, @Param("updateTime") Date updateTime);

}
