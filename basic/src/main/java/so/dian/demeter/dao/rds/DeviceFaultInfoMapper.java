package so.dian.demeter.dao.rds;

import org.apache.ibatis.annotations.Param;
import java.util.List;
import so.dian.demeter.pojo.entity.DeviceFaultInfoDO;

public interface DeviceFaultInfoMapper {

    int insert(@Param("pojo") DeviceFaultInfoDO pojo);

    int insertList(@Param("pojos") List< DeviceFaultInfoDO> pojo);

    List<DeviceFaultInfoDO> select(@Param("pojo") DeviceFaultInfoDO pojo);

    int update(@Param("pojo") DeviceFaultInfoDO pojo);

    /**
     * 故障信息分页查询
     * @param deviceNo
     * @param beginTime
     * @param endTime
     * @return
     */
    List<DeviceFaultInfoDO> queryByParam(@Param("deviceNo") String deviceNo, @Param("beginTime") String beginTime, @Param("endTime") String endTime);
}
