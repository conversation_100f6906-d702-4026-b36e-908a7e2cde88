package so.dian.demeter.dao.rds;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.demeter.pojo.entity.ShopBoxDO;
import so.dian.demeter.pojo.entity.StorageDeviceOperateDO;

/**
 * StorageDeviceOperateMapper
 *
 * <AUTHOR>
 */
public interface StorageDeviceOperateMapper {

    int batchInsert(List<StorageDeviceOperateDO> doList);

    List<StorageDeviceOperateDO> listInstallOperate(@Param("deviceNoList") List<String> deviceNoList,
            @Param("cooperatorId") Long cooperatorId);

    StorageDeviceOperateDO getOneInstallOperateByDeviceNo(@Param("deviceNo") String deviceNo);

    /**
     * 根据门店ID查询安装回收记录
     * @param operateType int
     * @param cooperatorId Long
     * @param shopId Long
     * @return List<StorageDeviceOperateDO>
     */
    List<StorageDeviceOperateDO> queryByShopId(
                                                @Param("operateType") Integer operateType,
                                                @Param("cooperatorId") Long cooperatorId,
                                                @Param("shopId") Long shopId);

    /**
     * 查询门店第一台盒子安装信息
     * @param shopIds List<Long>
     * @return List<StorageDeviceOperateDO>
     */
    List<StorageDeviceOperateDO> queryFirstInstalledInfo(@Param("shopIds") List<Long> shopIds);

    /**
     * 查询门店第一台盒子回收信息
     * @param deviceNos List<String>
     * @return StorageDeviceOperateDO
     */
    List<StorageDeviceOperateDO> queryFirstUnInstalledInfo(@Param("deviceNos") List<String> deviceNos,
                                                           @Param("shopIds") List<Long> shopIds);

    /**
     * 根据设备编号查询安装回收记录
     * @param operateType int
     * @param deviceNo String
     * @return List<StorageDeviceOperateDO>
     */
    List<StorageDeviceOperateDO> queryByDeviceNo(@Param("operateType") Integer operateType, @Param("deviceNo") String deviceNo,
                                                 @Param("startNum") Integer startNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据设备编号计算安装回收记录条数
     * @param operateType int
     * @param deviceNo String
     * @return Integer
     */
    Integer countByDeviceNo(@Param("operateType") Integer operateType, @Param("deviceNo") String deviceNo);
}
