package so.dian.demeter.dao.rds;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.demeter.pojo.entity.WarehouseIncomeDO;
import so.dian.demeter.pojo.entity.WarehouseIncomeDeviceDO;

/**
 * WarehouseIncomeDeviceMapper
 *
 * <AUTHOR>
 */
public interface WarehouseIncomeDeviceMapper {

    /**
     * 批量插入入库仓库设备记录
     */
    int batchInsert(List<WarehouseIncomeDeviceDO> warehouseIncomeDeviceDOList);

    /**
     * 根据入库统计记录id查询
     */
    List<WarehouseIncomeDeviceDO> listByIncomeCollectIdList(
            @Param("incomeCollectIdList") List<Long> incomeCollectIdList);

}
