package so.dian.demeter.dao.rds;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.demeter.pojo.entity.WarehouseIncomeDO;
import so.dian.demeter.pojo.param.WarehouseIncomeApplyQuery;

/**
 * WarehouseIncomeMapper
 *
 * <AUTHOR>
 */
public interface WarehouseIncomeMapper {

    /**
     * 批量插入入库仓库工单记录
     */
    int batchInsert(List<WarehouseIncomeDO> warehouseIncomeDOList);

    /**
     * 根据条件查询符合条件的记录数
     */
    Long countByApplyQuery(WarehouseIncomeApplyQuery query);

    /**
     * 根据条件查询符合条件的记录
     */
    List<WarehouseIncomeDO> listByApplyQuery(WarehouseIncomeApplyQuery query);

    /**
     * 根据工单号查询
     */
    WarehouseIncomeDO getByApplyNo(@Param("applyNo") String applyNo, @Param("cooperatorId") Long cooperatorId);

}
