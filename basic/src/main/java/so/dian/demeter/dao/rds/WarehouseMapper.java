package so.dian.demeter.dao.rds;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import so.dian.demeter.pojo.entity.WarehouseDO;

/**
 * WarehouseMapper
 *
 * <AUTHOR>
 */
public interface WarehouseMapper {

    /**
     * 批量插入仓库记录
     */
    int batchInsert(List<WarehouseDO> warehouseDOList);

    /**
     * 根据id查询
     */
    WarehouseDO getById(@Param("id") Long id);

    /**
     * 根据合作方id查询
     */
    List<WarehouseDO> getByCooperatorId(@Param("cooperatorId") Long cooperatorId);

    /**
     * 根据id列表删除记录
     */
    int deleteByIdList(@Param("idList") List<Long> idList, @Param("updateTime") Date updateTime);

    List<WarehouseDO> listByIdList(@Param("idList") List<Long> idList);

    /**
     * 根据仓库名称+合作方id查询
     */
    List<WarehouseDO> getByName(@Param("name") String name, @Param("cooperatorId") Long cooperatorId);
}
