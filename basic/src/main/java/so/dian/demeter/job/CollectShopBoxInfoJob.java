package so.dian.demeter.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import so.dian.demeter.biz.service.DeviceInfoService;
import so.dian.demeter.biz.service.DeviceService;
import so.dian.demeter.biz.service.StorageDeviceOperateService;
import so.dian.demeter.client.dto.ShopBoxDTO;
import so.dian.demeter.common.constant.CommonConstants;
import so.dian.demeter.common.util.LocalDateUtils;
import so.dian.demeter.pojo.bo.DeviceBO;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.demeter.pojo.converter.ShopBoxConverter;
import so.dian.demeter.pojo.entity.ShopBoxDO;
import so.dian.demeter.pojo.entity.StorageDeviceOperateDO;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class CollectShopBoxInfoJob {

    @Resource
    private DeviceService deviceService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    private StorageDeviceOperateService storageDeviceOperateService;

    private final static String LOCK_NAME = "demeter$SHOP_BOX_INFO_LOCK";
    private final static Long LOCK_TIME = 5*60L;

    // 每天晚上23:55分执行
    @Scheduled(cron = "0 55 23 * * ?")
    public void run () {
        RLock lock = redissonClient.getLock(LOCK_NAME);
        try {
            // 尝试获取锁
            boolean locked = lock.tryLock(LOCK_TIME, TimeUnit.SECONDS);
            if (locked) {
                // 执行任务
                collectShopBoxInfo();
            }
        } catch (Exception e) {
            log.error("CollectShopBoxInfoJob exception:{}", e.getMessage());
        } finally {
            // 释放锁
            lock.unlock();
        }
    }

    /**
     * 定时更新盒子基本信息（已安装盒子数、第一台盒子安装时间、第一台盒子安装天数）
     */
    private void collectShopBoxInfo () {
        // 1. 获取所有设备信息
        List<DeviceInfoBO> deviceInfoBOS = deviceInfoService.getAll();
        if (CollectionUtils.isEmpty(deviceInfoBOS)) {
            log.info("设备信息(deviceInfo)为空");
            return;
        }

        // 2. 获取所有盒子类型的设备信息的id
        List<Long> deviceInfoIds = new ArrayList<>();
        for (DeviceInfoBO bo : deviceInfoBOS) {
            if (!Objects.equals(CommonConstants.POWER_BANK_SUB_DEVICE_TYPE, bo.getSubDeviceType())) {
                deviceInfoIds.add(bo.getId());
            }
        }

        // 3. 获取所有有安装盒子的门店ID
        List<Long> shopIds = deviceService.queryAllShopId(deviceInfoIds);

        if (CollectionUtils.isEmpty(shopIds)) {
            return;
        }

        // 按门店ID获取门店盒子
        List<DeviceBO> deviceBOS = deviceService.listByShopIdAndDeviceInfoId(shopIds, deviceInfoIds);
        Map<Long, List<String>> shopDeviceNoMap = new HashMap<>();
        for (DeviceBO deviceBO : deviceBOS) {
            List<String> deviceNos = shopDeviceNoMap.get(deviceBO.getStorageId());
            if (CollectionUtils.isEmpty(deviceNos)) {
                deviceNos = new ArrayList<>();
                deviceNos.add(deviceBO.getDeviceNo());
                shopDeviceNoMap.put(deviceBO.getStorageId(), deviceNos);
                continue;
            }
            deviceNos.add(deviceBO.getDeviceNo());
            shopDeviceNoMap.put(deviceBO.getStorageId(), deviceNos);
        }
        // 4. 查询门店安装盒子个数
        List<ShopBoxDO> shopBoxDOS = deviceService.countBoxByShopId(shopIds, deviceInfoIds);
        Map<Long, ShopBoxDTO> shopBoxDTOMap = new HashMap<>(shopBoxDOS.size());
        shopBoxDOS.forEach(shopBoxDO -> shopBoxDTOMap.put(shopBoxDO.getShopId(), ShopBoxConverter.convertDO2DTO(shopBoxDO)));

        // 5. 查询门店第一台盒子安装信息
        List<StorageDeviceOperateDO> firstInstalledInfoDOS = storageDeviceOperateService.queryFirstInstalledInfo(shopIds);

        List<String> deviceNoList = new ArrayList<>();
        List<Long> shopIdParam = new ArrayList<>();

        // 6. 设置第一台盒子安装时间
        firstInstalledInfoDOS.forEach(storageDeviceOperateDO -> {
            deviceNoList.add(storageDeviceOperateDO.getDeviceNo());
            shopIdParam.add(storageDeviceOperateDO.getShopId());
            ShopBoxDTO tempDTO = shopBoxDTOMap.get(storageDeviceOperateDO.getShopId());
            if (null != tempDTO) {
                tempDTO.setFirstBoxInstalledTime(storageDeviceOperateDO.getCreateTime());
                shopBoxDTOMap.put(storageDeviceOperateDO.getShopId(), tempDTO);
            }
        });

        // 7. 查询门店第一台盒子回收信息
        List<StorageDeviceOperateDO> firstUnInstalledInfoDOS = storageDeviceOperateService.queryFirstUnInstalledInfo(deviceNoList, shopIdParam);

        // 获取当天日期作为hashMap的key
        String mapKey = LocalDateUtils.getDateStr(LocalDateUtils.now(), LocalDateUtils.DATE_DAY_PATTERN);

        // 8. 计算第一台盒子安装时间
        for (Map.Entry<Long, ShopBoxDTO> entry : shopBoxDTOMap.entrySet()) {
            RMap<String, ShopBoxDTO> rMap = redissonClient.getMap(getRedisKey(String.valueOf(entry.getKey())));
            ShopBoxDTO tempDTO = entry.getValue();
            tempDTO.setDeviceNos(shopDeviceNoMap.get(entry.getKey()));
            // 判断第一台盒子是否被回收
            StorageDeviceOperateDO firstUnInstalledInfoDO = checkFirstBoxIsUnInstalled(firstUnInstalledInfoDOS, tempDTO.getShopId());
            if (null != firstUnInstalledInfoDO) {
                // 计算时间差
                tempDTO.setFirstBoxInstalledDays(LocalDateUtils.differentDays(tempDTO.getFirstBoxInstalledTime(), firstUnInstalledInfoDO.getCreateTime()));
            } else {
                tempDTO.setFirstBoxInstalledDays(LocalDateUtils.differentDays(tempDTO.getFirstBoxInstalledTime(), LocalDateUtils.now()));
            }
            rMap.put(mapKey, tempDTO);
        }
    }


    /**
     * 判断门店第一台盒子是否被回收
     * @param firstUnInstalledInfoDOS 门店第一台盒子回收信息
     * @param shopId 门店ID
     * @return StorageDeviceOperateDO
     */
    private StorageDeviceOperateDO checkFirstBoxIsUnInstalled (List<StorageDeviceOperateDO> firstUnInstalledInfoDOS, Long shopId) {
        if (CollectionUtils.isEmpty(firstUnInstalledInfoDOS)) {
            return null;
        }
        for (StorageDeviceOperateDO storageDeviceOperateDO : firstUnInstalledInfoDOS) {
            if (storageDeviceOperateDO.getShopId().equals(shopId)) {
                return storageDeviceOperateDO;
            }
        }
        return null;
    }

    /**
     * 返回redis-key
     * @param redisKey String
     * @return String
     */
    private String getRedisKey(String redisKey) {
        return CommonConstants.REDIS_KEY_SHOP_BOX_INFO + redisKey;
    }

}
