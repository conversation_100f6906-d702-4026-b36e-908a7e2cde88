package so.dian.demeter.pojo.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.apollo.common.constant.device.BoxConstants.BoxStatusEnum;
import so.dian.demeter.common.enums.OnlineStatusEnum;
import so.dian.demeter.common.enums.OnlineTypeEnum;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * ApolloBoxInfoBO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApolloBoxInfoBO {
    // 设备编码
    private String deviceNo;
    private String deviceType;
    private BoxStatusEnum status;
    // 在线状态
    private OnlineStatusEnum onlineStatus;
    // 在线类型
    private OnlineTypeEnum onlineType;
    // 信号量
    private Integer signalValue;
    // 最后一次离线时间
    private Date lastOfflineTime;
    // 配置wifi名称
    private String wifiName;
    // wifi开启状态
    private Integer wifiStatus;
    // 蓝牙开启状态
    private Integer commBleStatus;
    // 是否支持蓝牙租借
    @Builder.Default
    private boolean supportBleOpenSlot = false;
    // 锁定仓位
    private Set<Integer> lockedSlotIndexes;
    // 禁用仓位
    private Set<Integer> disableSlotIndexes;
    // 仓位信息
    private List<BoxSlotInfoBO> slotInfoList;

    /**
     * 获取信号量
     *
     * @return
     */
    public Integer getSemaphore() {
        if (signalValue == null || signalValue <= 31) {
            return 1;
        } else if (signalValue <= 47) {
            return 2;
        } else if (signalValue <= 63) {
            return 3;
        } else if (signalValue <= 78) {
            return 4;
        }
        return 5;
    }

}
