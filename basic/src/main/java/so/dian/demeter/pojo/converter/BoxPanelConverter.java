package so.dian.demeter.pojo.converter;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import so.dian.apollo.dto.result.box.ApolloBoxDTO;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.demeter.client.dto.BoxPanelDTO;
import so.dian.demeter.client.dto.BoxPanelPowerBankDTO;
import so.dian.demeter.pojo.bo.BoxPanelBO;

import java.util.List;

/**
 * BoxPanelConverter
 *
 * <AUTHOR>
 */
public class BoxPanelConverter {

    public static BoxPanelDTO convertBO2DTO(BoxPanelBO boxPanelBO) {
        BoxPanelDTO boxPanelDTO = new BoxPanelDTO();
        boxPanelDTO.setPowerBankCount(boxPanelBO.getPowerBankCount());
        boxPanelDTO.setDeviceNo(boxPanelBO.getDeviceNo());
        boxPanelDTO.setShopId(boxPanelBO.getShopId());
        boxPanelDTO.setSignalValue(boxPanelBO.getSignalValue());
        boxPanelDTO.setStatus(boxPanelBO.getStatus());
        boxPanelDTO.setLastOfflineTime(boxPanelBO.getLastOfflineTime());
        boxPanelDTO.setDeviceType(boxPanelBO.getDeviceType());
        return boxPanelDTO;
    }

    public static List<BoxPanelDTO> convertBOs2DTOs(List<BoxPanelBO> boxPanelBOS) {
        if (CollectionUtils.isEmpty(boxPanelBOS)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(boxPanelBOS, BoxPanelConverter::convertBO2DTO);
    }

    public static BoxPanelPowerBankDTO convertSlotInfo2PowerBankDTO(ApolloBoxDTO.SlotInfo slotInfo) {
        BoxPanelPowerBankDTO boxPanelPowerBankDTO = new BoxPanelPowerBankDTO();
        boxPanelPowerBankDTO.setBattery(slotInfo.getBattery());
        boxPanelPowerBankDTO.setPowerBankNo(slotInfo.getPowerBankNo());
        boxPanelPowerBankDTO.setPowerBankStatus(slotInfo.getPowerBankStatus());
        boxPanelPowerBankDTO.setSlotId(slotInfo.getSlotId());
        boxPanelPowerBankDTO.setSlotStatus(slotInfo.getSlotStatus());
        return boxPanelPowerBankDTO;
    }

    public static List<BoxPanelPowerBankDTO> convertSlotInfo2PowerBankDTO(List<ApolloBoxDTO.SlotInfo> slotInfos) {
        if (CollectionUtils.isEmpty(slotInfos)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(slotInfos, BoxPanelConverter::convertSlotInfo2PowerBankDTO);
    }
}
