package so.dian.demeter.pojo.converter;

import so.dian.eros.interceptor.ThreadLanguageHolder;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import so.dian.commons.eden.enums.DeletedEnum;
import so.dian.demeter.client.dto.DeviceStorageLogDTO;
import so.dian.demeter.client.enu.ActionTypeEnum;
import so.dian.demeter.client.enu.DeviceOperateTypeEnum;
import so.dian.demeter.client.enu.DriveEnum;
import so.dian.demeter.common.enums.DeviceStorageTypeEnum;
import so.dian.demeter.common.util.LocalDateUtils;
import so.dian.demeter.mq.device.dto.PositionCalcResult;
import so.dian.demeter.mq.device.dto.PowerBankEventMonitorDTO;
import so.dian.demeter.pojo.bo.DeviceOperateBO;
import so.dian.demeter.pojo.entity.DeviceDO;
import so.dian.demeter.pojo.entity.DeviceStorageLogDO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 资产日志转换
 * <AUTHOR>
 * @Date 2023/12/26
 * @Version 1.0
 **/
public class DeviceStorageLogConverter {

    public static List<DeviceStorageLogDO> buildInitLogDOs(List<DeviceDO> deviceDOList, String relateNo, Long bizTimestamp, Long operateId) {
        ActionTypeEnum.SubType subType = ActionTypeEnum.SubType.WAREHOUSE_IN;
        List<DeviceStorageLogDO> logDOList = Lists.newArrayList();
        Long now = LocalDateUtils.now().getTime();
        for (DeviceDO deviceDO : deviceDOList) {
            DeviceStorageLogDO logDO = new DeviceStorageLogDO();
            logDO.setDeviceNo(deviceDO.getDeviceNo());
            logDO.setActionType(subType.getParentCode());
            logDO.setActionSubType(subType.getCode());
            logDO.setActionName(subType.getName());
            logDO.setStorageType(deviceDO.getStorageType());
            logDO.setStorageNo(deviceDO.getStorageId().toString());
            logDO.setStorageName(deviceDO.getStorageName());
            logDO.setContainerNo(deviceDO.getContainerNo());
            logDO.setPreStorageType(DeviceStorageTypeEnum.UNKNOW.getCode());
            logDO.setPreStorageNo("0");
            logDO.setPreStorageName("-");
            logDO.setPreContainerNo(deviceDO.getContainerNo());
            logDO.setBizTimestamp(bizTimestamp);
            logDO.setDrive(DriveEnum.ORDER.getCode());
            logDO.setMsgId(relateNo);
            logDO.setRemark("");
            logDO.setRelateOrderNo(relateNo);
            logDO.setOperatorId(operateId);
            logDO.setOperatorName(deviceDO.getOperateName());
            logDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
            logDO.setGmtCreate(now);
            logDO.setGmtUpdate(now);
            logDOList.add(logDO);
        }
        return logDOList;
    }

    public static List<DeviceStorageLogDO> buildLogDOs(DeviceOperateBO operateBO, List<DeviceDO> deviceDOList) {
        ActionTypeEnum.SubType subType = null;
        if (DeviceOperateTypeEnum.INSTALL.equals(operateBO.getOperateTypeEnum())) {
            subType = ActionTypeEnum.SubType.BIND;
        } else if (DeviceOperateTypeEnum.RECYCLE.equals(operateBO.getOperateTypeEnum())) {
            subType = ActionTypeEnum.SubType.UNBIND;
        } else {
            return Collections.emptyList();
        }
        List<DeviceStorageLogDO> logDOList = Lists.newArrayList();
        Long now = LocalDateUtils.now().getTime();
        for (DeviceDO deviceDO : deviceDOList) {
            DeviceStorageLogDO logDO = new DeviceStorageLogDO();
            logDO.setDeviceNo(deviceDO.getDeviceNo());
            logDO.setActionType(subType.getParentCode());
            logDO.setActionSubType(subType.getCode());
            logDO.setActionName(subType.getName());
            logDO.setStorageType(operateBO.getTargetStorageType());
            logDO.setStorageNo(operateBO.getTargetStorageId().toString());
            logDO.setStorageName(operateBO.getTargetStorageName());
            logDO.setContainerNo(deviceDO.getContainerNo());
            logDO.setPreStorageType(deviceDO.getStorageType());
            logDO.setPreStorageNo(deviceDO.getStorageId().toString());
            logDO.setPreStorageName(deviceDO.getStorageName());
            logDO.setPreContainerNo(deviceDO.getContainerNo());
            logDO.setBizTimestamp(operateBO.getOperateTime().getTime());
            logDO.setDrive(DriveEnum.ORDER.getCode());
            logDO.setMsgId(operateBO.getRelateNo());
            if (StringUtils.isNotBlank(operateBO.getRemark()) && operateBO.getRemark().length() > 32) {
                logDO.setRemark(operateBO.getRemark().substring(0, 32));
            } else {
                logDO.setRemark(operateBO.getRemark());
            }
            logDO.setRelateOrderNo(operateBO.getRelateNo());
            logDO.setOperatorId(operateBO.getOperateId());
            logDO.setOperatorName(operateBO.getOperateName());
            logDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
            logDO.setGmtCreate(now);
            logDO.setGmtUpdate(now);
            logDOList.add(logDO);
        }
        return logDOList;
    }

    public static DeviceStorageLogDO buildLogDO(PositionCalcResult calcResult, PowerBankEventMonitorDTO eventDTO) {
        Long now = LocalDateUtils.now().getTime();

        DeviceStorageLogDO logDO = new DeviceStorageLogDO();
        logDO.setDeviceNo(calcResult.getDeviceNo());
        logDO.setBizTimestamp(calcResult.getPositionTimeStamp());
        logDO.setMsgId(eventDTO.getMsgId());
        logDO.setDrive(DriveEnum.EVENT.getCode());
        logDO.setActionType(calcResult.getActionSubType().getParentCode());
        logDO.setActionSubType(calcResult.getActionSubType().getCode());
        logDO.setActionName(calcResult.getActionSubType().getName());
        logDO.setPreStorageType(calcResult.getPreStorageType().getCode());
        logDO.setPreStorageNo(calcResult.getPreStorageId().toString());
        logDO.setPreStorageName(calcResult.getPreStorageName());
        logDO.setPreContainerNo(calcResult.getPreContainerNo());
        logDO.setStorageType(calcResult.getStorageType().getCode());
        logDO.setStorageNo(calcResult.getStorageId().toString());
        logDO.setStorageName(calcResult.getStorageName());
        logDO.setContainerNo(calcResult.getContainerNo());
        logDO.setRelateOrderNo(calcResult.getRelateOrderNo());
        logDO.setOperatorId(calcResult.getOperatorId());
        logDO.setOperatorName(calcResult.getOperatorName());
        logDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
        logDO.setGmtCreate(now);
        logDO.setGmtUpdate(now);
        return logDO;
    }

    public static List<DeviceStorageLogDTO> convertDOToDTO(List<DeviceStorageLogDO> storageLogDOS) {
        if (CollectionUtils.isEmpty(storageLogDOS)) {
            return Collections.emptyList();
        }
        String lang = ThreadLanguageHolder.getCurrentLang();
        return storageLogDOS.stream().map(storageLogDO -> {
            DeviceStorageLogDTO storageLogDTO = new DeviceStorageLogDTO();
            BeanUtils.copyProperties(storageLogDO, storageLogDTO);
            if (!StringUtils.equalsIgnoreCase("zh-cn", lang)) {
                ActionTypeEnum.SubType actionSubType = ActionTypeEnum.getSubTypeByCode(storageLogDO.getActionSubType());
                storageLogDTO.setActionName(actionSubType!=null?actionSubType.getEnName():storageLogDO.getActionName());
            }
            return storageLogDTO;
        }).collect(Collectors.toList());
    }
}
