package so.dian.demeter.pojo.converter;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalListUtils;
import so.dian.demeter.client.dto.DeviceLogForDeviceInfoDTO;
import so.dian.demeter.client.dto.DeviceLogForLogDTO;
import so.dian.demeter.client.dto.StorageDeviceOperateDTO;
import so.dian.demeter.client.enu.DeviceOperateTypeEnum;
import so.dian.demeter.client.enu.DeviceRecycleReasonEnum;
import so.dian.demeter.client.param.DeviceInstallParam;
import so.dian.demeter.client.param.DeviceRecycleParam;
import so.dian.demeter.common.util.LocalDateUtils;
import so.dian.demeter.common.util.biz.BizNoUtils;
import so.dian.demeter.pojo.bo.ApolloBoxInfoBO;
import so.dian.demeter.pojo.bo.StorageDeviceOperateBO;
import so.dian.demeter.pojo.entity.StorageDeviceOperateDO;

/**
 * StorageDeviceOperateConverter
 *
 * <AUTHOR>
 */
public class StorageDeviceOperateConverter {

    public static StorageDeviceOperateDO buildInstallForInsert(DeviceInstallParam param) {
        StorageDeviceOperateDO result = new StorageDeviceOperateDO();
        result.setCooperatorId(param.getCooperatorId());
        result.setAccountId(param.getAccountId());
        result.setApplyNo(BizNoUtils.buildDeviceOperateApplyNo(DeviceOperateTypeEnum.INSTALL, param.getAccountId()));
        result.setOperateType(DeviceOperateTypeEnum.INSTALL.getCode());
        result.setOperateTime(LocalDateUtils.now());
        result.setShopId(param.getShopId());
        result.setReason(0);
        result.setReasonAddition("");
        result.setDeviceNo(param.getDeviceNo());
        BaseConverter.buildForInsert(result);
        return result;
    }

    public static StorageDeviceOperateDO buildRecycleForInsert(DeviceRecycleParam param) {
        StorageDeviceOperateDO result = new StorageDeviceOperateDO();
        result.setCooperatorId(param.getCooperatorId());
        result.setAccountId(param.getAccountId());
        result.setApplyNo(BizNoUtils.buildDeviceOperateApplyNo(DeviceOperateTypeEnum.RECYCLE, param.getAccountId()));
        result.setOperateType(DeviceOperateTypeEnum.RECYCLE.getCode());
        result.setOperateTime(LocalDateUtils.now());
        result.setShopId(param.getShopId());
        result.setReason(param.getReason());
        result.setReasonAddition(param.getReasonAddition());
        if (StringUtils.isBlank(param.getReasonAddition())) {
            result.setReasonAddition("");
        }
        result.setDeviceNo(param.getDeviceNo());
        BaseConverter.buildForInsert(result);
        return result;
    }

    public static List<StorageDeviceOperateBO> convertDO2BO(List<StorageDeviceOperateDO> operateDOList) {
        if(CollectionUtils.isEmpty(operateDOList)) {
            return Lists.newArrayList();
        }
        return LocalListUtils.transferList(operateDOList, StorageDeviceOperateConverter::convertDO2BO);
    }

    public static StorageDeviceOperateBO convertDO2BO(StorageDeviceOperateDO operateDO) {
        if (Objects.isNull(operateDO)) {
            return null;
        }
        StorageDeviceOperateBO operateBO = new StorageDeviceOperateBO();
        operateBO.setId(operateDO.getId());
        operateBO.setCooperatorId(operateDO.getCooperatorId());
        operateBO.setAccountId(operateDO.getAccountId());
        operateBO.setApplyNo(operateDO.getApplyNo());
        operateBO.setOperateType(LocalEnumUtils.findByCode(DeviceOperateTypeEnum.class, operateDO.getOperateType()));
        operateBO.setOperateTime(operateDO.getOperateTime());
        operateBO.setShopId(operateDO.getShopId());
        operateBO.setReason(LocalEnumUtils.findByCode(DeviceRecycleReasonEnum.class, operateDO.getReason()));
        operateBO.setReasonAddition(operateDO.getReasonAddition());
        operateBO.setDeviceNo(operateDO.getDeviceNo());
        return operateBO;
    }

    public static StorageDeviceOperateDTO convertDO2DTO(StorageDeviceOperateDO operateDO) {
        if (Objects.isNull(operateDO)) {
            return null;
        }
        StorageDeviceOperateDTO operateDTO = new StorageDeviceOperateDTO();
        operateDTO.setId(operateDO.getId());
        operateDTO.setCooperatorId(operateDO.getCooperatorId());
        operateDTO.setAccountId(operateDO.getAccountId());
        operateDTO.setApplyNo(operateDO.getApplyNo());
        operateDTO.setOperateType(operateDO.getOperateType());
        operateDTO.setOperateTime(operateDO.getOperateTime());
        operateDTO.setShopId(operateDO.getShopId());
        operateDTO.setReason(operateDO.getReason());
        operateDTO.setReasonAddition(operateDO.getReasonAddition());
        operateDTO.setDeviceNo(operateDO.getDeviceNo());
        return operateDTO;
    }

    public static List<StorageDeviceOperateDTO> convertDO2DTO(List<StorageDeviceOperateDO> operateDOS) {
        if (CollectionUtils.isEmpty(operateDOS)) {
            return null;
        }
        List<StorageDeviceOperateDTO> deviceOperateDTOS = new ArrayList<>();
        for (StorageDeviceOperateDO deviceOperateDO : operateDOS) {
            deviceOperateDTOS.add(convertDO2DTO(deviceOperateDO));
        }
        return deviceOperateDTOS;
    }

    public static DeviceLogForLogDTO convertDO2DeviceLogForDeviceInfoDTO (StorageDeviceOperateDO operateDO) {
        if (null == operateDO) {
            return null;
        }
        DeviceLogForLogDTO deviceLogForLogDTO = new DeviceLogForLogDTO();
        deviceLogForLogDTO.setCreateTime(operateDO.getCreateTime());
        deviceLogForLogDTO.setOperatorId(operateDO.getAccountId());
        deviceLogForLogDTO.setShopId(operateDO.getShopId());
        return deviceLogForLogDTO;
    }

    public static List<DeviceLogForLogDTO> convertDO2DeviceLogForDeviceInfoDTOList (List<StorageDeviceOperateDO> operateDOs) {
        if (CollectionUtils.isEmpty(operateDOs)) {
            return new ArrayList<>();
        }
        List<DeviceLogForLogDTO> deviceLogForLogDTOS = new ArrayList<>(operateDOs.size());
        for (StorageDeviceOperateDO operateDO : operateDOs) {
            deviceLogForLogDTOS.add(convertDO2DeviceLogForDeviceInfoDTO(operateDO));
        }
        return deviceLogForLogDTOS;
    }
}
