package so.dian.demeter.pojo.validator;

import java.util.Objects;

import org.apache.commons.lang.StringUtils;

import com.chargebolt.aeacus.common.exception.I18nMessageException;

import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.commons.eden.util.LocalObjectUtils;
import so.dian.demeter.client.enu.DeviceRecycleReasonEnum;
import so.dian.demeter.client.param.DeviceInstallParam;
import so.dian.demeter.client.param.DeviceRecycleParam;
import so.dian.demeter.common.exception.BizErrorCodeEnum;

/**
 * DeviceValidator
 *
 * <AUTHOR>
 */
public class DeviceValidator {

    public static void validDeviceInstallParam(DeviceInstallParam param) {
        if(Objects.isNull(param)) {
            throw new I18nMessageException(BizErrorCodeEnum.PARAMS_ERROR);
        }
        if(LocalObjectUtils.anyNull(param.getAccountId(), param.getCooperatorId(), param.getShopId())) {
            throw new I18nMessageException(BizErrorCodeEnum.PARAMS_ERROR);
        }
        if(StringUtils.isBlank(param.getDeviceNo())) {
            throw new I18nMessageException(BizErrorCodeEnum.PARAMS_ERROR);
        }
    }

    public static void validDeviceRecycleParam(DeviceRecycleParam param) {
        validDeviceInstallParam(param);
        DeviceRecycleReasonEnum reason = LocalEnumUtils.findByCode(DeviceRecycleReasonEnum.class, param.getReason());
        if(Objects.isNull(reason)) {
            throw new I18nMessageException(BizErrorCodeEnum.PARAMS_ERROR);
        }
        if(Objects.equals(DeviceRecycleReasonEnum.OTHER, reason)) {
            if(StringUtils.isBlank(param.getReasonAddition())) {
                throw new I18nMessageException(BizErrorCodeEnum.PARAMS_ERROR);
            }
        }
    }

}
