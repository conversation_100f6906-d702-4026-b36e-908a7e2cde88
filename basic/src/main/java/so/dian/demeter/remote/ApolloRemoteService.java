package so.dian.demeter.remote;

import com.alibaba.fastjson.JSON;
import feign.Headers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.apollo.common.constant.header.HttpHeaderConstants;
import so.dian.apollo.common.entity.BizResult;
import so.dian.apollo.dto.param.ApolloDeviceParamDTO;
import so.dian.apollo.dto.param.box.ApolloBoxParamDTO;
import so.dian.apollo.dto.param.powerbank.ApolloPowerBankParamDTO;
import so.dian.apollo.dto.result.box.ApolloBoxDTO;
import so.dian.apollo.dto.result.device.ApolloDeviceDTO;
import so.dian.apollo.dto.result.device.DeviceCommandRequest;
import so.dian.apollo.dto.result.powerbank.ApolloPowerBankDTO;
import so.dian.demeter.remote.ApolloRemoteService.ApolloRemoteServiceFallback;

import java.util.Map;

/**
 * ApolloRemoteService
 *
 * <AUTHOR>
 */
@FeignClient(url = "${remote.url.apollo}", name = "adela",contextId = "adela2",fallback = ApolloRemoteServiceFallback.class)
public interface ApolloRemoteService {

    /**
     * 获取盒子信息
     *
     * @return 设备信息
     * @RequestParam authToken         token校验
     * @RequestParam apolloBoxParamDTO 参数对象
     */
    @PostMapping("/1.0/inner/box/get")
    BizResult<ApolloBoxDTO> get(@RequestParam("authToken") String authToken, @RequestBody ApolloBoxParamDTO apolloBoxParamDTO);

    /**
     * 根据设备编号批量获取盒子信息
     *
     * @return 设备信息
     * @RequestParam authToken         token校验
     * @RequestParam apolloBoxParamDTO 参数对象
     */
    @PostMapping("/1.0/inner/box/gets")
    BizResult<Map<String, ApolloBoxDTO>> gets(@RequestParam("authToken") String authToken,
                                           @RequestBody ApolloBoxParamDTO apolloBoxParamDTO);

    /**
     * 打开盒子仓位
     *
     * @return 设备信息
     * @RequestParam authToken         token校验
     * @RequestParam apolloBoxParamDTO 参数对象
     */
    @PostMapping("/1.0/inner/box/openSlot")
    BizResult<ApolloBoxDTO> openSlot(@RequestParam("authToken") String authToken,
                                  @RequestBody ApolloBoxParamDTO apolloBoxParamDTO);

    /**
     * 强制打开仓位
     *
     * @return 设备信息
     * @RequestParam authToken         token校验
     * @RequestParam apolloBoxParamDTO 参数对象
     */
    @PostMapping("/1.0/inner/box/force/open")
    BizResult<ApolloBoxDTO> boxForceOpen(@RequestParam("authToken") String authToken,
                                      @RequestBody ApolloBoxParamDTO apolloBoxParamDTO);

    /**
     * 关闭盒子仓位
     *
     * @return 设备信息
     * @RequestParam authToken         token校验
     * @RequestParam apolloBoxParamDTO 参数对象
     */
    @PostMapping("/1.0/inner/box/closeSlot")
    BizResult<ApolloBoxDTO> closeSlot(@RequestParam("authToken") String authToken,
                                   @RequestBody ApolloBoxParamDTO apolloBoxParamDTO);

    /**
     * 设置盒子仓位损坏(不可用)
     *
     * @return 请求结果
     * @RequestParam authToken         token校验
     * @RequestParam apolloBoxParamDTO 参数对象
     */
    @PostMapping("/1.0/inner/box/setSlotUsable")
    BizResult<ApolloBoxDTO> setSlotUsable(@RequestParam("authToken") String authToken,
                                       @RequestParam("operUser") String operUser,
                                       @RequestParam("reason") String reason,
                                       @RequestBody ApolloBoxParamDTO apolloBoxParamDTO);

    /**
     * 设置wifi命令
     *
     * @param req
     * @param authToken
     * @param reason
     * @return
     */
    @PostMapping("/1.0/inner/device/operation/command")
    BizResult sendCommand(@RequestBody DeviceCommandRequest req, @RequestParam("authToken") String authToken, @RequestParam("reason") String reason);

    @Headers({"Content-Type: application/json", HttpHeaderConstants.XDCLOUD_GATEWAY_TOKEN + ": {apolloToken}"})
    @PostMapping("/1.0/inner/device/baseInfo/get")
    BizResult<ApolloDeviceDTO> getBaseInfo(@RequestHeader("apolloToken") String apolloToken, @RequestBody ApolloDeviceParamDTO apolloDeviceParamDTO);


    @Headers({"Content-Type: application/json", HttpHeaderConstants.XDCLOUD_GATEWAY_TOKEN + ": {apolloToken}"})
    @PostMapping("/1.0/inner/box/get")
    BizResult<ApolloBoxDTO> getBox(@RequestHeader("apolloToken") String apolloToken, @RequestBody ApolloBoxParamDTO apolloBoxParamDTO);

    @Headers({"Content-Type: application/json", HttpHeaderConstants.XDCLOUD_GATEWAY_TOKEN + ": {apolloToken}"})
    @PostMapping("/1.0/inner/powerBank/get")
    BizResult<ApolloPowerBankDTO> getPowerBank(@RequestHeader("apolloToken") String apolloToken, @RequestBody ApolloPowerBankParamDTO apolloPowerBankParamDTO);

    @Service
    @Slf4j(topic = "error")
    class ApolloRemoteServiceFallback implements ApolloRemoteService {

        @Override
        public BizResult<ApolloBoxDTO> get(String apolloToken,
                ApolloBoxParamDTO apolloBoxParamDTO) {
            log.error("|apolloRemoteService fallback| getBox |apolloBoxParamDTO:{}", JSON.toJSONString(apolloBoxParamDTO));
            return BizResult.create();
        }

        @Override
        public BizResult<Map<String, ApolloBoxDTO>> gets(String apolloToken, ApolloBoxParamDTO apolloBoxParamDTO) {
            log.error("|apolloRemoteService fallback| getBoxList |apolloBoxParamDTO:{}", JSON.toJSONString(apolloBoxParamDTO));
            return BizResult.create();
        }

        @Override
        public BizResult<ApolloBoxDTO> openSlot(String var1, ApolloBoxParamDTO apolloBoxParamDTO) {
            log.error("|apolloRemoteService fallback| openSlot |apolloBoxParamDTO:{}", JSON.toJSONString(apolloBoxParamDTO));
            return BizResult.create();
        }

        @Override
        public BizResult<ApolloBoxDTO> boxForceOpen(String authToken, ApolloBoxParamDTO apolloBoxParamDTO) {
            log.error("|apolloRemoteService fallback| boxForceOpen |apolloBoxParamDTO:{}", JSON.toJSONString(apolloBoxParamDTO));
            return BizResult.create();
        }

        @Override
        public BizResult<ApolloBoxDTO> closeSlot(String var1, ApolloBoxParamDTO apolloBoxParamDTO) {
            log.error("|apolloRemoteService fallback| closeSlot |apolloBoxParamDTO:{}", JSON.toJSONString(apolloBoxParamDTO));
            return BizResult.create();
        }

        @Override
        public BizResult<ApolloBoxDTO> setSlotUsable(String authToken, String operUser, String reason, ApolloBoxParamDTO apolloBoxParamDTO) {
            log.error("|apolloRemoteService fallback| setSlotUsable |apolloBoxParamDTO:{}", JSON.toJSONString(apolloBoxParamDTO));
            return BizResult.create();
        }

        @Override
        public BizResult sendCommand(DeviceCommandRequest req, String authToken, String reason) {
            log.error("|apolloRemoteService fallback| sendCommand |apolloBoxParamDTO:{}", JSON.toJSONString(req));
            return BizResult.create();
        }
        @Override
        public BizResult<ApolloDeviceDTO> getBaseInfo(String apolloToken, ApolloDeviceParamDTO apolloDeviceParamDTO) {
            log.error("|ApolloRemoteApi fallback| getBaseInfo |apolloBoxParamDTO:{}", apolloDeviceParamDTO);
            return BizResult.create();
        }

        @Override
        public BizResult<ApolloBoxDTO> getBox(String apolloToken, ApolloBoxParamDTO apolloBoxParamDTO) {
            log.error("|ApolloRemoteApi fallback| getBox |apolloBoxParamDTO:{}", apolloBoxParamDTO);
            return BizResult.create();
        }

        @Override
        public BizResult<ApolloPowerBankDTO> getPowerBank(String apolloToken, ApolloPowerBankParamDTO apolloPowerBankParamDTO) {
            log.error("|ApolloRemoteApi fallback| getPowerBank |ApolloPowerBankParamDTO:{}", apolloPowerBankParamDTO);
            return BizResult.create();
        }
    }
}
