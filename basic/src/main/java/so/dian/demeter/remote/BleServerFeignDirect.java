package so.dian.demeter.remote;

import java.util.Map;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.xdcloud.blesdk.client.BleServerFeign;
import so.dian.xdcloud.blesdk.request.BindingRequest;
import so.dian.xdcloud.blesdk.request.CipherRequest;
import so.dian.xdcloud.blesdk.request.CommandRequest;
import so.dian.xdcloud.blesdk.request.InitialRequest;
import so.dian.xdcloud.blesdk.request.Request;
import so.dian.xdcloud.blesdk.request.UnbindingRequest;
import so.dian.xdcloud.blesdk.response.BindingResult;
import so.dian.xdcloud.blesdk.response.BizResult;

@FeignClient(
        value = "${dian.services.bleService:ble-server}",
        contextId = "ble-server-stage-1",
        url = "${remote.url.apollo}"
)
public interface BleServerFeignDirect extends BleServerFeign {
    @PostMapping({"/ble-support/request-initial"})
    BizResult<Map<String, Object>> requestInitial(@RequestBody InitialRequest var1);

    @PostMapping({"/ble-support/request-command"})
    BizResult<Map<String, Object>> requestCommand(@RequestBody CommandRequest var1);

    @PostMapping({"/ble-support/response-decryption"})
    BizResult<Map<String, Object>> responseDecryption(@RequestBody CipherRequest var1);

    @PostMapping({"/ble-support/ble-finish"})
    BizResult finishBle(@RequestBody Request var1);

    @PostMapping({"/ble-support/device-binding"})
    BizResult<Map<String, Object>> deviceBinding(@RequestBody BindingRequest var1);

    @PostMapping({"/ble-support/device-unbinding"})
    BizResult deviceUnbinding(@Valid @RequestBody UnbindingRequest var1);

    @GetMapping({"/ble-support/binding-result"})
    BizResult<BindingResult> bindingResult(@RequestParam("deviceNo") String var1, @RequestParam("mac") String var2);
}
