package so.dian.demeter.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hermes.client.pojo.dto.oss.OssBoxOrderPageDTO;
import so.dian.hermes.client.pojo.param.oss.ListByOrderNoListParam;
import so.dian.hermes.client.pojo.param.oss.OssQueryOrderListParam;

@FeignClient(url = "${remote.url.hermes}", name = "hermes", contextId = "hermes-2",fallback = HermesRemoteService.HermesRemoteServiceFallback.class)
public interface HermesRemoteService {
    /**
     * OSS端查询用户盒子订单分页列表
     */
//    @HystrixCommand(commandProperties = {
//            @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "10000" )
//    })
    @PostMapping(path = "/hermes/oss/box/list", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<OssBoxOrderPageDTO> list(@RequestBody OssQueryOrderListParam param);

    @PostMapping({"/hermes/oss/box/listByOrderNoList"})
    BizResult<OssBoxOrderPageDTO> listByOrderNoList(@RequestBody ListByOrderNoListParam var1);

    @Service
    @Slf4j(topic = "error")
    class HermesRemoteServiceFallback implements HermesRemoteService {

        @Override
        public BizResult<OssBoxOrderPageDTO> list(OssQueryOrderListParam param) {
            log.error("|HermesRemoteService fallback| OSS端查询用户盒子订单分页列表接口| param=[{}]", param);
            return BizResult.create(null);
        }

        @Override
        public BizResult<OssBoxOrderPageDTO> listByOrderNoList(ListByOrderNoListParam var1) {
            log.error("|HermesRemoteService fallback| listByOrderNoList| param=[{}]", var1);
            return BizResult.create(null);
        }
    }
}
