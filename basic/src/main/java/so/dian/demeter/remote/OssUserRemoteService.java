package so.dian.demeter.remote;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.chargebolt.aeacus.dto.OssUserDTO;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.entity.BizResult;

/**
 * OssUserRemoteService
 *
 * <AUTHOR>
 */
//@FeignClient(name = "aeacus",  fallback = OssUserRemoteServiceFallback.class)
public interface OssUserRemoteService {

    @RequestMapping(value = "/{version}/user/queryByIds", method = RequestMethod.POST)
    BizResult<List<OssUserDTO>> queryByIds(@PathVariable(value = "version") String version, @RequestBody List<Long> ids);

    @RequestMapping(value = "/{version}/user/queryByName", method = RequestMethod.GET)
    BizResult<OssUserDTO> queryByName(@PathVariable(value = "version") String version, @RequestParam("name") String name);

    @Service
    @Slf4j(topic = "error")
    class OssUserRemoteServiceFallback implements OssUserRemoteService {

        @Override
        public BizResult<List<OssUserDTO>> queryByIds(String version, List<Long> ids) {
            log.error("|ossUserRemoteService fallback| queryByIds |ids:{}", ids);
            return BizResult.create(Lists.newArrayList());
        }

        @Override
        public BizResult<OssUserDTO> queryByName(String version, String name) {
            log.error("|ossUserRemoteService fallback| queryByName |deviceName:{}", name);
            return BizResult.create(null);
        }
    }

}
