package so.dian.demeter.remote;

import javax.validation.Valid;

import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.chargebolt.aeacus.common.exception.I18nMessageException;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.talos.client.dto.ShopDTO;
import so.dian.talos.client.param.UpdateShopStatusParam;

/**
 * ShopRemoteService
 *
 * <AUTHOR>
 */
//@FeignClient(name = "talos", fallback = ShopRemoteServiceFallback.class)
public interface ShopRemoteService {

    @GetMapping(value = "/shop/queryById")
    BizResult<ShopDTO> queryById(@RequestParam("id") @Valid Long id);

    @PostMapping(value = "/shop/updateShopStatusInstalled")
    BizResult updateShopStatusInstalled(@RequestBody UpdateShopStatusParam updateShopStatusParam);

    @PostMapping(value = "/shop/updateShopStatusUnInstalled")
    BizResult updateShopStatusUnInstalled(@RequestBody UpdateShopStatusParam updateShopStatusParam);

    @Service
    @Slf4j(topic = "error")
    class ShopRemoteServiceFallback implements ShopRemoteService {

        @Override
        public BizResult<ShopDTO> queryById(Long id) {
            log.error("|ShopRemoteService fallback| getById |id:{}", id);
            return BizResult.create(null);
        }

        @Override
        public BizResult updateShopStatusInstalled(UpdateShopStatusParam updateShopStatusParam) {
            log.error("|ShopRemoteService fallback| updateShopStatusInstalled |param:{}", updateShopStatusParam);
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }

        @Override
        public BizResult updateShopStatusUnInstalled(UpdateShopStatusParam updateShopStatusParam) {
            log.error("|ShopRemoteService fallback| updateShopStatusUnInstalled |param:{}", updateShopStatusParam);
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }
    }

}
