/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.demeter.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 门店信息
 *
 * <AUTHOR>
 * @version: ShopDetailResponse.java, v 1.0 2024-01-24 5:00 PM Exp $
 */
@Data
public class ShopDetailResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 17120240124170039L;
    /**
     * 门店ID
     */
    private Long shopId;
    /**
     * 门店名称
     */
    private String shopName;
    /**
     * 门店状态
     */
    private Integer status;
    /**
     * 门店状态描述
     */
    private String statusDesc;
    /**
     * 门店图片
     */
    private String picUrl;
    /**
     * 门店距离
     */
    private Double distance;
    /**
     * 门店负责人名称
     */
    private String sellerName;
    /**
     * 门店负责人联系电话
     */
    private String sellerMobile;
    /**
     * 门店联系人姓名
     */
    private String contactName;
    /**
     * 门店联系人电话
     */
    private String contactMobile;
    /**
     * 门店详细地址
     */
    private String address;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 完整地址
     */
    private String fullAddress;
    /**
     * 城市名称
     */
    private String administrativeDivisions;
    /**
     * 城市id
     */
    private Long administrativeDivisionsId;
    /**
     * 门店类型
     */
    private String  type;
    /**
     * 门店类型ID
     */
    private Long  typeId;

    private String provinceCode;
    private String provinceName;
    private String cityCode;
    private String cityName;

    private Double longitude;

    private Double latitude;
}