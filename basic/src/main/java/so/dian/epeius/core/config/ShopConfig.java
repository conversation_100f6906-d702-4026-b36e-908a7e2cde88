//package so.dian.epeius.core.config;
//
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
///**
// * <AUTHOR>
// * @date 2019-07-10 17:10
// */
//@ConfigurationProperties(prefix = ESConfig.PREFIX + ".shop")
//public class ShopConfig implements ESConfig {
//
//    private String index;
//
//    private String type;
//
//    @Override
//    public String getIndex() {
//        return index;
//    }
//
//    public void setIndex(String index) {
//        this.index = index;
//    }
//
//    @Override
//    public String getType() {
//        return type;
//    }
//
//    public void setType(String type) {
//        this.type = type;
//    }
//}
