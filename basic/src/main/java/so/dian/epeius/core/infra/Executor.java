package so.dian.epeius.core.infra;

import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-07-25 10:18
 */
@Component
public class Executor {

    private final ExecutorService executorService;

    public Executor() {
        executorService = new ThreadPoolExecutor(1, 200, 3, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500),
                (r, e) -> {
                    Thread t = new Thread(r, "Temporary task executor");
                    t.start();
                });
    }

    public void run(Runnable task) {
        executorService.execute(task);
    }
}
