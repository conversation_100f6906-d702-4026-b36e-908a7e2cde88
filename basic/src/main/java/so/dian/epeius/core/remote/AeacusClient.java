package so.dian.epeius.core.remote;

import com.alibaba.fastjson.JSON;
import com.chargebolt.aeacus.dto.OssUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.epeius.api.enums.ErrorEnum;

import java.util.List;

//@FeignClient(name = "aeacus", fallbackFactory = AeacusClient.AeacusFallbackFactory.class)
public interface AeacusClient {

    @PostMapping(value = {"/{version}/user/queryByIds"})
    BizResult<List<OssUserDTO>> queryByIds(@PathVariable("version") String var1, @RequestBody List<Long> idList);

    @RequestMapping(value = "/{version}/user/queryDpmCreatorByCode", method = RequestMethod.GET)
    BizResult<List<OssUserDTO>> queryDpmCreatorByCode(@PathVariable(value = "version") String version, @RequestParam(name = "dpmCode") String dpmCode, @RequestParam(name = "dataPerType") Integer dataPerType);

//    @Component
    @Slf4j(topic = "remote")
    class AeacusFallbackFactory implements FallbackFactory<AeacusClient> {

        @Override
        public AeacusClient create(Throwable throwable) {

            log.error("aeacus fallback.", throwable);

            return new AeacusClient() {
                @Override
                public BizResult<List<OssUserDTO>> queryByIds(String var1, List<Long> idList) {
                    log.error("aeacus create fallback. idList:{}", JSON.toJSONString(idList));
                    return BizResult.error(ErrorEnum.REMOTE_ERROR);
                }

                @Override
                public BizResult<List<OssUserDTO>> queryDpmCreatorByCode(String version, String dpmCode, Integer dataPerType) {
                    log.error("aeacus create fallback. version:{}, dpmCode:{}, dataPerType:{}", version, dpmCode, dataPerType.toString());
                    return BizResult.error(ErrorEnum.REMOTE_ERROR);
                }
            };
        }
    }

}
