package so.dian.epeius.core.remote;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.talos.client.dto.ShopDTO;
import so.dian.talos.client.param.SearchShopParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-07-23 10:16
 */
//@FeignClient("talos")
public interface ShopApi {

    @GetMapping(value = "/shop/queryById")
    @ResponseBody
    BizResult<ShopDTO> queryById(@RequestParam("id") Long id);

    @PostMapping(value = "/shop/shopList")
    @ResponseBody
    BizResult<List<ShopDTO>> getShopList(@RequestBody @Valid SearchShopParam searchShopParam);
}
