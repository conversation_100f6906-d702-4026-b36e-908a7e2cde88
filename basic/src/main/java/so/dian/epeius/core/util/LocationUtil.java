package so.dian.epeius.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LocationUtil {

    private static Logger logger = LoggerFactory.getLogger(LocationUtil.class);
    private static double x_pi = Math.PI * 3000.0 / 180.0;

    public static double distance(double latitude1, double longitude1, double latitude2, double longitude2) {
        // R是地球的半径，以KM为单位
        double deltaLatitude = (latitude2 - latitude1) * Math.PI / 180;
        double deltaLongitude = (longitude2 - longitude1) * Math.PI / 180;
        latitude1 = latitude1 * Math.PI / 180;
        latitude2 = latitude2 * Math.PI / 180;

        double a = Math.sin(deltaLatitude / 2) * Math.sin(deltaLatitude / 2) + Math.cos(latitude1)
                * Math.cos(latitude2) * Math.sin(deltaLongitude / 2) * Math.sin(deltaLongitude / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return 6371 * c;
    }
}
