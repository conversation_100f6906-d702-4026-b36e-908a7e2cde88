package so.dian.eros.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chargebolt.theseus.common.enums.ConfigKeyEnum;
import com.chargebolt.theseus.dto.ConfigNormalDTO;
import com.chargebolt.theseus.dto.RegionDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.util.ClassPathResourceUtils;
import so.dian.epeius.api.enums.ErrorEnum;
import so.dian.eros.cache.ThemeCache;
import so.dian.eros.manager.hermes.RegionManager;
import so.dian.eros.remote.theseus.ConfigClient;
import so.dian.talos.common.util.UploadHelper;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ThemeService {

    @Resource
    private RegionManager regionManager;
    @Resource
    private ThemeCache themeCache;
    @Resource
    private ConfigClient configClient;
    @Resource
    private UploadHelper uploadHelper;
    @Value("${ezreal.profile}")
    private String profile;
    // FIXME 不在多租户下配置，默认获取 0
    private static final Integer DEFAULT_REGION_ID = 0;
    public ThemeConfig get() {
        ThemeConfig themeConfig = JSONObject.parseObject(getThemeConfigJson().toJSONString(), ThemeConfig.class);

        BizResult<ConfigNormalDTO> bizResult = configClient.getConfigInString(ConfigKeyEnum.CONTACT);
        ConfigNormalDTO configNormalDTO = bizResult.getData();
        JSONObject contactObject = JSONObject.parseObject(configNormalDTO.getValue());

        themeConfig.setHotline(contactObject.getString("hotline"));
        return themeConfig;
    }

    public void initTheme(JSONObject themeConfig) {
        RegionDTO regionDTO = regionManager.getDefaultRegionDTO();
        themeCache.set(regionDTO.getId(), themeConfig);
    }

    private JSONObject getThemeConfigJson() {
        RegionDTO regionDTO = regionManager.getDefaultRegionDTO();

//        Integer regionId = 0;
        JSONObject cachedValue = themeCache.get(regionDTO.getId());
        if (cachedValue != null) {
            return cachedValue;
        } else {
            cachedValue = getEzrealThemeConfigFromOss();
            if (cachedValue == null) {
                try {
                    cachedValue = JSONObject.parseObject(ClassPathResourceUtils.readResourceAsString("default-theme.json"));
                } catch (IOException e) {
                    log.warn("从classpath读取default-theme.json失败", e);
                }
            }
            themeCache.set(regionDTO.getId(), cachedValue);
        }
        return cachedValue;
    }

    private boolean addColor0(String key, Object value) {
        RegionDTO regionDTO = regionManager.getDefaultRegionDTO();
        JSONObject config = getThemeConfigJson();
        JSONObject color = config.getJSONObject("color");
        if (color.containsKey(key)) {
            JSONArray themeColor = color.getJSONArray(key);
            themeColor.add(value);
            themeCache.set(regionDTO.getId(), config);
        }
        return true;
    }

    public Boolean addColor(SingleThemeColorReq singleThemeColorReq) {
        singleThemeColorReq.getSingleThemeColor().setId(UUID.randomUUID().toString());
        singleThemeColorReq.getSingleThemeColor().setIsDefault(0);
        singleThemeColorReq.getSingleThemeColor().setSelected(0);
        return addColor0(singleThemeColorReq.getKey(), singleThemeColorReq.getSingleThemeColor());
    }

    public Boolean addColor(GradientThemeColorReq gradientThemeColorReq) {
        if (gradientThemeColorReq.getGradientThemeColor().getColor().size() != 2) {
            // tag 未国际化
            throw BizException.create(ErrorEnum.PARAM_ERROR, "渐变色应有两种颜色");
        }
        gradientThemeColorReq.getGradientThemeColor().setId(UUID.randomUUID().toString());
        gradientThemeColorReq.getGradientThemeColor().setIsDefault(0);
        gradientThemeColorReq.getGradientThemeColor().setSelected(0);
        return addColor0(gradientThemeColorReq.getKey(), gradientThemeColorReq.getGradientThemeColor());
    }

    public Boolean deleteColor(String colorId) {
        RegionDTO regionDTO = regionManager.getDefaultRegionDTO();
        JSONObject config = getThemeConfigJson();
        JSONObject colorConfig = config.getJSONObject("color");
        for (String key : colorConfig.keySet()) {
            JSONArray array = colorConfig.getJSONArray(key);
            Iterator iterator = array.iterator();
            while (iterator.hasNext()) {
                Object next = iterator.next();
                if (next instanceof JSONObject) {
                    JSONObject color = (JSONObject) next;
                    if (color.getString("id").equals(colorId)) {
                        if (color.getInteger("default") == 1) {
                            // tag 未国际化
                            throw BizException.create(ErrorEnum.PARAM_ERROR, "默认颜色不能删除");
                        }
                        iterator.remove();
                        break;
                    }
                }
            }
        }
        themeCache.set(regionDTO.getId(), config);
        return true;
    }

    private void setSelectedColor(JSONObject themeConfig, String key, String themeColorId) {
        if (StringUtils.isEmpty(themeColorId)) {
            return;
        }
        JSONArray array = themeConfig.getJSONObject("color").getJSONArray(key);
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            object.put("selected", 0);
            if (object.getString("id").equals(themeColorId)) {
                object.put("selected", 1);
            }
        }
    }

    private JSONArray convert(List<String> img) {
        JSONArray array = new JSONArray();
        if (CollectionUtils.isEmpty(img)) {
            return array;
        }
        img.forEach(s -> {
            JSONObject object = new JSONObject();
            object.put("url", s);
            object.put("uid", UUID.randomUUID().toString());
            array.add(object);
        });
        return array;
    }

    private void setUpdatedImg(JSONObject themeConfig, String key, List<String> img) {
        if (CollectionUtils.isEmpty(img)) {
            return;
        }
        themeConfig.getJSONObject("img").getJSONObject(key).put("img", convert(img));
    }

    private void saveEzrealThemeConfigToOss(JSONObject themeConfig) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(themeConfig.toJSONString().getBytes());
        String fileName = "ezreal/data/" + profile + "/theme.json";
        uploadHelper.ossUploadFile(fileName, inputStream);
    }

    public JSONObject getEzrealThemeConfigFromOss() {
        try {
            String fileName = "ezreal/data/" + profile + "/theme.json";
            InputStream stream = uploadHelper.getObjectInputStream(fileName);
            String content = StreamUtils.copyToString(stream, Charset.defaultCharset());
            return StringUtils.isEmpty(content) ? null : JSON.parseObject(content);
        } catch (Exception e) {
            log.warn("从oss获取theme配置文件失败", e);
            return null;
        }
    }

    public Boolean update(ThemeConfigReq themeConfigReq) {
        JSONObject themeConfig = getThemeConfigJson();
        setSelectedColor(themeConfig, "themeColor", themeConfigReq.getThemeColorId());
        setSelectedColor(themeConfig, "auxiliaryColor1", themeConfigReq.getAuxiliaryColor1Id());
        setSelectedColor(themeConfig, "auxiliaryColor2", themeConfigReq.getAuxiliaryColor2Id());
        setSelectedColor(themeConfig, "auxiliaryColor3", themeConfigReq.getAuxiliaryColor3Id());
        setSelectedColor(themeConfig, "gradientColor1", themeConfigReq.getGradientColor1Id());
        setSelectedColor(themeConfig, "gradientColor2", themeConfigReq.getGradientColor2Id());
        setUpdatedImg(themeConfig, "earthLogo", themeConfigReq.earthLogo);
        setUpdatedImg(themeConfig, "papillonLoginLogo", themeConfigReq.papillonLoginLogo);
        setUpdatedImg(themeConfig, "papillonTopLogo", themeConfigReq.papillonTopLogo);
        setUpdatedImg(themeConfig, "merchantLoginLogo", themeConfigReq.merchantLoginLogo);
        setUpdatedImg(themeConfig, "merchantAboutLogo", themeConfigReq.merchantAboutLogo);
        setUpdatedImg(themeConfig, "merchantLoginBanner", themeConfigReq.merchantLoginBanner);
        setUpdatedImg(themeConfig, "customerLoginLogo", themeConfigReq.customerLoginLogo);
        setUpdatedImg(themeConfig, "customerHomeLogo", themeConfigReq.customerHomeLogo);
        setUpdatedImg(themeConfig, "powerbankEjectImg", themeConfigReq.powerbankEjectImg);
        setUpdatedImg(themeConfig, "powerbankChargingImg", themeConfigReq.powerbankChargingImg);
        setUpdatedImg(themeConfig, "returnPowerbankPageImg", themeConfigReq.returnPowerbankPageImg);
        setUpdatedImg(themeConfig, "pointUnselectedIcon", themeConfigReq.pointUnselectedIcon);
        setUpdatedImg(themeConfig, "pointSelectedIcon", themeConfigReq.pointSelectedIcon);
        setUpdatedImg(themeConfig, "orderQrCodePageImg", themeConfigReq.orderQrCodePageImg);
        setUpdatedImg(themeConfig, "returnScanPageImg", themeConfigReq.returnScanPageImg);
        setUpdatedImg(themeConfig, "defaultShopImg", themeConfigReq.defaultShopImg);
        setUpdatedImg(themeConfig, "howToReturnImg", themeConfigReq.howToReturnImg);
        setUpdatedImg(themeConfig, "customerHomeOnLoanImg", themeConfigReq.customerHomeOnLoanImg);
        setUpdatedImg(themeConfig, "customerQrCodeDownloadPageLogo", themeConfigReq.customerQrCodeDownloadPageLogo);
        setUpdatedImg(themeConfig, "customerAboutLogo", themeConfigReq.customerAboutLogo);
        setUpdatedImg(themeConfig, "paySuccessImg", themeConfigReq.paySuccessImg);
        setUpdatedImg(themeConfig, "payingImg", themeConfigReq.payingImg);
        log.info(themeConfig.toJSONString());
        saveEzrealThemeConfigToOss(themeConfig);
        log.info("更新主题配置 {}, {}", regionManager.getDefaultRegionDTO().getId(), themeConfig.toJSONString());
        themeCache.set(regionManager.getDefaultRegionDTO().getId(), themeConfig);
        JSONObject kronosObject = createKronosThemeConfig(themeConfig);
        configClient.setTheme(regionManager.getDefaultRegionDTO().getId(), kronosObject);

        if (!StringUtils.isEmpty(themeConfigReq.getHotline())) {
            BizResult<ConfigNormalDTO> bizResult = configClient.getConfigInString(ConfigKeyEnum.CONTACT);
            ConfigNormalDTO configNormalDTO = bizResult.getData();
            JSONObject contactObject = JSONObject.parseObject(configNormalDTO.getValue());
            contactObject.put("hotline", themeConfigReq.getHotline());
            configNormalDTO.setValue(contactObject.toString());
            configClient.update(configNormalDTO);
        }
        return true;
    }

    private void createKronosColorConfig(JSONObject themeConfig, JSONObject kronosColorConfig, String key) {
        JSONArray array = themeConfig.getJSONObject("color").getJSONArray(key);
        JSONObject defaultObject = null;
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            if (object.getInteger("default") == 1) {
                defaultObject = object;
            }
            if (object.getInteger("selected") == 1) {
                kronosColorConfig.put(key, object);
                break;
            }
        }
        if (kronosColorConfig.getJSONObject(key) == null) {
            kronosColorConfig.put(key, defaultObject);
        }
        kronosColorConfig.getJSONObject(key).remove("id");
        kronosColorConfig.getJSONObject(key).remove("selected");
        kronosColorConfig.getJSONObject(key).remove("default");
    }

    private void createImgConfig(JSONObject themeConfig, JSONObject kronosImgConfig, String key) {
        JSONObject object = themeConfig.getJSONObject("img").getJSONObject(key);
        if(Objects.isNull(object)){
            kronosImgConfig.put(key, "");

        }else {
            JSONArray img = object.getJSONArray("img");
            if (img == null) {
                img = new JSONArray();
            }
            kronosImgConfig.put(key, img.stream().map(new Function<Object, String>() {
                @Override
                public String apply(Object o) {
                    return ((JSONObject) o).getString("url");
                }
            }).collect(Collectors.toList()));
        }
    }

    private JSONObject createKronosThemeConfig(JSONObject themeConfig) {
        JSONObject kronosThemeConfig = new JSONObject();
        JSONObject kronosColorConfig = new JSONObject();
        createKronosColorConfig(themeConfig, kronosColorConfig, "themeColor");
        createKronosColorConfig(themeConfig, kronosColorConfig, "auxiliaryColor1");
        createKronosColorConfig(themeConfig, kronosColorConfig, "auxiliaryColor2");
        createKronosColorConfig(themeConfig, kronosColorConfig, "auxiliaryColor3");
        createKronosColorConfig(themeConfig, kronosColorConfig, "gradientColor1");
        createKronosColorConfig(themeConfig, kronosColorConfig, "gradientColor2");
        kronosThemeConfig.put("color", kronosColorConfig);

        JSONObject kronosImgConfig = new JSONObject();
//        createKronosImgConfig(themeConfig, kronosImgConfig, "earthLogo");
//        createKronosImgConfig(themeConfig, kronosImgConfig, "papillonLoginLogo");
//        createKronosImgConfig(themeConfig, kronosImgConfig, "papillonTopLogo");
//        createKronosImgConfig(themeConfig, kronosImgConfig, "merchantLoginLogo");
//        createKronosImgConfig(themeConfig, kronosImgConfig, "merchantAboutLogo");
        createImgConfig(themeConfig, kronosImgConfig, "customerLoginLogo");
        createImgConfig(themeConfig, kronosImgConfig, "customerHomeLogo");
        createImgConfig(themeConfig, kronosImgConfig, "powerbankEjectImg");
        createImgConfig(themeConfig, kronosImgConfig, "powerbankChargingImg");
        createImgConfig(themeConfig, kronosImgConfig, "returnPowerbankPageImg");
        createImgConfig(themeConfig, kronosImgConfig, "pointUnselectedIcon");
        createImgConfig(themeConfig, kronosImgConfig, "pointSelectedIcon");
        createImgConfig(themeConfig, kronosImgConfig, "orderQrCodePageImg");
        createImgConfig(themeConfig, kronosImgConfig, "returnScanPageImg");
        createImgConfig(themeConfig, kronosImgConfig, "defaultShopImg");
        createImgConfig(themeConfig, kronosImgConfig, "howToReturnImg");
        createImgConfig(themeConfig, kronosImgConfig, "customerHomeOnLoanImg");
        createImgConfig(themeConfig, kronosImgConfig, "customerQrCodeDownloadPageLogo");
        createImgConfig(themeConfig, kronosImgConfig, "customerAboutLogo");
        createImgConfig(themeConfig, kronosImgConfig, "paySuccessImg");
        createImgConfig(themeConfig, kronosImgConfig, "payingImg");
        kronosThemeConfig.put("img", kronosImgConfig);
        return kronosThemeConfig;
    }

    public JSONObject getEzrealTheme() {
        JSONObject themeConfig = getThemeConfigJson();
        return createEzrealConfig(themeConfig);
    }

    private JSONObject createEzrealConfig(JSONObject themeConfig) {
        JSONObject ezrealThemeConfig = new JSONObject();
        JSONObject ezrealColorConfig = new JSONObject();
        createKronosColorConfig(themeConfig, ezrealColorConfig, "themeColor");
        createKronosColorConfig(themeConfig, ezrealColorConfig, "auxiliaryColor1");
        createKronosColorConfig(themeConfig, ezrealColorConfig, "auxiliaryColor2");
        createKronosColorConfig(themeConfig, ezrealColorConfig, "auxiliaryColor3");
        createKronosColorConfig(themeConfig, ezrealColorConfig, "gradientColor1");
        createKronosColorConfig(themeConfig, ezrealColorConfig, "gradientColor2");
        ezrealThemeConfig.put("color", ezrealColorConfig);

        JSONObject ezrealImgConfig = new JSONObject();
        createImgConfig(themeConfig, ezrealImgConfig, "earthLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "papillonLoginLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "papillonTopLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "merchantLoginLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "merchantAboutLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "merchantLoginBanner");
        createImgConfig(themeConfig, ezrealImgConfig, "customerLoginLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "customerHomeLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "powerbankEjectImg");
        createImgConfig(themeConfig, ezrealImgConfig, "powerbankChargingImg");
        createImgConfig(themeConfig, ezrealImgConfig, "returnPowerbankPageImg");
        createImgConfig(themeConfig, ezrealImgConfig, "pointUnselectedIcon");
        createImgConfig(themeConfig, ezrealImgConfig, "pointSelectedIcon");
        createImgConfig(themeConfig, ezrealImgConfig, "orderQrCodePageImg");
        createImgConfig(themeConfig, ezrealImgConfig, "returnScanPageImg");
        createImgConfig(themeConfig, ezrealImgConfig, "defaultShopImg");
        createImgConfig(themeConfig, ezrealImgConfig, "howToReturnImg");
        createImgConfig(themeConfig, ezrealImgConfig, "customerHomeOnLoanImg");
        createImgConfig(themeConfig, ezrealImgConfig, "customerQrCodeDownloadPageLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "customerAboutLogo");
        createImgConfig(themeConfig, ezrealImgConfig, "paySuccessImg");
        createImgConfig(themeConfig, ezrealImgConfig, "payingImg");
        ezrealThemeConfig.put("img", ezrealImgConfig);
        return ezrealThemeConfig;
    }

    @Data
    public static abstract class Color {
        private String id;
        @JsonProperty("default")
        private Integer isDefault;
        private Integer selected;
    }

    @Data
    public static class SingleThemeColor extends Color {
        private String color;
        private Integer opacity;
        private String rgba;
    }

    @Data
    public static class GradientThemeColor extends Color {
        private List<ColorDTO> color;

        @Data
        public static class ColorDTO {
            private String color;
            private Integer opacity;
            private String rgba;
        }
    }

    @Data
    public static class SingleThemeColorReq {
        private String key;
        private ThemeService.SingleThemeColor singleThemeColor;
    }

    @Data
    public static class GradientThemeColorReq {
        private String key;
        private ThemeService.GradientThemeColor gradientThemeColor;
    }

    @Data
    public static class ThemeConfigReq {
        private String themeColorId;
        private String auxiliaryColor1Id;
        private String auxiliaryColor2Id;
        private String auxiliaryColor3Id;
        private String gradientColor1Id;
        private String gradientColor2Id;

        private List<String> earthLogo;
        private List<String> papillonLoginLogo;
        private List<String> papillonTopLogo;
        private List<String> merchantLoginLogo;
        private List<String> merchantAboutLogo;
        private List<String> merchantLoginBanner;
        private List<String> customerLoginLogo;
        private List<String> customerHomeLogo;
        private List<String> powerbankEjectImg;
        private List<String> powerbankChargingImg;
        private List<String> returnPowerbankPageImg;
        private List<String> pointUnselectedIcon;
        private List<String> pointSelectedIcon;
        private List<String> orderQrCodePageImg;
        private List<String> returnScanPageImg;
        private List<String> defaultShopImg;
        private List<String> howToReturnImg;
        private List<String> customerHomeOnLoanImg;
        private List<String> customerQrCodeDownloadPageLogo;
        private List<String> customerAboutLogo;
        private List<String> paySuccessImg;
        private List<String> payingImg;

        private String hotline;
    }

    @Data
    public static class ThemeColorConfig {
        private List<SingleThemeColor> themeColor;
        private List<SingleThemeColor> auxiliaryColor1;
        private List<SingleThemeColor> auxiliaryColor2;
        private List<SingleThemeColor> auxiliaryColor3;
        private List<GradientThemeColor> gradientColor1;
        private List<GradientThemeColor> gradientColor2;
    }

    @Data
    public static class ThemeImg {
        private List<ThemeImgItem> img;
        private String exampleUrl;
    }

    @Data
    public static class ThemeImgItem {
        private String uid;
        private String url;
    }

    @Data
    public static class ThemeImgConfig {
        private ThemeImg earthLogo;
        private ThemeImg papillonLoginLogo;
        private ThemeImg papillonTopLogo;
        private ThemeImg merchantLoginLogo;
        private ThemeImg merchantAboutLogo;
        private ThemeImg merchantLoginBanner;
        private ThemeImg customerLoginLogo;
        private ThemeImg customerHomeLogo;
        private ThemeImg powerbankEjectImg;
        private ThemeImg powerbankChargingImg;
        private ThemeImg returnPowerbankPageImg;
        private ThemeImg pointUnselectedIcon;
        private ThemeImg pointSelectedIcon;
        private ThemeImg orderQrCodePageImg;
        private ThemeImg returnScanPageImg;
        private ThemeImg defaultShopImg;
        private ThemeImg howToReturnImg;
        private ThemeImg customerHomeOnLoanImg;
        private ThemeImg customerQrCodeDownloadPageLogo;
        private ThemeImg customerAboutLogo;
        private ThemeImg paySuccessImg;
        private ThemeImg payingImg;
    }

    @Data
    public static class ThemeConfig {
        private ThemeColorConfig color;
        private ThemeImgConfig img;

        private String hotline;
    }

    public void reload() {
        RegionDTO regionDTO = regionManager.getDefaultRegionDTO();
        try {
            JSONObject cachedValue = JSONObject.parseObject(ClassPathResourceUtils.readResourceAsString("default-theme.json"));
            themeCache.set(regionDTO.getId(), cachedValue);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
