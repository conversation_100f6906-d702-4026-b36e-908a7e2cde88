package so.dian.eros.common.enums;

import java.util.Objects;
import lombok.Getter;
import so.dian.talos.client.enums.LanguageEnum;

@Getter
public enum OrderExportHeaderEnum {

    ORDER_CREATE_TIME(0, "订单创建时间", "Order creation time"),
    ORDER_NO(1, "订单编号", "Order number"),
    ORDER_STATUS(2, "订单状态", "Order status"),
    LOGIN_WAY(3, "登录方式", "Login mode"),
    USER_NAME(4, "用户名称", "User name"),
    USER_ID(5, "用户ID", "User ID"),
    RENT_STORE_ID(6, "借出门店ID", "Rent store ID"),
    RENT_STORE_NAME(7, "借出门店名称", "Rent store name"),
    RENT_DEVICE_ID(8, "借出设备编号", "Rent device id"),
    RENT_POSITION_NUMBER(9, "借出仓位号", "Rent device position number"),
    RETURN_DEVICE_ID(10, "归还设备编号", "Return device ID"),
    RETURN_POSITION_NUMBER(11, "归还仓位号", "Return position number"),
    RENT_TIME(12, "借出时间", "Rent time"),
    RETURN_TIME(13, "归还时间", "Return time"),
    PAID_AMOUNT(14, "实际支付金额", "Paid amount"),
    REFUND_AMOUNT(15, "退款金额", "Refund amount"),
    CURRENCY_CODE(16, "币种代码", "Currency code"),
    ORDER_AMOUNT(17, "订单金额", "Order amount"),
    PAY_TIME(18, "支付时间", "Payment time"),
    PAY_WAY(19, "支付方式", "Payway"),
    RETURN_STORE_ID(20, "归还门店ID", "Return store id"),
    RETURN_STORE_NAME(21, "归还门店名称", "Return store name"),
    AGENT_ID(22, "代理商ID", "Agent id"),
    AGENT_NAME(23, "代理商名称", "Agent name"),
    REFUND_TIME(24, "退款时间", "Refund time"),
    ;
    private Integer code;
    private String ch_zn;
    private String en_gb;

    OrderExportHeaderEnum(Integer code, String ch_zn, String en_gb) {
        this.code = code;
        this.ch_zn = ch_zn;
        this.en_gb = en_gb;
    }

    private static OrderExportHeaderEnum getByCode(Integer code) {
        for (OrderExportHeaderEnum orderExportHeaderEnum : OrderExportHeaderEnum.values()) {
            if (orderExportHeaderEnum.getCode().equals(code)) {
                return orderExportHeaderEnum;
            }
        }
        return null;
    }

    public String getByLang(String lang) {
        return getByEnumAndLang(this, lang);
    }

    public static String getByEnumAndLang(OrderExportHeaderEnum orderExportHeaderEnum, String lang) {
        if(Objects.isNull(orderExportHeaderEnum)) {
            return "";
        }
        LanguageEnum langEnum = LanguageEnum.getByLanguage(lang);
        if(Objects.equals(langEnum, LanguageEnum.EN_GB)) {
            return orderExportHeaderEnum.getEn_gb();
        }
        if(Objects.equals(langEnum, LanguageEnum.ZH_CN)) {
            return orderExportHeaderEnum.getCh_zn();
        }
        return orderExportHeaderEnum.getEn_gb();
    }


}
