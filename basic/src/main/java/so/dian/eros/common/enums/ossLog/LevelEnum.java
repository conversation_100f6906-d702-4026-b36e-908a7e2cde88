package so.dian.eros.common.enums.ossLog;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Locale;

/**
 * LevelEnum
 *
 * <AUTHOR>
 * @desc 描述信息
 * @date 20/4/8
 */
@Getter
@AllArgsConstructor
public enum LevelEnum {
    EMERGENCY(1, "紧急", "Emergency"),
    NORMAL(2, "普通", "Normal"),
    DEFAULT(99, "未知", "Unknown")
    ;

    private Integer code;
    private String cnDesc;
    private String enDesc;


    public static String getDescByCodeAndLocale(Integer code, Locale locale) {
        for (LevelEnum eventEnum : LevelEnum.values()) {
            if (eventEnum.code.equals(code)) {
                if (Locale.UK.equals(locale)) {
                    return eventEnum.enDesc;
                }
                return eventEnum.cnDesc;
            }
        }
        if (Locale.UK.equals(locale)) {
            return DEFAULT.enDesc;
        }
        return DEFAULT.cnDesc;
    }
}
