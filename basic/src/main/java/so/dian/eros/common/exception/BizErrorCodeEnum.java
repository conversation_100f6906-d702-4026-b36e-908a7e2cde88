package so.dian.eros.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.exception.ErrorCodeEnum;

/**
 * 业务异常 <br/>
 *
 * <AUTHOR>
 * @date 2018/10/24 11:38 AM
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Getter
@AllArgsConstructor
public enum BizErrorCodeEnum implements EnumInterface {

    ACCESS_TOKEN_ERROR(1100, "access token 获取异常"),

    SCAN_URL_EMPTY(1200, "scan url 为空"),
    SCAN_URL_ERROR(1201, "错误的二维码"),
//    MEMBER_DATE_ERROR(1301, "会员过期"),
//    MEMBER_NOT_EXIST(1302, "会员订单不存在"),
//    MEMBER_PAYMENT_NO_NOT_EXIST(1303, "会员订单交易凭证不存在"),
//    MEMBER_END_TIME_PARSE_ERROR(1304, "会员的有效期格式错误"),
    ;

    private Integer code;
    private String desc;


    @Override
    public EnumInterface getDefault() {
        return ErrorCodeEnum.UNKNOWN_ERROR;
    }
}
