package so.dian.eros.common.exception;


import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

@Getter
@AllArgsConstructor
public enum Exec implements EnumInterface {

    INVALID_PARAM(501, "不支持的参数"),

    WX_REQUEST_FAILURE(601, "微信接口调用失败"),
    ;

    private Integer code;

    private String desc;

    @Override
    public String toString() {
        return code + ":" + desc;
    }

    @Override
    public EnumInterface getDefault() {
        return null;
    }

}
