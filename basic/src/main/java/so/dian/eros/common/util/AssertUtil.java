package so.dian.eros.common.util;

import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.exception.BizException;

import java.util.Collection;
import java.util.Map;

/**
 * @Description 校验
 * <AUTHOR>
 * @Date 2023/8/10
 * @Version 1.0
 **/
public class AssertUtil {

    private AssertUtil() {
    }

    public static void isSuccess(BizResult<?> result) {
        if (!result.isSuccess()) {
            throw BizException.create(result);
        }
    }

    public static void isSuccess(BizResult<?> result, EnumInterface errorCode) {
        if (!result.isSuccess()) {
            throw BizException.create(errorCode);
        }
    }

    public static void isTrue(boolean expression, EnumInterface errorCode) {
        if (!expression) {
            throw BizException.create(errorCode);
        }
    }

    public static void isTrue(boolean expression, EnumInterface errorCode, String message) {
        if (!expression) {
            throw BizException.create(errorCode, message);
        }
    }

    public static void notNull(Object object, EnumInterface errorCode) {
        if (object == null) {
            throw BizException.create(errorCode);
        }
    }

    public static void notNull(Object object, EnumInterface errorCode, String message) {
        if (object == null) {
            throw BizException.create(errorCode, message);
        }
    }

    public static void hasLength(String text, EnumInterface errorCode) {
        if (!StringUtils.hasLength(text)) {
            throw BizException.create(errorCode);
        }
    }

    public static void hasLength(String text, EnumInterface errorCode, String message) {
        if (!StringUtils.hasLength(text)) {
            throw BizException.create(errorCode, message);
        }
    }

    public static void notEmpty(String text, EnumInterface errorCode) {
        hasLength(text, errorCode);
    }

    public static void notEmpty(String text, EnumInterface errorCode, String message) {
        hasLength(text, errorCode, message);
    }

    public static void hasText(String text, EnumInterface errorCode) {
        if (!StringUtils.hasText(text)) {
            throw BizException.create(errorCode);
        }
    }

    public static void hasText(String text, EnumInterface errorCode, String message) {
        if (!StringUtils.hasText(text)) {
            throw BizException.create(errorCode, message);
        }
    }

    public static void notBlank(String text, EnumInterface errorCode) {
        hasText(text, errorCode);
    }

    public static void notBlank(String text, EnumInterface errorCode, String message) {
        hasText(text, errorCode, message);
    }

    public static void notEmpty(Object[] array, EnumInterface errorCode) {
        if (ObjectUtils.isEmpty(array)) {
            throw BizException.create(errorCode);
        }
    }

    public static void notEmpty(Object[] array, EnumInterface errorCode, String message) {
        if (ObjectUtils.isEmpty(array)) {
            throw BizException.create(errorCode, message);
        }
    }

    public static void notEmpty(Collection<?> collection, EnumInterface errorCode) {
        if (CollectionUtils.isEmpty(collection)) {
            throw BizException.create(errorCode);
        }
    }

    public static void notEmpty(Collection<?> collection, EnumInterface errorCode, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw BizException.create(errorCode, message);
        }
    }

    public static void notEmpty(Map<?, ?> map, EnumInterface errorCode) {
        if (CollectionUtils.isEmpty(map)) {
            throw BizException.create(errorCode);
        }
    }

    public static void notEmpty(Map<?, ?> map, EnumInterface errorCode, String message) {
        if (CollectionUtils.isEmpty(map)) {
            throw BizException.create(errorCode, message);
        }
    }
}
