/*
 * Dian.so Inc.
 * Copyright (c) 2016-2019 All Rights Reserved.
 */
package so.dian.eros.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * 数据库配置信息获取对象
 *
 * <AUTHOR>
 * @version: HikariDataSourceProperties.java, v 1.0 2019-11-02 1:33 下午 Exp $
 */
@Component
@Getter
@Setter
@ConfigurationProperties(prefix = "datasource.rds")
public class DataSourceProperties {
    private String  url;
    private String  userName;
    private String  password;
    private String  driverClassName;
    private Integer initialSize;
    private Integer minIdle;
    private Integer maxActive;
    private Integer maxWait;
    private Integer timeBetweenEvictionRunsMillis;
    private Integer minEvictableIdleTimeMillis;
    private String  validationQuery;
    private Integer validationQueryTimeout;
    private Boolean testWhileIdle;
    private Boolean testOnBorrow;
    private Boolean testOnReturn;
    private Boolean poolPreparedStatements;
    private Integer maxPoolPreparedStatementPerConnectionSize;
    private String  filters;
    private String  connectionProperties;
}