package so.dian.eros.controller;

import com.alibaba.fastjson.JSONObject;
import com.chargebolt.aeacus.annotation.Authentication;
import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.controller.ResourceController;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.commons.enums.MultilingualEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.repository.init.ResourceReader;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.common.constant.AeacusModuleConstants;
import so.dian.eros.common.exception.UserErrorEnum;
import so.dian.eros.interceptor.ThreadLanguageHolder;
import so.dian.eros.pojo.dto.HomeIconDTO;
import so.dian.mofa3.lang.util.JsonUtil;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
@Slf4j
@RestController
public class HomeController extends BaseController {
    private static final Map<Integer, Map<MultilingualEnum, String>> MENU_CACHE = new ConcurrentHashMap<>();
    @Resource
    private ResourceController resourceController;

    @Login
    @RequestMapping(value = "/{version}/home", method = RequestMethod.GET)
    public BizResult home(@PathVariable(value = "version") String version) {
        JSONObject jsonObject = new JSONObject();
//        BizResult<List<OssPermissionDTO>> bizResult = aeacusPermissionServiceFacade.getIconsByUser(getUser().getUserId());
//        if (bizResult != null && bizResult.isSuccess()) {
//            List<OssPermissionDTO> ossPermissionDTOS = bizResult.getData();
//            jsonObject.put("icons", ossPermissionDTOS);
//        }

        OssUserDTO ossUserDTO = getUser();
        if (ossUserDTO == null) {
            return BizResult.error(UserErrorEnum.NEED_LOGIN);
        }

        BizResult<List<OssIconDTO>> result = resourceController
                .getIconsByUser(ossUserDTO.getUserId(), AeacusModuleConstants.BUZINESS_DEVELOP);
        if (result != null && result.isSuccess()) {
            List<OssIconDTO> response= result.getData();

            // 国际化适配
            String lang= ThreadLanguageHolder.getCurrentLang();

            response= response.stream()
                    .map(icon -> {
                        // FIXME 方便旧功能回归，完成后删除
                        if(icon.getId()==3){
                            icon.setNewUrl("/shops/check-repeat");
                        }
                        if(icon.getId()==4){
                            icon.setNewUrl("/shops/list");
                        }
                        if(icon.getId()==5){
                            icon.setNewUrl("/shops/ownership");
                        }
                        if(icon.getId()==7){
                            icon.setNewUrl("/merchant/list");
                        }
                        return icon;
                    })
                    .collect(Collectors.toList());
            return BizResult.create(build(response, lang));
        }
        return BizResult.create(new ArrayList<>());
    }

    private Map<String, HomeIconDTO> build(List<OssIconDTO> iconDTOList, String lang) {
        List<OssIconDTO> type1IconList = Lists.newArrayList();
        List<OssIconDTO> type2IconList = Lists.newArrayList();
        for (OssIconDTO iconDTO : iconDTOList) {
            Map<MultilingualEnum, String> menuCache =  MENU_CACHE.get(iconDTO.getId().intValue());
            if(Boolean.FALSE.equals(Optional.ofNullable(menuCache)
                    .map(Map::isEmpty)
                    .orElse(true))){
                String title= menuCache.getOrDefault(MultilingualEnum.findLanguageEnum(lang), iconDTO.getTitle());
                if(StringUtils.isNotBlank(title)){
                    iconDTO.setTitle(title);
                }
            }

            if (Objects.equals(iconDTO.getType(), 1)) {
                type1IconList.add(iconDTO);
            }
            if (Objects.equals(iconDTO.getType(), 2)) {
                type2IconList.add(iconDTO);
            }
        }
        Map<String, HomeIconDTO> result = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(type1IconList)) {
            result.put("icons0", new HomeIconDTO("Store management", type1IconList));
        }
        if(CollectionUtils.isNotEmpty(type2IconList)) {
            result.put("icons1", new HomeIconDTO("Equipment management", type2IconList));
        }
        return result;
    }
    static {
        ClassLoader classLoader = ResourceReader.class.getClassLoader();
        try (InputStream inputStream = classLoader.getResourceAsStream("menu/app_menu.json")) {
            if (inputStream != null) {
                String fileContent = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8)).lines().collect(Collectors.joining(""));
                Map<Integer, Map<String, String>> multilingualMap = JsonUtil.jsonToBean(fileContent, new TypeReference<Map<Integer, Map<String, String>>>() {});

                for (Map.Entry<Integer, Map<String, String>> entry : multilingualMap.entrySet()) {
                    Map<MultilingualEnum, String> translationForValue = entry.getValue().entrySet().stream()
                            .collect(Collectors.toMap(
                                    e -> MultilingualEnum.findLanguageEnum(e.getKey()),
                                    Map.Entry::getValue,
                                    (v1, v2) -> v1
                            ));
                    MENU_CACHE.put(entry.getKey(), translationForValue);
                }

            } else {
                log.error("Resource file not found: menu/app_menu.json");
            }
        } catch (Exception e) {
            log.error("File loading exception: {}", e);
        }
    }

}
