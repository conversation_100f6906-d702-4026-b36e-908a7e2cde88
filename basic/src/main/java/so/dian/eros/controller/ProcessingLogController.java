package so.dian.eros.controller;

import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.talos.anonations.RTMonitor;
import so.dian.eros.biz.facade.ProcessingLogFacade;
import so.dian.eros.common.constant.CommonConstants;
import so.dian.eros.common.util.DateUtil;
import so.dian.eros.pojo.entity.LogsDO;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 处理日志控制类
 */
@RestController
@Slf4j
public class ProcessingLogController extends BaseController{

    @Resource
    private ProcessingLogFacade processingLogFacade;

    /**
     * pc端客服：新增处理日志
     */
    @RTMonitor
    @PostMapping(value = "/processingLog", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult addProcessingLog(@RequestBody @Valid AddProcessingLogParam param) {
        log.info("addProcessingLog-新增处理日志, param=[{}]", param);
        processingLogFacade.add(param.transToLogsDO(getUser().getUserId()));
        return BizResult.create(null);
    }

    @Data
    static class AddProcessingLogParam {
        /**
         * 来电电话
         */
        @NonNull
        private String incomingCall;
        /**
         * 来电身份, 1:消费者; 2:合作商户; 3:其他
         */
        @NonNull
        private Integer callType;
        /**
         * 城市ID
         */
        @NonNull
        private Long cityId;
        /**
         * 紧急程度, 1:紧急; 2:普通
         */
        @NonNull
        private Integer level;
        /**
         * 问题描述
         */
        @NonNull
        private String description;
        /**
         * 处理措施
         */
        private String measure;
        /**
         * 业务类型, 1:异常订单
         */
        @NonNull
        private Integer businessType;
        /**
         * 业务单号
         */
        @NonNull
        private String businessNo;
        /**
         * 状态, 0:处理中; 1:已处理
         */
        @NonNull
        private Integer status;

        LogsDO transToLogsDO(Long ossUserId) {
            LogsDO param = new LogsDO();
            param.setBusinessNo(businessNo);
            param.setBusinessType(businessType);
            param.setCallType(callType);
            param.setDeleted(CommonConstants.ZERO);
            param.setDescription(description);
            param.setGmtCreate(DateUtil.converter2ZeroTimeZone(System.currentTimeMillis()));
            param.setGmtUpdate(DateUtil.converter2ZeroTimeZone(System.currentTimeMillis()));
            param.setIncomingCall(incomingCall);
            param.setLevel(level);
            param.setMeasure(measure);
            param.setStatus(status);
            param.setOperatorId(ossUserId);
            param.setCityId(cityId);
            return param;
        }
    }
}
