package so.dian.eros.controller.aeacus;

import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.dto.OssPermissionDTO;
import com.chargebolt.aeacus.entity.dataobject.PermissionDO;
import com.chargebolt.aeacus.service.manager.PermissionManager;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.controller.BaseController;
import so.dian.eros.manager.aeacus.PermissionRemoteManager;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class PermissionOperateController extends BaseController{

    @Resource
    private PermissionManager permissionManager;
    /**
     * 获取当前登陆用户的所有权限
     * @return BizResult<List<OssPermissionDTO>>
     */
    @Login
    @RequestMapping(value = "/permission/getCurrentUser", method = RequestMethod.GET)
    public BizResult<List<OssPermissionDTO>> getCurrentUser(){
        List<OssPermissionDTO> ossPermissionDTOS = Lists.newLinkedList();
        List<PermissionDO> permissionDOS = permissionManager.getPermissionByUserId(getUser().getUserId());

        if (!CollectionUtils.isEmpty(permissionDOS)) {
            for (PermissionDO permissionDO : permissionDOS) {
                ossPermissionDTOS.add(OssPermissionDTO
                        .builder()
                        .id(permissionDO.getId())
                        .groupId(permissionDO.getGroupId())
                        .url(permissionDO.getUrl())
                        .img(permissionDO.getImg())
                        .code(permissionDO.getCode())
                        .build());
            }
        }
        return BizResult.create(ossPermissionDTOS);
    }
}
