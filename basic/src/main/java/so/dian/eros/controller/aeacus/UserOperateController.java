package so.dian.eros.controller.aeacus;

import com.chargebolt.aeacus.annotation.Authentication;
import com.chargebolt.aeacus.annotation.Login;
import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.controller.BaseController;
import so.dian.eros.manager.UserRemoteManager;

import javax.annotation.Resource;

@RestController
@Slf4j
public class UserOperateController extends BaseController{

    @Resource
    private UserRemoteManager userRemoteManager;

    @Login
    @Authentication(permissionCode = "30202")
    @PostMapping(value = "/user/create", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<Boolean> create(@RequestBody OssUserOpDTO userOpDTO){
        OssUserDTO ossUserDTO = getUser();
//        if(!StringUtils.isEmpty(userOpDTO.getMobile())){
//            userOpDTO.setMobile("+" + mobilePrefix + userOpDTO.getMobile());
//        }
        if(StringUtils.isBlank(userOpDTO.getNationCode())){

        }
        if(StringUtils.isBlank(userOpDTO.getMobile())){

        }
        // 组装号码

        return userRemoteManager.create(userOpDTO,ossUserDTO);
    }

    @Login
    @Authentication(permissionCode = "30202")
    @PostMapping(value = "/user/update", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<Boolean> update(@RequestBody OssUserOpDTO updateDTO){
        OssUserDTO ossUserDTO = getUser();
//        if(!StringUtils.isEmpty(updateDTO.getMobile()) && !updateDTO.getMobile().startsWith("+")){
//            updateDTO.setMobile("+" + mobilePrefix + updateDTO.getMobile());
//        }
        return userRemoteManager.update(updateDTO,ossUserDTO);
    }

    @Login
    @PostMapping(value = "/user/update/password", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<Boolean> updatePassword(@RequestBody OssUserPasswordOpDTO passwordOpDTO){
        OssUserDTO ossUserDTO = getUser();
        passwordOpDTO.setId(ossUserDTO.getUserId());
        passwordOpDTO.setType(AeacusConstsnts.USER_PASSWORD_UPDATE);
        return userRemoteManager.updatePassword(passwordOpDTO,ossUserDTO);
    }

    @Login
    @Authentication(permissionCode = "30204")
    @PostMapping(value = "/user/update/passwordReset", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<Boolean> resetPassword(@RequestBody OssUserPasswordOpDTO passwordOpDTO){
        OssUserDTO ossUserDTO = getUser();
        passwordOpDTO.setType(AeacusConstsnts.USER_PASSWORD_RESET);
        return userRemoteManager.updatePassword(passwordOpDTO,ossUserDTO);
    }

    @Login
    @Authentication(permissionCode = "30205")
    @PostMapping(value = "/user/disable", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<Boolean> disable(@RequestBody OssUserOpDTO userOpDTO){
        OssUserDTO ossUserDTO = getUser();
        return userRemoteManager.disable(userOpDTO,ossUserDTO);
    }

    @Login
    @Authentication(permissionCode = "30205")
    @PostMapping(value = "/user/enable", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<Boolean> enable(@RequestBody OssUserOpDTO userOpDTO){
        OssUserDTO ossUserDTO = getUser();
        return userRemoteManager.enable(userOpDTO,ossUserDTO);
    }

    @Login
    @Authentication(permissionCode = "30201")
    @PostMapping(value = "/user/queryByPage", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<PageData<OssUserDTO>> queryByPage(@RequestBody OssUserQueryDTO queryDTO){
        return userRemoteManager.query(queryDTO);
    }

    @Login
//    @Authentication(permissionCode = "30201")
    @RequestMapping(value = "/user/queryById", method = RequestMethod.GET)
    public BizResult<OssUserDTO> queryById(@RequestParam("id") Long id){
        return userRemoteManager.queryById(id);
    }

    @Login
    @RequestMapping(value = "/user/getPermissions", method = RequestMethod.GET)
    public BizResult<OssUserDTO> getPermissions(@RequestParam("id") Long id){
        return userRemoteManager.queryById(id);
    }

//    @MerchantLogin
    @PostMapping(value = "/user/update/merchantUserPassword", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BizResult<Boolean> merchantUserPassword(@RequestBody OssUserPasswordOpDTO passwordOpDTO){
        log.info("merchantUserPassword, oriPassword:{}, password:{}", passwordOpDTO.getOriPassword(), passwordOpDTO.getPassword());
        OssMerchantUserDTO ossMerchantUserDTO = getMerchantUser();
        passwordOpDTO.setId(ossMerchantUserDTO.getUserId());
        passwordOpDTO.setOperatorId(ossMerchantUserDTO.getUserId());
        passwordOpDTO.setOperatorName(ossMerchantUserDTO.getName());
        return userRemoteManager.updateMerchantUserPassword(passwordOpDTO);
    }
}
