package so.dian.eros.manager.aeacus;

import com.chargebolt.aeacus.dto.OssIconDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.remote.aeacus.AeacusIconServiceFacade;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class IconRemoteManager {
    @Resource
    private AeacusIconServiceFacade aeacusIconServiceFacade;

    /**
     * 获取所有icon信息
     * @return BizResult<List<OssIconDTO>>
     */
    public BizResult<List<OssIconDTO>> getAllIcons() {
        return aeacusIconServiceFacade.getAllIcons();
    }
}
