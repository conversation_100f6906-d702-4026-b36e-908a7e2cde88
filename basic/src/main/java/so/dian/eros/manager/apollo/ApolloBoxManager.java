package so.dian.eros.manager.apollo;

import com.alibaba.fastjson.JSON;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import so.dian.apollo.common.entity.BizResult;
import so.dian.apollo.dto.param.ApolloDeviceParamDTO;
import so.dian.apollo.dto.param.box.ApolloBoxParamDTO;
import so.dian.apollo.dto.param.powerbank.ApolloPowerBankParamDTO;
import so.dian.apollo.dto.result.box.ApolloBoxDTO;
import so.dian.apollo.dto.result.device.ApolloDeviceDTO;
import so.dian.apollo.dto.result.powerbank.ApolloPowerBankDTO;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.demeter.remote.ApolloRemoteService;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * Created by tom on 2017/6/8.
 */
@Service
@Slf4j(topic = "apollo")
public class ApolloBoxManager {

    @Resource
    private ApolloRemoteService apolloRemoteApi;

    /**
     * 从小电云获取盒子信息
     *
     * @param boxNo 盒子编号
     */
    public ApolloBoxDTO getBoxFromApollo(String boxNo) {
        ApolloBoxParamDTO boxParamDTO = new ApolloBoxParamDTO();
        boxParamDTO.setDeviceNo(boxNo);
        BizResult<ApolloBoxDTO> bizResult = apolloRemoteApi
                .getBox("apolloToken", boxParamDTO);
        if (!bizResult.isSuccess()) {
            log.error("调用Apollo接口获取盒子信息失败,设备编号：{},错误信息：{},", boxNo, JSON.toJSONString(bizResult));
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.SERVER_UNAVAILABLE, "设备编号：" + boxNo + ",Apollo异常：" + bizResult.getMsg());
        }
        return bizResult.getData();
    }

    /**
     * 从小电云获取盒子信息
     *
     * @param boxNos 盒子编号
     */
    public Map<String, ApolloBoxDTO> getBoxFromApolloWithBoxNos(List<String> boxNos) {
        ApolloBoxParamDTO boxParamDTO = new ApolloBoxParamDTO();
        boxParamDTO.setDeviceNos(boxNos);
        BizResult<Map<String, ApolloBoxDTO>> bizResult = apolloRemoteApi.gets("apolloToken", boxParamDTO);
        log.info("getBoxFromApolloWithBoxNos, boxNos=[{}], data=[{}]", boxNos, JsonUtil.beanToJson(bizResult));

        if (!bizResult.isSuccess()) {
            log.error("调用Apollo接口获取盒子信息失败,设备编号：{},错误信息：{},", boxNos, JSON.toJSONString(bizResult));
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.SERVER_UNAVAILABLE, "设备编号：" + boxNos + ",Apollo异常：" + bizResult.getMsg());
        }
        return bizResult.getData();
    }

    /**
     * 从小电云获取充电宝信息
     *
     * @param powerBankNo 充电宝编号
     */
    public ApolloPowerBankDTO getPowerBankFromApollo(String powerBankNo) {
        ApolloPowerBankParamDTO powerBankParamDTO = new ApolloPowerBankParamDTO();
        powerBankParamDTO.setPowerBankNo(powerBankNo);
        BizResult<ApolloPowerBankDTO> bizResult = apolloRemoteApi
                .getPowerBank("apolloToken", powerBankParamDTO);
        if (!bizResult.isSuccess()) {
            log.error("调用Apollo接口获取充电宝信息失败,设备编号：{},错误信息：{},", powerBankNo, JSON.toJSONString(bizResult));
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.SERVER_UNAVAILABLE, "设备编号：" + powerBankNo + ",Apollo异常：" + bizResult.getMsg());
        }
        return bizResult.getData();
    }

    /**
     * 从小电云获取盒子cloudId信息
     *
     * @param deviceNo
     */
    public String getCloudId(String deviceNo) {
        ApolloDeviceParamDTO apolloDeviceParamDTO = new ApolloDeviceParamDTO();
        apolloDeviceParamDTO.setDeviceNo(deviceNo);
        BizResult<ApolloDeviceDTO> bizResult = apolloRemoteApi.getBaseInfo("apolloToken", apolloDeviceParamDTO);
        if (!bizResult.isSuccess()) {
            log.error("调用Apollo接口获取设备信息失败,设备编号：{},错误信息：{},", deviceNo, JSON.toJSONString(bizResult));
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.SERVER_UNAVAILABLE, "设备编号：" + deviceNo + ",Apollo异常：" + bizResult.getMsg());
        }
        if (Objects.isNull(bizResult.getData()) || StringUtils.isBlank(bizResult.getData().getCloudId())) {
            return null;
        }
        return bizResult.getData().getCloudId();
    }
}
