package so.dian.eros.manager.hermes;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.aeacus.dto.OssUserDTO;
import com.chargebolt.aeacus.service.AeacusUserService;
import com.chargebolt.commons.enums.AuthorityLevelEnum;
import com.chargebolt.commons.enums.language.OrderDetailStateLanguageEnum;
import com.chargebolt.commons.enums.language.UserSignTypeLanguageEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.currency.CurrencyManager;
import com.chargebolt.ezreal.response.agent.AgentSimpleResponse;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.service.agent.AgentService;
import com.chargebolt.service.authority.LoginUserDataAuthorityService;
import com.chargebolt.tenant.service.TenantService;
import com.chargebolt.theseus.common.exce.SysErrorEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.commons.eden.util.LocalEnumUtils;
import so.dian.talos.anonations.RTMonitor;
import so.dian.eros.biz.facade.CommonFacade;
import so.dian.eros.biz.facade.ProcessingLogFacade;
import so.dian.eros.biz.service.AliyunLogService;
import so.dian.eros.common.constant.CommonConstants;
import so.dian.eros.common.constant.OrderConstants;
import so.dian.eros.common.enums.OrderExportHeaderEnum;
import so.dian.eros.common.enums.aliyunLog.MqttActionEnum;
import so.dian.eros.common.enums.aliyunLog.MqttEventEnum;
import so.dian.eros.common.enums.aliyunLog.PowerbankStatusEnum;
import so.dian.eros.common.enums.aliyunLog.SlotStatusEnum;
import so.dian.eros.common.enums.ossLog.StatusEnum;
import so.dian.eros.common.exception.AliyunLogErrorEnum;
import so.dian.eros.common.exception.HermesErrorEnum;
import so.dian.eros.common.util.BizResultUtil;
import so.dian.eros.common.util.DateUtil;
import so.dian.eros.common.util.LocationUtil;
import so.dian.eros.controller.hermes.BaseHermesOrderController;
import so.dian.eros.manager.apollo.ApolloBoxManager;
import so.dian.eros.manager.hera.HeraManager;
import so.dian.eros.manager.hermes.convert.ExBoxOrderVoConverter;
import so.dian.eros.manager.hermes.convert.OrderDtoVoConverter;
import so.dian.eros.manager.hermes.vo.*;
import so.dian.eros.manager.prometheus.PrometheusManager;
import so.dian.eros.pojo.dto.LogsDTO;
import so.dian.eros.pojo.enums.RefundErrorTypeEnum;
import so.dian.eros.pojo.param.ReportLogParam;
import so.dian.eros.remote.aeacus.AeacusUserServiceFacade;
import so.dian.eros.remote.hermes.OssBoxApi;
import so.dian.eros.remote.hermes.OssBoxOperateApi;
import so.dian.eros.remote.theseus.RegionClient;
import so.dian.hermes.client.pojo.dto.oss.OssBoxOrderDTO;
import so.dian.hermes.client.pojo.dto.oss.OssBoxOrderPageDTO;
import so.dian.hermes.client.pojo.dto.oss.QueryOrderExceptionListDTO;
import so.dian.hermes.client.pojo.enums.OrderStatusEnum;
import so.dian.hermes.client.pojo.param.BaseOperateParam;
import so.dian.hermes.client.pojo.param.oss.*;
import so.dian.mofa3.lang.money.CurrencyEnum;
import so.dian.mofa3.lang.money.MoneyFormatter;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.prometheus.client.common.enums.UserLoginTypeEnum;
import so.dian.mofa3.lang.util.Profiler;
import so.dian.prometheus.client.dto.UserDTO;
import so.dian.talos.client.dto.MerchantDTO;
import so.dian.talos.client.dto.ShopDTO;
import so.dian.talos.client.enums.LanguageEnum;
import so.dian.talos.common.exception.OrderErrorEnum;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * HermesBoxManager
 *
 * <AUTHOR>
 * @desc 订单中心盒子订单服务
 * @date 18/1/26
 */
@Slf4j(topic = "hermes")
@Service
public class HermesBoxManager {

    private static final int REFUND_ERROR_CODE_BEGIN = 40000;
    private static final int REFUND_ERROR_CODE_END = 40100;
    private static final int REFUND_ZERO_CODE = 10103;

    @Resource
    private OssBoxOperateApi ossBoxOperateApi;
    @Resource
    private OssBoxApi ossBoxApi;
    @Resource
    private BoxDeviceOrderManager boxDeviceOrderManager;
    @Resource
    private PrometheusManager prometheusManager;
    @Resource
    private HeraManager heraManager;
    @Resource
    private AeacusUserServiceFacade aeacusUserServiceFacade;
    @Resource
    private ApolloBoxManager apolloBoxManager;
    @Resource
    private AliyunLogService aliyunLogService;
    @Resource
    private CommonFacade commonFacade;
    @Resource
    private ProcessingLogFacade processingLogFacade;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private RegionClient regionClient;
    @Resource
    private CurrencyManager currencyManager;
    @Resource
    private LoginUserDataAuthorityService loginUserDataAuthorityService;

    @Resource
    private AeacusUserService aeacusUserService;
    @Resource
    private AgentService agentService;
    @Resource
    private TenantService tenantService;

    /**
     * 订单详情是否查询退款信息开关
     */
    @Value("${hera.refundSwitch}")
    private Boolean refundSwitch;

    /**
     * 取消订单
     *
     * @param operateParam BaseOperateParam
     * @return BizResult
     */
    @RTMonitor
    public BizResult cancel(BaseOperateParam operateParam) {
        BizResult result = ossBoxOperateApi.cancel(operateParam);
        if (null != result && result.isSuccess()) {
            return result;
        }
        if (null == result) {
            log.error("盒子订单取消错误[code:{},msg={}],param=[{}]", 2000, "订单不存在", operateParam);
            return BizResult.error(ErrorCodeEnum.NOT_FOUND);
        }
        log.error("盒子订单取消错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), operateParam);
        return result;
    }

    /**
     * 订单暂停计费
     *
     * @param operateParam BaseOperateParam
     * @return BizResult
     */
    @RTMonitor
    public BizResult pause(BaseOperateParam operateParam) {
        BizResult result = ossBoxOperateApi.pause(operateParam);
        if (null != result && result.isSuccess()) {
            return result;
        }
        if (null == result) {
            log.error("盒子订单暂停计费错误[code:{},msg={}],param=[{}]", 2000, "订单不存在", operateParam);
            return BizResult.error(ErrorCodeEnum.NOT_FOUND);
        }
        log.error("盒子订单暂停计费错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), operateParam);
        return result;
    }

    /**
     * 结束订单
     *
     * @param boxAlterAmountParam BoxAlterAmountParam
     * @return BizResult
     */
    @RTMonitor
    public BizResult end(BoxAlterAmountParam boxAlterAmountParam) {
        BizResult result = ossBoxOperateApi.end(boxAlterAmountParam);
        if (null != result && result.isSuccess()) {
            return result;
        }
        if (null == result) {
            log.error("盒子订单暂停计费错误[code:{},msg={}],param=[{}]", 2000, "订单不存在", boxAlterAmountParam);
            return BizResult.error(ErrorCodeEnum.NOT_FOUND);
        }
        log.error("盒子订单结束订单错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), boxAlterAmountParam);
        return result;
    }

    /**
     * 订单列表查询
     *
     * @param listParam OssQueryOrderListParam
     * @return BizResult
     */
    @RTMonitor
    public BizResult getBoxOrderPage(OssQueryOrderListParam listParam, ApplicationContext ctx, Locale locale) {
        Profiler.enter("ossBoxApi.list");
        BizResult<OssBoxOrderPageDTO> result = ossBoxApi.list(listParam);
        Profiler.release();
        if (null != result && result.isSuccess()) {
            OssBoxOrderPageDTO dto = result.getData();
            //无结果
            if (dto == null || CollectionUtils.isEmpty(dto.getList())) {
                return BizResult.create(PageVO.of());
            }
            //dto -> vo
            Profiler.enter("OrderDtoVoConverter.boxList");
            List<BoxOrderVO> list = OrderDtoVoConverter.boxList(dto.getList(), false, ctx, locale);
            Profiler.release();
            Profiler.enter("handleBoxOrderVo");
            handleBoxOrderVo(list, listParam.getCooperatorId(), false, ctx, locale);
            Profiler.release();
            return BizResult.create(PageVO.of(listParam.getPageNum(), dto.getTotal(), list));
        }
        if (null == result) {
            log.error("盒子订单列表查询错误[code:{},msg={}],param=[{}]", 2000, "订单不存在", listParam);
            return BizResult.error(ErrorCodeEnum.NOT_FOUND);
        }

        log.error("盒子订单列表查询错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), listParam);
        return result;
    }

    @RTMonitor
    public HSSFWorkbook exportList(OssQueryOrderListParam listParam, LanguageEnum language) {
        int pageNo = 1;
        listParam.setPageNum(pageNo);
        listParam.setPageSize(OrderConstants.EXPORT_PAGE_SIZE);

        BizResult<OssBoxOrderPageDTO> firstPageResult = ossBoxApi.list(listParam);
        if (Objects.isNull(firstPageResult) || !firstPageResult.isSuccess()) {
            log.error("导出订单列表错误[code:{},msg={}],param=[{}]", 2000, "调用远程出错", listParam);
            throw new I18nMessageException(ErrorCodeEnum.FALLBACK);
        }

        if (firstPageResult.getData().getTotal() > OrderConstants.EXPORT_MAX_SIZE) {
            throw new I18nMessageException(OrderErrorEnum.EXPORT_TOO_MUCH);
        }

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("box order");
        buildHeader(sheet, language);

        List<OssBoxOrderDTO> orderDTOList = firstPageResult.getData().getList();
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return workbook;
        }
        // 第一页数据
        boolean needContinue = true;
        if (orderDTOList.size() < OrderConstants.EXPORT_PAGE_SIZE) {
            needContinue = false;
        }
        int rowIndex = 0;
        rowIndex = buildOrderData(orderDTOList, sheet, rowIndex, language, listParam.getCooperatorId());

        while (needContinue) {
            pageNo = pageNo + 1;
            listParam.setPageNum(pageNo);
            BizResult<OssBoxOrderPageDTO> orderPageResult = ossBoxApi.list(listParam);
            orderDTOList = orderPageResult.getData().getList();
            if (CollectionUtils.isEmpty(orderDTOList)) {
                break;
            }
            if (orderDTOList.size() < OrderConstants.EXPORT_PAGE_SIZE) {
                needContinue = false;
            }
            rowIndex = buildOrderData(orderDTOList, sheet, rowIndex, language, listParam.getCooperatorId());
        }
        return workbook;
    }

    public Integer buildOrderData(List<OssBoxOrderDTO> orderDTOList, HSSFSheet sheet, int rowIndex, LanguageEnum language, Long cooperatorId) {
        Set<Long> userIds = new HashSet<>();
        Set<Long> shopIds = new HashSet<>();
        for (OssBoxOrderDTO orderDTO : orderDTOList) {
            userIds.add(orderDTO.getUserId());
            shopIds.add(orderDTO.getLoanShopId());
            shopIds.add(orderDTO.getReturnShopId());
        }
        // 获取用户信息
        List<UserDTO> userDTOS = prometheusManager.getUsers(userIds);
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userDTOS)) {
            for (UserDTO dto : userDTOS) {
                userDTOMap.put(dto.getId(), dto);
            }
        }

        // 获取门店ID与门店信息的map
        Map<Long, ShopDTO> shopDTOMap = commonFacade.getShopDTOMap(new ArrayList<>(shopIds), cooperatorId);
        for (OssBoxOrderDTO orderDTO : orderDTOList) {
            rowIndex = rowIndex + 1;
            buildOrderData(orderDTO, sheet, rowIndex, language, userDTOMap, shopDTOMap);
        }
        // 写数据
        return rowIndex;
    }

    public void buildOrderData(OssBoxOrderDTO orderDTO, HSSFSheet sheet, int rowIndex, LanguageEnum language,
                               Map<Long, UserDTO> userDTOMap, Map<Long, ShopDTO> shopDTOMap) {
        HSSFRow row = sheet.createRow(rowIndex);
        Locale locale = LocationUtil.getLocale(language.getLanguage());
        Object[] objects = {};
        String createTime = DateUtil.formatDate2String(orderDTO.getLoanTime(), OrderConstants.DATETIME_FORMAT);
        buildCell(row, OrderExportHeaderEnum.ORDER_CREATE_TIME.getCode(), createTime);
        buildCell(row, OrderExportHeaderEnum.ORDER_NO.getCode(), orderDTO.getOrderNo());
        buildCell(row, OrderExportHeaderEnum.ORDER_STATUS.getCode(), OrderDetailStateLanguageEnum.getDescrById(orderDTO.getStatus()));
        String signType = "";
        String userId = "";
        String userName = "";
        if (Objects.nonNull(orderDTO.getUserId())) {
            UserDTO userDTO = userDTOMap.get(orderDTO.getUserId());
            if (Objects.nonNull(userDTO)) {
                userId = orderDTO.getUserId().toString();
                userName = userDTO.getNick();
                signType = LocalEnumUtils.getDescByCode(UserLoginTypeEnum.class, userDTO.getSignType());
            }

        }
        buildCell(row, OrderExportHeaderEnum.LOGIN_WAY.getCode(), signType);
        buildCell(row, OrderExportHeaderEnum.USER_ID.getCode(), userId);
        buildCell(row, OrderExportHeaderEnum.USER_NAME.getCode(), userName);
        String loanShopIdStr = "";
        String loanShopName = "";
        Long loanShopId = orderDTO.getLoanShopId();
        if (Objects.nonNull(loanShopId)) {
            loanShopIdStr = loanShopId.toString();
        }
        ShopDTO loanShopDTO = shopDTOMap.get(loanShopId);
        if (Objects.nonNull(loanShopDTO)) {
            loanShopName = loanShopDTO.getName();
        }
        AgentSimpleResponse agentSimpleResponse= agentService.getDeviceTopLevelAgent(orderDTO.getLoanBoxNo());
        buildCell(row, OrderExportHeaderEnum.CURRENCY_CODE.getCode(), agentSimpleResponse.getCurrencyCode());
        MultiCurrencyMoney orderAmountMoney = new MultiCurrencyMoney(0, agentSimpleResponse.getCurrencyCode());
        if(Objects.nonNull(orderDTO.getOrderAmount())){
            orderAmountMoney.setCent(orderDTO.getOrderAmount());
        }
        buildCell(row, OrderExportHeaderEnum.ORDER_AMOUNT.getCode(), MoneyFormatter.format(orderAmountMoney));
        MultiCurrencyMoney payAmountMoney = new MultiCurrencyMoney(0, agentSimpleResponse.getCurrencyCode());
        if(Objects.nonNull(orderDTO.getPayAmount())){
            payAmountMoney.setCent(orderDTO.getPayAmount());
        }
        buildCell(row, OrderExportHeaderEnum.PAID_AMOUNT.getCode(), MoneyFormatter.format(payAmountMoney));

        buildCell(row, OrderExportHeaderEnum.PAY_TIME.getCode(), DateUtil.formatDate2String(orderDTO.getPayTime(), OrderConstants.DATETIME_FORMAT));
        buildCell(row, OrderExportHeaderEnum.REFUND_AMOUNT.getCode(), buildAmount(orderDTO.getRefundAmount(), agentSimpleResponse.getCurrencyCode()));
        buildCell(row, OrderExportHeaderEnum.REFUND_TIME.getCode(), DateUtil.formatDate2String(orderDTO.getRefundTime(), OrderConstants.DATETIME_FORMAT));
        buildCell(row, OrderExportHeaderEnum.RETURN_TIME.getCode(), DateUtil.formatDate2String(orderDTO.getReturnTime(), OrderConstants.DATETIME_FORMAT));
        String payway = "";
        if(Objects.nonNull(orderDTO.getPayway())){
            payway= PaywayEnum.explain(orderDTO.getPayway()).getName();
        }
        buildCell(row, OrderExportHeaderEnum.PAY_WAY.getCode(),payway);

        buildCell(row, OrderExportHeaderEnum.RENT_STORE_ID.getCode(), loanShopIdStr);
        buildCell(row, OrderExportHeaderEnum.RENT_STORE_NAME.getCode(), loanShopName);
        buildCell(row, OrderExportHeaderEnum.RENT_DEVICE_ID.getCode(), orderDTO.getLoanBoxNo());
        buildCell(row, OrderExportHeaderEnum.RENT_POSITION_NUMBER.getCode(), String.valueOf(orderDTO.getLoanSlot()));
        buildCell(row, OrderExportHeaderEnum.RENT_TIME.getCode(), DateUtil.formatDate2String(orderDTO.getLoanTime(), OrderConstants.DATETIME_FORMAT));
        String returnShopIdStr = "";
        String returnShopName = "";
        Long returnShopId = orderDTO.getReturnShopId();
        if (Objects.nonNull(returnShopId)) {
            returnShopIdStr = returnShopId.toString();
        }
        ShopDTO returnShopDTO = shopDTOMap.get(returnShopId);
        if (Objects.nonNull(returnShopDTO)) {
            returnShopName = returnShopDTO.getName();
        }
        buildCell(row, OrderExportHeaderEnum.RETURN_STORE_ID.getCode(),returnShopIdStr);
        buildCell(row, OrderExportHeaderEnum.RETURN_STORE_NAME.getCode(), returnShopName);
        buildCell(row, OrderExportHeaderEnum.RETURN_DEVICE_ID.getCode(), orderDTO.getReturnBoxNo());
        buildCell(row, OrderExportHeaderEnum.RETURN_POSITION_NUMBER.getCode(), Objects.nonNull(orderDTO.getReturnSlot())?orderDTO.getReturnSlot().toString():"");
        buildCell(row, OrderExportHeaderEnum.AGENT_ID.getCode(), agentSimpleResponse.getId().toString());
        buildCell(row, OrderExportHeaderEnum.AGENT_NAME.getCode(), agentSimpleResponse.getAgentName());

//        String returnSlot = "";
//        if (Objects.nonNull(orderDTO.getReturnSlot())) {
//            returnSlot = orderDTO.getReturnSlot().toString();
//        }


//        buildCell(row, OrderExportHeaderEnum.RETURN_POSITION_NUMBER.getCode(), returnSlot);
    }

    private String buildAmount(Integer amount, String currencyCode) {
        if (Objects.isNull(amount)) {
            return "";
        }
        MultiCurrencyMoney money = new MultiCurrencyMoney(0, Currency.getInstance(currencyCode));
        money.setCent(amount);
        return currencyCode + " " + money.getAmount().toString();
    }

    private static void buildHeader(HSSFSheet sheet, LanguageEnum language) {
        HSSFRow header = sheet.createRow(0);
        for (OrderExportHeaderEnum headerEnum : OrderConstants.NEED_EXPORT_HEADERS) {
            buildCell(header, headerEnum.getCode(), headerEnum.getByLang(language.getLanguage()));
        }
    }

    private static void buildCell(HSSFRow row, int column, String value) {
        HSSFCell cell = row.createCell(column);
        cell.setCellValue(value);
    }

    /**
     * 订单详情查询
     *
     * @param orderDetailParam BaseHermesOrderController.OrderDetailParam
     * @return BizResult
     */
    @RTMonitor
    public BizResult getBoxOrderDetail(BaseHermesOrderController.OrderDetailParam orderDetailParam, ApplicationContext ctx, Locale locale) {

        OssBoxOrderDTO orderDetail = getOrderDetail(orderDetailParam.getOrderNo(), orderDetailParam.getCooperatorId());
        checkEditAuth(orderDetail);
        List<BoxOrderVO> list = OrderDtoVoConverter.boxList(Collections.singletonList(orderDetail), true, ctx, locale);
        handleBoxOrderVo(list, orderDetailParam.getCooperatorId(), true, ctx, locale);
        BoxOrderVO boxOrderVO = list.get(CommonConstants.ZERO);
        boxDeviceOrderManager.setDeviceInfo(boxOrderVO, ctx, locale);
        return BizResult.create(boxOrderVO);
    }

    /**
     * 订单客服改价
     *
     * @param alterAmountParam BoxAlterAmountParam
     * @return BizResult
     */
    @RTMonitor
    public BizResult alterAmount(BoxAlterAmountParam alterAmountParam,OssUserDTO currentUser) {

        OssBoxOrderDTO orderDetail = getOrderDetail(alterAmountParam.getOrderNo(), currentUser.getCooperatorId());
        checkEditAuth(orderDetail);
        BizResult result = ossBoxOperateApi.alterAmount(alterAmountParam);
        if (null != result && result.isSuccess()) {
            return result;
        }
        if (null == result) {
            log.error("盒子订单改价错误[code:{},msg={}],param=[{}]", 2000, "订单不存在", alterAmountParam);
            return BizResult.error(ErrorCodeEnum.NOT_FOUND);
        }
        log.error("盒子订单改价错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), alterAmountParam);
        return result;
    }

    /**
     * 获取订单详情
     */
    public OssBoxOrderDTO getOrderDetail(String orderNo, Long cooperatorId) {

        BizResult<OssBoxOrderDTO> result = ossBoxApi.detail(orderNo, cooperatorId);
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            log.error("盒子异常订单详情查询错误[code:{},msg={}],orderNo=[{}]", 2000, "订单不存在", orderNo);
            throw new I18nMessageException(SysErrorEnum.OBJECT_NOT_EXISTED);
        }
        return result.getData();
    }

    /**
     * 数据编辑鉴权
     */
    private void checkEditAuth(OssBoxOrderDTO orderDTO) {

        UserDataAuthorityContext authority = loginUserDataAuthorityService.getLoginUserDataAuthority();
        // 数据权限校验：1、普通用户没有分配权限 2、代理商只能分配自己公司的商户
        if (AuthorityLevelEnum.USER.getCode().equals(authority.getAuthorityLevel())
                && !orderDTO.getSellerId().equals(authority.getUserId())) {
            log.error("订单鉴权异常[code:{},msg={}],orderNo=[{}]", 2000, "当前用户:" + authority.getUserId() + "无权限", orderDTO.getOrderNo());
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.UNAUTHORIZED);
        }
        if (AuthorityLevelEnum.AGENT.getCode().equals(authority.getAuthorityLevel())) {
            // 根据订单的sellerId查询用户信息
            List<OssUserDTO> userList = aeacusUserService.queryByIds(Lists.newArrayList(orderDTO.getSellerId()));
            if (CollectionUtils.isEmpty(userList)) {
                log.error("订单鉴权异常[code:{},msg={}],orderNo=[{}]", 2000, "获取用户信息失败", orderDTO.getOrderNo());
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.UNAUTHORIZED);
            }
            if (!Objects.equals(authority.getAgentId(), userList.get(0).getAgentId())) {
                log.error("订单鉴权异常[code:{},msg={}],orderNo=[{}]", 2000, "当前代理商：" + authority.getAgentId() + "无权限", orderDTO.getOrderNo());
                // tag 未国际化
                throw BizException.create(ErrorCodeEnum.UNAUTHORIZED);
            }
        }
    }

    /**
     * 订单客服退款
     *
     * @param refundParam CsRefundParam
     * @return BizResult
     */
    @RTMonitor
    public BizResult csRefund(CsRefundParam refundParam, OssUserDTO currentUser) {

        OssBoxOrderDTO orderDetail = getOrderDetail(refundParam.getOrderNo(), currentUser.getCooperatorId());
        checkEditAuth(orderDetail);
        BizResult result = ossBoxOperateApi.csRefund(refundParam);
        if (null != result && result.isSuccess()) {
            return result;
        } else if (null == result) {
            log.error("盒子订单客服退款错误[code:{},msg={}],param=[{}]", 2000, "订单不存在", refundParam);
            // tag 未国际化
            throw BizException.create(ErrorCodeEnum.NOT_FOUND);
        } else if (REFUND_ERROR_CODE_BEGIN <= result.getCode() && REFUND_ERROR_CODE_END >= result.getCode()) {
            log.error("盒子订单退款错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), refundParam);
            return result.setCode(40001);
        } else if (REFUND_ZERO_CODE == result.getCode()) {
            log.error("盒子订单退款错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), refundParam);
            // tag 未国际化
            throw BizException.create(RefundErrorTypeEnum.ZERO_ORDER_CANNOT_REFUND);
        }
        log.error("盒子订单退款错误[code:{},msg={}],param=[{}]", result.getCode(), result.getMsg(), refundParam);
        return result;
    }

    /**
     * 根据用户ID集合查询用户最后一个未支付的订单
     */
    public OssBoxOrderPageDTO lastNoPayOrderByUserIdList(LastNoPayOrderByUserIdListParam var1) {
        BizResult<OssBoxOrderPageDTO> result = ossBoxApi.lastNoPayOrderByUserIdList(var1);
        if (null != result && result.isSuccess()) {
            return result.getData();
        }
        return null;
    }

    /**
     * 查询订单异常记录列表
     *
     * @return PageVO<BoxOrderExceptionListVO>
     */
    public PageVO<ExBoxOrderListVO> getExceptionOrderList(QueryOrderExceptionListParam queryParam, ApplicationContext ctx, Locale locale, Long cooperatorId) {
        // 1. 分页获取异常订单列表
        BizResult<QueryOrderExceptionListDTO> result = ossBoxApi.queryOrderExceptionList(queryParam);
        if (null == result || !result.isSuccess() || result.getData().getTotal() <= CommonConstants.ZERO) {
            return PageVO.of(CommonConstants.ZERO, CommonConstants.ZERO, new ArrayList<>());
        }
        // FIXME 没有使用，先固定写死
        List<ExBoxOrderListVO> exBoxOrderListVOS = ExBoxOrderVoConverter.converter2ExBoxOrderList(result.getData().getOrderExceptionListDTOList(), ctx, locale, "HKD");
        Set<Long> shopIds = exBoxOrderListVOS.stream().map(ExBoxOrderListVO::getShopId).collect(Collectors.toSet());

        // 2. 获取门店信息
        Map<Long, ShopDTO> shopDTOMap = commonFacade.getShopDTOMap(new ArrayList<>(shopIds), cooperatorId);

        // 3. 设置门店名称
        exBoxOrderListVOS.forEach(exBoxOrderListVO -> {
            ShopDTO shopDTO = shopDTOMap.get(exBoxOrderListVO.getShopId());
            if (null != shopDTO) {
                exBoxOrderListVO.setShopName(shopDTO.getName());
            }
        });

        // 4. 返回结果集合
        return PageVO.of(queryParam.getPageNum(), result.getData().getTotal(), exBoxOrderListVOS);
    }

    /**
     * 查询异常订单详情
     *
     * @param orderDetailParam OrderDetailParam
     * @param ctx              ApplicationContext
     * @param locale           Locale
     * @return ExBoxOrderVO
     */
    public ExBoxOrderVO getExceptionBoxOrderDetail(BaseHermesOrderController.OrderDetailParam orderDetailParam, ApplicationContext ctx, Locale locale) {
        // 1. 获取订单详情
        OssBoxOrderDTO orderDetail = getOrderDetail(orderDetailParam.getOrderNo(), orderDetailParam.getCooperatorId());
        // dto -> vo
        BoxOrderVO vo = OrderDtoVoConverter.getBaseBoxOrderVO(orderDetail, true, ctx, locale);
        List<BoxOrderVO> list = new ArrayList<>(CommonConstants.ONE);
        list.add(vo);

        // 2. 处理门店、商户及用户信息
        handleBoxOrderVo(list, orderDetailParam.getCooperatorId(), true, ctx, locale);
        BoxOrderVO boxOrderVO = list.get(CommonConstants.ZERO);
        boxDeviceOrderManager.setDeviceInfo(boxOrderVO, ctx, locale);

        // 3. 设置异常信息集合
        ExBoxOrderVO exBoxOrderVO = new ExBoxOrderVO();
        BeanUtils.copyProperties(boxOrderVO, exBoxOrderVO);
        exBoxOrderVO.setExList(ExBoxOrderVoConverter.converter2ExDetailVOList(orderDetail.getExceptionList(), ctx, locale));

        // 4. 设置处理日志集合
        List<Integer> processingLogStatus = new ArrayList<>();
        processingLogStatus.add(StatusEnum.ONGOING.getCode());
        processingLogStatus.add(StatusEnum.DONE.getCode());
        // 4.1 获取处理日志
        exBoxOrderVO.setProcessingLogs(processingLogFacade.queryByBusinessNoAndTypeAndStatus(orderDetailParam.getOrderNo(), CommonConstants.ONE, processingLogStatus, locale));
        // 4.2 默认显示新增处理日志按钮
        exBoxOrderVO.setCanAddProcessingLog(true);
        for (LogsDTO logsDTO : exBoxOrderVO.getProcessingLogs()) {
            if (StatusEnum.DONE.getCode().equals(logsDTO.getStatus())) {
                // 4.3 如果处理日志中有已处理状态的数据, 则隐藏新增处理日志按钮
                exBoxOrderVO.setCanAddProcessingLog(false);
                break;
            }
        }

        return exBoxOrderVO;
    }

    /**
     * 查询设备日志
     *
     * @param param  ReportLogParam
     * @param ctx    ApplicationContext
     * @param locale Locale
     * @return List<ReportLogVO>
     */
    public List<ReportLogVO> getReportLog(ReportLogParam param, ApplicationContext ctx, Locale locale) {
        String cloudId = param.getDeviceNo().startsWith("p") ? param.getDeviceNo() : apolloBoxManager.getCloudId(param.getDeviceNo());
        if (StringUtils.isBlank(cloudId)) {
            // tag 未国际化
            throw so.dian.commons.eden.exception.BizException.create(AliyunLogErrorEnum.CLOUD_ID_IS_NULL);
        } else {
            List<ReportLogVO> reportLogVOS = aliyunLogService.getMqttBoxReportLog(param.getFrom(), cloudId, param.getTo());
            reportLogVOS.forEach(reportLogVO -> {
                if (reportLogVO.getEvent() <= CommonConstants.ZERO) {
                    reportLogVO.setEventDesc(MqttActionEnum.getDescByCodeAndLocale(reportLogVO.getEvent(), locale));
                } else {
                    reportLogVO.setEventDesc(MqttEventEnum.getDescByCodeAndLocale(reportLogVO.getEvent(), locale));
                }
                if (null != reportLogVO.getEventStatus()) {
                    reportLogVO.setEventDesc(reportLogVO.getEventDesc() + ctx.getMessage("EVENT_STATUS_" + reportLogVO.getEventStatus(), null, locale));
                }
                reportLogVO.setSlotStatusDesc(SlotStatusEnum.getDescByCodeAndLocale(reportLogVO.getSlotStatus(), locale));
                reportLogVO.setPowerBankStatusDesc(PowerbankStatusEnum.getDescByCodeAndLocale(reportLogVO.getPowerBankStatus(), locale));
            });
            return reportLogVOS;
        }
    }

    /**
     * 完结异常订单记录
     *
     * @param orderNo String
     */
    public void finishOrderException(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            log.info("finishOrderException-完结异常订单记录, orderNo is null.");
            return;
        }
        BizResult result = ossBoxOperateApi.finishOrderException(orderNo);
        if (BizResultUtil.checkResult(result)) {
            log.error("完结异常订单记录错误, orderNo=[{}], result=[{}]", orderNo, result);
            throw so.dian.commons.eden.exception.BizException.create(HermesErrorEnum.FINISH_EXCEPTION_ORDER_ERROR);
        }
    }

    /**
     * 处理门店、商户及用户信息
     *
     * @param list         List<BoxOrderVO>
     * @param cooperatorId Long
     */
    private void handleBoxOrderVo(List<BoxOrderVO> list, Long cooperatorId, Boolean isDetails, ApplicationContext ctx, Locale locale) {

        Set<Long> userIds = new HashSet<>();
        Set<Long> shopIds = new HashSet<>();
        Set<Long> sellerIds = new HashSet<>();
        for (BoxOrderVO vo : list) {
            sellerIds.add(vo.getSellerId());
            userIds.add(vo.getBuyerId());
            shopIds.add(vo.getLoanShopId());
            shopIds.add(vo.getReturnShopId());
        }
        // 获取用户信息
        List<UserDTO> userDTOS = prometheusManager.getUsers(userIds);
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userDTOS)) {
            for (UserDTO dto : userDTOS) {
                userDTOMap.put(dto.getId(), dto);
            }
        }
        // 获取代理商信息
        List<OssUserDTO> userDTOList = aeacusUserService.queryByIds(new ArrayList<>(sellerIds));
        Map<Long, OssUserDTO> ossUserDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userDTOList)) {
            ossUserDTOMap= userDTOList.stream().collect(Collectors.toMap(OssUserDTO::getUserId, Function.identity()));
        }
        // 获取门店ID与门店信息的map
        Map<Long, ShopDTO> shopDTOMap = commonFacade.getShopDTOMap(new ArrayList<>(shopIds), cooperatorId);

        // 获取商户ID与商户信息的map
        Map<Long, MerchantDTO> merchantDTOMap = commonFacade.getMerchantDTOMap(new ArrayList<>(shopDTOMap.keySet()), cooperatorId);

        if (isDetails) {
            BoxOrderVO vo = list.get(CommonConstants.ZERO);
            // 设置用户信息
            setCommonInfo(vo, userDTOMap, shopDTOMap, merchantDTOMap, ctx, locale,ossUserDTOMap);
            // 设置详情信息
            setDetails(vo, locale);
            vo.setRefundSwitch(refundSwitch);
            if (refundSwitch) {
                // 开关开启, 则查询并设置退款信息
                setRefundListInfo(vo, ctx, locale);
            }
        } else {
            for (BoxOrderVO vo : list) {
                // 设置共有信息，包括用户、借出门店信息,代理商信息
                setCommonInfo(vo, userDTOMap, shopDTOMap, merchantDTOMap, ctx, locale,ossUserDTOMap);
                vo.setRentalTimes(buildRentalTimes( vo.getLoanTime(), vo.getReturnTime()));
            }
        }
    }

    private String buildRentalTimes(Long startTime, Long endTime){
        if (startTime == null || endTime == null) {
            return "";
        }
        Duration duration = Duration.ofMillis(endTime - startTime);
        long totalSeconds = duration.getSeconds();
        long hours = totalSeconds / 3600;
        long minutes = (totalSeconds % 3600) / 60;
        long seconds = totalSeconds % 60;
        StringBuilder timeStr = new StringBuilder();

        // 如果有小时，显示小时和分钟（即使分钟为0）
        if (hours > 0) {
            timeStr.append(hours).append("h");
            timeStr.append(minutes).append("m");
        }
        // 如果只有分钟，显示分钟
        else if (minutes > 0) {
            timeStr.append(minutes).append("m");
        }

        // 如果有秒数，总是显示秒数
        if (seconds > 0 || timeStr.length() > 0) {
            timeStr.append(seconds).append("s");
        }
        // 时间差
        return timeStr.toString();
    }
//    private String getRegionCurrency(AdministrativeDivisionsDTO region) {
//        if (Objects.isNull(region)) {
//            return currencyManager.getCurrency().getCurrencyCode();
//        }
//        try {
//            BizResult<RegionDTO> regionDTOBizResult = regionService.queryById(region.getRegionId().intValue());
//            if (Boolean.FALSE.equals(regionDTOBizResult.isSuccess())) {
//                return currencyManager.getCurrency().getCurrencyCode();
//            }
//            RegionDTO regionDTO = regionDTOBizResult.getData();
//            if (Objects.isNull(regionDTO) || Objects.isNull(regionDTO.getConfigMap())
//                    || Objects.isNull(regionDTO.getConfigMap().get("CURRENCY"))
//                    || StringUtils.isBlank(regionDTO.getConfigMap().get("CURRENCY"))) {
//                return currencyManager.getCurrency().getCurrencyCode();
//            }
//            return regionDTO.getConfigMap().get("CURRENCY");
//        } catch (Exception e) {
//            log.error("get regionId failed! {}", e.getMessage());
//        }
//        return null;
//    }

    /**
     * 设置用户信息
     *
     * @param vo         BoxOrderVO
     * @param userDTOMap Map<Long, UserDTO>
     */
    private void setCommonInfo(BoxOrderVO vo, Map<Long, UserDTO> userDTOMap,
                               Map<Long, ShopDTO> shopDTOMap, Map<Long, MerchantDTO> merchantDTOMap,
                               ApplicationContext ctx, Locale locale,Map<Long, OssUserDTO> ossUserDTOMap) {
        // 国际化参数
        Object[] objects = {};
        Long userId = vo.getBuyerId();

        if (null != userId) {
            UserDTO userDTO = userDTOMap.get(userId);
            if (null != userDTO) {
                vo.setUserNick(userDTO.getNick());
                vo.setMobile(StringUtils.isBlank(userDTO.getMobile()) ? ctx.getMessage("ORDER_BUYER_MOBILE", objects, locale) : userDTO.getMobile());
                vo.setEmail(userDTO.getEmail());
                Integer signTypeCode = userDTO.getSignType();
                if (null != signTypeCode) {
                    vo.setUserSignTypeCode(signTypeCode);
                    vo.setUserSignType(UserSignTypeLanguageEnum.getTranslationByValue(signTypeCode));
                }
            } else {
                vo.setUserNick(ctx.getMessage("ORDER_BUYER_NOT_EXIST", objects, locale));
                vo.setMobile(ctx.getMessage("ORDER_BUYER_MOBILE", objects, locale));
            }
        } else {
            vo.setUserNick(ctx.getMessage("ORDER_BUYER_NOT_EXIST", objects, locale));
            vo.setMobile(ctx.getMessage("ORDER_BUYER_MOBILE", objects, locale));
        }

        ShopDTO loanShopDTO = shopDTOMap.get(vo.getLoanShopId());
//         AdministrativeDivisionsDTO administrativeDivisionsDTO=  administrativeDivisionsService.queryById(loanShopDTO.getAdministrativeDivisionsId());
//         String currency = getRegionCurrency(administrativeDivisionsDTO);
        TenantAgentInfoResponse tenantAgentInfoResponse= tenantService.getTopTenantInfoByShopId(vo.getLoanShopId());
        CurrencyEnum currencyEnum = currencyManager.getCurrency(tenantAgentInfoResponse.getCurrencyCode());
        vo.setCurrency(currencyEnum.getCurrencyCode());
        vo.setCurrencySymbol(currencyEnum.getCurrencyLabel());
        // 设置借出门店信息
        if (null != loanShopDTO) {
            vo.setLoanShopName(loanShopDTO.getName());
            vo.setLoanShopContactName(loanShopDTO.getContactName());
            vo.setLoanShopContactMobile(loanShopDTO.getContactMobile());
            Long merchantId = loanShopDTO.getMerchantId();
            if (null != merchantId) {
                MerchantDTO merchantDTO = merchantDTOMap.get(merchantId);
                if (null != merchantDTO) {
                    vo.setLoanMerchantId(merchantId);
                    vo.setLoanMerchantName(merchantDTO.getName());
                }
            }
        }
        ShopDTO returnShopDTO = shopDTOMap.get(vo.getReturnShopId());
        // 设置归还门店信息
        if (null != returnShopDTO) {
            vo.setReturnShopName(returnShopDTO.getName());
            vo.setReturnShopContactName(returnShopDTO.getContactName());
            vo.setReturnShopContactMobile(returnShopDTO.getContactMobile());
            Long merchantId = returnShopDTO.getMerchantId();
            if (null != merchantId) {
                MerchantDTO merchantDTO = merchantDTOMap.get(merchantId);
                if (null != merchantDTO) {
                    vo.setReturnMerchantId(merchantId);
                    vo.setReturnMerchantName(merchantDTO.getName());
                }
            }
        }
        // 设置代理商信息
        OssUserDTO ossUserDTO = ossUserDTOMap.get(vo.getSellerId());
        if(Objects.nonNull(ossUserDTO)){
            vo.setAgentId(ossUserDTO.getAgentId());
            vo.setAgentName(ossUserDTO.getAgentName());
        }
    }

    /**
     * 设置详情信息
     *
     * @param vo BoxOrderVO
     */
    private void setDetails(BoxOrderVO vo, Locale locale) {
        // 设置支付、退款信息
        Integer payType = vo.getPayType();
        vo.setPaymentCode(payType);
        vo.setPayment(convertPaymentDesc(PaywayEnum.explain(payType), locale));

        if (PaywayEnum.DEPOSIT.getPayway().equals(payType)
                || PaywayEnum.PRE_AUTH_CAPTURE.getPayway().equals(payType)) {
            if (OrderStatusEnum.REFUND.getCode().equals(vo.getStatus())) {
                vo.setRefundChannel(vo.getPayment());
                // 1.2 设置退款操作用户信息
                setRefundOperUserInfo(vo);
            }
        }
    }

    private String convertPaymentDesc(PaywayEnum payTypeEnum, Locale locale) {
        if (payTypeEnum == null) {
            return "";
        }
//        if (Locale.UK.equals(locale)) {
//            return payTypeEnum.name();
//        }
        String msgCode= "PAYWAY_NAME_" + payTypeEnum.getPayway();
        return applicationContext.getMessage(msgCode, null, msgCode, locale);
    }

    /**
     * 设置退款操作用户信息
     *
     * @param vo BoxOrderVO
     */
    private void setRefundOperUserInfo(BoxOrderVO vo) {
        if (null != vo.getRefundOperUserId()) {
            BizResult<OssUserDTO> userDTOBizResult = aeacusUserServiceFacade.queryById(vo.getRefundOperUserId());
            if (null != userDTOBizResult && userDTOBizResult.isSuccess()) {
                OssUserDTO userDTO = userDTOBizResult.getData();
                if (null != userDTO) {
                    vo.setRefundOperUser(userDTO.getNickName());
                }
            }
        }
    }

    /**
     * 设置退款信息
     *
     * @param vo BoxOrderVO
     */
    private void setRefundListInfo(BoxOrderVO vo, ApplicationContext ctx, Locale locale) {
        // 1. 获取最新的退款信息
        RefundDTO refundDTO = heraManager.getLast(vo.getPayNo(), vo.getTradeNo());
        if (null == refundDTO) {
            refundDTO = new RefundDTO();
        }

        // 2. 查询所有退款操作
        List<RefundDTO> refundDTOS = heraManager.record(vo.getPayNo(), vo.getTradeNo());

        // 3. 获取oss用户信息
//         Map<Long, OssUserDTO> userDTOMap = this.getUsersFromAeacus(userIds);

        // 4. 设置最新一次退款信息
        vo.setLastRefundVO(refundDTO2RefundVO(vo, refundDTO, ctx, locale, refundDTO.getCurrency()));

        if (!CollectionUtils.isEmpty(refundDTOS) && refundDTOS.size() >= CommonConstants.THREE) {
            // 如果退款操作达到三次, 标记为true
            vo.setIsThreeTime(true);
        } else {
            vo.setIsThreeTime(false);
        }
        // 5. 设置所有退款信息
        if (!CollectionUtils.isEmpty(refundDTOS)) {
            List<RefundVO> refundVOS = new ArrayList<>(refundDTOS.size());
            for (RefundDTO refundDTO1 : refundDTOS) {
                refundVOS.add(refundDTO2RefundVO(vo, refundDTO1, ctx, locale, refundDTO.getCurrency()));
            }
            vo.setRefundVOS(refundVOS);
        }
    }

    /**
     * RefundDTO -> RefundVO
     *
     * @param vo        BoxOrderVO
     * @param refundDTO RefundDTO
     * @return RefundVO
     */
    private RefundVO refundDTO2RefundVO(BoxOrderVO vo, RefundDTO refundDTO, ApplicationContext ctx, Locale locale, String currencyCode) {
        RefundVO refundVO = new RefundVO();
        if (null == refundDTO || null == refundDTO.getId()) {
            return refundVO;
        }
        refundVO.setId(refundDTO.getId());
        MultiCurrencyMoney money = new MultiCurrencyMoney(0, currencyCode);
        money.setCent(refundDTO.getAmount() == null ? CommonConstants.LONG_ZERO : (long) refundDTO.getAmount());
        refundVO.setAmount(money.getAmount().toString());
        refundVO.setAmountFormatted(MoneyFormatter.format(money));
        refundVO.setStartTime(refundDTO.getCreateTime());
        refundVO.setErrorMsg(refundDTO.getErrorMsg());
        refundVO.setRefundChannel(vo.getPayment());
        refundVO.setRefundNo(refundDTO.getRefundNo());
        RefundStatus status = RefundStatus.explain(refundDTO.getStatus());
        if (null != status) {
            refundVO.setStatus(status.getCode());
            if (RefundStatus.INIT.getCode().equals(status.getCode())) {
                // 初始化
                refundVO.setStatusText(ctx.getMessage("REFUND_STATUS_0", null, locale));
                refundVO.setEndTime(refundDTO.getUpdateTime());
            } else if (RefundStatus.REFUNDED.getCode().equals(status.getCode())) {
                // 已退款
                refundVO.setStatusText(ctx.getMessage("REFUND_STATUS_1", null, locale));
                refundVO.setEndTime(refundDTO.getRefundTime());
            } else if (RefundStatus.REFUNDING.getCode().equals(status.getCode())) {
                // 退款中
                refundVO.setStatusText(ctx.getMessage("REFUND_STATUS_2", null, locale));
                refundVO.setEndTime(refundDTO.getUpdateTime());
            } else if (RefundStatus.FAIL.getCode().equals(status.getCode())) {
                // 退款失败
                refundVO.setStatusText(ctx.getMessage("REFUND_STATUS_3", null, locale));
                refundVO.setEndTime(refundDTO.getUpdateTime());
            }
        }
        // 退款方式
        if (CommonConstants.ZERO.equals(refundDTO.getRefundType())) {
            // 线下退款
            refundVO.setRefundWay(ctx.getMessage("ORDER_REFUND_WAY_OFFLINE", null, locale));
        } else {
            // 原路返回
            refundVO.setRefundWay(ctx.getMessage("ORDER_REFUND_WAY_ORIGIN", null, locale));
        }
        return refundVO;
    }
}
