package so.dian.eros.manager.hermes.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * ExBoxOrderListVO
 *
 * <AUTHOR>
 * @desc 订单异常记录列表
 * @date 20/3/30
 */
@Data
public class ExBoxOrderListVO {
    @Schema(title = "用户ID")
    private Long buyerId;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "订单号")
    private String orderNo;

    @Schema(title = "订单状态")
    private Integer orderStatus;

    @Schema(title = "订单状态描述")
    private String orderStatusDesc;

    @Schema(title = "借出设备编号")
    private String boxNo;

    @Schema(title = "充电宝编号")
    private String powerBankNo;

    @Schema(title = "借出门店ID")
    private Long shopId;

    @Schema(title = "借出门店名称")
    private String shopName;

    @Schema(title = "订单金额")
    private String orderAmount;

    @Schema(title = "异常类型：1、无法取出，2、无法归还，3、系统自动上报异常-无法取出，4、系统自动上报异常-无法归还")
    private Integer exType;

    @Schema(title = "异常类型描述")
    private String exTypeDesc;
}
