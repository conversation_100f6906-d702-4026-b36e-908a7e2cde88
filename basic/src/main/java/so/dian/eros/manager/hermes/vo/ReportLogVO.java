package so.dian.eros.manager.hermes.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ReportLogVO {
    @Schema(title = "时间")
    private String createTime;

    @Schema(title = "事件")
    private Integer event;

    @Schema(title = "事件状态")
    private Integer eventStatus;

    @Schema(title = "事件描述")
    private String eventDesc;

    @Schema(title = "异常事件描述")
    private String exEventDesc;

    @Schema(title = "仓位号")
    private Integer slot;

    @Schema(title = "仓位状态")
    private Integer slotStatus;

    @Schema(title = "仓位状态描述")
    private String slotStatusDesc;

    @Schema(title = "异常仓位状态描述")
    private String exSlotStatusDesc;

    @Schema(title = "充电宝编号")
    private String powerBankNo;

    @Schema(title = "充电宝状态")
    private Integer powerBankStatus;

    @Schema(title = "充电宝状态描述")
    private String powerBankStatusDesc;

    @Schema(title = "电压值")
    private Integer battery;

    @Schema(title = "最后归还时间")
    private String returnTime;
}
