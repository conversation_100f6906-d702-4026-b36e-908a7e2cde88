package so.dian.eros.pojo.converter;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import com.google.common.collect.Lists;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;
import so.dian.ares.dto.shop.QueryBoxCustomPriceDTO;
import so.dian.eros.common.util.DateUtil;
import so.dian.eros.common.util.LocationUtil;
import so.dian.eros.manager.hermes.convert.OrderDtoVoConverter;
import so.dian.eros.manager.hermes.vo.BoxCustomPriceVO;
import so.dian.eros.pojo.dto.PricingModelDTO;

import so.dian.ares.dto.shop.BoxCustomPriceDTO;
import so.dian.ares.param.QueryBoxCustomPriceListParam;
import so.dian.eros.pojo.dto.SkuListDTO;
import so.dian.mofa3.lang.money.MoneyFormatter;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;

public abstract class SkuConverter {

    public static QueryBoxCustomPriceListParam queryBoxPriceListParamtoQueryBoxCustomPriceListParam(
            Date endTime, Date startTime, Integer pageNum, Integer pageSize, Long shopId){
        QueryBoxCustomPriceListParam queryBoxCustomPriceListParam = new QueryBoxCustomPriceListParam();
        queryBoxCustomPriceListParam.setShopId(shopId);
        queryBoxCustomPriceListParam.setPageNum(pageNum);
        queryBoxCustomPriceListParam.setPageSize(pageSize);
        queryBoxCustomPriceListParam.setStartTime(startTime);
        queryBoxCustomPriceListParam.setEndTime(endTime);
        return queryBoxCustomPriceListParam;
    }

    public static SkuListDTO boxCustomPriceDTOtoSkuListDTO(BoxCustomPriceDTO boxCustomPriceDTO, String shopName,
                                                           String lang, ApplicationContext ctx){
        SkuListDTO skuListDTO = new SkuListDTO();
        skuListDTO.setId(boxCustomPriceDTO.getBoxCustomPriceId());

        skuListDTO.setShopId(boxCustomPriceDTO.getShopId());
        skuListDTO.setShopName(shopName);
        skuListDTO.setUpdateTime(DateUtil.changeDayLightTime(new Date(boxCustomPriceDTO.getUpdateTime())));
        skuListDTO.setUpdaterName(boxCustomPriceDTO.getUpdater());
        skuListDTO.setRates(formatRates(boxCustomPriceDTO, lang,ctx));
        return skuListDTO;
    }
    public static String formatRates(BoxCustomPriceDTO boxCustomPriceDTO, String language,ApplicationContext ctx) {
        Locale locale = LocationUtil.getLocale(language);
        MultiCurrencyMoney depositMoney= new MultiCurrencyMoney(0, boxCustomPriceDTO.getCurrencyCode());
        depositMoney.setCent(boxCustomPriceDTO.getMaxCost());

        MultiCurrencyMoney price= new MultiCurrencyMoney(0, boxCustomPriceDTO.getCurrencyCode());
        price.setCent(boxCustomPriceDTO.getUnitPrice());

        // 24小时封顶价
        MultiCurrencyMoney dailyMaxCostMoney= new MultiCurrencyMoney(0, boxCustomPriceDTO.getCurrencyCode());
        dailyMaxCostMoney.setCent(boxCustomPriceDTO.getMaxCostPerDay());
        // 国际化参数
        Object[] objects = {OrderDtoVoConverter.buildFreeTimeStr(boxCustomPriceDTO.getFreeTime(), ctx, locale),
                MoneyFormatter.format(price),
                OrderDtoVoConverter.buildPriceCycle(boxCustomPriceDTO.getPeriodSecond(), ctx, locale),
                MoneyFormatter.format(depositMoney),
                MoneyFormatter.format(dailyMaxCostMoney)
        };
        return ctx.getMessage("ORDER_DESCRIPTIONS", objects, locale);
    }
}
