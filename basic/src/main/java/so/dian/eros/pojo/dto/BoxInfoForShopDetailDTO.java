package so.dian.eros.pojo.dto;

import lombok.Data;
import so.dian.demeter.client.dto.BoxOrderForShopDetailDTO;
import so.dian.mofa3.lang.money.CurrencyEnum;

@Data
public class BoxInfoForShopDetailDTO {
    // 设备名称
    private  String deviceName;
    // 设备编号
    private String deviceNo;
    // 盒子在线天数
    private int onlineDays;
    // 盒子实付金额
    private double paymentOrderAmount;
    // 盒子支付订单数
    private int paymentOrderNum;
    // 总订单数
    private int totalOrderNum;
    private String currencySymbol;
    private String currencyCode;
    private Integer status;
    private String statusText;
    /**
     * 设备安装时间
     */
    private Long installTime;
    // 设备类型
    private  Integer deviceType;

    public static BoxInfoForShopDetailDTO build(BoxOrderForShopDetailDTO boxOrderForShopDetailDTO, String currencyCode) {
        BoxInfoForShopDetailDTO boxInfoForShopDetailDTO = new BoxInfoForShopDetailDTO();
        boxInfoForShopDetailDTO.setDeviceName(boxOrderForShopDetailDTO.getDeviceName());
        boxInfoForShopDetailDTO.setDeviceNo(boxOrderForShopDetailDTO.getDeviceNo());
        boxInfoForShopDetailDTO.setOnlineDays(boxOrderForShopDetailDTO.getOnlineDays());
        boxInfoForShopDetailDTO.setPaymentOrderAmount(boxOrderForShopDetailDTO.getPaymentOrderAmount());
        boxInfoForShopDetailDTO.setPaymentOrderNum(boxOrderForShopDetailDTO.getPaymentOrderNum());
        boxInfoForShopDetailDTO.setTotalOrderNum(boxOrderForShopDetailDTO.getTotalOrderNum());
        CurrencyEnum currencyEnum= CurrencyEnum.getByCurrencyCode(currencyCode);
        boxInfoForShopDetailDTO.setCurrencySymbol(currencyEnum.getCurrencyLabel());
        boxInfoForShopDetailDTO.setCurrencyCode(currencyEnum.getCurrencyCode());
        return boxInfoForShopDetailDTO;
    }
}
