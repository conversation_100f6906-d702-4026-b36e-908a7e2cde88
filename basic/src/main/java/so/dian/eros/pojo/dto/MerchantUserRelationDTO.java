/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package so.dian.eros.pojo.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * merchant_user_relation
 *
 * <AUTHOR>
 * @version $Id: MerchantUserRelation.java, v 0.1 2023-12-14 14:51:49 Exp $
 */
@Data
public class MerchantUserRelationDTO implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 173332424521504266L;

    private Long id;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 员工账号id
     */
    private Long userId;

    /**
     * 商户人员类型：1负责人
     */
    private Integer userType;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 创建人id
     */
    private Long creatorId;

    private Long cooperaterId;

    private Long gmtCreate;

    private Long gmtUpdate;

    private Integer deleted;
}