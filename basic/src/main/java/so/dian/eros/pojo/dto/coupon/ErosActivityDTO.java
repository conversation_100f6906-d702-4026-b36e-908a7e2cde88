package so.dian.eros.pojo.dto.coupon;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 活动列表DTO
 */
@Data
public class ErosActivityDTO {

    @Schema(title = "活动ID")
    private Integer activityId;

    @Schema(title = "活动名称")
    private String activityName;

    @Schema(title = "优惠券ID集合")
    private List<Integer> couponIds;

    @Schema(title = "优惠券名称+优惠券类型")
    private List<String> couponList;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "创建者ID")
    private Integer creatorId;

    @Schema(title = "创建者名称")
    private String creatorName;

    @Schema(title = "活动结束时间")
    private Date endTime;

    @Schema(title = "发放数量")
    private Integer issuedNumber;

    @Schema(title = "领取数量")
    private Integer recipientsNumber;

    @Schema(title = "活动开始时间")
    private Date startTime;
}
