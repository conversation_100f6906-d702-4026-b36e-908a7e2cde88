package so.dian.eros.pojo.dto.coupon;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 活动详情DTO
 */
@Data
public class ErosActivityDetailsDTO {

    @Schema(title = "活动ID")
    private Integer activityId;

    @Schema(title = "活动中文名称")
    private String cnName;

    @Schema(title = "活动英文名称")
    private String enName;

    @Schema(title = "中文活动说明")
    private String cnExplain;

    @Schema(title = "英文活动说明")
    private String enExplain;

    @Schema(title = "背景色")
    private String backColor;

    @Schema(title = "活动结束时间")
    private String endTime;

    @Schema(title = "活动头图地址")
    private String picUrl;

    @Schema(title = "活动开始时间")
    private String startTime;

    @Schema(title = "优惠券ID集合")
    private List<Integer> couponIds;

    @Schema(title = "优惠券")
    private List<CouponDTO> couponList;

    @Schema(title = "发放数量")
    private Integer issuedNumber;

    @Schema(title = "领取数量")
    private Integer recipientsNumber;

    @Schema(title = "每人限额")
    private Integer limitNumber;

    @Schema(title = "创建者ID")
    private Integer creatorId;

    @Schema(title = "领取规则, 1:新用户可领; 2:无门槛领取")
    private Integer ruleType;
}
