package so.dian.eros.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeviceTypeEnum {


    BOX(8, "小电盒子版本", 1),
    BOX_4G(18, "盒子4g版本", 1),
    BOX_NEW_4G(22, "盒子新4g版本", 1),
    BOX_4_SLOT_4G(26, "盒子4孔4g版本", 1),
    BOX_4G_ASR(27, " 4G版盒子(有方-ASR)", 1),
    BOX_GB(32, "英国盒子", 1),
    BOX_HK(34, "香港盒子", 1),
    BOX_AE_1(45, "迪拜盒子", 1),
    BOX_62(62, "16孔", 1),

    CABINET_SMALL(23, "小柜机", 2),
    CABINET_BIG(24, "大柜机", 2),
    CABINET_18_SLOT(31, "18孔柜机", 2),

    CABINET_6_SLOT(67, "6孔柜机", 2),
    CABINET_12_SLOT(72, "12孔柜机", 2),
    CABINET_30_SLOT(69, "30孔柜机", 2),

    ;


    private Integer code;

    private String name;

    /**
     * 1、盒子，2、柜机
     */
    private Integer type;

    public static boolean isBox(Integer code) {

        if (code == null) {
            return false;
        }

        for (DeviceTypeEnum deviceTypeEnum : DeviceTypeEnum.values()) {
            if (deviceTypeEnum.getCode().equals(code)) {
                if (deviceTypeEnum.getType() == 1) {
                    return true;
                }
                return false;
            }
        }

        return false;
    }

    public static boolean isCabinet(Integer code) {

        if (code == null) {
            return false;
        }

        for (DeviceTypeEnum deviceTypeEnum : DeviceTypeEnum.values()) {
            if (deviceTypeEnum.getCode().equals(code)) {
                if (deviceTypeEnum.getType() == 2) {
                    return true;
                }
                return false;
            }
        }

        return false;
    }

    public static DeviceTypeEnum findByCode(Integer code) {

        if (code == null) {
            return null;
        }

        for (DeviceTypeEnum deviceTypeEnum : DeviceTypeEnum.values()) {
            if (deviceTypeEnum.getCode().equals(code)) {
                return deviceTypeEnum;
            }
        }

        return null;
    }
}
