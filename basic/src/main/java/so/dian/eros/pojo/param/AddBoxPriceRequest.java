package so.dian.eros.pojo.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddBoxPriceRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 17120240250140950L;
    // 免费时长
    private Boolean freeTimeSelected;
    private Integer freeTime;

    private Boolean priceSelected;
    // 计价周期 分钟
    private Integer periodMinute;
    // 计价单价，单位：元
    private Double unitPrice;
    // 24小时封顶价格
    private Double maxCostPerDay;
    // 总封顶价
    private Double maxCost;

    private Long shopId;
    private List<Long> shopIdList;

    private Long tenantId;

}
