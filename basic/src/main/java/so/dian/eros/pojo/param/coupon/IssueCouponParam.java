package so.dian.eros.pojo.param.coupon;


import lombok.Data;
import lombok.NonNull;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;
/**
 * 发放优惠券接口参数
 */
@Data
public class IssueCouponParam{

    @Schema(title = "优惠券ID")
    @NonNull
    private Integer couponId;

    @Schema(title = "用户ID列表")
    private List<Long> userIds;

    @Schema(title = "投放类型 1:所有用户; 2:指定用户ID")
    @NonNull
    private Integer type;
}
