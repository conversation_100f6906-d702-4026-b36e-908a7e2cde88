package so.dian.eros.remote.aeacus;

import com.chargebolt.aeacus.api.manager.OssGroupService;
import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.dto.group.OssGroupQueryDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.common.exception.SystemErrorEnum;

//@FeignClient(name = "aeacus", fallback = AeacusGroupService.AeacusGroupServiceFallback.class)
interface AeacusGroupService extends OssGroupService{

    @Component
    class AeacusGroupServiceFallback implements AeacusGroupService {

        @Override
        public BizResult<List<OssGroupDTO>> query(String version, OssGroupQueryDTO ossGroupQueryDTO) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }
    }
}
