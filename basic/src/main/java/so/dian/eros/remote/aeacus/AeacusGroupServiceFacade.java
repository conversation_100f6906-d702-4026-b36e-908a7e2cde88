package so.dian.eros.remote.aeacus;

import com.chargebolt.aeacus.controller.GroupController;
import com.chargebolt.aeacus.dto.group.OssGroupDTO;
import com.chargebolt.aeacus.dto.group.OssGroupQueryDTO;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;

@Component
public class AeacusGroupServiceFacade {

    @Resource
    private GroupController groupController;

    public BizResult<List<OssGroupDTO>> query(OssGroupQueryDTO ossGroupQueryDTO) {
        return groupController.query("1.0", ossGroupQueryDTO);
    }
}
