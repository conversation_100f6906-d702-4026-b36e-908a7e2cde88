package so.dian.eros.remote.aeacus;

import com.chargebolt.aeacus.controller.ModuleController;
import com.chargebolt.aeacus.dto.OssModuleDTO;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AeacusModuleServiceFacade{

    @Resource
    private ModuleController moduleController;

    public BizResult<List<OssModuleDTO>> getModulesByUser(Long userId) {
        return moduleController.getModulesByUser("1.0", userId);
    }
}
