package so.dian.eros.remote.aeacus;

import com.chargebolt.aeacus.api.manager.OssResourceService;
import com.chargebolt.aeacus.dto.OssIconDTO;
import com.chargebolt.aeacus.dto.OssMobileDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.common.exception.SystemErrorEnum;

import java.util.List;
//@FeignClient(name = "aeacus", fallback = AeacusResourceService.AeacusResourceServiceFallback.class)
public interface AeacusResourceService extends OssResourceService {

    @Component
    class AeacusResourceServiceFallback implements AeacusResourceService{

        @Override
        public BizResult<List<OssIconDTO>> getIconsByUser(Long userId, Long moduleId) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<List<OssMobileDTO>> getSupportMobileNations() {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }
    }
}
