package so.dian.eros.remote.aeacus;

import com.chargebolt.aeacus.api.manager.OssRoleService;
import com.chargebolt.aeacus.dto.OssRoleCreateDTO;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.dto.OssRoleDeleteDTO;
import com.chargebolt.aeacus.dto.OssRoleQueryDTO;
import com.chargebolt.aeacus.dto.OssRoleUpdateDTO;
import com.chargebolt.aeacus.dto.OssRoleUserAuthDTO;
import com.chargebolt.aeacus.dto.PageData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.eros.common.exception.SystemErrorEnum;

import java.util.List;

//@FeignClient(name = "aeacus", fallback = AeacusRoleService.AeacusRoleServiceFallback.class)
interface AeacusRoleService extends OssRoleService {

    @Component
    class AeacusRoleServiceFallback implements AeacusRoleService{

        @Override
        public BizResult<List<OssRoleDTO>> getRolesByUser(String version, Long userId) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<Boolean> authToUesr(String version, OssRoleUserAuthDTO ossRoleUserAuthDTO) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<Boolean> add(String version, OssRoleCreateDTO ossRoleCreateDTO) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<Boolean> update(String version, OssRoleUpdateDTO ossRoleUpdateDTO) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<PageData<OssRoleDTO>> queryByPage(String version, OssRoleQueryDTO queryDTO) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<List<OssRoleDTO>> query(String version) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<OssRoleDTO> queryById(String version, Long roleId) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<Boolean> delete(String version, OssRoleDeleteDTO roleDeleteDTO) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }
    }
}
