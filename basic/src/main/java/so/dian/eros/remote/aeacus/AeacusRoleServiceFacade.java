package so.dian.eros.remote.aeacus;

import com.chargebolt.aeacus.controller.RoleController;
import com.chargebolt.aeacus.dto.OssRoleCreateDTO;
import com.chargebolt.aeacus.dto.OssRoleDTO;
import com.chargebolt.aeacus.dto.OssRoleDeleteDTO;
import com.chargebolt.aeacus.dto.OssRoleQueryDTO;
import com.chargebolt.aeacus.dto.OssRoleUpdateDTO;
import com.chargebolt.aeacus.dto.PageData;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AeacusRoleServiceFacade{

    @Resource
    private RoleController roleController;

    public BizResult<List<OssRoleDTO>> getRolesByUser(Long userId) {
        return roleController.getRolesByUser("1.0", userId);
    }

    public BizResult<Boolean> create(Oss<PERSON><PERSON><PERSON>reate<PERSON><PERSON> createDTO){
        return roleController.add("1.0",createDTO);
    }

    public BizResult<Boolean> update(OssRoleUpdateDTO updateDTO){
        return roleController.update("1.0",updateDTO);
    }

    public BizResult<Boolean> delete(OssRoleDeleteDTO deleteDTO){
        return roleController.delete("1.0",deleteDTO);
    }

    public BizResult<PageData<OssRoleDTO>> queryByPage(OssRoleQueryDTO queryDTO){
        return roleController.queryByPage("1.0",queryDTO);
    }

    public BizResult<List<OssRoleDTO>> queryAll(){
        return roleController.query("1.0");
    }

    public BizResult<OssRoleDTO> queryById(Long roleId){
        return roleController.queryById("1.0",roleId);
    }
}
