package so.dian.eros.remote.demeter.fallback;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.demeter.client.dto.*;
import so.dian.demeter.client.param.DeviceWithSlotQuery;
import so.dian.demeter.client.param.ExportShopParam;
import so.dian.demeter.client.param.ShopDeviceQuery;
import so.dian.eros.common.exception.SystemErrorEnum;
import so.dian.eros.remote.demeter.service.DeviceService;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DeviceServiceFallBack2 implements FallbackFactory<DeviceService> {

    @Override
    public DeviceService create(Throwable throwable) {
        return new DeviceServiceFallback(throwable);
    }

    private class DeviceServiceFallback implements DeviceService {

        private final Throwable e;

        DeviceServiceFallback(Throwable e) {
            this.e = e;
        }

        @Override
        public BizResult<DeviceWithStatusDetailDTO> getForInstallAndRecycleByDeviceNo(
                DeviceWithSlotQuery deviceWithSlotQuery) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<List<ShopDeviceInfoWithShopDTO>> getShopDeviceInfo(ShopDeviceQuery shopDeviceQuery) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<List<SimpleBoxInfoDTO>> getShopSimpleBoxInfo(ShopDeviceQuery shopDeviceQuery) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<SimpleBoxInfoDTO> getShopSimpleBoxInfoByDeviceNo(String deviceNo) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<List<BoxPanelDTO>> getAllBoxWithAeacus(List<Long> shopIds) {
            log.error("fallback:根据门店ID查询盒子信息接口失败", e);
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<List<BoxPanelPowerBankDTO>> getPowerBankByDeviceNo(String deviceNo) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<Map<String, List<BoxPanelPowerBankDTO>>> getPowerBankByDeviceNos(List<String> list) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<Map<Long, Map<String, BoxComprehensiveDTO>>> export(ExportShopParam param) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<List<BoxOrderForShopDetailDTO>> getBoxInfoByShopId(Long shopId) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }

        @Override
        public BizResult<BoxTotalOrderDTO> getTotalOrderInfo(Long shopId, String startTime, String endTime) {
            return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
        }
    }
}
