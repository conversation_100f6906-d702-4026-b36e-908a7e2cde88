package so.dian.eros.remote.demeter.fallback;

import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.demeter.client.dto.WarehouseInfoDTO;
import so.dian.eros.common.exception.SystemErrorEnum;
import so.dian.eros.remote.demeter.service.WarehouseService;

@Service
public class WarehouseServiceFallBack implements WarehouseService {

    @Override
    public BizResult<WarehouseInfoDTO> getByCooperatorId(Long cooperatorId) {
        return BizResult.error(SystemErrorEnum.REMOTE_FALLBACK);
    }
}
