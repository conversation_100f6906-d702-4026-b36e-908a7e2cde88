package so.dian.eros.remote.hermes.fallback;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.eros.remote.hermes.OssBoxApi;
import so.dian.hermes.client.pojo.dto.oss.OssBoxOrderDTO;
import so.dian.hermes.client.pojo.dto.oss.OssBoxOrderPageDTO;
import so.dian.hermes.client.pojo.dto.oss.QueryOrderExceptionListDTO;
import so.dian.hermes.client.pojo.param.oss.LastNoPayOrderByUserIdListParam;
import so.dian.hermes.client.pojo.param.oss.OssQueryOrderListParam;
import so.dian.hermes.client.pojo.param.oss.QueryOrderExceptionListParam;

/**
 * <AUTHOR>
 */
@Service
@Slf4j(topic = "hermes")
public class OssBoxOrderFallback implements OssBoxApi {
    @Override
    public BizResult<OssBoxOrderPageDTO> list(OssQueryOrderListParam param) {
        log.error("fallback:查询用户盒子订单分页列表接口失败:param=[{}]", param);
        return BizResult.error(ErrorCodeEnum.FALLBACK);
    }

    @Override
    public BizResult<OssBoxOrderDTO> detail(String var1, Long var2) {
        log.error("fallback:查询用户盒子订单详情接口失败:orderNo=[{}], cooperatorId=[{}]", var1, var2);
        return BizResult.error(ErrorCodeEnum.FALLBACK);
    }

    @Override
    public BizResult<OssBoxOrderPageDTO> lastNoPayOrderByUserIdList(LastNoPayOrderByUserIdListParam var1) {
        log.error("fallback:hermes-lastNoPayOrderByUserIdList:param=[{}]", var1);
        return BizResult.error(ErrorCodeEnum.FALLBACK);
    }

    @Override
    public BizResult<QueryOrderExceptionListDTO> queryOrderExceptionList(QueryOrderExceptionListParam queryOrderExceptionListParam) {
        log.error("fallback:hermes-queryOrderExceptionList:param=[{}]", queryOrderExceptionListParam);
        return BizResult.error(ErrorCodeEnum.FALLBACK);
    }
}
