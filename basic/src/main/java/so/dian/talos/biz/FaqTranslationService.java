package so.dian.talos.biz;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.chargebolt.theseus.dto.TranslationResult;
import com.chargebolt.theseus.dto.rq.I18nTextsDTO;
import com.chargebolt.theseus.service.I18nTextsService;
import com.chargebolt.theseus.service.TranslateApi;
import com.meidalife.common.monitor.RTMonitor;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.talos.configuration.LanguageConfiguration;
import so.dian.talos.pojo.dto.CategoryFaqDTO;
import so.dian.talos.pojo.entity.FaqCategoryDO;
import so.dian.talos.pojo.entity.FaqRecordDO;

/**
 * FAQ翻译服务
 * 专注于FAQ内容的翻译处理，包括语言检测、批量翻译、结果保存等
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class FaqTranslationService {

    // ==================== 依赖注入 ====================

    /**
     * 翻译API服务
     */
    @Resource
    private TranslateApi translateApi;

    /**
     * 国际化文本服务
     */
    @Resource
    private I18nTextsService i18nTextsService;

    /**
     * 国际化编码生成服务
     */
    @Resource
    private FaqI18nService faqI18nService;

    /**
     * 语言配置
     */
    @Resource
    private LanguageConfiguration languageConfiguration;

    /**
     * 异步执行器
     */
    @Resource
    private Executor taskExecutor;

    // ==================== 公共方法 ====================

    /**
     * 异步翻译FAQ内容
     * 
     * @param categoryFaqDTOList 待翻译的分类FAQ列表
     */
    public void translateAsync(CategoryFaqDTO categoryFaqDTO) {
        if (categoryFaqDTO == null || categoryFaqDTO.getFaqs().isEmpty()) {
            log.info("没有需要翻译的内容，跳过翻译");
            return;
        }
        // 异步执行翻译任务
        taskExecutor.execute(() -> {
            try {
                translateSync(categoryFaqDTO);
            } catch (Exception e) {
                log.error("异步翻译FAQ失败", e);
            }
        });
    }

    /**
     * 同步翻译FAQ内容
     *
     * @param categoryFaqDTO 待翻译的分类FAQ列表
     */
    @RTMonitor
    public void translateSync(CategoryFaqDTO categoryFaqDTO) {
        if (categoryFaqDTO == null || categoryFaqDTO.getFaqs().isEmpty()) {
            log.info("没有需要翻译的内容，跳过翻译");
            return;
        }

        try {
            // 1. 检测源语言
            String sourceLang = detectSourceLanguage(categoryFaqDTO);
            if (StringUtils.isBlank(sourceLang)) {
                log.warn("源语言检测失败，跳过翻译");
                return;
            }

            // 2. 获取目标语言列表，排除源语言
            List<String> targetLangs = languageConfiguration.getTargetLanguages(sourceLang);
            if (targetLangs.isEmpty()) {
                log.info("没有目标语言需要翻译，sourceLang: {}", sourceLang);
                return;
            }

            // 3. 提取待翻译文本并更新国际化编码
            Map<String, String> textsToTranslate = extractTextsAndUpdateI18nCodes(categoryFaqDTO);
            if (textsToTranslate.isEmpty()) {
                log.info("没有需要翻译的文本");
                return;
            }

            // 4. 执行批量翻译
            Map<String, TranslationResult> translationResults = performBatchTranslation(
                    textsToTranslate, targetLangs, sourceLang);

            // 5. 保存翻译结果（包括源语言和目标语言）
            saveTranslationResults(textsToTranslate, translationResults, targetLangs, sourceLang);

            log.info("同步翻译FAQ完成，翻译文本数量: {}, 目标语言: {}",
                    textsToTranslate.size(), targetLangs);

        } catch (Exception e) {
            log.error("同步翻译FAQ失败", e);
            throw BizException.create(ErrorCodeEnum.INTERNAL_SERVER_ERROR, "翻译处理失败: " + e.getMessage());
        }
    }

    /**
     * 实时翻译单个文本
     * 用于缺失翻译时的实时补充
     * 
     * @param text       待翻译文本
     * @param targetLang 目标语言
     * @return 翻译结果
     */
    public String translateRealtime(String text, String targetLang) {
        if (StringUtils.isBlank(text) || StringUtils.isBlank(targetLang)) {
            return text;
        }

        try {
            return translateApi.translate(text, targetLang);
        } catch (Exception e) {
            log.error("实时翻译失败，text: {}, targetLang: {}", text, targetLang, e);
            return text; // 翻译失败时返回原文
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 检测源语言
     * 
     * @param categoryFaqDTOList 分类FAQ列表
     * @return 源语言代码
     */
    private String detectSourceLanguage(CategoryFaqDTO categoryFaqDTO) {
        try {
            // 寻找第一个有FAQ内容的项目进行语言检测
            List<FaqRecordDO> faqs = categoryFaqDTO.getFaqs();

            String textForDetection = categoryFaqDTO.getCategory().getName();

            if (!CollectionUtils.isEmpty(faqs)) {
                FaqRecordDO firstFaq = faqs.get(0);
                textForDetection = textForDetection + " " + firstFaq.getQuestion() + " " + firstFaq.getAnswer();
            }

            String detectedLang = translateApi.detectLanguage(textForDetection);
            log.info("检测到源语言: {}", detectedLang);

            // 验证检测到的语言是否在支持列表中
            if (languageConfiguration.isSupported(detectedLang)) {
                return detectedLang;
            }

            return null;

        } catch (Exception e) {
            log.error("语言检测失败", e);
            throw BizException.create(ErrorCodeEnum.INTERNAL_SERVER_ERROR,
                    "无法检测文本语言: FAQ内容");
        }
    }

    /**
     * 提取待翻译文本并更新国际化编码
     * 
     * @param categoryFaqDTOList 分类FAQ列表
     * @return 待翻译文本映射 (key: 唯一标识, value: 文本内容)
     */
    private Map<String, String> extractTextsAndUpdateI18nCodes(CategoryFaqDTO categoryFaqDTO) {
        Map<String, String> textsToTranslate = new HashMap<>();

            // 处理分类名称
            FaqCategoryDO category = categoryFaqDTO.getCategory();
            if (category != null && StringUtils.isNotBlank(category.getName())) {
                String categoryKey = "category_" + category.getId();
                textsToTranslate.put(categoryKey, category.getName());

                // 生成并设置分类的国际化编码
                String categoryI18nCode = faqI18nService.generateCategoryI18nCode(category.getId());
                faqI18nService.updateCategoryI18nCode(category.getId(), categoryI18nCode);

                log.debug("添加分类翻译任务: {} -> {}", categoryKey, category.getName());
            }

            // 处理FAQ内容
            List<FaqRecordDO> faqs = categoryFaqDTO.getFaqs();

            // 反向判断，避免嵌套
            if (faqs == null || faqs.isEmpty()) {
                return textsToTranslate;
            }

            for (FaqRecordDO faq : faqs) {
                // 处理问题
                if (StringUtils.isNotBlank(faq.getQuestion())) {
                    String questionKey = "question_" + faq.getId();
                    textsToTranslate.put(questionKey, faq.getQuestion());

                    // 生成并设置问题的国际化编码
                    String questionI18nCode = faqI18nService.generateQuestionI18nCode(faq.getId());
                    faqI18nService.updateFaqQuestionI18nCode(faq.getId(), questionI18nCode);

                    log.debug("添加问题翻译任务: {} -> {}", questionKey, faq.getQuestion());
                }

                // 处理答案
                if (StringUtils.isNotBlank(faq.getAnswer())) {
                    String answerKey = "answer_" + faq.getId();
                    textsToTranslate.put(answerKey, faq.getAnswer());

                    // 生成并设置答案的国际化编码
                    String answerI18nCode = faqI18nService.generateAnswerI18nCode(faq.getId());
                    faqI18nService.updateFaqAnswerI18nCode(faq.getId(), answerI18nCode);

                    log.debug("添加答案翻译任务: {} -> {}", answerKey, faq.getAnswer());
                }
            }

        log.info("提取待翻译文本完成，共{}个文本", textsToTranslate.size());
        return textsToTranslate;
    }

    /**
     * 执行批量翻译
     * 
     * @param textsToTranslate 待翻译文本映射
     * @param targetLangs      目标语言列表
     * @param sourceLang       源语言
     * @return 翻译结果映射
     */
    private Map<String, TranslationResult> performBatchTranslation(
            Map<String, String> textsToTranslate, List<String> targetLangs, String sourceLang) {

        try {
            log.info("开始批量翻译，文本数量: {}, 目标语言: {}, 源语言: {}",
                    textsToTranslate.size(), targetLangs, sourceLang);

            // 调用翻译API
            Map<String, TranslationResult> results = translateApi.batchTranslate(
                    textsToTranslate, targetLangs, sourceLang);

            if (results == null || results.isEmpty()) {
                log.warn("翻译API返回空结果");
                return new HashMap<>();
            }

            log.info("批量翻译完成，返回{}种语言的翻译结果", results.size());
            return results;

        } catch (Exception e) {
            log.error("批量翻译失败", e);
            throw BizException.create(ErrorCodeEnum.INTERNAL_SERVER_ERROR, "翻译服务不可用");
        }
    }

    /**
     * 保存翻译结果到数据库（包括源语言和目标语言）
     * 
     * @param textsToTranslate   原始文本映射
     * @param translationResults 翻译结果映射
     * @param targetLanguages    目标语言列表
     * @param sourceLanguage     源语言
     */
    private void saveTranslationResults(Map<String, String> textsToTranslate,
            Map<String, TranslationResult> translationResults,
            List<String> targetLanguages,
            String sourceLanguage) {

        try {
            // 1. 保存源语言的原始文本
            saveSourceLanguageTexts(textsToTranslate, sourceLanguage);

            // 2. 保存目标语言的翻译结果
            saveTargetLanguageTranslations(textsToTranslate, translationResults, targetLanguages);

        } catch (Exception e) {
            log.error("保存翻译结果时发生异常", e);
            throw BizException.create(ErrorCodeEnum.INTERNAL_SERVER_ERROR, "翻译结果保存失败");
        }
    }

    /**
     * 保存源语言的原始文本
     * 
     * @param textsToTranslate 原始文本映射
     * @param sourceLanguage   源语言
     */
    private void saveSourceLanguageTexts(Map<String, String> textsToTranslate, String sourceLanguage) {
        Integer sourceLangId = languageConfiguration.getLangId(sourceLanguage);
        if (sourceLangId == null) {
            log.warn("未找到源语言[{}]对应的ID，跳过保存源语言文本", sourceLanguage);
            return;
        }

        batchSaveTextsForLanguage(textsToTranslate, sourceLangId, sourceLanguage);
    }

    /**
     * 保存目标语言的翻译结果
     * 
     * @param textsToTranslate   原始文本映射
     * @param translationResults 翻译结果映射
     * @param targetLanguages    目标语言列表
     */
    private void saveTargetLanguageTranslations(Map<String, String> textsToTranslate,
            Map<String, TranslationResult> translationResults,
            List<String> targetLanguages) {
        if (translationResults == null || translationResults.isEmpty()) {
            log.warn("翻译结果为空，跳过目标语言保存");
            return;
        }

        for (String targetLang : targetLanguages) {
            saveTranslationForLanguage(textsToTranslate, translationResults, targetLang);
        }
    }

    /**
     * 保存单个目标语言的翻译结果
     * 
     * @param textsToTranslate   原始文本映射
     * @param translationResults 翻译结果映射
     * @param targetLang         目标语言
     */
    private void saveTranslationForLanguage(Map<String, String> textsToTranslate,
            Map<String, TranslationResult> translationResults,
            String targetLang) {
        TranslationResult result = translationResults.get(targetLang);
        if (result == null) {
            log.warn("语言[{}]的翻译结果为空，跳过保存", targetLang);
            return;
        }

        Integer langId = languageConfiguration.getLangId(targetLang);
        if (langId == null) {
            log.warn("未找到语言[{}]对应的ID，跳过保存", targetLang);
            return;
        }

        Map<String, String> translationMap = result.getTranslationMap();
        if (translationMap == null || translationMap.isEmpty()) {
            log.warn("语言[{}]的翻译映射为空，跳过保存", targetLang);
            return;
        }

        // 收集该语言的所有翻译文本
        Map<String, String> validTranslations = new HashMap<>();
        for (Map.Entry<String, String> originalEntry : textsToTranslate.entrySet()) {
            String key = originalEntry.getKey();
            String translatedText = translationMap.get(key);

            if (StringUtils.isNotBlank(translatedText)) {
                validTranslations.put(key, translatedText);
            }
        }

        if (!validTranslations.isEmpty()) {
            batchSaveTextsForLanguage(validTranslations, langId, targetLang);
        }
    }

    /**
     * 批量保存指定语言的所有文本
     * 
     * @param textsToSave 待保存的文本映射
     * @param langId      语言ID
     * @param language    语言描述（用于日志）
     */
    private void batchSaveTextsForLanguage(Map<String, String> textsToSave, Integer langId, String language) {
        if (textsToSave == null || textsToSave.isEmpty()) {
            return;
        }

        try {
            // 收集所有需要保存的DTO
            List<I18nTextsDTO> i18nTextsList = new ArrayList<>();

            for (Map.Entry<String, String> entry : textsToSave.entrySet()) {
                String key = entry.getKey();
                String text = entry.getValue();

                if (StringUtils.isNotBlank(text)) {
                    I18nTextsDTO i18nTexts = createI18nTextsDTO(key, text, langId);
                    if (i18nTexts != null) {
                        i18nTextsList.add(i18nTexts);
                    }
                }
            }

            if (!i18nTextsList.isEmpty()) {
                // 批量保存
                boolean success = i18nTextsService.batchAddText(i18nTextsList);

                if (!success) {
                    log.warn("语言[{}]批量保存失败", language);
                }
            }

        } catch (Exception e) {
            log.error("语言[{}]批量保存文本异常", language, e);
            throw e;
        }
    }

    /**
     * 创建I18nTextsDTO对象
     * 
     * @param key    文本key
     * @param text   文本内容
     * @param langId 语言ID
     * @return I18nTextsDTO对象，如果key格式不正确返回null
     */
    private I18nTextsDTO createI18nTextsDTO(String key, String text, Integer langId) {
        String i18nCode;

        if (key.startsWith("category_")) {
            // 分类翻译
            Long categoryId = Long.parseLong(key.substring("category_".length()));
            i18nCode = faqI18nService.generateCategoryI18nCode(categoryId);

        } else if (key.startsWith("question_")) {
            // 问题翻译
            Long faqId = Long.parseLong(key.substring("question_".length()));
            i18nCode = faqI18nService.generateQuestionI18nCode(faqId);

        } else if (key.startsWith("answer_")) {
            // 答案翻译
            Long faqId = Long.parseLong(key.substring("answer_".length()));
            i18nCode = faqI18nService.generateAnswerI18nCode(faqId);

        } else {
            log.warn("未知的key前缀，无法创建I18nTextsDTO: {}", key);
            return null;
        }

        I18nTextsDTO i18nTexts = new I18nTextsDTO();
        i18nTexts.setModule("faq");
        i18nTexts.setKey(i18nCode);
        i18nTexts.setValue(text);
        i18nTexts.setLangId(langId);
        i18nTexts.setOrigin(1); // 1表示机器翻译

        return i18nTexts;
    }
}