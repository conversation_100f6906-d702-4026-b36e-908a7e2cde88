package so.dian.talos.biz.service;

import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import so.dian.demeter.client.dto.PageData;
import so.dian.talos.cache.GeoCache;
import so.dian.talos.client.dto.GeoDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import so.dian.talos.common.constant.CommonConstants;
import so.dian.talos.dao.rds.BindSellerLogDAO;
import so.dian.talos.dao.rds.ShopDAO;
import so.dian.talos.pojo.dto.ShopGroupSearchDTO;
import so.dian.talos.pojo.entity.BindSellerLogDO;
import so.dian.talos.pojo.entity.ShopDO;

@Service
@Slf4j
public class TalosShopService {

    @Resource
    private ShopDAO shopDAO;
    @Resource
    private BindSellerLogDAO bindSellerLogDAO;
    @Resource
    private GeoCache geoCache;

    public ShopDO queryById(Long id){
        if(id == null){
            return null;
        }
        return shopDAO.queryById(id);
    }

    public List<ShopDO> queryByMerchantId(Long merchantId, Integer startIndex, Integer endIndex){

        if(merchantId == null){
            return new ArrayList<>(0);
        }

        return shopDAO.queryByMerchantId(merchantId, startIndex, endIndex);
    }

    public Integer queryCount(Long merchantId){

        if(merchantId == null){
            return 0;
        }

        return shopDAO.queryCount(merchantId);
    }

    public Long add(ShopDO shopDO){

        if(shopDO == null){
            return null;
        }

        shopDAO.add(shopDO);
        try {
            GeoDTO geoDTO = new GeoDTO(shopDO.getId().toString(),shopDO.getPoiLongitude(), shopDO.getPoiLatitude());
            geoCache.geoAdd(geoDTO);
        } catch (Exception e){
            log.error("添加geo缓存失败！");
        }

        return shopDO.getId();
    }

    public void bindMerchant(Long id, Long merchantId){

        if(id == null || merchantId == null){
            return;
        }

        shopDAO.bindMerchant(id, merchantId);
    }

    public void updateSeller(Long id, Long sellerId){

        if(id == null || sellerId == null){
            return;
        }

        shopDAO.updateSeller(id, sellerId);
        addBindSellerLog(new BindSellerLogDO(id, sellerId));
    }

    public List<ShopDO> queryListByName(String name, Integer startIndex, Integer endIndex, Integer shopStatus,Long agentId,Long sellerId){
        int offset = (startIndex - 1) * endIndex;
        Page<ShopDO> page = PageHelper.offsetPage(offset, endIndex, Boolean.FALSE)
                .setOrderBy(" id DESC")
                .doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        shopDAO.queryListByName(name, shopStatus,agentId,sellerId);
                    }
                });
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(page.getResult())) {
            return page.getResult();
        }
        return new ArrayList<>();
    }

    public List<ShopDO> queryByName(String name){

        if(StringUtils.isEmpty(name)){
            return null;
        }

        return shopDAO.queryByName(name);
    }

//    public List<ShopDO> queryByAdministrativeDivisionsId(Long administrativeDivisionsId){
//
//        if(null == administrativeDivisionsId){
//            return null;
//        }
//
//        return shopDAO.queryByAdministrativeDivisionsId(administrativeDivisionsId);
//    }

    public List<ShopDO> queryListByIds(List<Long> shopIds){

        if(CollectionUtils.isEmpty(shopIds)){
            return new ArrayList<>(0);
        }

        return shopDAO.queryListByIds(shopIds);
    }


    private void addBindSellerLog(BindSellerLogDO bindSellerLogDO){

        bindSellerLogDAO.add(bindSellerLogDO);
    }

    public void updateShopStatusInstalled(Long id){

        shopDAO.updateShopStatusInstalled(id);
    }


    public void updateShopStatusUnInstalled(Long id){


        shopDAO.updateShopStatusUnInstalled(id);
    }

    public List<ShopDO> queryBySellerId(Long sellerId, Integer startIndex, Integer endIndex, Integer shopStatus,Long agentId){

        return shopDAO.queryBySellerId(sellerId, startIndex, endIndex, shopStatus,agentId);
    }

    public List<ShopDO> queryOwnListByName(String name, Integer startIndex, Integer endIndex, Integer shopStatus, Long sellerId){

        return shopDAO.queryOwnListByName(name, startIndex, endIndex, shopStatus, sellerId);
    }

    public List<ShopDO> queryList(){
        return shopDAO.queryList();
    }

    public void update(ShopDO shopDO){
        shopDAO.update(shopDO);
        try {
            // geo缓存
            GeoDTO geoDTO = new GeoDTO(shopDO.getId().toString(),shopDO.getPoiLongitude(), shopDO.getPoiLatitude());
            geoCache.geoUpdate(geoDTO);
        }catch (Exception e){
            log.error("更新geo缓存失败！");
        }
    }

    /**
     * 根据条件查询门店信息
     * @param name String
     * @param startIndex Integer
     * @param endIndex Integer
     * @param shopStatus Integer
     * @param sellerIds List<Long> 当前登陆用户ID集合
     * @return List<ShopDTO>
     */
    public List<ShopDO> queryListWithAeacus(Long shopId, String name, Integer startIndex, Integer endIndex, Integer shopStatus,
                                            List<Long> sellerIds, Long typeId, Long administrativeDivisionsId,
                                            String merchantName, Long cooperatorId, Integer memberFlag){
        return  shopDAO.queryListWithAeacus(shopId, name, startIndex, endIndex, shopStatus, sellerIds, typeId, administrativeDivisionsId, merchantName, cooperatorId, memberFlag);
    }

    /**
     * 根据条件查询门店总个数
     * @param name String
     * @param shopStatus Integer
     * @param sellerIds List<Long> 当前登陆用户ID集合
     * @return Long
     */
    public Long getCountWithAeacus(Long shopId, String name, Integer shopStatus,List<Long> sellerIds,
                                           Long typeId, Long administrativeDivisionsId, String merchantName, Long cooperatorId){
        return  shopDAO.getCountWithAeacus(shopId, name, shopStatus, sellerIds, typeId, administrativeDivisionsId, merchantName, cooperatorId);
    }

    /**
     * 根据shopId删除门店信息
     * @param id Long
     */
    public void deleteById(Long id) {
        if (null == id) {
            return;
        }
        ShopDO shopDO = shopDAO.queryById(id);
        // 判断门店状态是否为待安装
        if (null != shopDO) {
            shopDAO.deleteById(id);
            // 设置门店为删除
            shopDO.setDeleted(CommonConstants.INTEGER_ONE);
            try {
                geoCache.geoDelete(String.valueOf(shopDO.getId()));
            }catch (Exception e){
                log.error("删除geo缓存失败！");
            }

            // epeius delete
//            shopServiceImpl.delete(id);
        }
    }

    /**
     * 获取所有BD的ID
     * @return List<Long>
     */
    public List<Long> queryAllBDWithAeacus() {
        return shopDAO.queryAllBDWithAeacus();
    }

    /**
     * 门店断约
     * @param id Long
     */
    public void breakOff(Long id) {
        if (null == id) {
            return;
        }
        // 断约操作
        shopDAO.breakOffById(id);
    }

    /**
     * 门店续约
     * @param id Long
     */
    public void renewContract(Long id) {
        if (null == id) {
            return;
        }
        // 续约操作
        shopDAO.renewContractById(id);
    }

    /**
     * 解绑商户
     * @param id Long
     */
    public void unBindMerchant(Long id) {
        if (null == id) {
            return;
        }
        // 续约操作
        shopDAO.unBindMerchantById(id);
    }

    /**
     * 更新门店tip
     * @param id Long
     * @param tips String
     * @param updaterId Long
     */
    public void updateTips(Long id, String tips, Long updaterId) {
        if (null == id) {
            return;
        }
        shopDAO.updateTips(id, tips,updaterId);
    }

    /**
     * 根据门店id获取tip信息
     * @param id Long
     * @return String
     */
    public String queryTipById(Long id) {
        if (null == id) {
            return null;
        }
        return shopDAO.queryTipById(id);
    }

    /**
     * 根据门店类型查询门店个数
     * @param shopTypeIds List<Long>
     * @return Long
     */
    public Long getCountByShopTypeId (List<Long> shopTypeIds) {
        return shopDAO.getCountByShopTypeId(shopTypeIds);
    }

    /**
     * 查询非会员店铺
     * @return
     */
    public List<ShopDO> queryNotMemberShops(){
        return shopDAO.queryNotMemberShops();
    }

    /**
     * 根据店铺名字模糊查询
     * @param name
     * @return
     */
    public List<ShopDO> queryLikeByName(String name){
        return shopDAO.queryLikeByName(name);
    }

    /**
     * 门店数量统计
     *
     * @param shopDO
     * @return
     */
    public Integer shopStatisticCount(ShopDO shopDO){
        return shopDAO.shopStatisticCount(shopDO);
    }


    public List<ShopDO> queryShopList(ShopGroupSearchDTO shopGroupSearchDTO){
        return shopDAO.queryShopList(shopGroupSearchDTO);
    }
}
