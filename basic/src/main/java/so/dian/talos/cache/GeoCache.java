package so.dian.talos.cache;

import so.dian.talos.client.dto.GeoDTO;
import so.dian.talos.client.param.GeoParam;
import org.springframework.data.geo.*;
import org.springframework.stereotype.Component;
import so.dian.eros.cache.RedisClient;
import so.dian.talos.common.enums.RedisEnum;

import javax.annotation.Resource;
import java.util.*;

@Component
public class GeoCache {

    @Resource
    private RedisClient redisClient;

    public List<Map<String, Object>> geoSelectByPoint(GeoParam geoParam){
        if (geoParam.getLongitude() != null && geoParam.getLatitude() != null && geoParam.getRadius() != null) {
            int count = 20;
            if (geoParam.getCount() != null){
                count = geoParam.getCount();
            }
            Point point = new Point(geoParam.getLongitude(), geoParam.getLatitude());
            return redisClient.geoSelectByPoint(point, geoParam.getRadius(), count);
        }
        return null;
    }

    public void geoAdd(GeoDTO geoDTO){
        Optional.ofNullable(geoDTO)
                .filter(dto -> dto.getLongitude() != null && dto.getLatitude() != null && dto.getShopId() != null)
                .ifPresent(dto -> {
                    Point point = new Point(dto.getLongitude(), dto.getLatitude());
                    redisClient.geoAdd(RedisEnum.GEO_MAP.getKey(), point, dto.getShopId());
                });
    }

    public void geoAddAll(List<GeoDTO> geoDTOList){
        geoDTOList.forEach(this::geoAdd);
    }

    public void geoUpdate(GeoDTO geoDTO){
        this.geoAdd(geoDTO);
    }

    public void geoDelete(String shopId){
        redisClient.geoDelete(shopId);
    }
}
