package so.dian.talos.client.api;

import java.util.List;

import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.talos.client.dto.ShopTypeDTO;
import so.dian.talos.client.dto.ShopTypePageDTO;
import so.dian.talos.client.param.AddShopTypeParam;
import so.dian.talos.client.param.DeleteShopTypeParam;
import so.dian.talos.client.param.SearchShopTypeParam;

import javax.validation.Valid;

public interface ShopTypeApi {

    @GetMapping(value = "/shopType/queryList")
    @ResponseBody
    BizResult<List<ShopTypeDTO>> queryList(@RequestParam(value = "language", required = false) String language);

    @GetMapping(value = "/shopType/queryById")
    @ResponseBody
    BizResult<ShopTypePageDTO.ShopType> queryById(@RequestParam(value = "id", required = false) Long id);

    @PostMapping(value = "/shopType/queryForPage")
    @ResponseBody
    BizResult<ShopTypePageDTO> queryForPage(@RequestBody @Valid SearchShopTypeParam searchShopTypeParam);

    @PostMapping(value = "/shopType/add")
    @ResponseBody
    BizResult add(@RequestBody @Valid AddShopTypeParam addShopTypeParam);

    @PutMapping(value = "/shopType/update")
    @ResponseBody
    BizResult update(@RequestBody @Valid AddShopTypeParam addShopTypeParam);

    @DeleteMapping(value = "/shopType/deleteById")
    @ResponseBody
    BizResult deleteById(@RequestBody @Valid DeleteShopTypeParam deleteShopTypeParam);

    @GetMapping(value = "/shopType/getLevelTwoIdsByLevelOneId")
    @ResponseBody
    BizResult<List<Long>> getLevelTwoIdsByLevelOneId(@RequestParam(value = "id", required = false) Long id);

    @PostMapping(value = "/shopType/isExistShopType")
    @ResponseBody
    BizResult<Integer> isExistShopType(@RequestBody @Valid AddShopTypeParam addShopTypeParam);
}
