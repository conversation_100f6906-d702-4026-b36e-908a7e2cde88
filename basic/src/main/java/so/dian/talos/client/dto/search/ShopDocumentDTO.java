package so.dian.talos.client.dto.search;

import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 门店实体对象
 * <AUTHOR>
 * @date        2018-04-04
 * @version     1.0.0
 * @Copyright   北京伊电园网络科技有限公司 2016-2018 © 版权所有 京ICP备17000101号
 */
@Data
@NoArgsConstructor
public class ShopDocumentDTO {

    /**
     * 门店ID
     */
    private Long id;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 门店电话
     */
    private String mobile;

    /**
     * 门店图片
     */
    private String picUrl;

    /**
     * 门店地址
     */
    private String address;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在市
     */
    private String city;

    /**
     * 城市编码
     */
    private Integer cityCode;

    /**
     * 所在区
     */
    private String district;

    /**
     * 区域编码
     */
    private Integer districtCode;

    /**
     * 代理商ID
     */
    private Integer agentId;

    /**
     * 商户ID
     */
    private Integer merchantId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 备用联系人名称
     */
    private String secondaryContactName;

    /**
     * 备用联系人电话
     */
    private String secondaryContactMobile;

    /**
     * 日订单
     */
    private Integer dailyOrderNum;

    /**
     * 日最高订单
     */
    private Integer dailyTopOrderNum;

    /**
     * 门店状态
     */
    private Integer status;

    /**
     * 纬度
     */
    private Double poiLatitude;

    /**
     * 经度
     */
    private Double poiLongitude;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间字符串
     */
    private String createTimeStr;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新时间字符串
     */
    private String updateTimeStr;

    /**
     * 首次安装时间
     */
    private Date confirmTime;

    /**
     * 首次安装时间字符串
     */
    private String confirmTimeStr;

    /**
     * 领取时间
     */
    private Date receiveTime;

    /**
     * 领取时间字符串
     */
    private String receiveTimeStr;

    /**
     * 释放时间
     */
    private Date releaseTime;

    /**
     * 释放时间字符串
     */
    private String releaseTimeStr;

    /**
     * 签约时间
     */
    private Date contractTime;


    /**
     * 签约时间字符串
     */
    private String contractTimeStr;

    /**
     * 类别
     */
    private Integer typeId;

    /**
     * 来源ID
     */
    private Integer originId;

    /**
     * 小二ID
     */
    private Integer sellerId;

    /**
     * 营业时间
     */
    private String openingHours;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 海域
     */
    private Integer seaZone;

    /**
     * 来源类型
     */
    private Integer originType;

    /**
     * 门店等级
     */
    private Integer shopLevel;

    /**
     * 状态变更时间
     */
    private Date statusChangeTime;

    /**
     * 状态改变时间字符
     */
    private String statusChangeTimeStr;

    /**
     * 海域变更时间
     */
    private Date seaZoneTime;

    /**
     * 海域变更时间字符串
     */
    private String seaZoneTimeStr;

    /**
     * 坐标，geo_point
     */
    private String location;

    /**
     * 小二花名
     */
    private String sellerNick;

    /**
     * 小二真名
     */
    private String sellerName;

    /**
     * 小二电话
     */
    private String sellerMobile;

    /**
     * 距离
     */
    private Double distance;


    private String contractPicUrl;

    private Integer leadsId;

    private Integer deliveryType;

    private Date allocateTime;

    private String allocateTimeStr;

    private Integer isKa;

    private Integer priceId;

    private Integer feeRate;

    private Integer seatNum;

    private Integer chargeFrequency;

    private String deviceKeeper;

    private String keeperMobile;

    private Integer deviceKeeperId;

    private Integer category;

    private Integer contractOrigin;

    private Integer freeStatus;

    private Integer brandId;

    private Integer brandType;

    private Integer mallId;

    private Integer mcId;

    private Long administrativeDivisionsId;

    private String signTime;

    private Long cooperaterId;

    /**
     * 拜访计划
     */
    private List<VisitDocumentDTO> vists;
}
