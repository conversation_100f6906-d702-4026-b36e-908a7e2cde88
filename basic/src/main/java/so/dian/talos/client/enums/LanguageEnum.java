package so.dian.talos.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum LanguageEnum {
    ZH_CN(1, "zh-cn", "中文简体"),
    EN_GB(2, "en-gb", "英文"),
    ZH_HK(3, "zh-hk", "中文繁体"),
    VI_VN(4, "vi-vn", "越南"),
    @Deprecated
    ZH_HANS(-3, "zh-hans", "中文繁体"),
    ;

    // code对应theseus库的18n_lang表中数据的id
    private Integer code;

    // language对应theseus库的18n_lang表中数据的lang
    private String language;

    private String desc;

    public static LanguageEnum getByCode (Integer code) {
        if (code.equals(0)) {
            return null;
        }
        for (LanguageEnum e : LanguageEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static LanguageEnum getByLanguage (String language) {
        if (StringUtils.isEmpty(language)) {
            return null;
        }
        for (LanguageEnum e : LanguageEnum.values()) {
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(e.getLanguage(), language)) {
                return e;
            }
        }
        return null;
    }

}
