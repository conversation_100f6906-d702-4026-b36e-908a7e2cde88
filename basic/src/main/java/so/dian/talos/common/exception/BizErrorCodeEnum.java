package so.dian.talos.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.exception.ErrorCodeEnum;

/**
 * 业务异常 <br/>
 *
 * <AUTHOR>
 * @date 2018/10/24 11:38 AM
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Getter
@AllArgsConstructor
public enum BizErrorCodeEnum implements EnumInterface {

    RECORD_NOT_EXISTED(30001, "record not existed"),
    //------device----------
    DEVICE_NOT_EXISTED(31001, "device not existed"),
    DEVICE_TYPE_NOT_DEFINITION(31002, "device type not definition"),
    //------warehouse---------
    DEVICE_HAS_INCOME_WAREHOUSE(32001, "device has income warehouse"),
    WAREHOUSE_NOT_EXISTED(32002, "warehouse not existed"),
    DEVICE_HAS_EXISTED(32003, "device has existed"),
    //-----account storage--------------
    ACCOUNT_STORAGE_NOT_EXISTED(33001, "account storage not existed"),
    ;

    private Integer code;
    private String desc;


    @Override
    public EnumInterface getDefault() {
        return ErrorCodeEnum.UNKNOWN_ERROR;
    }
}
