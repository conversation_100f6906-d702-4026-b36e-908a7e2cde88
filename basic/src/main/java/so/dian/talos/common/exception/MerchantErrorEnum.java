package so.dian.talos.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;
import so.dian.commons.eden.exception.ErrorCodeEnum;

@Getter
@AllArgsConstructor
public enum MerchantErrorEnum implements EnumInterface {

    MERCHANT_EXIST(203001, "商户已存在"),
    MERCHANT_BIND_SHOP(203002, "商户已绑定门店"),
    MERCHANT_NAME_REPEAT(203003, "商户名称重复"),
    MERCHANT_CONTACT_NAME_REPEAT(203004, "商户联系人重复"),
    MERCHANT_CONTACT_MOBILE_REPEAT(203005, "商户联系人电话重复"),
    MERCHANT_NOT_EXIST(203006, "商户不存在"),

    MERCHANT_EXIST_PERCENTAGE(204001, "商户已开通分成"),
    MERCHANT_CLOSED_PERCENTAGE(204002, "商户已关闭分成"),
    MERCHANT_NOT_OPEN_PERCENTAGE(204003, "商户未开通分成"),
    MERCHANT_PERCENTAGE_SAME(204004, "修改分成和当前分成相同"),
    MERCHANT_PRINCIPAL_EMPTY(204005, "商户负责人不能为空"),
    MERCHANT_PRINCIPAL_NOT_EXIST(204006, "商户负责人不存在"),
    MERCHANT_PRINCIPAL_NOT_SAME(204007, "商户负责人不一致"),


    MERCHANT_PRINCIPAL_SAME(204009, "商户负责人不能和当前负责人相同"),
    MERCHANT_PERCENTAGE_STATUS_ERROR(204010, "分成状态错误"),
    MERCHANT_SEARCH_TYPE_ERROR(204011, "商户搜索类型错误"),
    MERCHANT_SEARCH_WORD_NULL(204012, "商户搜索关键字不能为空"),
    MERCHANT_PRINCIPAL_NOT_ROOT(204013, "商户负责人非根账号"),
    MERCHANT_PRINCIPAL_DISABLE(204014, "商户负责人已被禁用"),
    MERCHANT_USER_NOT_AUTH(204015, "当前用户无权限"),
    ;


    private Integer code;
    private String desc;


    @Override
    public EnumInterface getDefault() {
        return ErrorCodeEnum.UNKNOWN_ERROR;
    }
}
