package so.dian.talos.controller;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.commons.enums.language.FaqBizEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.ezreal.response.faq.CategoryFaqLangDTO;
import com.chargebolt.framework.i18n.I18nUtil;
import com.chargebolt.template.BaseChargeboltController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import so.dian.mofa3.lang.domain.Result;
import so.dian.talos.pojo.dto.MultilingualFaqDTO;
import so.dian.talos.request.FaqCreateRQ;
import so.dian.talos.request.FaqTranslateSaveRQ;

/**
 * FAQ控制器
 */
@Tag(name = "FaqControllerAPI", description = "FAQ")
@RestController
@RequestMapping("/faq")
public class FaqController extends BaseChargeboltController {

    @Resource
    private FaqService faqService;
    @Resource
    private I18nUtil i18nUtil;

    /**
     * FAQ列表查询接口
     */
    @Operation(summary = "FAQ列表查询接口")
    @GetMapping("/list")
    public Result<List<CategoryFaqLangDTO>> list(@RequestParam Long agentId) {
        UserDataAuthorityContext userDataAuthorityContext = getUserDataAuthorityContext();
        if (userDataAuthorityContext.getAuthorityLevel() != AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_1) {
            agentId = userDataAuthorityContext.getAgentId();
        }
        List<CategoryFaqLangDTO> categoryFaqLangDTOList = faqService.list(agentId);
        return Result.success(categoryFaqLangDTOList);
    }

    /**
     * FAQ新建保存接口
     */
    @Operation(summary = "FAQ新建保存接口")
    @PostMapping("/save")
    public Result<Long> save(@RequestBody FaqCreateRQ createRQ) {
        if (createRQ == null) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_PARAM_NULL));
        }
        if (createRQ.getCategoryFaq() == null) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_PARAM_NULL));
        }
        if (createRQ.getAgentId() == null) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_AGENT_ID_NULL));
        }

        // 验证登录用户是否有权限，平台账号有权限操作所有代理商，代理商只能操作自己
        if (!checkPermission(createRQ.getAgentId())) {
            return Result.fail("no permission");
        }
        Long userId = getUserDataAuthorityContext().getUserId();
        Long categoryId = faqService.save(createRQ, userId);
        return Result.success(categoryId);
    }

    /**
     * 保存并翻译
     */
    @Operation(summary = "保存并翻译")
    @PostMapping("/saveAndTranslate")
    public Result<List<MultilingualFaqDTO>> saveAndTranslate(@RequestBody FaqCreateRQ createRQ) {
        // 参数校验
        if (createRQ == null) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_PARAM_NULL));
        }
        if (createRQ.getCategoryFaq() == null) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_PARAM_NULL));
        }
        if (createRQ.getAgentId() == null) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_AGENT_ID_NULL));
        }

        // 验证登录用户是否有权限，平台账号有权限操作所有代理商，代理商只能操作自己
        if (!checkPermission(createRQ.getAgentId())) {
            return Result.fail("no permission");
        }
        List<MultilingualFaqDTO> dtos = faqService.saveAndTranslate(createRQ,
                getUserDataAuthorityContext().getUserId());
        return Result.success(dtos);
    }

    /**
     * FAQ新建保存接口
     */
    @Operation(summary = "删除 Faq 分类")
    @PostMapping("/deleteCategory")
    public Result<Boolean> deleteCategory(@RequestParam Long categoryId) {
        // 参数校验
        if (categoryId == null) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_CATEGORY_ID_REQUIRED));
        }
        UserDataAuthorityContext context = getUserDataAuthorityContext();
        boolean isPlatformUser = context.getAuthorityLevel() == AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_1;
        faqService.deleteCategory(categoryId, isPlatformUser, context.getAgentId(), context.getUserId());
        return Result.success(true);
    }

    /**
     * FAQ翻译保存接口
     */
    @Operation(summary = "FAQ翻译保存接口")
    @PostMapping("/translateCheckSave")
    public Result<Boolean> translateSave(@RequestBody List<FaqTranslateSaveRQ> listRQ) {
        // 参数校验
        if (CollectionUtils.isEmpty(listRQ)) {
            return Result.fail(i18nUtil.getMessage(FaqBizEnum.FAQ_PARAM_NULL));
        }

        faqService.translateCheckSave(listRQ,
                getUserDataAuthorityContext().getAgentId(),
                getUserDataAuthorityContext().getUserId());
        return Result.success(true);
    }

    private boolean checkPermission(Long agentId) {
        UserDataAuthorityContext userDataAuthorityContext = getUserDataAuthorityContext();
        if (userDataAuthorityContext.getAuthorityLevel() != AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_1) {
            // 代理商只能操作自己
            if (!userDataAuthorityContext.getAgentId().equals(agentId)) {
                return false;
            }
        }
        return true;
    }

}
