package so.dian.talos.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chargebolt.aeacus.common.exception.I18nMessageException;
import com.chargebolt.commons.enums.language.FaqBizEnum;
import com.chargebolt.component.rocketmq.FaqDeleteProducer;
import com.chargebolt.context.FaqDeleteContext;
import com.chargebolt.ezreal.response.faq.CategoryFaqLangDTO;
import com.chargebolt.ezreal.response.faq.FaqLangDTO;
import com.chargebolt.theseus.dto.I18nKV;
import com.chargebolt.theseus.dto.rq.I18nTextsDTO;
import com.chargebolt.theseus.service.I18nTextsService;
import com.google.common.collect.Lists;
import com.meidalife.common.monitor.RTMonitor;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.talos.biz.FaqCrudService;
import so.dian.talos.biz.FaqI18nService;
import so.dian.talos.biz.FaqTranslationService;
import so.dian.talos.configuration.LanguageConfiguration;
import so.dian.talos.pojo.dto.CategoryFaqDTO;
import so.dian.talos.pojo.dto.MultilingualFaqDTO;
import so.dian.talos.pojo.entity.FaqCategoryDO;
import so.dian.talos.pojo.entity.FaqRecordDO;
import so.dian.talos.request.CategoryTranslateDTO;
import so.dian.talos.request.FaqCreateRQ;
import so.dian.talos.request.FaqTranslateDTO;
import so.dian.talos.request.FaqTranslateSaveRQ;

/**
 * FAQ门面服务
 * 作为FAQ模块的统一入口，协调各个子服务完成FAQ的完整业务流程
 * 重构后职责更加清晰，遵循单一职责原则
 * 
 * <AUTHOR>
 * @since 2024-12-20 (重构版本)
 */
@Slf4j
@Service
public class FaqService {

    /**
     * FAQ CRUD服务 - 负责FAQ数据的增删改查
     */
    @Resource
    private FaqCrudService faqCrudService;
    /**
     * FAQ翻译服务 - 负责FAQ内容的翻译处理
     */
    @Resource
    private FaqTranslationService faqTranslationService;
    /**
     * FAQ国际化服务 - 负责国际化编码管理
     */
    @Resource
    private FaqI18nService faqI18nService;
    /**
     * 语言配置 - 负责语言相关配置管理
     */
    @Resource
    private LanguageConfiguration languageConfiguration;
    /**
     * I18nTexts服务 - 负责国际化文本管理
     */
    @Resource
    private I18nTextsService i18nTextsService;

    /**
     * 任务执行线程池 - 用于FAQ并发处理
     */
    @Resource
    private TaskExecutor taskExecutor;

    /**
     * FAQ删除消息生产者 - 用于发送删除通知消息
     */
    @Resource
    private FaqDeleteProducer faqDeleteProducer;

    /**
     * 保存FAQ数据（异步翻译版本）
     * 先保存FAQ数据，然后异步进行翻译处理，保证主流程响应速度
     * 
     * @param rq      FAQ创建请求对象
     * @param agentId 代理商ID
     */
    @RTMonitor
    @Transactional(rollbackFor = Exception.class)
    public Long save(FaqCreateRQ createRQ, Long operatorId) {
        // 1. 执行CRUD操作，获取需要翻译的数据
        CategoryFaqDTO categoryFaq = faqCrudService.processCategoryFaq(createRQ, operatorId);
        // 2. 如果有需要翻译的内容，异步执行翻译
        faqTranslationService.translateAsync(categoryFaq);
        // 3. 返回分类ID
        return categoryFaq.getCategory().getId();
    }

    /**
     * 保存并同步翻译FAQ数据
     * 保存FAQ数据后立即进行翻译，返回多语言结果
     * 适用于需要立即获取翻译结果的场景
     * 
     * @param rq      FAQ创建请求对象
     * @param agentId 代理商ID
     * @return 多语言FAQ数据
     */
    @RTMonitor
    public List<MultilingualFaqDTO> saveAndTranslate(FaqCreateRQ createRQ, Long operatorId) {
        // 1. 执行CRUD操作，获取需要翻译的数据
        CategoryFaqDTO categoryFaqDTO = faqCrudService.processCategoryFaq(createRQ, operatorId);
        // 2. 同步翻译
        faqTranslationService.translateSync(categoryFaqDTO);
        // 3. 构建并返回多语言数据
        return buildMultilingualFaqData(categoryFaqDTO.getCategory().getId());
    }

    /**
     * 获取FAQ列表
     * 查询指定代理商的FAQ数据，返回原始语言版本
     * 
     * @param limitAgentIds 限制的代理商ID列表
     * @return FAQ分类列表
     */
    @RTMonitor
    public List<CategoryFaqLangDTO> list(Long agentId) {
        // 1. 查询分类信息
        List<FaqCategoryDO> categoryList = faqCrudService.findCategoriesByAgentId(agentId);

        // 2. 查询FAQ记录
        List<FaqRecordDO> faqRecordList = faqCrudService.findFaqsByAgentId(agentId);

        // 3. 组装返回数据
        return buildCategoryFaqLangDTOList(categoryList, faqRecordList);

    }

    // ==================== 私有方法 ====================

    /**
     * 构建多语言FAQ数据
     * 
     * @param categoryId 分类ID
     * @return 多语言FAQ数据
     */
    @RTMonitor
    private List<MultilingualFaqDTO> buildMultilingualFaqData(Long categoryId) {
        // 参数校验
        if (categoryId == null) {
            log.warn("分类ID为空，返回空结果");
            return Collections.emptyList();
        }

        try {
            // 1. 查询FAQ数据
            List<FaqRecordDO> faqRecords = faqCrudService.findFaqsByCategoryId(categoryId);
            if (faqRecords.isEmpty()) {
                return Collections.emptyList();
            }

            // 2. 查询分类数据
            FaqCategoryDO category = faqCrudService.getCategoryById(categoryId);
            if (category == null) {
                log.warn("FAQ分类数据为空，categoryId: {}", categoryId);
                return Collections.emptyList();
            }

            // 3. 构建多语言数据
            return buildMultilingualResult(category, faqRecords);

        } catch (Exception e) {
            log.error("构建多语言FAQ数据失败，categoryId: {}", categoryId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建多语言结果
     * 
     * @param category   分类信息
     * @param faqRecords FAQ记录列表
     * @return 多语言FAQ数据
     */
    private List<MultilingualFaqDTO> buildMultilingualResult(FaqCategoryDO category, List<FaqRecordDO> faqRecords) {
        List<MultilingualFaqDTO> result = new ArrayList<>();

        // 构建原始语言数据
        CategoryFaqLangDTO originData = buildOriginLanguageData(category, faqRecords);
        result.add(new MultilingualFaqDTO("origin", originData));

        // 构建各种翻译语言数据 - 使用并发处理
        List<String> supportedLangCodes = languageConfiguration.getSupported();
        // 多语言时使用并发处理
        List<MultilingualFaqDTO> concurrentResults = buildMultilingualTranslationsConcurrent(
                category, faqRecords, supportedLangCodes);
        result.addAll(concurrentResults);

        return result;
    }

    /**
     * 并发构建多语言翻译数据
     * 使用taskExecutor线程池并发处理各种语言的翻译
     * 增加了更好的错误处理和超时控制
     *
     * @param category           分类信息
     * @param faqRecords         FAQ记录列表
     * @param supportedLangCodes 支持的语言代码列表
     * @return 多语言FAQ数据列表
     */
    private List<MultilingualFaqDTO> buildMultilingualTranslationsConcurrent(
            FaqCategoryDO category, List<FaqRecordDO> faqRecords, List<String> supportedLangCodes) {

        // 过滤有效的语言代码
        List<String> validLangCodes = supportedLangCodes.stream()
                .filter(langCode -> languageConfiguration.getLangId(langCode) != null)
                .collect(Collectors.toList());

        if (validLangCodes.isEmpty()) {
            log.warn("没有有效的语言代码需要处理");
            return new ArrayList<>();
        }

        // 限制并发数量，避免线程池耗尽
        int maxConcurrency = Math.min(validLangCodes.size(), 5); // 最多5个并发
        log.info("开始并发处理{}种语言的翻译，最大并发数: {}", validLangCodes.size(), maxConcurrency);

        // 创建异步任务列表
        List<CompletableFuture<MultilingualFaqDTO>> futures = validLangCodes.stream()
                .map(langCode -> CompletableFuture.supplyAsync(() -> {
                    try {
                        Integer langId = languageConfiguration.getLangId(langCode);
                        log.debug("开始处理语言翻译：{}", langCode);

                        // 主要异常点：翻译处理可能失败
                        CategoryFaqLangDTO langCategoryData = buildTranslatedLanguageData(
                                category, faqRecords, langId, langCode);

                        log.debug("完成语言翻译：{}", langCode);
                        return new MultilingualFaqDTO(langCode, langCategoryData);
                    } catch (Exception e) {
                        log.error("语言[{}]翻译处理失败", langCode, e);
                        // 异常时返回原始语言数据作为降级
                        CategoryFaqLangDTO originData = buildOriginLanguageData(category, faqRecords);
                        return new MultilingualFaqDTO(langCode, originData);
                    }
                }, taskExecutor))
                .collect(Collectors.toList());

        try {
            // 增加超时时间，避免过早超时
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(120, TimeUnit.SECONDS); // 增加到120秒

            // 收集结果
            List<MultilingualFaqDTO> results = futures.stream()
                    .map(future -> {
                        try {
                            return future.get();
                        } catch (Exception e) {
                            log.error("获取多语言翻译结果失败", e);
                            return null;
                        }
                    })
                    .filter(dto -> dto != null)
                    .collect(Collectors.toList());

            log.info("并发多语言翻译完成，成功处理{}种语言", results.size());
            return results;

        } catch (TimeoutException e) {
            log.error("多语言并发翻译超时，尝试获取已完成的结果：{}", e.getMessage());
            // 超时时尝试获取已完成的结果
            return getCompletedResults(futures, validLangCodes, category, faqRecords);
        } catch (Exception e) {
            log.error("多语言并发翻译异常，降级使用串行处理：{}", e.getMessage());
            return buildMultilingualTranslationsSerial(category, faqRecords, validLangCodes);
        }
    }

    /**
     * 获取已完成的翻译结果
     * 当并发翻译超时时，尝试获取已经完成的结果
     */
    private List<MultilingualFaqDTO> getCompletedResults(List<CompletableFuture<MultilingualFaqDTO>> futures,
            List<String> validLangCodes, FaqCategoryDO category, List<FaqRecordDO> faqRecords) {
        List<MultilingualFaqDTO> results = new ArrayList<>();

        for (int i = 0; i < futures.size(); i++) {
            CompletableFuture<MultilingualFaqDTO> future = futures.get(i);
            String langCode = validLangCodes.get(i);

            try {
                if (future.isDone()) {
                    MultilingualFaqDTO result = future.get();
                    if (result != null) {
                        results.add(result);
                    }
                } else {
                    // 未完成的任务，使用原始数据作为降级
                    log.warn("语言[{}]翻译未完成，使用原始数据", langCode);
                    CategoryFaqLangDTO originData = buildOriginLanguageData(category, faqRecords);
                    results.add(new MultilingualFaqDTO(langCode, originData));
                }
            } catch (Exception e) {
                log.error("获取语言[{}]翻译结果失败，使用原始数据", langCode, e);
                CategoryFaqLangDTO originData = buildOriginLanguageData(category, faqRecords);
                results.add(new MultilingualFaqDTO(langCode, originData));
            }
        }

        log.info("超时情况下获取到{}种语言的翻译结果", results.size());
        return results;
    }

    /**
     * 串行构建多语言翻译数据
     * 作为并发翻译失败时的降级方案
     */
    private List<MultilingualFaqDTO> buildMultilingualTranslationsSerial(
            FaqCategoryDO category, List<FaqRecordDO> faqRecords, List<String> validLangCodes) {
        List<MultilingualFaqDTO> results = new ArrayList<>();

        log.info("开始串行处理{}种语言的翻译", validLangCodes.size());

        for (String langCode : validLangCodes) {
            try {
                Integer langId = languageConfiguration.getLangId(langCode);
                log.debug("串行处理语言翻译：{}", langCode);

                CategoryFaqLangDTO langCategoryData = buildTranslatedLanguageData(
                        category, faqRecords, langId, langCode);

                results.add(new MultilingualFaqDTO(langCode, langCategoryData));
                log.debug("串行完成语言翻译：{}", langCode);
            } catch (Exception e) {
                log.error("串行处理语言[{}]翻译失败，使用原始数据", langCode, e);
                CategoryFaqLangDTO originData = buildOriginLanguageData(category, faqRecords);
                results.add(new MultilingualFaqDTO(langCode, originData));
            }
        }

        log.info("串行多语言翻译完成，处理了{}种语言", results.size());
        return results;
    }

    /**
     * 构建原始语言数据
     * 
     * @param categories     分类列表
     * @param categoryFaqMap 分类FAQ映射
     * @return 原始语言的分类数据列表
     */
    private CategoryFaqLangDTO buildOriginLanguageData(
            FaqCategoryDO category, List<FaqRecordDO> categoryFaqs) {

        // 空指针检查
        if (category == null) {
            throw new I18nMessageException(FaqBizEnum.FAQ_CATEGORY_DATA_NULL);

        }

        CategoryFaqLangDTO categoryDto = new CategoryFaqLangDTO();
        categoryDto.setCategoryId(category.getId());
        categoryDto.setCategoryName(category.getName());

        List<FaqLangDTO> faqDtos = categoryFaqs.stream()
                .map(this::buildOriginFaqLangDTO)
                .collect(Collectors.toList());
        categoryDto.setFaqs(faqDtos);

        return categoryDto;
    }

    /**
     * 批量获取翻译文本
     * 一次性查询所有需要的翻译，避免N+1查询问题
     * 
     * @param category     分类信息
     * @param categoryFaqs FAQ列表
     * @param langId       语言ID
     * @return 翻译文本映射 (i18nCode -> translatedText)
     */
    @RTMonitor
    private Map<String, String> batchGetTranslatedTexts(
            FaqCategoryDO category, List<FaqRecordDO> categoryFaqs, Integer langId) {

        try {
            List<String> i18nCodes = collectAllI18nCodes(category, categoryFaqs);

            // 批量查询翻译文本
            if (!i18nCodes.isEmpty()) {
                List<I18nKV> translations = i18nTextsService.batchGetText(langId, i18nCodes);
                return translations.stream()
                        .collect(Collectors.toMap(I18nKV::getKey, I18nKV::getValue));
            }

            return new HashMap<>();

        } catch (Exception e) {
            log.error("批量查询翻译失败，langId={}", langId, e);
            return new HashMap<>();
        }
    }

    /**
     * 收集所有国际化编码
     * 
     * @param category     分类信息
     * @param categoryFaqs FAQ列表
     * @return 国际化编码列表
     */
    private List<String> collectAllI18nCodes(FaqCategoryDO category, List<FaqRecordDO> categoryFaqs) {
        List<String> i18nCodes = new ArrayList<>();

        // 收集分类的国际化编码
        if (category != null && category.getId() != null) {
            String categoryI18nCode = getOrGenerateI18nCode(
                    category.getI18nCode(),
                    () -> faqI18nService.generateCategoryI18nCode(category.getId()));
            i18nCodes.add(categoryI18nCode);
        }

        // 收集FAQ的国际化编码
        for (FaqRecordDO faq : categoryFaqs) {
            if (faq.getId() != null) {
                // 问题编码
                String questionI18nCode = getOrGenerateI18nCode(
                        faq.getQuestionI18nCode(),
                        () -> faqI18nService.generateQuestionI18nCode(faq.getId()));
                i18nCodes.add(questionI18nCode);

                // 答案编码
                String answerI18nCode = getOrGenerateI18nCode(
                        faq.getAnswerI18nCode(),
                        () -> faqI18nService.generateAnswerI18nCode(faq.getId()));
                i18nCodes.add(answerI18nCode);
            }
        }

        return i18nCodes.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 获取或生成国际化编码
     * 统一处理i18n编码获取逻辑，减少重复代码
     * 
     * @param existingCode 已存在的编码
     * @param generator    编码生成器
     * @return 国际化编码
     */
    private String getOrGenerateI18nCode(String existingCode, java.util.function.Supplier<String> generator) {
        return StringUtils.isNotBlank(existingCode) ? existingCode : generator.get();
    }

    /**
     * 构建翻译语言数据
     * 
     * @param categories     分类列表
     * @param categoryFaqMap 分类FAQ映射
     * @param langId         语言ID
     * @param langCode       语言代码
     * @return 翻译语言的分类数据列表
     */
    private CategoryFaqLangDTO buildTranslatedLanguageData(
            FaqCategoryDO category, List<FaqRecordDO> categoryFaqs,
            Integer langId, String langCode) {

        // 批量获取所有翻译文本
        Map<String, String> translations = batchGetTranslatedTexts(category, categoryFaqs, langId);

        CategoryFaqLangDTO categoryDto = new CategoryFaqLangDTO();
        categoryDto.setCategoryId(category.getId());

        // 获取分类名称的翻译
        String translatedCategoryName = getTranslatedText(
                category.getI18nCode(),
                () -> faqI18nService.generateCategoryI18nCode(category.getId()),
                category.getName(),
                translations,
                langCode);
        categoryDto.setCategoryName(translatedCategoryName);

        // 根据FAQ数量判断是否使用并发处理
        List<FaqLangDTO> faqDtos;
        if (categoryFaqs.size() > 1) {
            // 数量大于1时使用线程池并发处理
            faqDtos = buildTranslatedFaqLangDTOsConcurrent(categoryFaqs, langCode, translations);
        } else {
            // 数量不大于1时使用串行处理
            faqDtos = categoryFaqs.stream()
                    .map(faq -> buildTranslatedFaqLangDTO(faq, langCode, translations))
                    .collect(Collectors.toList());
        }
        categoryDto.setFaqs(faqDtos);

        return categoryDto;
    }

    /**
     * 构建原始FAQ语言DTO
     * 
     * @param faq FAQ记录
     * @return FAQ语言DTO
     */
    private FaqLangDTO buildOriginFaqLangDTO(FaqRecordDO faq) {

        FaqLangDTO dto = new FaqLangDTO();
        dto.setFaqId(faq.getId());
        dto.setQuestion(faq.getQuestion());
        dto.setAnswer(faq.getAnswer());
        dto.setRank(faq.getRank());
        dto.setQuestionI18nCode(faq.getQuestionI18nCode());
        dto.setAnswerI18nCode(faq.getAnswerI18nCode());
        return dto;
    }

    /**
     * 并发构建翻译FAQ语言DTO列表
     * 使用taskExecutor线程池并发处理FAQ翻译
     * 增加了更好的错误处理和降级机制
     *
     * @param categoryFaqs FAQ记录列表
     * @param langCode     语言代码
     * @param translations 翻译文本映射
     * @return FAQ语言DTO列表
     */
    private List<FaqLangDTO> buildTranslatedFaqLangDTOsConcurrent(
            List<FaqRecordDO> categoryFaqs, String langCode, Map<String, String> translations) {

        try {
            log.debug("开始并发处理{}个FAQ的翻译，语言: {}", categoryFaqs.size(), langCode);

            // 创建异步任务列表
            List<CompletableFuture<FaqLangDTO>> futures = categoryFaqs.stream()
                    .map(faq -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return buildTranslatedFaqLangDTO(faq, langCode, translations);
                        } catch (Exception e) {
                            log.error("FAQ翻译处理失败，faqId: {}, langCode: {}", faq.getId(), langCode, e);
                            // 异常时返回原始数据
                            return buildOriginFaqLangDTO(faq);
                        }
                    }, taskExecutor))
                    .collect(Collectors.toList());

            // 等待所有任务完成，增加超时时间
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(30, TimeUnit.SECONDS); // 增加到30秒

            // 收集结果
            List<FaqLangDTO> results = futures.stream()
                    .map(future -> {
                        try {
                            return future.get();
                        } catch (Exception e) {
                            log.error("获取FAQ翻译结果失败", e);
                            return null;
                        }
                    })
                    .filter(dto -> dto != null)
                    .collect(Collectors.toList());

            log.debug("FAQ并发翻译完成，成功处理{}个FAQ", results.size());
            return results;

        } catch (TimeoutException e) {
            log.error("FAQ并发翻译超时，降级使用串行处理：langCode={}", langCode, e);
            return buildTranslatedFaqLangDTOsSerial(categoryFaqs, langCode, translations);
        } catch (Exception e) {
            log.error("FAQ并发翻译处理异常，降级使用串行处理：langCode={}", langCode, e);
            return buildTranslatedFaqLangDTOsSerial(categoryFaqs, langCode, translations);
        }
    }

    /**
     * 串行构建翻译FAQ语言DTO列表
     * 作为并发翻译失败时的降级方案
     */
    private List<FaqLangDTO> buildTranslatedFaqLangDTOsSerial(
            List<FaqRecordDO> categoryFaqs, String langCode, Map<String, String> translations) {

        log.info("开始串行处理{}个FAQ的翻译，语言: {}", categoryFaqs.size(), langCode);

        return categoryFaqs.stream()
                .map(faq -> {
                    try {
                        return buildTranslatedFaqLangDTO(faq, langCode, translations);
                    } catch (Exception e) {
                        log.error("串行FAQ翻译处理失败，faqId: {}, langCode: {}", faq.getId(), langCode, e);
                        // 异常时返回原始数据
                        return buildOriginFaqLangDTO(faq);
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建翻译FAQ语言DTO（统一方法）
     * 
     * @param faq          FAQ记录
     * @param langCode     语言代码
     * @param translations 翻译文本映射
     * @return FAQ语言DTO
     */
    private FaqLangDTO buildTranslatedFaqLangDTO(FaqRecordDO faq, String langCode,
            Map<String, String> translations) {
        FaqLangDTO dto = new FaqLangDTO();
        dto.setFaqId(faq.getId());
        dto.setRank(faq.getRank());

        // 获取问题的翻译
        String translatedQuestion = getTranslatedText(
                faq.getQuestionI18nCode(),
                () -> faqI18nService.generateQuestionI18nCode(faq.getId()),
                faq.getQuestion(),
                translations,
                langCode);
        dto.setQuestion(translatedQuestion);

        // 获取答案的翻译
        String translatedAnswer = getTranslatedText(
                faq.getAnswerI18nCode(),
                () -> faqI18nService.generateAnswerI18nCode(faq.getId()),
                faq.getAnswer(),
                translations,
                langCode);
        dto.setAnswer(translatedAnswer);

        return dto;
    }

    /**
     * 通用翻译获取方法
     * 统一处理分类名称、FAQ问题、FAQ答案的翻译逻辑
     * 
     * @param i18nCode          当前的国际化编码
     * @param i18nCodeGenerator 国际化编码生成器
     * @param originalText      原始文本
     * @param translations      批量查询的翻译结果
     * @param langCode          目标语言代码
     * @return 翻译后的文本
     */
    private String getTranslatedText(String i18nCode,
            java.util.function.Supplier<String> i18nCodeGenerator,
            String originalText,
            Map<String, String> translations,
            String langCode) {
        try {
            // 1. 优先从批量查询结果中获取翻译
            if (StringUtils.isNotBlank(i18nCode)) {
                String translatedText = Optional.ofNullable(translations).map(m -> m.get(i18nCode)).orElse(null);
                if (StringUtils.isNotBlank(translatedText)) {
                    return translatedText;
                }
            }

            // 2. 尝试生成国际化编码并从批量查询结果中查找
            if (StringUtils.isBlank(i18nCode)) {
                String generatedI18nCode = i18nCodeGenerator.get();
                String translatedText = Optional.ofNullable(translations).map(m -> m.get(generatedI18nCode))
                        .orElse(null);
                if (StringUtils.isNotBlank(translatedText)) {
                    return translatedText;
                }
            }

            // 3. 批量查询结果没有翻译，使用实时翻译作为降级
            log.debug("批量查询结果无翻译，使用实时翻译降级：langCode={}", langCode);
            return faqTranslationService.translateRealtime(originalText, langCode);

        } catch (Exception e) {
            log.error("获取翻译失败，使用原文：langCode={}", langCode, e);
            return originalText; // 异常时返回原文
        }
    }

    /**
     * 构建分类FAQ语言DTO列表
     * 
     * @param categories FAQ分类列表
     * @param faqRecords FAQ记录列表
     * @return 分类FAQ语言DTO列表
     */
    @RTMonitor
    private List<CategoryFaqLangDTO> buildCategoryFaqLangDTOList(
            List<FaqCategoryDO> categories,
            List<FaqRecordDO> faqRecords) {

        // 按分类ID分组FAQ记录
        Map<Long, List<FaqRecordDO>> categoryFaqMap = faqRecords.stream()
                .collect(Collectors.groupingBy(FaqRecordDO::getCategoryId));

        return categories.stream().map(category -> {
            CategoryFaqLangDTO dto = new CategoryFaqLangDTO();
            dto.setCategoryId(category.getId());
            dto.setCategoryName(category.getName());
            dto.setCategoryI18nCode(category.getI18nCode());
            List<FaqRecordDO> categoryFaqs = categoryFaqMap.get(category.getId());
            if (categoryFaqs != null) {
                List<FaqLangDTO> faqDtos = categoryFaqs.stream()
                        .map(this::buildOriginFaqLangDTO)
                        .collect(Collectors.toList());
                dto.setFaqs(faqDtos);
            } else {
                dto.setFaqs(Lists.newArrayList());
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 保存FAQ翻译校验结果
     * 此接口明确的点：
     * 原文不会变，但翻译结果会变
     * 所以不需要修改 faq 和 faqcategory 表，只需要修改 i18n_texts 表
     * 
     * @param rq         翻译保存请求列表
     * @param agentId    代理商ID
     * @param operatorId 操作员ID
     */
    @RTMonitor
    public void translateCheckSave(List<FaqTranslateSaveRQ> translateSaveRQs, Long agentId, Long operatorId) {
        // 1. 提取原文数据并构建基础映射
        FaqTranslateSaveRQ origin = extractOriginData(translateSaveRQs);
        if (origin.getCategoryFaq() == null || origin.getCategoryFaq().getCategoryId() == null) {
            throw new I18nMessageException(FaqBizEnum.FAQ_CATEGORY_ID_REQUIRED);

        }
        Long categoryId = origin.getCategoryFaq().getCategoryId();
        FaqCategoryDO category = faqCrudService.findCategoryById(categoryId);
        if (category == null) {
            log.error("FAQ分类不存在,categoryId: {}", categoryId);
            throw new I18nMessageException(FaqBizEnum.FAQ_CATEGORY_NOT_FOUND);

        }

        Map<Long, FaqRecordDO> faqMap = buildFaqMap(origin);

        // 2. 处理各语言的翻译数据
        processTranslationData(translateSaveRQs, category, faqMap);
    }

    /**
     * 提取原文数据
     * 
     * @param rq 翻译保存请求列表
     * @return 原文数据对象
     */
    private FaqTranslateSaveRQ extractOriginData(List<FaqTranslateSaveRQ> translateSaveRQs) {
        return translateSaveRQs.stream()
                .filter(item -> item != null && "origin".equals(item.getLangCode()))
                .findFirst()
                .orElseThrow(() -> new I18nMessageException(FaqBizEnum.FAQ_ORIGIN_CONTENT_REQUIRED));
    }

    /**
     * 构建FAQ映射
     * 
     * @param origin 原文数据
     * @return FAQ ID到FAQ对象的映射
     */
    private Map<Long, FaqRecordDO> buildFaqMap(FaqTranslateSaveRQ origin) {
        List<FaqTranslateDTO> faqs = origin.getCategoryFaq().getFaqs();
        if (CollectionUtils.isEmpty(faqs)) {
            throw new I18nMessageException(FaqBizEnum.FAQ_LIST_IS_EMPTY);
        }

        List<Long> faqIds = faqs.stream()
                .map(FaqTranslateDTO::getFaqId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<FaqRecordDO> faqRecords = faqCrudService.findFaqRecordsByIds(faqIds);
        return faqRecords.stream()
                .collect(Collectors.toMap(FaqRecordDO::getId, Function.identity()));
    }

    /**
     * 处理翻译数据（批量优化版本）
     * 
     * @param translateSaveRQs 翻译保存请求列表
     * @param category         分类
     * @param faqMap           FAQ映射
     */
    private void processTranslationData(List<FaqTranslateSaveRQ> translateSaveRQs,
            FaqCategoryDO category,
            Map<Long, FaqRecordDO> faqMap) {
        // 按语言分组收集所有需要保存的文本数据
        Map<Integer, List<I18nTextsDTO>> batchDataByLang = new HashMap<>();

        for (FaqTranslateSaveRQ item : translateSaveRQs) {
            if ("origin".equals(item.getLangCode())) {
                continue;
            }

            String langCode = item.getLangCode();
            Integer langId = LanguageConfiguration.LANG_ID_MAPPING.get(langCode);

            if (langId == null) {
                log.warn("未找到语言代码[{}]对应的语言ID，跳过处理", langCode);
                continue;
            }

            // 收集该语言的所有翻译数据
            List<I18nTextsDTO> langTexts = collectLanguageTranslationData(
                    item.getCategoryFaq(), langId, category, faqMap);

            if (!langTexts.isEmpty()) {
                batchDataByLang.put(langId, langTexts);
            }
        }

        // 批量保存所有收集到的数据
        batchSaveAllTranslations(batchDataByLang);
    }

    /**
     * 收集单个语言的翻译数据（不立即保存到数据库）
     * 
     * @param categoryFaqs 分类FAQ列表
     * @param langId       语言ID
     * @param categoryMap  分类映射
     * @param faqMap       FAQ映射
     * @return 该语言的所有翻译数据列表
     */
    private List<I18nTextsDTO> collectLanguageTranslationData(CategoryTranslateDTO categoryFaq,
            Integer langId,
            FaqCategoryDO category,
            Map<Long, FaqRecordDO> faqMap) {
        List<I18nTextsDTO> allTexts = new ArrayList<>();

        // 收集分类翻译数据
        I18nTextsDTO categoryText = collectCategoryTranslationData(category, langId);
        if (categoryText != null) {
            allTexts.add(categoryText);
        }

        // 收集FAQ翻译数据
        List<I18nTextsDTO> faqTexts = collectFaqTranslationsData(categoryFaq.getFaqs(), langId, faqMap);
        allTexts.addAll(faqTexts);

        return allTexts;
    }

    /**
     * 批量保存所有语言的翻译数据 - 优化版本
     * 汇总所有语言数据，统一调用一次批量保存，提升性能
     * 
     * @param batchDataByLang 按语言分组的翻译数据
     */
    private void batchSaveAllTranslations(Map<Integer, List<I18nTextsDTO>> batchDataByLang) {
        if (batchDataByLang == null || batchDataByLang.isEmpty()) {
            return;
        }

        // 汇总所有语言的翻译数据到一个List中
        List<I18nTextsDTO> allTranslationTexts = batchDataByLang.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 统一保存所有语言的翻译数据 - 只调用一次数据库
        boolean success = i18nTextsService.batchAddText(allTranslationTexts);
        if (!success) {
            throw new I18nMessageException(FaqBizEnum.FAQ_TRANSLATION_SAVE_FAILED);
        }
    }

    /**
     * 收集分类翻译数据
     * 
     * @param category 分类对象
     * @param langId   语言ID
     * @return 分类翻译数据，如果无效则返回null
     */
    private I18nTextsDTO collectCategoryTranslationData(FaqCategoryDO category, Integer langId) {
        if (category == null || StringUtils.isBlank(category.getName()) || category.getId() == null) {
            return null;
        }

        String categoryI18nCode = getOrGenerateI18nCode(
                category.getI18nCode(),
                () -> faqI18nService.generateCategoryI18nCode(category.getId()));

        // 直接创建对象，避免过度抽象
        I18nTextsDTO i18nTexts = new I18nTextsDTO();
        i18nTexts.setModule("faq");
        i18nTexts.setLangId(langId);
        i18nTexts.setOrigin(1); // 1表示机器翻译
        i18nTexts.setKey(categoryI18nCode);
        i18nTexts.setValue(category.getName());
        return i18nTexts;
    }

    /**
     * 收集FAQ翻译数据列表
     * 
     * @param faqTranslateDTOs FAQ翻译DTO列表
     * @param langId           语言ID
     * @param faqMap           FAQ映射
     * @return FAQ翻译数据列表
     */
    private List<I18nTextsDTO> collectFaqTranslationsData(List<FaqTranslateDTO> faqTranslateDTOs,
            Integer langId,
            Map<Long, FaqRecordDO> faqMap) {
        List<I18nTextsDTO> faqTexts = new ArrayList<>();

        if (faqTranslateDTOs == null || faqTranslateDTOs.isEmpty()) {
            return faqTexts;
        }

        for (FaqTranslateDTO faqDto : faqTranslateDTOs) {
            FaqRecordDO faqRecord = faqMap.get(faqDto.getFaqId());
            if (faqRecord == null) {
                continue;
            }

            List<I18nTextsDTO> singleFaqTexts = collectSingleFaqTranslationData(faqRecord, faqDto, langId);
            faqTexts.addAll(singleFaqTexts);
        }

        return faqTexts;
    }

    /**
     * 收集单个FAQ的翻译数据（问题和答案）
     * 
     * @param faqRecord FAQ记录
     * @param langId    语言ID
     * @return FAQ翻译数据列表
     */
    private List<I18nTextsDTO> collectSingleFaqTranslationData(FaqRecordDO faqRecord,FaqTranslateDTO faqDto, Integer langId) {
        List<I18nTextsDTO> texts = new ArrayList<>();

        if (faqRecord == null || faqRecord.getId() == null) {
            return texts;
        }

        // 收集问题翻译
        if (StringUtils.isNotBlank(faqDto.getQuestion())) {
            String questionI18nCode = getOrGenerateI18nCode(
                    faqRecord.getQuestionI18nCode(),
                    () -> faqI18nService.generateQuestionI18nCode(faqRecord.getId()));

            I18nTextsDTO questionText = I18nTextsDTO.builder()
                    .module("faq")
                    .langId(langId)
                    .origin(1)
                    .key(questionI18nCode)
                    .value(faqDto.getQuestion())
                    .build();
            texts.add(questionText);
        }

        // 收集答案翻译
        if (StringUtils.isNotBlank(faqDto.getAnswer())) {
            String answerI18nCode = getOrGenerateI18nCode(
                    faqRecord.getAnswerI18nCode(),
                    () -> faqI18nService.generateAnswerI18nCode(faqRecord.getId()));

            I18nTextsDTO answerText = I18nTextsDTO.builder()
                    .module("faq")
                    .langId(langId)
                    .origin(1)
                    .key(answerI18nCode)
                    .value(faqDto.getAnswer())
                    .build();
            texts.add(answerText);
        }

        return texts;
    }

    /**
     * 删除FAQ分类
     * 修复权限校验问题，确保分类下所有FAQ都属于同一代理商
     * 
     * @param categoryId   分类ID
     * @param loginAgentId 登录代理商ID
     * @param operatorId   操作员ID
     */
    public void deleteCategory(Long categoryId, boolean isPlatformUser, Long loginAgentId, Long operatorId) {
        // 查询分类是否存在
        FaqCategoryDO category = faqCrudService.findCategoryById(categoryId);
        if (category == null) {
            throw new I18nMessageException(FaqBizEnum.FAQ_CATEGORY_NOT_FOUND);

        }

        if (!isPlatformUser && !category.getAgentId().equals(loginAgentId)) {
            throw BizException.create(ErrorCodeEnum.INTERNAL_SERVER_ERROR, "no permission");
        }

        // 删除分类
        faqCrudService.deleteCategoryById(categoryId, operatorId);

        // 发送消息通知，监听删除国际化文案
        try {
            FaqDeleteContext deleteContext = FaqDeleteContext.createCategoryDelete(
                    categoryId, category.getAgentId(), operatorId);
            faqDeleteProducer.sendMsg(deleteContext);
            log.info("FAQ分类删除通知消息发送成功，categoryId: {}", categoryId);
        } catch (Exception e) {
            log.error("FAQ分类删除通知消息发送失败，categoryId: {}", categoryId, e);
        }
    }
}
