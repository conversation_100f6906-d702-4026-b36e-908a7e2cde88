package so.dian.talos.pojo.converter;

import so.dian.talos.pojo.bo.CooperaterBO;
import so.dian.talos.pojo.entity.CooperaterDO;

public abstract class CooperaterConverter {

    public static CooperaterBO cooperaterDO2CooperaterBO (CooperaterDO cooperaterDO){
        if (null == cooperaterDO) {
            return new CooperaterBO();
        }
        CooperaterBO cooperaterBO = new CooperaterBO();
        cooperaterBO.setId(cooperaterDO.getId());
        cooperaterBO.setCurrencySymbol(cooperaterDO.getCurrencySymbol());
        cooperaterBO.setCurrency(cooperaterDO.getCurrency());
        cooperaterBO.setGmtCreate(cooperaterDO.getGmtCreate());
        cooperaterBO.setGmtUpdate(cooperaterDO.getGmtUpdate());
        return cooperaterBO;
    }
}
