package so.dian.talos.pojo.converter;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import so.dian.talos.client.dto.ShopTipDTO;
import so.dian.talos.client.enums.LanguageEnum;
import so.dian.talos.client.param.ShopTipParam;
import so.dian.talos.common.constant.CommonConstants;

import java.util.HashMap;
import java.util.Map;

public abstract class ShopTipConverter {

    public static ShopTipDTO tipStr2ShopTipDTO(String tip) {
        if (StringUtils.isEmpty(tip)) {
            return null;
        }
        Map<String, String> tipMap = (Map)JSON.parse(tip);
        ShopTipDTO shopTipDTO = new ShopTipDTO();
        shopTipDTO.setCnTip(tipMap.get(LanguageEnum.ZH_CN.getLanguage()));
        shopTipDTO.setEnTip(tipMap.get(LanguageEnum.EN_GB.getLanguage()));
        shopTipDTO.setHansTip(tipMap.get(LanguageEnum.ZH_HANS.getLanguage()));
        shopTipDTO.setDisplay(tipMap.get(CommonConstants.DISPLAY_TIP));
        return shopTipDTO;
    }

    public static String ShopTipDTO2tipStr(ShopTipParam shopTipParam){
        if (StringUtils.isEmpty(shopTipParam.getEnTip()) && StringUtils.isEmpty(shopTipParam.getCnTip())) {
            return null;
        }
        Map<String, String> tipMap = new HashMap<>();
        tipMap.put(LanguageEnum.ZH_CN.getLanguage(), shopTipParam.getCnTip());
        tipMap.put(LanguageEnum.EN_GB.getLanguage(), shopTipParam.getEnTip());
        tipMap.put(LanguageEnum.ZH_HANS.getLanguage(), shopTipParam.getHansTip());
        tipMap.put(CommonConstants.DISPLAY_TIP, shopTipParam.getDisplay());
        return JSON.toJSONString(tipMap);

    }
}
