/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package so.dian.talos.request;

import com.chargebolt.commons.enums.DataCycleEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ShopStatisticRequest.java, v 1.0 2023-11-28 5:24 PM Exp $
 */
@Data
public class ShopStatisticRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202311332172446L;
    /**
     * {@link DataCycleEnum}
     */
    private Integer type;
    /**
     * 时间戳
     */
    private Long time;
    /**
     * 员工ID
     */
    private Long userId;

}