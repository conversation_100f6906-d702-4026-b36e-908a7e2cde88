/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.bill.dao.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import so.dian.mofa3.template.model.BaseModel;

/**
 * shop_daily_bill
 *
 * <AUTHOR>
 * @version $Id: ShopDailyBill.java, v 0.1 2023-10-24 09:53:39 Exp $
 */
@Data
public class ShopDailyBill implements Serializable{
    /** serialVersionUID */
    private static final long serialVersionUID = 175713544626138880L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 商户日账单号
     */
    private String mchBillNo;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 分成金额，单位：分
     */
    private Long amount;

    /**
     * 分成比例
     */
    private Integer percent;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 结算方ID
     */
    private Long settleId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;
    /**
     * 商户类型 1商家 2代理商
     */
    private Integer merchantType;

    private List<Long> mchIds;
}