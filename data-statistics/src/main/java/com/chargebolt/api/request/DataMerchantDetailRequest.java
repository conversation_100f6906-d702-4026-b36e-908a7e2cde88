/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.api.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DataMerchantDetailRequest.java, v 1.0 2024-01-09 9:52 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataMerchantDetailRequest extends DataIndicatorsRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171223887394514339L;

    /**
     * 指标条件
     * 1.所有商家
     * 2.开通分成商家
     */
    private Integer indicators;
    /**
     * 门店ID
     */
    private Long shopId;
    /**
     * 商户负责人ID
     */
    private Long sellerId;
}