/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.dao.statistics.rds.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: BusinessSummaryDTO.java, v 1.0 2024-01-02 3:42 PM Exp $
 */
@Data
public class OrderSummaryDTO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 17120249830829487L;

    private String currency;
    /**
     * 付费订单数
     */
    private Integer paidOrders;

    /**
     * 付费订单金额
     */
    private Long paidAmount;

    /**
     * 付费均单价
     */
    private Double paidUnitPrice;

    /**
     * 退款订单数
     */
    private Integer refundOrders;

    /**
     * 退款订单金额
     */
    private Long refundAmount;


    /**
     * 封顶价订单数
     */
    private Integer cappingOrders;

    /**
     * 封顶价订单金额
     */
    private Long cappingAmount;

    /**
     * 0元订单数
     */
    private Integer zeroOrders;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 员工ID
     */
    private Long sellerId;

}