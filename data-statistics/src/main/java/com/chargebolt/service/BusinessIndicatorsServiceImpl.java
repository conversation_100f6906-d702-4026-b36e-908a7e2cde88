/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.service;

import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.api.request.StatisticBusinessRequest;
import com.chargebolt.api.request.StatisticDeviceRequest;
import com.chargebolt.api.request.StatisticOrderRequest;
import com.chargebolt.api.request.StatisticShopRequest;
import com.chargebolt.api.response.*;
import com.chargebolt.bill.api.response.DivideStatisticsResponse;
import com.chargebolt.bill.dao.model.PercentageDetail;
import com.chargebolt.bill.service.PercentageDetailService;
import com.chargebolt.common.utils.CycleDateUtil;
import com.chargebolt.commons.utils.ExtendDateUtil;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.currency.CurrencyManager;
import com.chargebolt.dao.pg.model.OrderDataStatisticsModel;
import com.chargebolt.dao.pg.model.ShopOrderStatisticsDO;
import com.chargebolt.dao.statistics.rds.MchPercentageStatisticsDAO;
import com.chargebolt.dao.statistics.rds.OrderBoxStatisticsDAO;
import com.chargebolt.dao.statistics.rds.OrderHistorySnapshotDAO;
import com.chargebolt.dao.statistics.rds.ShopMerchantDeviceStatisticsDAO;
import com.chargebolt.dao.statistics.rds.dto.*;
import com.chargebolt.dao.statistics.rds.model.*;
import com.chargebolt.device.dto.DeviceOnlineStateDTO;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.fund.common.enums.BalanceTypeEnum;
import com.chargebolt.service.context.DataIndicatorsContext;
import com.chargebolt.service.context.SearchDateContext;
import com.chargebolt.tenant.service.TenantService;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.demeter.biz.service.DeviceInfoService;
import so.dian.demeter.biz.service.DeviceService;
import so.dian.demeter.dao.rds.DeviceMapper;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.eros.common.util.DeviceUtil;
import so.dian.hermes.client.pojo.enums.OrderStatusEnum;
import so.dian.mofa3.lang.money.CurrencyEnum;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.talos.biz.service.ShopStatisticService;
import so.dian.talos.biz.service.TalosShopService;
import so.dian.talos.common.enums.ShopStatusEnum;
import so.dian.talos.pojo.entity.ShopDO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: BusinessIndicatorsServiceImpl.java, v 1.0 2023-12-26 10:28 AM Exp $
 */
@Service
@Slf4j
public class BusinessIndicatorsServiceImpl implements BusinessIndicatorsService {
    private final PercentageDetailService percentageDetailService;
    private final OrderDataStatisticsService orderDataStatisticsService;
    private final OrderHistorySnapshotService orderHistorySnapshotService;
    private final CurrencyManager currencyManager;
    private final DeviceService deviceService;
    private final DeviceInfoService deviceInfoService;
    private final TalosShopService talosShopService;
    private final MchPercentageStatisticsDAO mchPercentageStatisticsDAO;

    private final OrderBoxStatisticsDAO orderBoxStatisticsDAO;
    private final OrderHistorySnapshotDAO orderHistorySnapshotDAO;
    private final UserManager userManager;
    private final ShopStatisticService shopStatisticService;
    private final ShopMerchantDeviceStatisticsDAO shopMerchantDeviceStatisticsDAO;
    private final DeviceMapper deviceMapper;
    private final TenantService tenantService;
    public BusinessIndicatorsServiceImpl(ObjectProvider<PercentageDetailService> percentageDetailServiceProvider,
                                         ObjectProvider<OrderDataStatisticsService> orderDataStatisticsServiceProvider,
                                         ObjectProvider<OrderHistorySnapshotService> orderHistorySnapshotServiceProvider,
                                         ObjectProvider<CurrencyManager> currencyManagerProvider,
                                         ObjectProvider<DeviceService> deviceServiceProvider,
                                         ObjectProvider<DeviceInfoService> deviceInfoServiceProvider,
                                         ObjectProvider<TalosShopService> talosShopServiceProvider,
                                         ObjectProvider<MchPercentageStatisticsDAO> mchPercentageStatisticsDAOProvider,
                                         ObjectProvider<OrderBoxStatisticsDAO> orderBoxStatisticsDAOProvider,
                                         ObjectProvider<OrderHistorySnapshotDAO> orderHistorySnapshotDAOProvider,
                                         ObjectProvider<UserManager> userManagerProvider,
                                         ObjectProvider<ShopStatisticService> shopStatisticServiceProvider,
                                         ObjectProvider<ShopMerchantDeviceStatisticsDAO> shopMerchantDeviceStatisticsDAOProvider,
                                         ObjectProvider<DeviceMapper> deviceMapperProvider,
                                         ObjectProvider<TenantService> tenantServiceProvider
                                         ) {
        this.percentageDetailService = percentageDetailServiceProvider.getIfUnique();
        this.orderDataStatisticsService = orderDataStatisticsServiceProvider.getIfUnique();
        this.orderHistorySnapshotService = orderHistorySnapshotServiceProvider.getIfUnique();
        this.currencyManager = currencyManagerProvider.getIfUnique();
        this.deviceService = deviceServiceProvider.getIfUnique();
        this.deviceInfoService = deviceInfoServiceProvider.getIfUnique();
        this.talosShopService = talosShopServiceProvider.getIfUnique();
        this.mchPercentageStatisticsDAO = mchPercentageStatisticsDAOProvider.getIfUnique();
        this.orderBoxStatisticsDAO = orderBoxStatisticsDAOProvider.getIfUnique();
        this.orderHistorySnapshotDAO = orderHistorySnapshotDAOProvider.getIfUnique();
        this.userManager= userManagerProvider.getIfUnique();
        this.shopStatisticService = shopStatisticServiceProvider.getIfUnique();
        this.shopMerchantDeviceStatisticsDAO = shopMerchantDeviceStatisticsDAOProvider.getIfUnique();
        this.deviceMapper= deviceMapperProvider.getIfUnique();
        this.tenantService= tenantServiceProvider.getIfUnique();
    }

    @Override
    public BusinessStatisticsResponse constantlyBusiness(final DataIndicatorsContext context, final UserDataAuthorityContext userDataAuthorityContext) {
        BusinessStatisticsResponse response = new BusinessStatisticsResponse();
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        log.info("数据查询时间:{}", JsonUtil.beanToJson(searchDateContext));
        response.setStartTime(new DateBuild().start().toDate().getTime());
        response.setEndTime(new DateBuild().toDate().getTime());
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());
        CurrencyEnum currencyEnum= currencyManager.getCurrency(tenantAgentInfoResponse.getCurrencyCode());
        response.setCurrency(currencyEnum.getCurrencyCode());
        response.setCurrencySymbol(currencyEnum.getCurrencyLabel());
        // 今日，查询实时订单数据
        OrderDataStatisticsModel queryModel = new OrderDataStatisticsModel();
        queryModel.setStartCreateTime(searchDateContext.getStartTime());
        queryModel.setEndCreateTime(searchDateContext.getEndTime());
        queryModel.setOrderStatus(OrderStatusEnum.PAID.getCode());
        // 数据权限范围，自有门店
        queryModel.setShopIds(userDataAuthorityContext.getWrite().getShopIds());
        ShopOrderStatisticsDO shopOrderStatisticsDO = orderDataStatisticsService.getOrderCountAndAmount(queryModel);
        response.setSuccessOrders(shopOrderStatisticsDO.getOrderCount());
        MultiCurrencyMoney successAmount = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        successAmount.setCent(shopOrderStatisticsDO.getPayAmount());
        response.setSuccessAmount(successAmount.getAmount().toString());
        // 环比，同比从快照数据获取
        OrderHistorySnapshot orderHistorySnapshot = new OrderHistorySnapshot();
        // 数据权限范围，自己的员工
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndTime()).formatter(DateBuild.SIMPLE_DATE)));

        //
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        SuccessOrderStatisticsDTO successOrderStatisticsDTOYoY = orderHistorySnapshotService.successOrderStatistics(orderHistorySnapshot);

        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        SuccessOrderStatisticsDTO successOrderStatisticsDTOMoM = orderHistorySnapshotService.successOrderStatistics(orderHistorySnapshot);

        response.setSuccessOrdersYoY(calculateYoY(shopOrderStatisticsDO.getOrderCount(), successOrderStatisticsDTOYoY.getSuccessOrders()));
        response.setSuccessOrdersMoM(calculateMoM(shopOrderStatisticsDO.getOrderCount(), successOrderStatisticsDTOMoM.getSuccessOrders()));

        response.setSuccessAmountYoY(calculateYoY(shopOrderStatisticsDO.getPayAmount(), successOrderStatisticsDTOYoY.getSuccessAmount()));
        response.setSuccessAmountMoM(calculateMoM(shopOrderStatisticsDO.getPayAmount(), successOrderStatisticsDTOMoM.getSuccessAmount()));
        // 分成部分
        response = buildDivideStatisticsResponse(response, context, userDataAuthorityContext, tenantAgentInfoResponse.getCurrencyCode());
        return response;
    }

    @Override
    public BusinessStatisticsResponse historyBusiness(DataIndicatorsContext context,
                                                      UserDataAuthorityContext userDataAuthorityContext) {
        // 自然日，选择到今日
        if(Objects.equals(4, context.getCycle())&&Objects.nonNull(context.getTime())
                && ExtendDateUtil.isToday(context.getTime())){
            context.setCycle(0);
            return constantlyBusiness(context, userDataAuthorityContext);
        }
        BusinessStatisticsResponse response = new BusinessStatisticsResponse();
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        log.info("数据查询时间:{}", JsonUtil.beanToJson(searchDateContext));
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());
        CurrencyEnum currencyEnum= currencyManager.getCurrency(tenantAgentInfoResponse.getCurrencyCode());
        response.setCurrency(currencyEnum.getCurrencyCode());
        response.setCurrencySymbol(currencyEnum.getCurrencyLabel());
        OrderHistorySnapshot orderHistorySnapshot = new OrderHistorySnapshot();
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndTime()).formatter(DateBuild.SIMPLE_DATE)));
        SuccessOrderStatisticsDTO successOrderStatisticsDTO = orderHistorySnapshotService.successOrderStatistics(orderHistorySnapshot);
        response.setSuccessOrders(successOrderStatisticsDTO.getSuccessOrders());
        MultiCurrencyMoney successAmount = new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        successAmount.setCent(successOrderStatisticsDTO.getSuccessAmount());
        response.setSuccessAmount(successAmount.getAmount().toString());

        //
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        SuccessOrderStatisticsDTO successOrderStatisticsDTOYoY = orderHistorySnapshotService.successOrderStatistics(orderHistorySnapshot);

        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        SuccessOrderStatisticsDTO successOrderStatisticsDTOMoM = orderHistorySnapshotService.successOrderStatistics(orderHistorySnapshot);

        response.setSuccessOrdersYoY(calculateYoY(successOrderStatisticsDTO.getSuccessOrders(), successOrderStatisticsDTOYoY.getSuccessOrders()));
        response.setSuccessOrdersMoM(calculateMoM(successOrderStatisticsDTO.getSuccessOrders(), successOrderStatisticsDTOMoM.getSuccessOrders()));

        response.setSuccessAmountYoY(calculateYoY(successOrderStatisticsDTO.getSuccessAmount(), successOrderStatisticsDTOYoY.getSuccessAmount()));
        response.setSuccessAmountMoM(calculateMoM(successOrderStatisticsDTO.getSuccessAmount(), successOrderStatisticsDTOMoM.getSuccessAmount()));
        // 分成部分
        response = buildDivideStatisticsResponse(response, context, userDataAuthorityContext, tenantAgentInfoResponse.getCurrencyCode());
        return response;
    }

    @Override
    public OrderStatisticsResponse constantlyOrder(final DataIndicatorsContext context, final UserDataAuthorityContext userDataAuthorityContext) {
        OrderStatisticsResponse response = new OrderStatisticsResponse();
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());
        CurrencyEnum currencyEnum= currencyManager.getCurrency(tenantAgentInfoResponse.getCurrencyCode());
        response.setCurrency(currencyEnum.getCurrencyCode());
        response.setCurrencySymbol(currencyEnum.getCurrencyLabel());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());

        OrderHistorySnapshot orderHistorySnapshot = new OrderHistorySnapshot();
        // 数据权限范围，用户
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        OrderDataStatisticsModel queryModel = new OrderDataStatisticsModel();
        queryModel.setStartCreateTime(searchDateContext.getStartTime());
        queryModel.setEndCreateTime(searchDateContext.getEndTime());
        // 数据权限范围，门店
        queryModel.setShopIds(userDataAuthorityContext.getWrite().getShopIds());
        // 支付订单
        queryModel.setOrderStatus(OrderStatusEnum.PAID.getCode());
//        ShopOrderStatisticsDO payOrderStatisticsDO = orderDataStatisticsService.getOrderCountAndAmount(queryModel);
        queryModel.setOrderStatus(null);

        // 退款订单
        queryModel.setOrderStatus(OrderStatusEnum.REFUND.getCode());
        ShopOrderStatisticsDO refundOrderStatisticsDO = orderDataStatisticsService.getOrderCountAndAmount(queryModel);
        queryModel.setOrderStatus(null);

        // 付费订单，已支付，支付金额>0
        queryModel.setOrderStatus(OrderStatusEnum.PAID.getCode());
        queryModel.setPayOrderGT0(Boolean.TRUE);
        ShopOrderStatisticsDO paidOrderStatisticsDO = orderDataStatisticsService.getOrderCountAndAmount(queryModel);
        queryModel.setPayOrderGT0(null);

        // 0元订单
        queryModel.setPayOrderGT0(Boolean.FALSE);
        ShopOrderStatisticsDO zeroOrderStatisticsDO = orderDataStatisticsService.getOrderCountAndAmount(queryModel);
        queryModel.setPayOrderGT0(null);

        // 封顶价订单
        queryModel.setCappingOrder(Boolean.TRUE);
        ShopOrderStatisticsDO cappingOrderStatisticsDO = orderDataStatisticsService.getOrderCountAndAmount(queryModel);
        queryModel.setCappingOrder(null);


        // 退款订单数、退款订单金额
        response.setRefundOrders(refundOrderStatisticsDO.getOrderCount());
        MultiCurrencyMoney refundAmount = new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        refundAmount.setCent(refundOrderStatisticsDO.getRefundAmount());
        response.setRefundAmount(refundAmount.getAmount().toString());

        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        GenericOrderStatisticsDTO genericOrderStatisticsDTOYoY = orderHistorySnapshotService.genericOrderStatistics(orderHistorySnapshot);

        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        GenericOrderStatisticsDTO genericOrderStatisticsDTOMoM = orderHistorySnapshotService.genericOrderStatistics(orderHistorySnapshot);
        response.setRefundOrdersYoY(calculateYoY(refundOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOYoY.getRefundCount()));
        response.setRefundOrdersMoM(calculateMoM(refundOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOMoM.getRefundCount()));
        response.setRefundAmountYoY(calculateYoY(refundOrderStatisticsDO.getRefundAmount(), genericOrderStatisticsDTOYoY.getRefundAmount()));
        response.setRefundAmountMoM(calculateMoM(refundOrderStatisticsDO.getRefundAmount(), genericOrderStatisticsDTOMoM.getRefundAmount()));

        // 付费订单数、付费订单金额
        response.setPaidOrderCount(paidOrderStatisticsDO.getOrderCount());
        response.setPaidOrderCountYoY(calculateYoY(paidOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOYoY.getPayCount()));
        response.setPaidOrderCountMoM(calculateMoM(paidOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOMoM.getPayCount()));

        MultiCurrencyMoney paidAmount = new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        paidAmount.setCent(paidOrderStatisticsDO.getPayAmount());
        MultiCurrencyMoney paidUnitPriceMoney= new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        if(paidOrderStatisticsDO.getOrderCount()>0){
            paidUnitPriceMoney= paidAmount.divide(paidOrderStatisticsDO.getOrderCount());
            response.setPaidUnitPrice(paidUnitPriceMoney.getAmount().toString());
        }

        MultiCurrencyMoney paidAmountYoY = new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        paidAmountYoY.setCent(genericOrderStatisticsDTOYoY.getPayAmount());
        MultiCurrencyMoney paidUnitPriceMoneyYoY= new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        if(genericOrderStatisticsDTOYoY.getPayCount()>0){
            paidUnitPriceMoneyYoY= paidAmountYoY.divide(genericOrderStatisticsDTOYoY.getPayCount());
            response.setPaidUnitPriceYoY(calculateYoY(paidUnitPriceMoney.getAmount().doubleValue(), paidUnitPriceMoneyYoY.getAmount().doubleValue()));
        }

        MultiCurrencyMoney paidAmountMoM = new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        paidAmountMoM.setCent(genericOrderStatisticsDTOMoM.getPayAmount());
        MultiCurrencyMoney paidUnitPriceMoneyMoM = new MultiCurrencyMoney(0, currencyEnum.getCurrencyCode());
        if(genericOrderStatisticsDTOMoM.getPayCount()>0){
            paidUnitPriceMoneyMoM= paidAmountMoM.divide(genericOrderStatisticsDTOMoM.getPayCount());
            response.setPaidUnitPriceMoM(calculateMoM(paidUnitPriceMoney.getAmount().doubleValue(), paidUnitPriceMoneyMoM.getAmount().doubleValue()));
        }

        // 0元订单
        response.setZeroOrders(zeroOrderStatisticsDO.getOrderCount());
        response.setZeroOrdersYoY(calculateYoY(zeroOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOYoY.getZeroOrderCount()));
        response.setZeroOrdersMoM(calculateMoM(zeroOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOMoM.getZeroOrderCount()));

//        // 单宝日收益
//        List<DeviceInfoBO> deviceInfoDOList = deviceInfoService.getAll();
//        List<Long> shopIds = new ArrayList<>();
//        if (Boolean.FALSE.equals(context.getAll())) {
//            shopIds = context.getShopIds();
//        }
//        List<Long> deviceTypeIdList = deviceInfoDOList.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList());

//        Integer powerBankCount = deviceService.getDeviceRelationShopCount(searchDateContext.getStartTime(),
//                searchDateContext.getEndTime(), deviceTypeIdList, shopIds);
//        Integer powerBankCountYoY = deviceService.getDeviceRelationShopCount(searchDateContext.getStartYoYTime(),
//                searchDateContext.getEndYoYTime(), deviceTypeIdList, shopIds);
//        Integer powerBankCountMoM = deviceService.getDeviceRelationShopCount(searchDateContext.getStartMoMTime(),
//                searchDateContext.getEndMoMTime(), deviceTypeIdList, shopIds);

//        MultiCurrencyMoney payAmount = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        payAmount.setCent(payOrderStatisticsDO.getPayAmount());
//        MultiCurrencyMoney powerBankMoney = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        if (powerBankCount > 0) {
//            powerBankMoney= payAmount.divide(powerBankCount);
//            response.setPowerBankAmount(powerBankMoney.getAmount().toString());
//        }

//        MultiCurrencyMoney powerBankAmountYoY = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        powerBankAmountYoY.setCent(genericOrderStatisticsDTOYoY.getPayAmount());
//        MultiCurrencyMoney powerBankMoneyYoY = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        if (powerBankCountYoY > 0) {
//            powerBankMoneyYoY= powerBankAmountYoY.divide(powerBankCountYoY);
//            response.setPowerBankAmountYoY(calculateYoY(powerBankMoney.getAmount().doubleValue(), powerBankMoneyYoY.getAmount().doubleValue()));
//        }

//        MultiCurrencyMoney powerBankAmountMoM = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        powerBankAmountMoM.setCent(genericOrderStatisticsDTOMoM.getPayAmount());
//        MultiCurrencyMoney powerBankMoneyMoM = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        if (powerBankCountMoM > 0) {
//            powerBankMoneyMoM= powerBankAmountMoM.divide(powerBankCountMoM);
//            response.setPowerBankAmountMoM(calculateMoM(powerBankMoney.getAmount().doubleValue(), powerBankMoneyMoM.getAmount().doubleValue()));
//        }


        // 封顶价订单、封顶价订单金额
        response.setCappingOrderCount(cappingOrderStatisticsDO.getOrderCount());
        response.setCappingOrderCountYoY(calculateYoY(cappingOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOYoY.getCappingCount()));
        response.setCappingOrderCountMoM(calculateMoM(cappingOrderStatisticsDO.getOrderCount(), genericOrderStatisticsDTOMoM.getCappingCount()));

        MultiCurrencyMoney cappingAmount = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        cappingAmount.setCent(cappingOrderStatisticsDO.getPayAmount());
        response.setCappingOrderAmount(cappingAmount.getAmount().toString());

        MultiCurrencyMoney cappingAmountYoY = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        cappingAmountYoY.setCent(genericOrderStatisticsDTOYoY.getCappingAmount());

        MultiCurrencyMoney cappingAmountMoM = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        cappingAmountMoM.setCent(genericOrderStatisticsDTOMoM.getCappingAmount());
        response.setCappingOrderAmountYoY(calculateYoY(cappingAmount.getAmount().doubleValue(), cappingAmountYoY.getAmount().doubleValue()));
        response.setCappingOrderAmountMoM(calculateMoM(cappingAmount.getAmount().doubleValue(), cappingAmountMoM.getAmount().doubleValue()));

        return response;
    }

    @Override
    public OrderStatisticsResponse historyOrder(final DataIndicatorsContext context, final UserDataAuthorityContext userDataAuthorityContext) {
        OrderStatisticsResponse response = new OrderStatisticsResponse();
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());
        CurrencyEnum currencyEnum= currencyManager.getCurrency(tenantAgentInfoResponse.getCurrencyCode());
        response.setCurrency(currencyEnum.getCurrencyCode());
        response.setCurrencySymbol(currencyEnum.getCurrencyLabel());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());

        OrderHistorySnapshot orderHistorySnapshot = new OrderHistorySnapshot();
        // 数据权限
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndTime()).formatter(DateBuild.SIMPLE_DATE)));
        GenericOrderStatisticsDTO genericOrderStatisticsDTO = orderHistorySnapshotService.genericOrderStatistics(orderHistorySnapshot);
        // 退款订单数、退款订单金额
        response.setRefundOrders(genericOrderStatisticsDTO.getRefundCount());
        MultiCurrencyMoney refundAmount = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        refundAmount.setCent(genericOrderStatisticsDTO.getRefundAmount());
        response.setRefundAmount(refundAmount.getAmount().toString());

        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndYoYTime()).formatter(DateBuild.SIMPLE_DATE)));
        GenericOrderStatisticsDTO genericOrderStatisticsDTOYoY = orderHistorySnapshotService.genericOrderStatistics(orderHistorySnapshot);

        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndMoMTime()).formatter(DateBuild.SIMPLE_DATE)));
        GenericOrderStatisticsDTO genericOrderStatisticsDTOMoM = orderHistorySnapshotService.genericOrderStatistics(orderHistorySnapshot);
        response.setRefundOrdersYoY(calculateYoY(genericOrderStatisticsDTO.getRefundCount(), genericOrderStatisticsDTOYoY.getRefundCount()));
        response.setRefundOrdersMoM(calculateMoM(genericOrderStatisticsDTO.getRefundCount(), genericOrderStatisticsDTOMoM.getRefundCount()));
        response.setRefundAmountYoY(calculateYoY(genericOrderStatisticsDTO.getRefundAmount(), genericOrderStatisticsDTOYoY.getRefundAmount()));
        response.setRefundAmountMoM(calculateMoM(genericOrderStatisticsDTO.getRefundAmount(), genericOrderStatisticsDTOMoM.getRefundAmount()));

        // 付费订单数、付费订单金额
        response.setPaidOrderCount(genericOrderStatisticsDTO.getPayCount());
        response.setPaidOrderCountYoY(calculateYoY(genericOrderStatisticsDTO.getPayCount(), genericOrderStatisticsDTOYoY.getPayCount()));
        response.setPaidOrderCountMoM(calculateMoM(genericOrderStatisticsDTO.getPayCount(), genericOrderStatisticsDTOMoM.getPayCount()));

        MultiCurrencyMoney paidAmount = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        paidAmount.setCent(genericOrderStatisticsDTO.getPayAmount());
        MultiCurrencyMoney paidUnitPriceMoney = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        if(genericOrderStatisticsDTO.getPayCount()>0){
            paidUnitPriceMoney= paidAmount.divide(genericOrderStatisticsDTO.getPayCount());
            response.setPaidUnitPrice(paidUnitPriceMoney.getAmount().toString());
        }

        MultiCurrencyMoney paidAmountYoY = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        if (Objects.isNull(genericOrderStatisticsDTOYoY.getPayCount()) || genericOrderStatisticsDTOYoY.getPayCount().equals(0)) {
            paidAmountYoY.setCent(0);
        } else {
            paidAmountYoY.setCent(genericOrderStatisticsDTOYoY.getPayAmount());
            paidAmountYoY= paidAmountYoY.divide(genericOrderStatisticsDTOYoY.getPayCount());
        }

        MultiCurrencyMoney paidAmountMoM = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        if (Objects.isNull(genericOrderStatisticsDTOMoM.getPayCount()) || genericOrderStatisticsDTOMoM.getPayCount().equals(0)) {
            paidAmountMoM.setCent(0);
        } else {
            paidAmountMoM.setCent(genericOrderStatisticsDTOMoM.getPayAmount());
            paidAmountMoM= paidAmountMoM.divide(genericOrderStatisticsDTOMoM.getPayCount());
        }
        response.setPaidUnitPriceYoY(calculateYoY(paidUnitPriceMoney.getAmount().doubleValue(), paidAmountYoY.getAmount().doubleValue()));
        response.setPaidUnitPriceMoM(calculateMoM(paidUnitPriceMoney.getAmount().doubleValue(), paidAmountMoM.getAmount().doubleValue()));

        // 0元订单
        response.setZeroOrders(genericOrderStatisticsDTO.getZeroOrderCount());
        response.setZeroOrdersYoY(calculateYoY(genericOrderStatisticsDTO.getZeroOrderCount(), genericOrderStatisticsDTOYoY.getZeroOrderCount()));
        response.setZeroOrdersMoM(calculateMoM(genericOrderStatisticsDTO.getZeroOrderCount(), genericOrderStatisticsDTOMoM.getZeroOrderCount()));

        // 单宝日收益
//        List<DeviceInfoBO> deviceInfoDOList = deviceInfoService.getAll();
//        List<Long> shopIds = new ArrayList<>();
//        if (Boolean.FALSE.equals(context.getAll())) {
//            shopIds = context.getShopIds();
//        }
//        List<Long> deviceTypeIdList = deviceInfoDOList.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList());
//
//        Integer powerBankCount = deviceService.getDeviceRelationShopCount(searchDateContext.getStartTime(),
//                searchDateContext.getEndTime(), deviceTypeIdList, shopIds);
//        Integer powerBankCountYoY = deviceService.getDeviceRelationShopCount(searchDateContext.getStartYoYTime(),
//                searchDateContext.getEndYoYTime(), deviceTypeIdList, shopIds);
//        Integer powerBankCountMoM = deviceService.getDeviceRelationShopCount(searchDateContext.getStartMoMTime(),
//                searchDateContext.getEndMoMTime(), deviceTypeIdList, shopIds);

//        MultiCurrencyMoney powerBankAmount = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        if (Objects.isNull(powerBankCount) || powerBankCount.equals(0)) {
//            powerBankAmount.setCent(0);
//        } else {
//            powerBankAmount.setCent(genericOrderStatisticsDTO.getPayAmount());
//            powerBankAmount.divide(powerBankCount);
//        }
//        response.setPowerBankAmount(powerBankAmount.getAmount().toString());
//
//        MultiCurrencyMoney powerBankAmountYoY = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        if (Objects.isNull(powerBankCountYoY) || powerBankCountYoY.equals(0)) {
//            powerBankAmountYoY.setCent(0);
//        } else {
//            powerBankAmountYoY.setCent(genericOrderStatisticsDTOYoY.getPayAmount());
//            powerBankAmountYoY.divide(powerBankCountYoY);
//        }
//        response.setPowerBankAmountYoY(calculateYoY(powerBankAmount.getAmount().doubleValue(), powerBankAmountYoY.getAmount().doubleValue()));
//
//        MultiCurrencyMoney powerBankAmountMoM = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//        if (Objects.isNull(powerBankCountMoM) || powerBankCountMoM.equals(0)) {
//            powerBankAmountMoM.setCent(0);
//        } else {
//            powerBankAmountMoM.setCent(genericOrderStatisticsDTOMoM.getPayAmount());
//            powerBankAmountMoM.divide(powerBankCountMoM);
//        }
//        response.setPowerBankAmountMoM(calculateMoM(powerBankAmount.getAmount().doubleValue(), powerBankAmountMoM.getAmount().doubleValue()));


        // 封顶价订单、封顶价订单金额
        response.setCappingOrderCount(genericOrderStatisticsDTO.getCappingCount());
        response.setCappingOrderCountYoY(calculateYoY(genericOrderStatisticsDTO.getCappingCount(), genericOrderStatisticsDTOYoY.getCappingCount()));
        response.setCappingOrderCountMoM(calculateMoM(genericOrderStatisticsDTO.getCappingCount(), genericOrderStatisticsDTOMoM.getCappingCount()));

        MultiCurrencyMoney cappingAmount = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        cappingAmount.setCent(genericOrderStatisticsDTO.getCappingAmount());
        response.setCappingOrderAmount(cappingAmount.getAmount().toString());

        MultiCurrencyMoney cappingAmountYoY = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        cappingAmountYoY.setCent(genericOrderStatisticsDTOYoY.getCappingAmount());

        MultiCurrencyMoney cappingAmountMoM = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        cappingAmountMoM.setCent(genericOrderStatisticsDTOMoM.getCappingAmount());
        response.setCappingOrderAmountYoY(calculateYoY(cappingAmount.getAmount().doubleValue(), cappingAmountYoY.getAmount().doubleValue()));
        response.setCappingOrderAmountMoM(calculateMoM(cappingAmount.getAmount().doubleValue(), cappingAmountMoM.getAmount().doubleValue()));

        return response;
    }

    @Override
    public ShopMerchantStatisticsResponse constantlyShopMerchant(final DataIndicatorsContext context, final UserDataAuthorityContext userDataAuthorityContext) {
        ShopMerchantStatisticsResponse response= new ShopMerchantStatisticsResponse();
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        ShopDO newInstallShopDO = new ShopDO();
        // 数据权限,用户维度
        newInstallShopDO.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        newInstallShopDO.setStartInstalledTime(searchDateContext.getStartTime());
        newInstallShopDO.setEndInstalledTime(searchDateContext.getEndTime());
        newInstallShopDO.setStatus(ShopStatusEnum.INSTALLED.getStatus());
        Integer newInstalledCount= talosShopService.shopStatisticCount(newInstallShopDO);
        response.setNewInstallShopCount(newInstalledCount);
        newInstallShopDO.setStatus(null);

        newInstallShopDO.setStartInstalledTime(searchDateContext.getStartYoYTime());
        newInstallShopDO.setEndInstalledTime(searchDateContext.getEndYoYTime());
        Integer newInstalledCountYoY= talosShopService.shopStatisticCount(newInstallShopDO);

        newInstallShopDO.setStartInstalledTime(searchDateContext.getStartMoMTime());
        newInstallShopDO.setEndInstalledTime(searchDateContext.getEndMoMTime());
        Integer newInstalledCountMoM= talosShopService.shopStatisticCount(newInstallShopDO);

        response.setNewInstallShopCountYoY(calculateYoY(newInstalledCount, newInstalledCountYoY));
        response.setNewInstallShopCountMoM(calculateMoM(newInstalledCount, newInstalledCountMoM));

        // 门店总数
        ShopDO allShopDO = new ShopDO();
        // 数据权限,用户维度
        allShopDO.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        Integer allShopCount= talosShopService.shopStatisticCount(allShopDO);
        response.setShopCount(allShopCount);

        // 商家总数
        MerchantSearchModel queryMerchantSearchModel= new MerchantSearchModel();
        // 数据权限,用户维度
        queryMerchantSearchModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        Integer mchCount= mchPercentageStatisticsDAO.merchantCount(queryMerchantSearchModel);
        response.setMerchantCount(mchCount);

        // 分成
        MchPercentageAvgSearchModel mchPercentageAvgSearchModel= new MchPercentageAvgSearchModel();
        mchPercentageAvgSearchModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
//        mchPercentageAvgSearchModel.setStartGmtCreate(searchDateContext.getStartTime().getTime());
        mchPercentageAvgSearchModel.setEndGmtCreate(searchDateContext.getEndTime().getTime());
        MchPercentageAvgDTO mchPercentageAvgDTO= mchPercentageStatisticsDAO.mchDivideAvg(mchPercentageAvgSearchModel);
        response.setOpenDivideMerchantCount(mchPercentageAvgDTO.getMchCount());

//        mchPercentageAvgSearchModel.setStartGmtCreate(searchDateContext.getStartYoYTime().getTime());
//        mchPercentageAvgSearchModel.setEndGmtCreate(searchDateContext.getEndYoYTime().getTime());
//        MchPercentageAvgDTO mchPercentageAvgDTOYoY= mchPercentageStatisticsDAO.mchDivideAvg(mchPercentageAvgSearchModel);
//        response.setOpenDivideMerchantCountYoY(calculateYoY(mchPercentageAvgDTO.getMchCount(), mchPercentageAvgDTOYoY.getMchCount()));
//
//        mchPercentageAvgSearchModel.setStartGmtCreate(searchDateContext.getStartMoMTime().getTime());
//        mchPercentageAvgSearchModel.setEndGmtCreate(searchDateContext.getEndMoMTime().getTime());
//        MchPercentageAvgDTO mchPercentageAvgDTOMoM= mchPercentageStatisticsDAO.mchDivideAvg(mchPercentageAvgSearchModel);
//        response.setOpenDivideMerchantCountMoM(calculateMoM(mchPercentageAvgDTO.getMchCount(), mchPercentageAvgDTOMoM.getMchCount()));
//
//        // 分成比例
//        response.setAvgDivideRatio(Double.valueOf(String.format("%.2f", mchPercentageAvgDTO.getAvgRate())));
//        response.setAvgDivideRatioYoY(calculateYoY(mchPercentageAvgDTO.getAvgRate(), mchPercentageAvgDTOYoY.getAvgRate()));
//        response.setAvgDivideRatioMoM(calculateMoM(mchPercentageAvgDTO.getAvgRate(), mchPercentageAvgDTOMoM.getAvgRate()));
        return response;
    }

    @Override
    public ShopMerchantStatisticsResponse historyShopMerchant(final DataIndicatorsContext context, final UserDataAuthorityContext userDataAuthorityContext) {
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        ShopMerchantStatisticsResponse response= this.constantlyShopMerchant(context, userDataAuthorityContext);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        // 门店总数，创建时间
        ShopDO shopDO= new ShopDO();
        // 数据权限,用户维度
        shopDO.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        shopDO.setStartCreateTime(searchDateContext.getStartTime());
        shopDO.setEndCreateTime(searchDateContext.getEndTime());
        Integer shopCount= talosShopService.shopStatisticCount(shopDO);
        response.setShopCount(shopCount);

        shopDO.setStartCreateTime(searchDateContext.getStartYoYTime());
        shopDO.setEndCreateTime(searchDateContext.getEndYoYTime());
        Integer shopCountYoY= talosShopService.shopStatisticCount(shopDO);
        response.setShopCountYoY(calculateYoY(shopCount, shopCountYoY));

        shopDO.setStartCreateTime(searchDateContext.getStartMoMTime());
        shopDO.setEndCreateTime(searchDateContext.getEndMoMTime());
        Integer shopCountMoM= talosShopService.shopStatisticCount(shopDO);
        response.setShopCountMoM(calculateMoM(shopCount, shopCountMoM));

        // 商家总数
        MerchantSearchModel queryMerchantSearchModel= new MerchantSearchModel();
        // 数据权限,用户维度
        queryMerchantSearchModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        queryMerchantSearchModel.setStartCreateTime(searchDateContext.getStartTime());
        queryMerchantSearchModel.setEndCreateTime(searchDateContext.getEndTime());
        Integer mchCount= mchPercentageStatisticsDAO.merchantCount(queryMerchantSearchModel);
        response.setMerchantCount(mchCount);

        queryMerchantSearchModel.setStartCreateTime(searchDateContext.getStartYoYTime());
        queryMerchantSearchModel.setEndCreateTime(searchDateContext.getEndYoYTime());
        Integer mchCountYoY= mchPercentageStatisticsDAO.merchantCount(queryMerchantSearchModel);
        response.setMerchantCountYoY(calculateYoY(mchCount, mchCountYoY));

        queryMerchantSearchModel.setStartCreateTime(searchDateContext.getStartMoMTime());
        queryMerchantSearchModel.setEndCreateTime(searchDateContext.getEndMoMTime());
        Integer mchCountMoM= mchPercentageStatisticsDAO.merchantCount(queryMerchantSearchModel);
        response.setMerchantCountMoM(calculateMoM(mchCount, mchCountMoM));

        return response;
    }

    @Override
    public DeviceStateStatisticsResponse constantlyDeviceState(final DataIndicatorsContext context, final UserDataAuthorityContext userDataAuthorityContext) {
        DeviceStateStatisticsResponse response= new DeviceStateStatisticsResponse();
        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
        List<Long> deviceTypeIds= deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList());
        DeviceOnlineStateDTO deviceOnlineStateDTO= deviceMapper.deviceOnlineStateStatistics(deviceTypeIds);
        response.setDeviceOfflineCount(deviceOnlineStateDTO.getDeviceOfflineCount());
        if(deviceOnlineStateDTO.getDeviceOfflineCount()>0|| deviceOnlineStateDTO.getDeviceOnlineCount()>0){
            response.setDeviceOnlineRatio(divideRatio(deviceOnlineStateDTO.getDeviceOnlineCount(),deviceOnlineStateDTO.getDeviceOfflineCount()+deviceOnlineStateDTO.getDeviceOnlineCount()));
        }
        // 设备总数
        StatisticsDeviceSearchModel allStatisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
        allStatisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
        // 数据权限,用户维度
        allStatisticsDeviceSearchModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        Integer deviceAllCount=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsDeviceSearchModel);
        response.setDeviceAllCount(deviceAllCount);

        // 门店设备
        StatisticsDeviceSearchModel statisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
        statisticsDeviceSearchModel.setDeviceLocation(1);
        statisticsDeviceSearchModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        // 设备类型条件
        statisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
        Integer shopDeviceCount=mchPercentageStatisticsDAO.statisticsDeviceCount(statisticsDeviceSearchModel);
        response.setShopDeviceCount(shopDeviceCount);

        // 充电宝总数
        StatisticsDeviceSearchModel allStatisticsPowerBankSearchModel = new StatisticsDeviceSearchModel();
        allStatisticsPowerBankSearchModel.setDeviceLocation(1);
        // 数据权限,用户维度
        allStatisticsPowerBankSearchModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        // 充电宝
        allStatisticsPowerBankSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));

        Integer powerBankAllCount=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsPowerBankSearchModel);
        response.setPowerBankAllCount(powerBankAllCount);
        return response;
    }

//    @Override
//    public DeviceStatisticsResponse historyDeviceStatistics(final DataIndicatorsContext context, final UserDataAuthorityContext userDataAuthorityContext) {
//        DeviceStatisticsResponse response= new DeviceStatisticsResponse();
//
//        if(Boolean.FALSE.equals(context.getAll())){
//            // 员工
//            return historyDeviceStatisticsForStaff(context, response);
//        }
//        // 根账号
//        return historyDeviceStatisticsForAdmin(context, response);
//    }

    @Override
    public BusinessSummaryListResponse businessSummaryByStaff(final StatisticBusinessRequest request,
                                                              final UserDataAuthorityContext userDataAuthorityContext) {
        BusinessSummaryListResponse response= new BusinessSummaryListResponse();
        response.setPageNum(request.getPageNum());
        response.setPageSize(request.getPageSize());
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());

        // 今日数据，或者自然日选择今日
        if(request.getCycle().equals(0)|| (request.getCycle().equals(4) && ExtendDateUtil.isToday(request.getTime()))){
            return todayBusinessSummaryByStaff(request, response, searchDateContext, userDataAuthorityContext, tenantAgentInfoResponse.getCurrencyCode());
        }
        // 总计
        OrderHistorySnapshot orderHistorySnapshot= new OrderHistorySnapshot();
        // 数据权限，用户维度
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndTime()).formatter(DateBuild.SIMPLE_DATE)));
        BusinessSummaryDTO all= orderHistorySnapshotDAO.businessSummaryByAll(orderHistorySnapshot);
        if(Objects.isNull(all)){
            return null;
        }
        String currency= tenantAgentInfoResponse.getCurrencyCode();
        response.setCurrency(CurrencyEnum.getByCurrencyCode(currency).getCurrencyCode());
        response.setCurrencySymbol(CurrencyEnum.getByCurrencyCode(currency).getCurrencyLabel());
        BusinessSummaryResponse allSummaryResponse= new BusinessSummaryResponse();
        allSummaryResponse.setSuccessOrders(all.getSuccessOrders());
        MultiCurrencyMoney successAmount= new MultiCurrencyMoney(0,currency);
        successAmount.setCent(all.getSuccessAmount());
        allSummaryResponse.setSuccessAmount(successAmount.getAmount().toString());
        allSummaryResponse.setDivideOrders(all.getDivideOrders());
        MultiCurrencyMoney divideExpendAmount= new MultiCurrencyMoney(0,currency);
        divideExpendAmount.setCent(all.getDivideExpendAmount());
        allSummaryResponse.setDivideExpendAmount(divideExpendAmount.getAmount().toString());
        if(all.getSuccessAmount()>0){
            allSummaryResponse.setDivideExpendRatio(divideRatio(all.getDivideExpendAmount(),all.getSuccessAmount()).toString());
        }

        response.setAll(allSummaryResponse);
        // List
        Page<BusinessSummaryDTO> businessSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(businessByStaffOrderBy(request)).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderHistorySnapshotDAO.businessSummaryByStaffSeller(orderHistorySnapshot);
                    }
                });
        response.setTotal((int)businessSummaryDTOPage.getTotal());
        List<Long> allSellerId = businessSummaryDTOPage.getResult().stream()
                .map(BusinessSummaryDTO::getSellerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<OssUserDO> ossUserDOList = userManager.queryByIds(allSellerId);
        List<BusinessSummaryResponse> list= new ArrayList<>();
        if(businessSummaryDTOPage.getResult()!=null&& !businessSummaryDTOPage.getResult().isEmpty()){
            for(BusinessSummaryDTO businessSummaryDTO:businessSummaryDTOPage.getResult()){
                BusinessSummaryResponse businessSummaryResponse= new BusinessSummaryResponse();
                businessSummaryResponse.setBizId(businessSummaryDTO.getSellerId());
                businessSummaryResponse.setTitle(getOssUserName(ossUserDOList, businessSummaryDTO.getSellerId()));
                businessSummaryResponse.setSuccessOrders(businessSummaryDTO.getSuccessOrders());
                MultiCurrencyMoney listSuccessAmount= new MultiCurrencyMoney(0,currency);
                listSuccessAmount.setCent(businessSummaryDTO.getSuccessAmount());
                businessSummaryResponse.setSuccessAmount(listSuccessAmount.getAmount().toString());
                businessSummaryResponse.setDivideOrders(businessSummaryDTO.getDivideOrders());
                MultiCurrencyMoney listDivideExpendAmount= new MultiCurrencyMoney(0,currency);
                listDivideExpendAmount.setCent(businessSummaryDTO.getDivideExpendAmount());
                businessSummaryResponse.setDivideExpendAmount(listDivideExpendAmount.getAmount().toString());
                if(businessSummaryDTO.getSuccessAmount()>0){
                    businessSummaryResponse.setDivideExpendRatio(divideRatio(businessSummaryDTO.getDivideExpendAmount(),businessSummaryDTO.getSuccessAmount()).toString());
                }
                list.add(businessSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

    private  BusinessSummaryListResponse todayBusinessSummaryByStaff( StatisticBusinessRequest request,
                                                                 BusinessSummaryListResponse response,
                                                                 SearchDateContext searchDateContext,
                                                                      UserDataAuthorityContext userDataAuthorityContext,
                                                                      String currencyCode){
        OrderDataStatisticsModel orderDataStatisticsModel= new OrderDataStatisticsModel();
        orderDataStatisticsModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderDataStatisticsModel.setStartCreateTime(searchDateContext.getStartTime());
        orderDataStatisticsModel.setEndCreateTime(searchDateContext.getEndTime());

        TodayBusinessSummaryDTO all= orderBoxStatisticsDAO.todayBusinessSummaryByAll(orderDataStatisticsModel);
        // 分成订单
        PercentageDetail queryPercentageDetail= new PercentageDetail();
        queryPercentageDetail.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        queryPercentageDetail.setStartBizTime(searchDateContext.getStartTime());
        queryPercentageDetail.setEndBizTime(searchDateContext.getEndTime());
        queryPercentageDetail.setBizType(BalanceTypeEnum.INCOME.getId());
        DivideStatisticsResponse divideStatistics= percentageDetailService.divideStatistics(queryPercentageDetail);
        String currency= currencyCode;
        response.setCurrency(CurrencyEnum.getByCurrencyCode(currency).getCurrencyCode());
        response.setCurrencySymbol(CurrencyEnum.getByCurrencyCode(currency).getCurrencyLabel());
        BusinessSummaryResponse allSummaryResponse= new BusinessSummaryResponse();
        allSummaryResponse.setSuccessOrders(all.getSuccessOrders());
        MultiCurrencyMoney successAmount= new MultiCurrencyMoney(0,currency);
        successAmount.setCent(all.getSuccessAmount());
        allSummaryResponse.setSuccessAmount(successAmount.getAmount().toString());
        allSummaryResponse.setDivideOrders(divideStatistics.getDivideCount());
        if(Objects.nonNull(divideStatistics.getDivideAmount())&&divideStatistics.getDivideAmount()>0){
            MultiCurrencyMoney divideExpendAmount= new MultiCurrencyMoney(0,currency);
            divideExpendAmount.setCent(divideStatistics.getDivideAmount());
            allSummaryResponse.setDivideExpendAmount(divideExpendAmount.getAmount().toString());
        }
        if(all.getSuccessAmount()>0){
            allSummaryResponse.setDivideExpendRatio(divideRatio(divideStatistics.getDivideAmount(),all.getSuccessAmount()).toString());
        }
        response.setAll(allSummaryResponse);
        String sort= "desc";
        if(StringUtils.equalsIgnoreCase(request.getSort(),"asc")|| StringUtils.equalsIgnoreCase(request.getSort(),"desc")){
            sort= request.getSort();
        }
        String orderBy="";
        switch (request.getSortField()){
            case 1:
                orderBy= " successOrders "+ sort;
                break;
            case 2:
                orderBy= " successAmount "+sort;
                break;
            default:
                orderBy= " successOrders "+ sort;
        }

        // List
        Page<TodayBusinessSummaryDTO> businessSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(orderBy).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderBoxStatisticsDAO.todayBusinessSummaryByStaff(orderDataStatisticsModel);
                    }
                });
        response.setTotal((int)businessSummaryDTOPage.getTotal());
        List<Long> allSellerId = businessSummaryDTOPage.getResult().stream()
                .map(TodayBusinessSummaryDTO::getSellerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<OssUserDO> ossUserDOList = userManager.queryByIds(allSellerId);
        List<BusinessSummaryResponse> list= new ArrayList<>();
        if(businessSummaryDTOPage.getResult()!=null&& !businessSummaryDTOPage.getResult().isEmpty()){
            for(TodayBusinessSummaryDTO businessSummaryDTO:businessSummaryDTOPage.getResult()){
                // 分成订单
                PercentageDetail queryPercentageOrder= new PercentageDetail();
                queryPercentageOrder.setSellerId(businessSummaryDTO.getSellerId());
                queryPercentageOrder.setStartBizTime(searchDateContext.getStartTime());
                queryPercentageOrder.setEndBizTime(searchDateContext.getEndTime());
                queryPercentageOrder.setBizType(BalanceTypeEnum.INCOME.getId());
                DivideStatisticsResponse divideOrderStatistics= percentageDetailService.divideStatistics(queryPercentageOrder);

                BusinessSummaryResponse businessSummaryResponse= new BusinessSummaryResponse();
                businessSummaryResponse.setBizId(businessSummaryDTO.getSellerId());
                businessSummaryResponse.setTitle(getOssUserName(ossUserDOList, businessSummaryDTO.getSellerId()));
                businessSummaryResponse.setSuccessOrders(businessSummaryDTO.getSuccessOrders());
                MultiCurrencyMoney listSuccessAmount= new MultiCurrencyMoney(0,currency);
                listSuccessAmount.setCent(businessSummaryDTO.getSuccessAmount());
                businessSummaryResponse.setSuccessAmount(listSuccessAmount.getAmount().toString());
                businessSummaryResponse.setDivideOrders(divideOrderStatistics.getDivideCount());
                if(Objects.nonNull(divideOrderStatistics.getDivideAmount())&&divideOrderStatistics.getDivideAmount()>0){
                    MultiCurrencyMoney divideExpendAmount= new MultiCurrencyMoney(0,currency);
                    divideExpendAmount.setCent(divideOrderStatistics.getDivideAmount());
                    allSummaryResponse.setDivideExpendAmount(divideExpendAmount.getAmount().toString());
                }
                if(businessSummaryDTO.getSuccessAmount()>0){
                    allSummaryResponse.setDivideExpendRatio(divideRatio(divideOrderStatistics.getDivideAmount(),businessSummaryDTO.getSuccessAmount()).toString());
                }
                list.add(businessSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

    @Override
    public BusinessSummaryListResponse businessSummaryByShop(final StatisticBusinessRequest request, final UserDataAuthorityContext userDataAuthorityContext) {
        BusinessSummaryListResponse response= new BusinessSummaryListResponse();
        response.setPageNum(request.getPageNum());
        response.setPageSize(request.getPageSize());
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());
        // 今日数据，或者自然日选择今日
        if(request.getCycle().equals(0)|| (request.getCycle().equals(4) && ExtendDateUtil.isToday(request.getTime()))){
            return todayBusinessSummaryByShop(request, response, searchDateContext, userDataAuthorityContext,
                    tenantAgentInfoResponse.getCurrencyCode());
        }

        // 总计
        OrderHistorySnapshot orderHistorySnapshot= new OrderHistorySnapshot();
        // 数据权限，用户维度
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndTime()).formatter(DateBuild.SIMPLE_DATE)));
        BusinessSummaryDTO all= orderHistorySnapshotDAO.businessSummaryByAll(orderHistorySnapshot);
        if(Objects.isNull(all)){
            return null;
        }
        String currency= tenantAgentInfoResponse.getCurrencyCode();
        response.setCurrency(currency);
        response.setCurrencySymbol(CurrencyEnum.getByCurrencyCode(currency).getCurrencyLabel());
        BusinessSummaryResponse allSummaryResponse= new BusinessSummaryResponse();
        allSummaryResponse.setSuccessOrders(all.getSuccessOrders());
        MultiCurrencyMoney successAmount= new MultiCurrencyMoney(0,currency);
        successAmount.setCent(all.getSuccessAmount());
        allSummaryResponse.setSuccessAmount(successAmount.getAmount().toString());
        allSummaryResponse.setDivideOrders(all.getDivideOrders());
        MultiCurrencyMoney divideExpendAmount= new MultiCurrencyMoney(0,currency);
        divideExpendAmount.setCent(all.getDivideExpendAmount());
        allSummaryResponse.setDivideExpendAmount(divideExpendAmount.getAmount().toString());
        if(all.getSuccessAmount()>0){
            allSummaryResponse.setDivideExpendRatio(divideRatio(all.getDivideExpendAmount(),all.getSuccessAmount()).toString());
        }

        response.setAll(allSummaryResponse);
        // List
        Page<BusinessSummaryDTO> businessSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(businessByStaffOrderBy(request)).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderHistorySnapshotDAO.businessSummaryByStaffShop(orderHistorySnapshot);
                    }
                });
        response.setTotal((int)businessSummaryDTOPage.getTotal());
        List<Long> allShopId = businessSummaryDTOPage.getResult().stream()
                .map(BusinessSummaryDTO::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ShopDO> shopDOList= talosShopService.queryListByIds(allShopId);
        List<BusinessSummaryResponse> list= new ArrayList<>();
        if(businessSummaryDTOPage.getResult()!=null&& !businessSummaryDTOPage.getResult().isEmpty()){
            for(BusinessSummaryDTO businessSummaryDTO:businessSummaryDTOPage.getResult()){
                BusinessSummaryResponse businessSummaryResponse= new BusinessSummaryResponse();
                businessSummaryResponse.setTitle(getShopName(shopDOList, businessSummaryDTO.getShopId()));
                businessSummaryResponse.setBizId(businessSummaryDTO.getShopId());
                businessSummaryResponse.setSuccessOrders(businessSummaryDTO.getSuccessOrders());
                MultiCurrencyMoney listSuccessAmount= new MultiCurrencyMoney(0,currency);
                listSuccessAmount.setCent(businessSummaryDTO.getSuccessAmount());
                businessSummaryResponse.setSuccessAmount(listSuccessAmount.getAmount().toString());
                businessSummaryResponse.setDivideOrders(businessSummaryDTO.getDivideOrders());
                MultiCurrencyMoney listDivideExpendAmount= new MultiCurrencyMoney(0,currency);
                listDivideExpendAmount.setCent(businessSummaryDTO.getDivideExpendAmount());
                businessSummaryResponse.setDivideExpendAmount(listDivideExpendAmount.getAmount().toString());
                if(businessSummaryDTO.getSuccessAmount()>0){
                    businessSummaryResponse.setDivideExpendRatio(divideRatio(businessSummaryDTO.getDivideExpendAmount(),businessSummaryDTO.getSuccessAmount()).toString());
                }
                list.add(businessSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

    private  BusinessSummaryListResponse todayBusinessSummaryByShop( StatisticBusinessRequest request,
                                                                      BusinessSummaryListResponse response,
                                                                      SearchDateContext searchDateContext,
                                                                     UserDataAuthorityContext userDataAuthorityContext,
                                                                     String currencyCode){
        OrderDataStatisticsModel orderDataStatisticsModel= new OrderDataStatisticsModel();
        orderDataStatisticsModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderDataStatisticsModel.setStartCreateTime(searchDateContext.getStartTime());
        orderDataStatisticsModel.setEndCreateTime(searchDateContext.getEndTime());

        TodayBusinessSummaryDTO all= orderBoxStatisticsDAO.todayBusinessSummaryByAll(orderDataStatisticsModel);
        PercentageDetail queryPercentageDetail= new PercentageDetail();
        // 数据权限，用户维度
        queryPercentageDetail.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        queryPercentageDetail.setStartBizTime(searchDateContext.getStartTime());
        queryPercentageDetail.setEndBizTime(searchDateContext.getEndTime());
        queryPercentageDetail.setBizType(BalanceTypeEnum.INCOME.getId());
        DivideStatisticsResponse divideStatistics= percentageDetailService.divideStatistics(queryPercentageDetail);
        String currency= currencyCode;
        response.setCurrency(currency);
        response.setCurrencySymbol(CurrencyEnum.getByCurrencyCode(currency).getCurrencyLabel());
        BusinessSummaryResponse allSummaryResponse= new BusinessSummaryResponse();
        allSummaryResponse.setSuccessOrders(all.getSuccessOrders());
        MultiCurrencyMoney successAmount= new MultiCurrencyMoney(0,currency);
        successAmount.setCent(all.getSuccessAmount());
        allSummaryResponse.setSuccessAmount(successAmount.getAmount().toString());
        allSummaryResponse.setDivideOrders(divideStatistics.getDivideCount());
        if(Objects.nonNull(divideStatistics.getDivideAmount())&&divideStatistics.getDivideAmount()>0){
            MultiCurrencyMoney divideExpendAmount= new MultiCurrencyMoney(0,currency);
            divideExpendAmount.setCent(divideStatistics.getDivideAmount());
            allSummaryResponse.setDivideExpendAmount(divideExpendAmount.getAmount().toString());
        }
        if(all.getSuccessAmount()>0){
            allSummaryResponse.setDivideExpendRatio(divideRatio(divideStatistics.getDivideAmount(),all.getSuccessAmount()).toString());
        }
        response.setAll(allSummaryResponse);
        String sort= "desc";
        if(StringUtils.equalsIgnoreCase(request.getSort(),"asc")|| StringUtils.equalsIgnoreCase(request.getSort(),"desc")){
            sort= request.getSort();
        }
        String orderBy="";
        switch (request.getSortField()){
            case 1:
                orderBy= " successOrders "+ sort;
                break;
            case 2:
                orderBy= " successAmount "+sort;
                break;
            default:
                orderBy= " successOrders "+ sort;
        }
        // List
        Page<TodayBusinessSummaryDTO> businessSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(orderBy).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderBoxStatisticsDAO.todayBusinessSummaryByShop(orderDataStatisticsModel);
                    }
                });
        response.setTotal((int)businessSummaryDTOPage.getTotal());
        List<Long> allShopId = businessSummaryDTOPage.getResult().stream()
                .map(TodayBusinessSummaryDTO::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ShopDO> shopDOList= talosShopService.queryListByIds(allShopId);
        List<BusinessSummaryResponse> list= new ArrayList<>();
        if(businessSummaryDTOPage.getResult()!=null&& !businessSummaryDTOPage.getResult().isEmpty()){
            for(TodayBusinessSummaryDTO businessSummaryDTO:businessSummaryDTOPage.getResult()){
                // 分成订单
                PercentageDetail queryPercentageOrder= new PercentageDetail();
                queryPercentageOrder.setShopId(businessSummaryDTO.getShopId());
                queryPercentageOrder.setStartBizTime(searchDateContext.getStartTime());
                queryPercentageOrder.setEndBizTime(searchDateContext.getEndTime());
                queryPercentageOrder.setBizType(BalanceTypeEnum.INCOME.getId());
                DivideStatisticsResponse divideOrderStatistics= percentageDetailService.divideStatistics(queryPercentageOrder);

                BusinessSummaryResponse businessSummaryResponse= new BusinessSummaryResponse();
                businessSummaryResponse.setBizId(businessSummaryDTO.getShopId());
                businessSummaryResponse.setTitle(getShopName(shopDOList, businessSummaryDTO.getShopId()));
                businessSummaryResponse.setSuccessOrders(businessSummaryDTO.getSuccessOrders());
                MultiCurrencyMoney listSuccessAmount= new MultiCurrencyMoney(0,currency);
                listSuccessAmount.setCent(businessSummaryDTO.getSuccessAmount());
                businessSummaryResponse.setSuccessAmount(listSuccessAmount.getAmount().toString());
                businessSummaryResponse.setDivideOrders(divideOrderStatistics.getDivideCount());
                if(Objects.nonNull(divideOrderStatistics.getDivideAmount())&&divideOrderStatistics.getDivideAmount()>0){
                    MultiCurrencyMoney divideExpendAmount= new MultiCurrencyMoney(0,currency);
                    divideExpendAmount.setCent(divideOrderStatistics.getDivideAmount());
                    allSummaryResponse.setDivideExpendAmount(divideExpendAmount.getAmount().toString());
                }
                if(businessSummaryDTO.getSuccessAmount()>0){
                    allSummaryResponse.setDivideExpendRatio(divideRatio(divideOrderStatistics.getDivideAmount(),businessSummaryDTO.getSuccessAmount()).toString());
                }
                list.add(businessSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

    @Override
    public OrderSummaryListResponse orderSummaryByStaff(final StatisticOrderRequest request, final UserDataAuthorityContext userDataAuthorityContext) {
        OrderSummaryListResponse response= new OrderSummaryListResponse();
        response.setPageNum(request.getPageNum());
        response.setPageSize(request.getPageSize());
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());

        // 今日数据，或者自然日选择今日
        if(request.getCycle().equals(0)|| (request.getCycle().equals(4) && ExtendDateUtil.isToday(request.getTime()))){
            return toDayOrderSummaryByStaff(request, response, searchDateContext, userDataAuthorityContext, tenantAgentInfoResponse.getCurrencyCode());
        }
        // 总计
        OrderHistorySnapshot orderHistorySnapshot= new OrderHistorySnapshot();
        // 数据权限，用户维度
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndTime()).formatter(DateBuild.SIMPLE_DATE)));
        OrderSummaryDTO all= orderHistorySnapshotDAO.orderSummaryByAll(orderHistorySnapshot);
        if(Objects.isNull(all)){
            return null;
        }

        // 市场上投入的充电宝
        Integer allMarketPowerBank= getMarketPowerBankBySeller(userDataAuthorityContext.getWrite().getUserIds(), searchDateContext.getStartTime(), searchDateContext.getEndTime());
        String currency= tenantAgentInfoResponse.getCurrencyCode();
        response.setCurrency(CurrencyEnum.getByCurrencyCode(currency).getCurrencyCode());
        response.setCurrencySymbol(CurrencyEnum.getByCurrencyCode(currency).getCurrencyLabel());
        OrderSummaryResponse allSummaryResponse = getSummaryResponse(all, currency, allMarketPowerBank);
        response.setAll(allSummaryResponse);

        // List
        Page<OrderSummaryDTO> orderSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(orderByStaffOrderBy(request)).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderHistorySnapshotDAO.orderSummaryByStaffSeller(orderHistorySnapshot);
                    }
                });
        response.setTotal((int) orderSummaryDTOPage.getTotal());
        List<Long> allSellerId = orderSummaryDTOPage.getResult().stream()
                .map(OrderSummaryDTO::getSellerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<OssUserDO> ossUserDOList = userManager.queryByIds(allSellerId);
        List<OrderSummaryResponse> list= new ArrayList<>();
        if(orderSummaryDTOPage.getResult()!=null&& !orderSummaryDTOPage.getResult().isEmpty()){
            for(OrderSummaryDTO orderSummaryDTO : orderSummaryDTOPage.getResult()){
                OrderSummaryResponse orderSummaryResponse= new OrderSummaryResponse();
                orderSummaryResponse.setBizId(orderSummaryDTO.getSellerId());
                orderSummaryResponse.setTitle(getOssUserName(ossUserDOList, orderSummaryDTO.getSellerId()));
                orderSummaryResponse.setPaidOrders(orderSummaryDTO.getPaidOrders());
                if(Objects.nonNull(orderSummaryDTO.getPaidUnitPrice())){
                    MultiCurrencyMoney listPaidUnitPrice= new MultiCurrencyMoney(0,currency);
                    listPaidUnitPrice.setCent(orderSummaryDTO.getPaidUnitPrice().longValue());
                    orderSummaryResponse.setPaidUnitPrice(listPaidUnitPrice.getAmount().toString());
                }
                orderSummaryResponse.setRefundOrders(orderSummaryDTO.getRefundOrders());
                if(Objects.nonNull(orderSummaryDTO.getRefundAmount())){
                    MultiCurrencyMoney listRefundAmount= new MultiCurrencyMoney(0,currency);
                    listRefundAmount.setCent(orderSummaryDTO.getRefundAmount());
                    orderSummaryResponse.setRefundAmount(listRefundAmount.getAmount().toString());
                }
                orderSummaryResponse.setCappingOrders(orderSummaryDTO.getCappingOrders());
                if(Objects.nonNull(orderSummaryDTO.getCappingAmount())){
                    MultiCurrencyMoney listCappingAmount= new MultiCurrencyMoney(0,currency);
                    listCappingAmount.setCent(orderSummaryDTO.getCappingAmount());
                    orderSummaryResponse.setCappingAmount(listCappingAmount.getAmount().toString());
                }
                orderSummaryResponse.setZeroOrders(orderSummaryDTO.getZeroOrders());
//                Integer sellerMarketPowerBank= getMarketPowerBankBySeller(orderSummaryDTO.getSellerId(), searchDateContext.getStartTime(), searchDateContext.getEndTime());
//                if(Objects.nonNull(sellerMarketPowerBank)&& sellerMarketPowerBank>0){
//                    MultiCurrencyMoney listPaidAmount= new MultiCurrencyMoney(0,currency);
//                    listPaidAmount.setCent(orderSummaryDTO.getPaidAmount());
//                    orderSummaryResponse.setPowerBankAmount(listPaidAmount.divide(sellerMarketPowerBank).getAmount().toString());
//                }
                list.add(orderSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

    private OrderSummaryListResponse toDayOrderSummaryByStaff(StatisticOrderRequest request,
                                                              OrderSummaryListResponse response,
                                                              SearchDateContext searchDateContext,
                                                              UserDataAuthorityContext userDataAuthorityContext,
                                                              String currencyCode){
        // 总计
        OrderDataStatisticsModel orderDataStatisticsModel= new OrderDataStatisticsModel();
        // 数据权限、用户维度
        orderDataStatisticsModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderDataStatisticsModel.setStartCreateTime(searchDateContext.getStartTime());
        orderDataStatisticsModel.setEndCreateTime(searchDateContext.getEndTime());
        OrderSummaryDTO all= orderBoxStatisticsDAO.todayOrderSummaryByAll(orderDataStatisticsModel);
        // 市场上投入的充电宝
        Integer allMarketPowerBank= getMarketPowerBankBySeller(userDataAuthorityContext.getWrite().getUserIds(), searchDateContext.getStartTime(), searchDateContext.getEndTime());
        String currency= currencyCode;
        response.setCurrency(CurrencyEnum.getByCurrencyCode(currency).getCurrencyCode());
        response.setCurrencySymbol(CurrencyEnum.getByCurrencyCode(currency).getCurrencyLabel());
        OrderSummaryResponse allSummaryResponse = getSummaryResponse(all, currency, allMarketPowerBank);
        response.setAll(allSummaryResponse);

        // List
        Page<OrderSummaryDTO> orderSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(orderByStaffOrderBy(request)).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderBoxStatisticsDAO.todayOrderSummaryByStaff(orderDataStatisticsModel);
                    }
                });

        response.setTotal((int) orderSummaryDTOPage.getTotal());
        List<Long> allSellerId = orderSummaryDTOPage.getResult().stream()
                .map(OrderSummaryDTO::getSellerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<OssUserDO> ossUserDOList = userManager.queryByIds(allSellerId);
        List<OrderSummaryResponse> list= new ArrayList<>();
        if(orderSummaryDTOPage.getResult()!=null&& !orderSummaryDTOPage.getResult().isEmpty()){
            for(OrderSummaryDTO orderSummaryDTO : orderSummaryDTOPage.getResult()){
                OrderSummaryResponse orderSummaryResponse= new OrderSummaryResponse();
                orderSummaryResponse.setBizId(orderSummaryDTO.getSellerId());
                orderSummaryResponse.setTitle(getOssUserName(ossUserDOList, orderSummaryDTO.getSellerId()));
                orderSummaryResponse.setPaidOrders(orderSummaryDTO.getPaidOrders());
                MultiCurrencyMoney listPaidUnitPrice= new MultiCurrencyMoney(0,currency);
                listPaidUnitPrice.setCent(orderSummaryDTO.getPaidUnitPrice().longValue());
                orderSummaryResponse.setPaidUnitPrice(listPaidUnitPrice.getAmount().toString());
                orderSummaryResponse.setRefundOrders(orderSummaryDTO.getRefundOrders());
                MultiCurrencyMoney listRefundAmount= new MultiCurrencyMoney(0,currency);
                listRefundAmount.setCent(orderSummaryDTO.getRefundAmount());
                orderSummaryResponse.setRefundAmount(listRefundAmount.getAmount().toString());
                orderSummaryResponse.setCappingOrders(orderSummaryDTO.getCappingOrders());
                MultiCurrencyMoney listCappingAmount= new MultiCurrencyMoney(0,currency);
                listCappingAmount.setCent(orderSummaryDTO.getCappingAmount());
                orderSummaryResponse.setCappingAmount(listCappingAmount.getAmount().toString());
                orderSummaryResponse.setZeroOrders(orderSummaryDTO.getZeroOrders());
//                Integer sellerMarketPowerBank= getMarketPowerBankBySeller(userDataAuthorityContext.getWrite().getUserIds(), searchDateContext.getStartTime(), searchDateContext.getEndTime());
//                if(Objects.nonNull(sellerMarketPowerBank)&& sellerMarketPowerBank>0){
//                    MultiCurrencyMoney listPaidAmount= new MultiCurrencyMoney(0,currency);
//                    listPaidAmount.setCent(orderSummaryDTO.getPaidAmount());
//                    orderSummaryResponse.setPowerBankAmount(listPaidAmount.divide(sellerMarketPowerBank).getAmount().toString());
//                }
                list.add(orderSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }


    private OrderSummaryResponse getSummaryResponse(final OrderSummaryDTO all, final String currency, final Integer allMarketPowerBank) {
        OrderSummaryResponse allSummaryResponse= new OrderSummaryResponse();
        allSummaryResponse.setPaidOrders(all.getPaidOrders());
        if(Objects.nonNull(all.getPaidUnitPrice())){
            MultiCurrencyMoney paidUnitPriceAmount= new MultiCurrencyMoney(0, currency);
            paidUnitPriceAmount.setCent(all.getPaidUnitPrice().longValue());
            allSummaryResponse.setPaidUnitPrice(paidUnitPriceAmount.getAmount().toString());
        }
        allSummaryResponse.setRefundOrders(all.getRefundOrders());
        if(Objects.nonNull(all.getRefundAmount())){
            MultiCurrencyMoney refundAmount= new MultiCurrencyMoney(0, currency);
            refundAmount.setCent(all.getRefundAmount());
            allSummaryResponse.setRefundAmount(refundAmount.getAmount().toString());
        }
        allSummaryResponse.setCappingOrders(all.getCappingOrders());
        if(Objects.nonNull(all.getCappingAmount())){
            MultiCurrencyMoney cappingAmount= new MultiCurrencyMoney(0, currency);
            cappingAmount.setCent(all.getCappingAmount());
            allSummaryResponse.setCappingAmount(cappingAmount.getAmount().toString());
        }
        allSummaryResponse.setZeroOrders(all.getZeroOrders());
//        if(Objects.nonNull(allMarketPowerBank)&& allMarketPowerBank >0){
//            MultiCurrencyMoney paidAmount= new MultiCurrencyMoney(0, currency);
//            paidAmount.setCent(all.getPaidAmount());
//            allSummaryResponse.setPowerBankAmount(paidAmount.divide(allMarketPowerBank).getAmount().toString());
//        }
        return allSummaryResponse;
    }

    @Override
    public OrderSummaryListResponse orderSummaryByShop(final StatisticOrderRequest request, final UserDataAuthorityContext userDataAuthorityContext) {
        OrderSummaryListResponse response= new OrderSummaryListResponse();
        response.setPageNum(request.getPageNum());
        response.setPageSize(request.getPageSize());
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        TenantAgentInfoResponse tenantAgentInfoResponse =tenantService.getTopTenantInfo(userDataAuthorityContext.getAgentId());

        // 今日数据，或者自然日选择今日
        if(request.getCycle().equals(0)|| (request.getCycle().equals(4) && ExtendDateUtil.isToday(request.getTime()))){
            return toDayOrderSummaryByShop(request, response, searchDateContext, userDataAuthorityContext, tenantAgentInfoResponse.getCurrencyCode());
        }
        // 总计
        OrderHistorySnapshot orderHistorySnapshot= new OrderHistorySnapshot();
        // 数据权限，用户维度
        orderHistorySnapshot.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderHistorySnapshot.setStartOrderDate(Long.valueOf(new DateBuild(searchDateContext.getStartTime()).formatter(DateBuild.SIMPLE_DATE)));
        orderHistorySnapshot.setEndOrderDate(Long.valueOf(new DateBuild(searchDateContext.getEndTime()).formatter(DateBuild.SIMPLE_DATE)));
        OrderSummaryDTO all= orderHistorySnapshotDAO.orderSummaryByAll(orderHistorySnapshot);
        if(Objects.isNull(all)){
            return null;
        }

        // 市场上投入的充电宝
//        Integer allMarketPowerBank= getMarketPowerBankByShop(null, request.getSellerId(), searchDateContext.getStartTime(), searchDateContext.getEndTime());

        CurrencyEnum currencyEnum= currencyManager.getCurrency(all.getCurrency());
        response.setCurrency(currencyEnum.getCurrencyCode());
        response.setCurrencySymbol(currencyEnum.getCurrencyLabel());
//        OrderSummaryResponse allSummaryResponse = getOrderSummaryResponse(all, currencyEnum.getCurrencyCode(), allMarketPowerBank);
        OrderSummaryResponse allSummaryResponse = getOrderSummaryResponse(all, currencyEnum.getCurrencyCode(), null);
        response.setAll(allSummaryResponse);

        // List
        Page<OrderSummaryDTO> orderSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(orderByStaffOrderBy(request)).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderHistorySnapshotDAO.orderSummaryByStaffShop(orderHistorySnapshot);
                    }
                });
        response.setTotal((int) orderSummaryDTOPage.getTotal());
        List<Long> allShopId = orderSummaryDTOPage.getResult().stream()
                .map(OrderSummaryDTO::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ShopDO> shopDOList= talosShopService.queryListByIds(allShopId);
        List<OrderSummaryResponse> list= new ArrayList<>();
        if(orderSummaryDTOPage.getResult()!=null&& !orderSummaryDTOPage.getResult().isEmpty()){
            for(OrderSummaryDTO orderSummaryDTO : orderSummaryDTOPage.getResult()){
                OrderSummaryResponse orderSummaryResponse= new OrderSummaryResponse();
                orderSummaryResponse.setBizId(orderSummaryDTO.getShopId());
                orderSummaryResponse.setTitle(getShopName(shopDOList, orderSummaryDTO.getShopId()));
                orderSummaryResponse.setPaidOrders(orderSummaryDTO.getPaidOrders());
                MultiCurrencyMoney listPaidUnitPrice= new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
                listPaidUnitPrice.setCent(orderSummaryDTO.getPaidUnitPrice().longValue());
                orderSummaryResponse.setPaidUnitPrice(listPaidUnitPrice.getAmount().toString());
                orderSummaryResponse.setRefundOrders(orderSummaryDTO.getRefundOrders());
                MultiCurrencyMoney listRefundAmount= new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
                listRefundAmount.setCent(orderSummaryDTO.getRefundAmount());
                orderSummaryResponse.setRefundAmount(listRefundAmount.getAmount().toString());
                orderSummaryResponse.setCappingOrders(orderSummaryDTO.getCappingOrders());
                MultiCurrencyMoney listCappingAmount= new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
                listCappingAmount.setCent(orderSummaryDTO.getCappingAmount());
                orderSummaryResponse.setCappingAmount(listCappingAmount.getAmount().toString());
                orderSummaryResponse.setZeroOrders(orderSummaryDTO.getZeroOrders());

//                Integer shopMarketPowerBank = getMarketPowerBankByShop(orderSummaryDTO.getShopId(), null,
//                        searchDateContext.getStartTime(), searchDateContext.getEndTime());
//                if(Objects.nonNull(shopMarketPowerBank)&& shopMarketPowerBank >0){
//                    MultiCurrencyMoney listPaidAmount= new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
//                    listPaidAmount.setCent(orderSummaryDTO.getPaidAmount());
//                    orderSummaryResponse.setPowerBankAmount(listPaidAmount.divide(shopMarketPowerBank).getAmount().toString());
//                }
                list.add(orderSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

    private OrderSummaryListResponse toDayOrderSummaryByShop(StatisticOrderRequest request,
                                                              OrderSummaryListResponse response,
                                                              SearchDateContext searchDateContext,
                                                             UserDataAuthorityContext userDataAuthorityContext,
                                                             String currencyCode){
        // 总计
        OrderDataStatisticsModel orderDataStatisticsModel= new OrderDataStatisticsModel();
        // 数据权限，用户维度
        orderDataStatisticsModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        orderDataStatisticsModel.setStartCreateTime(searchDateContext.getStartTime());
        orderDataStatisticsModel.setEndCreateTime(searchDateContext.getEndTime());
        OrderSummaryDTO all= orderBoxStatisticsDAO.todayOrderSummaryByAll(orderDataStatisticsModel);
        // 市场上投入的充电宝
        Integer allMarketPowerBank= getMarketPowerBankBySeller(userDataAuthorityContext.getWrite().getUserIds(), searchDateContext.getStartTime(), searchDateContext.getEndTime());
        String currency= currencyCode;
        response.setCurrency(CurrencyEnum.getByCurrencyCode(currency).getCurrencyCode());
        response.setCurrencySymbol(CurrencyEnum.getByCurrencyCode(currency).getCurrencyLabel());
        OrderSummaryResponse allSummaryResponse = getSummaryResponse(all, currency, allMarketPowerBank);
        response.setAll(allSummaryResponse);

        // List
        Page<OrderSummaryDTO> orderSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(orderByStaffOrderBy(request)).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderBoxStatisticsDAO.todayOrderSummaryByShop(orderDataStatisticsModel);
                    }
                });

        response.setTotal((int) orderSummaryDTOPage.getTotal());
        List<Long> allShopId = orderSummaryDTOPage.getResult().stream()
                .map(OrderSummaryDTO::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ShopDO> shopDOList= talosShopService.queryListByIds(allShopId);;
        List<OrderSummaryResponse> list= new ArrayList<>();
        if(orderSummaryDTOPage.getResult()!=null&& !orderSummaryDTOPage.getResult().isEmpty()){
            for(OrderSummaryDTO orderSummaryDTO : orderSummaryDTOPage.getResult()){
                OrderSummaryResponse orderSummaryResponse= new OrderSummaryResponse();
                orderSummaryResponse.setBizId(orderSummaryDTO.getShopId());
                orderSummaryResponse.setTitle(getShopName(shopDOList, orderSummaryDTO.getShopId()));
                orderSummaryResponse.setPaidOrders(orderSummaryDTO.getPaidOrders());
                MultiCurrencyMoney listPaidUnitPrice= new MultiCurrencyMoney(0,currency);
                listPaidUnitPrice.setCent(orderSummaryDTO.getPaidUnitPrice().longValue());
                orderSummaryResponse.setPaidUnitPrice(listPaidUnitPrice.getAmount().toString());
                orderSummaryResponse.setRefundOrders(orderSummaryDTO.getRefundOrders());
                MultiCurrencyMoney listRefundAmount= new MultiCurrencyMoney(0,currency);
                listRefundAmount.setCent(orderSummaryDTO.getRefundAmount());
                orderSummaryResponse.setRefundAmount(listRefundAmount.getAmount().toString());
                orderSummaryResponse.setCappingOrders(orderSummaryDTO.getCappingOrders());
                MultiCurrencyMoney listCappingAmount= new MultiCurrencyMoney(0,currency);
                listCappingAmount.setCent(orderSummaryDTO.getCappingAmount());
                orderSummaryResponse.setCappingAmount(listCappingAmount.getAmount().toString());
                orderSummaryResponse.setZeroOrders(orderSummaryDTO.getZeroOrders());
//                Integer sellerMarketPowerBank= getMarketPowerBankBySeller(userDataAuthorityContext.getWrite().getUserIds(), searchDateContext.getStartTime(), searchDateContext.getEndTime());
//                if(Objects.nonNull(sellerMarketPowerBank)&& sellerMarketPowerBank>0){
//                    MultiCurrencyMoney listPaidAmount= new MultiCurrencyMoney(0,currency);
//                    listPaidAmount.setCent(orderSummaryDTO.getPaidAmount());
//                    orderSummaryResponse.setPowerBankAmount(listPaidAmount.divide(sellerMarketPowerBank).getAmount().toString());
//                }
                list.add(orderSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

    @Override
    public ShopSummaryListResponse shopSummaryByStaff(final StatisticShopRequest request, final UserDataAuthorityContext userDataAuthorityContext) {
        ShopSummaryListResponse response= new ShopSummaryListResponse();
        response.setPageNum(request.getPageNum());
        response.setPageSize(request.getPageSize());
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        // 总计
        ShopSearchModel shopSearchModel= new ShopSearchModel();
        shopSearchModel.setStartCreateTime(searchDateContext.getStartTime());
        shopSearchModel.setEndCreateTime(searchDateContext.getEndTime());
        shopSearchModel.setStartInstallTime(searchDateContext.getStartTime());
        shopSearchModel.setEndInstallTime(searchDateContext.getEndTime());
        shopSearchModel.setStartPercentageStamp(searchDateContext.getStartTime().getTime());
        shopSearchModel.setEndPercentageStamp(searchDateContext.getEndTime().getTime());
        // 数据权限，用户维度
        shopSearchModel.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        ShopSummaryDTO shopAllSummary= shopMerchantDeviceStatisticsDAO.shopAllSummary(shopSearchModel);
        ShopSummaryResponse all= new ShopSummaryResponse();
//        all.setNewInstallShopCount(shopAllSummary.getNewInstallShopCount());
        all.setShopCount(shopAllSummary.getShopCount());
        all.setMerchantCount(shopAllSummary.getMerchantCount());
        all.setOpenDivideMerchantCount(shopAllSummary.getPercentageCount());
//        if(Objects.nonNull(shopAllSummary.getAvgRate())&& shopAllSummary.getAvgRate() > 0){
//            all.setAvgDivideRatio(String.format("%.2f", shopAllSummary.getAvgRate()));
//        }
        response.setAll(all);
        // List
        Page<ShopSummaryDTO> shopSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .setOrderBy(orderByStaffShopBy(request)).doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        shopMerchantDeviceStatisticsDAO.shopSummaryBySeller(shopSearchModel);
                    }
                });
        response.setTotal((int) shopSummaryDTOPage.getTotal());
        List<Long> allSellerId = shopSummaryDTOPage.getResult().stream()
                .map(ShopSummaryDTO::getSellerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<OssUserDO> ossUserDOList = userManager.queryByIds(allSellerId);
        List<ShopSummaryResponse> list= new ArrayList<>();
        if(shopSummaryDTOPage.getResult()!=null&& !shopSummaryDTOPage.getResult().isEmpty()){
            for(ShopSummaryDTO shopSummaryDTO : shopSummaryDTOPage.getResult()){
                ShopSummaryResponse shopSummaryResponse= new ShopSummaryResponse();
                shopSummaryResponse.setBizId(shopSummaryDTO.getSellerId());
                shopSummaryResponse.setTitle(getOssUserName(ossUserDOList, shopSummaryDTO.getSellerId()));
//                shopSummaryResponse.setNewInstallShopCount(shopSummaryDTO.getNewInstallShopCount());
                shopSummaryResponse.setShopCount(shopSummaryDTO.getShopCount());
                shopSummaryResponse.setMerchantCount(shopSummaryDTO.getMerchantCount());
                shopSummaryResponse.setOpenDivideMerchantCount(shopSummaryDTO.getPercentageCount());
//                if(Objects.nonNull(shopSummaryDTO.getAvgRate())&& shopSummaryDTO.getAvgRate() > 0){
//                    shopSummaryResponse.setAvgDivideRatio(String.format("%.2f", shopSummaryDTO.getAvgRate()));
//                }
                list.add(shopSummaryResponse);
            }
        }
        response.setList(list);
        return response;
    }

//    @Override
//    public DeviceSummaryListResponse deviceSummaryByStaff(final StatisticDeviceRequest request) {
//        DeviceSummaryListResponse response= new DeviceSummaryListResponse();
//        response.setPageNum(request.getPageNum());
//        response.setPageSize(request.getPageSize());
//        DataIndicatorsContext context= new DataIndicatorsContext();
//        context.setCycle(request.getCycle());
//        context.setTime(request.getTime());
//        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
//        response.setStartTime(searchDateContext.getStartTime().getTime());
//        response.setEndTime(searchDateContext.getEndTime().getTime());
//
//        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
//        List<Long> boxTypeIds= deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList());
//        List<Long> deviceTypeIds= deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList());
//        DeviceSummarySearchModel model = new DeviceSummarySearchModel();
//        model.setSellerId(request.getSellerId());
//        model.setStartTime(searchDateContext.getStartTime());
//        model.setEndTime(searchDateContext.getEndTime());
//        model.setDeviceTypeIds(deviceTypeIds);
//        model.setBoxTypeIds(boxTypeIds);
//        // 总计
//
//        DeviceSummaryDTO deviceAllSummary= shopMerchantDeviceStatisticsDAO.deviceAllSellerSummary(model);
//        DeviceSummaryResponse all= new DeviceSummaryResponse();
//        all.setDeviceCount(deviceAllSummary.getDeviceCount());
//        all.setOfflineDeviceCount(deviceAllSummary.getOfflineDeviceCount());
//        if(deviceAllSummary.getDeviceCount()>0){
//            all.setOnlineDeviceRatio(divideRatio(deviceAllSummary.getOnlineDeviceCount(), deviceAllSummary.getDeviceCount()).toString());
//        }
//        all.setRecoveryDeviceCount(deviceAllSummary.getRecoveryDeviceCount());
//        all.setShopDeviceCount(deviceAllSummary.getShopDeviceCount());
//        all.setWarehouseDeviceCount(deviceAllSummary.getWarehouseDeviceCount());
//        all.setWarehousePowerBankCount(deviceAllSummary.getWarehousePowerBankCount());
//        all.setMarketPowerBankCount(deviceAllSummary.getMarketPowerBankCount());
//        all.setPowerBankAllCount(deviceAllSummary.getPowerBankAllCount());
//        // 设备产单率
//        DeviceOrderCountModel deviceOrderCountModel= new DeviceOrderCountModel();
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//        deviceOrderCountModel.setSellerId(request.getSellerId());
//        // 产单设备数
//        Integer deviceOrderCount=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(deviceAllSummary.getShopDeviceCount()>0){
//            all.setPowerBankOrderRatio(divideRatio(deviceOrderCount, deviceAllSummary.getShopDeviceCount()).toString());
//        }
//        response.setAll(all);
//
//        // List
//        Page<DeviceSummaryDTO> deviceSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
//                        request.getPageSize(), Boolean.TRUE)
//                .setOrderBy(deviceOrderBy(request)).doSelectPage(new ISelect() {
//                    @Override
//                    public void doSelect() {
//                        shopMerchantDeviceStatisticsDAO.deviceSummaryBySeller(model);
//                    }
//                });
//        response.setTotal((int) deviceSummaryDTOPage.getTotal());
//        List<Long> allSellerId = deviceSummaryDTOPage.getResult().stream()
//                .map(DeviceSummaryDTO::getSellerId)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList());
//        List<OssUserDO> ossUserDOList = userManager.queryByIds(allSellerId);
//
//        List<DeviceSummaryResponse> list= new ArrayList<>();
//        for(DeviceSummaryDTO deviceSummaryDTO:deviceSummaryDTOPage.getResult()){
//            DeviceSummaryResponse deviceSummaryResponse= new DeviceSummaryResponse();
//            deviceSummaryResponse.setBizId(deviceSummaryDTO.getSellerId());
//            deviceSummaryResponse.setTitle(getOssUserName(ossUserDOList, deviceSummaryDTO.getSellerId()));
//            deviceSummaryResponse.setOfflineDeviceCount(deviceSummaryDTO.getOfflineDeviceCount());
////            deviceSummaryResponse.setDeviceCount(deviceSummaryDTO.getDeviceCount());
//            if(deviceSummaryDTO.getShopDeviceCount()>0){
//                deviceSummaryResponse.setOnlineDeviceRatio(divideRatio(deviceSummaryDTO.getOnlineDeviceCount(), deviceSummaryDTO.getShopDeviceCount()).toString());
//            }
//            deviceSummaryResponse.setRecoveryDeviceCount(deviceSummaryDTO.getRecoveryDeviceCount());
//            deviceSummaryResponse.setShopDeviceCount(deviceSummaryDTO.getShopDeviceCount());
////            deviceSummaryResponse.setWarehouseDeviceCount(deviceSummaryDTO.getWarehouseDeviceCount());
//            deviceSummaryResponse.setMarketPowerBankCount(deviceSummaryDTO.getMarketPowerBankCount());
////            deviceSummaryResponse.setWarehousePowerBankCount(deviceSummaryDTO.getWarehousePowerBankCount());
//            deviceSummaryResponse.setPowerBankAllCount(deviceSummaryDTO.getPowerBankAllCount());
//            // 设备产单率
//            List<Long> shopIds= shopStatisticService.getShopIdsBySeller(context.getSellerId());
//            DeviceOrderCountModel sellerDeviceOrderCountModel= new DeviceOrderCountModel();
//            sellerDeviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//            sellerDeviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//            if(CollectionUtils.isEmpty(shopIds)){
//                sellerDeviceOrderCountModel.setShopIds(Arrays.asList(-1L));
//            }else{
//                sellerDeviceOrderCountModel.setShopIds(shopIds);
//            }
//            // 产单设备数
//            Integer sellerDeviceOrderCount=orderBoxStatisticsDAO.deviceOrderCount(sellerDeviceOrderCountModel);
//            if(deviceSummaryDTO.getShopDeviceCount()>0){
//                deviceSummaryResponse.setPowerBankOrderRatio(divideRatio(sellerDeviceOrderCount, deviceSummaryDTO.getShopDeviceCount()).toString());
//            }
//            list.add(deviceSummaryResponse);
//
//        }
//        response.setList(list);
//        return response;
//    }

//    @Override
//    public DeviceSummaryListResponse deviceSummaryByShop(final StatisticDeviceRequest request) {
//        DeviceSummaryListResponse response= new DeviceSummaryListResponse();
//        response.setPageNum(request.getPageNum());
//        response.setPageSize(request.getPageSize());
//        DataIndicatorsContext context= new DataIndicatorsContext();
//        context.setCycle(request.getCycle());
//        context.setTime(request.getTime());
//        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
//        response.setStartTime(searchDateContext.getStartTime().getTime());
//        response.setEndTime(searchDateContext.getEndTime().getTime());
//
//        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
//        List<Long> boxTypeIds= deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList());
//        List<Long> deviceTypeIds= deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList());
//        DeviceSummarySearchModel model = new DeviceSummarySearchModel();
//        model.setSellerId(request.getSellerId());
//        model.setStartTime(searchDateContext.getStartTime());
//        model.setEndTime(searchDateContext.getEndTime());
//        model.setDeviceTypeIds(deviceTypeIds);
//        model.setBoxTypeIds(boxTypeIds);
//        // 总计
//        DeviceSummaryDTO deviceAllSummary= shopMerchantDeviceStatisticsDAO.deviceAllShopSummary(model);
//        DeviceSummaryResponse all= new DeviceSummaryResponse();
//        all.setDeviceCount(deviceAllSummary.getDeviceCount());
//        all.setOfflineDeviceCount(deviceAllSummary.getOfflineDeviceCount());
//        if(deviceAllSummary.getDeviceCount()>0){
//            all.setOnlineDeviceRatio(divideRatio(deviceAllSummary.getOnlineDeviceCount(), deviceAllSummary.getDeviceCount()).toString());
//        }
//        all.setRecoveryDeviceCount(deviceAllSummary.getRecoveryDeviceCount());
//        all.setShopDeviceCount(deviceAllSummary.getShopDeviceCount());
//        all.setWarehouseDeviceCount(deviceAllSummary.getWarehouseDeviceCount());
//        all.setWarehousePowerBankCount(deviceAllSummary.getWarehousePowerBankCount());
//        all.setMarketPowerBankCount(deviceAllSummary.getMarketPowerBankCount());
//        all.setPowerBankAllCount(deviceAllSummary.getPowerBankAllCount());
//        // 设备产单率
//        DeviceOrderCountModel deviceOrderCountModel= new DeviceOrderCountModel();
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//        deviceOrderCountModel.setSellerId(request.getSellerId());
//        // 产单设备数
//        Integer deviceOrderCount=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(deviceAllSummary.getShopDeviceCount()>0){
//            all.setPowerBankOrderRatio(divideRatio(deviceOrderCount, deviceAllSummary.getShopDeviceCount()).toString());
//        }
//        response.setAll(all);
//
//        // List
//        Page<DeviceSummaryDTO> deviceSummaryDTOPage = PageHelper.startPage(request.getPageNum(),
//                        request.getPageSize(), Boolean.TRUE)
//                .setOrderBy(deviceOrderBy(request)).doSelectPage(new ISelect() {
//                    @Override
//                    public void doSelect() {
//                        shopMerchantDeviceStatisticsDAO.deviceSummaryByShop(model);
//                    }
//                });
//        response.setTotal((int) deviceSummaryDTOPage.getTotal());
//
//        List<DeviceSummaryResponse> list= new ArrayList<>();
//        for(DeviceSummaryDTO deviceSummaryDTO:deviceSummaryDTOPage.getResult()){
//            DeviceSummaryResponse deviceSummaryResponse= new DeviceSummaryResponse();
//            deviceSummaryResponse.setBizId(deviceSummaryDTO.getShopId());
//            deviceSummaryResponse.setTitle(deviceSummaryDTO.getTitle());
//            deviceSummaryResponse.setOfflineDeviceCount(deviceSummaryDTO.getOfflineDeviceCount());
////            deviceSummaryResponse.setDeviceCount(deviceSummaryDTO.getDeviceCount());
//            if(deviceSummaryDTO.getShopDeviceCount()>0){
//                deviceSummaryResponse.setOnlineDeviceRatio(divideRatio(deviceSummaryDTO.getOnlineDeviceCount(), deviceSummaryDTO.getShopDeviceCount()).toString());
//            }
//            deviceSummaryResponse.setRecoveryDeviceCount(deviceSummaryDTO.getRecoveryDeviceCount());
//            deviceSummaryResponse.setShopDeviceCount(deviceSummaryDTO.getShopDeviceCount());
////            deviceSummaryResponse.setWarehouseDeviceCount(deviceSummaryDTO.getWarehouseDeviceCount());
//            deviceSummaryResponse.setMarketPowerBankCount(deviceSummaryDTO.getMarketPowerBankCount());
////            deviceSummaryResponse.setWarehousePowerBankCount(deviceSummaryDTO.getWarehousePowerBankCount());
//            deviceSummaryResponse.setPowerBankAllCount(deviceSummaryDTO.getPowerBankAllCount());
//            // 设备产单率
//            DeviceOrderCountModel sellerDeviceOrderCountModel= new DeviceOrderCountModel();
//            sellerDeviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//            sellerDeviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//            sellerDeviceOrderCountModel.setShopIds(Arrays.asList(deviceSummaryDTO.getShopId()));
//            // 产单设备数
//            Integer sellerDeviceOrderCount=orderBoxStatisticsDAO.deviceOrderCount(sellerDeviceOrderCountModel);
//            if(deviceSummaryDTO.getShopDeviceCount()>0){
//                deviceSummaryResponse.setPowerBankOrderRatio(divideRatio(sellerDeviceOrderCount, deviceSummaryDTO.getShopDeviceCount()).toString());
//            }
//            list.add(deviceSummaryResponse);
//
//        }
//        response.setList(list);
//        return response;
//    }

    private OrderSummaryResponse getOrderSummaryResponse(final OrderSummaryDTO all, final String currency, final Integer allMarketPowerBank) {
        OrderSummaryResponse allSummaryResponse= new OrderSummaryResponse();
        allSummaryResponse.setPaidOrders(all.getPaidOrders());
        MultiCurrencyMoney paidUnitPriceAmount= new MultiCurrencyMoney(0, currency);
        paidUnitPriceAmount.setCent(all.getPaidUnitPrice().longValue());
        allSummaryResponse.setPaidUnitPrice(paidUnitPriceAmount.getAmount().toString());
        allSummaryResponse.setRefundOrders(all.getRefundOrders());
        MultiCurrencyMoney refundAmount= new MultiCurrencyMoney(0, currency);
        refundAmount.setCent(all.getRefundAmount());
        allSummaryResponse.setRefundAmount(refundAmount.getAmount().toString());
        allSummaryResponse.setCappingOrders(all.getCappingOrders());
        MultiCurrencyMoney cappingAmount= new MultiCurrencyMoney(0, currency);
        cappingAmount.setCent(all.getCappingAmount());
        allSummaryResponse.setCappingAmount(cappingAmount.getAmount().toString());
        allSummaryResponse.setZeroOrders(all.getZeroOrders());
//        if(Objects.nonNull(allMarketPowerBank)&& allMarketPowerBank >0){
//            MultiCurrencyMoney paidAmount= new MultiCurrencyMoney(0, currency);
//            paidAmount.setCent(all.getPaidAmount());
//            allSummaryResponse.setPowerBankAmount(paidAmount.divide(allMarketPowerBank).getAmount().toString());
//        }
        return allSummaryResponse;
    }

    /**
     * 获取门店名称
     *
     * @param shopDOList
     * @param shopId
     * @return
     */
    private String getShopName(List<ShopDO> shopDOList, Long shopId){
        for(ShopDO shopDO:shopDOList){
            if(shopDO.getId().equals(shopId)){
                return shopDO.getName();
            }
        }
        return "";
    }
    /**
     * 获取user名称
     *
     * @param ossUserDOList
     * @param userId
     * @return
     */
    private String getOssUserName(List<OssUserDO> ossUserDOList, Long userId){
        for(OssUserDO ossUserDO:ossUserDOList){
            if(ossUserDO.getId().equals(userId)){
                return ossUserDO.getName();
            }
        }
        return "";
    }

    /**
     * 获取市场上充电宝数量
     *
     * 以安装时间统计
     *
     * @param shopId        查询指定门店的数据
     * @param sellerId      查询指定员工的数据
     * @param startTime
     * @param endTime
     * @return
     */
    private Integer getMarketPowerBankByShop(Long shopId, Long sellerId, Date startTime, Date endTime){
        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
        // 市场充电宝
        StatisticsDeviceSearchModel marketStatisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
        marketStatisticsDeviceSearchModel.setDeviceLocation(1);
        marketStatisticsDeviceSearchModel.setShopId(shopId);
        marketStatisticsDeviceSearchModel.setSellerId(sellerId);
        // 充电宝
        marketStatisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
        marketStatisticsDeviceSearchModel.setStartInstalledTime(startTime);
        marketStatisticsDeviceSearchModel.setEndInstalledTime(endTime);
        return mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsDeviceSearchModel);
    }

    /**
     * 获取市场上充电宝数量
     *
     * 以安装时间统计
     *
     * @param sellerIds
     * @param startTime
     * @param endTime
     * @return
     */
    private Integer getMarketPowerBankBySeller(List<Long> sellerIds, Date startTime, Date endTime){
        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
        // 市场充电宝
        StatisticsDeviceSearchModel marketStatisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
        marketStatisticsDeviceSearchModel.setDeviceLocation(1);
        // 数据权限，用户维度
        marketStatisticsDeviceSearchModel.setSellerIds(sellerIds);
        // 充电宝
        marketStatisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
        marketStatisticsDeviceSearchModel.setStartInstalledTime(startTime);
        marketStatisticsDeviceSearchModel.setEndInstalledTime(endTime);
        return mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsDeviceSearchModel);
    }

    /**
     * 订单数据排序
     /**
     * 排序字段：
     * 1.离线设备数
     * 2.设备总数
     * 3.设备在线率
     * 4.门店设备数
     * 5.投放充电宝数
     * 6.充电宝总数
     * @param request
     * @return
     */
    private String deviceOrderBy(StatisticDeviceRequest request){
        String sort= " desc";
        if(StringUtils.equalsIgnoreCase(request.getSort(),"asc")|| StringUtils.equalsIgnoreCase(request.getSort(),"desc")){
            sort= request.getSort();
        }
        String orderby= "";
        switch (request.getSortField()){
            case 1:
                orderby= " t.offlineDeviceCount "+ sort;
                break;
//            case 2:
//                orderby= " t.deviceCount "+sort;
//                break;
            case 3:
                orderby= " (t.onlineDeviceCount/t.shopDeviceCount) " + sort;
                break;
            case 4:
                orderby= " t.shopDeviceCount "+sort;
                break;
            case 5:
                orderby= " t.marketPowerBankCount "+sort;
                break;
//            case 6:
//                orderby= " t.powerBankAllCount "+sort;
//                break;
            default:
                orderby= " t.offlineDeviceCount "+ sort;
        }
        return orderby;
    }
    private String orderByStaffShopBy(StatisticShopRequest request){
        String sort= " desc";
        if(StringUtils.equalsIgnoreCase(request.getSort(),"asc")|| StringUtils.equalsIgnoreCase(request.getSort(),"desc")){
            sort= request.getSort();
        }
        String orderby= "";
        switch (request.getSortField()){
//            case 1:
//                orderby= " newInstallShopCount "+ sort;
//                break;
            case 2:
                orderby= " shopCount "+sort;
                break;
            default:
                orderby= " shopCount "+ sort;
        }
        return orderby;
    }
    /**
     * 订单数据排序
     *
     * @param request
     * @return
     */
    private String orderByStaffOrderBy(StatisticOrderRequest request){
        String sort= " desc";
        if(StringUtils.equalsIgnoreCase(request.getSort(),"asc")|| StringUtils.equalsIgnoreCase(request.getSort(),"desc")){
            sort= request.getSort();
        }
        String orderby= "";
        switch (request.getSortField()){
            case 1:
                orderby= " paidOrders "+ sort;
                break;
            case 2:
//                orderby= " COALESCE(sum(pay_amount),0)/COALESCE(sum(pay_count),0) "+sort;
                orderby= " paidUnitPrice "+sort;
                break;
            case 3:
                orderby= " refundOrders " + sort;
                break;
            case 4:
                orderby= " refundAmount "+sort;
                break;
            case 5:
                orderby= " cappingOrders "+sort;
                break;
            case 6:
                orderby= " cappingAmount "+sort;
                break;
            case 7:
                orderby= " zeroOrders "+sort;
                break;
            default:
                orderby= " paidOrders "+ sort;
        }
        return orderby;
    }
    /**
     * 经营数据排序
     *
     * @param request
     * @return
     */
    private String businessByStaffOrderBy(StatisticBusinessRequest request){
        String sort= " desc";
        if(StringUtils.equalsIgnoreCase(request.getSort(),"asc")|| StringUtils.equalsIgnoreCase(request.getSort(),"desc")){
            sort= request.getSort();
        }
        String orderby= "";
        switch (request.getSortField()){
            case 1:
                orderby= " successOrders "+ sort;
                break;
            case 2:
                orderby= " successAmount "+sort;
                break;
            case 3:
                orderby= " divideOrders " + sort;
                break;
            case 4:
                orderby= " divideExpendAmount "+sort;
                break;
            case 5:
                orderby= " COALESCE(sum(divide_order_count),0)/COALESCE(sum(success_order_count),0) "+sort;
                break;
            default:
                orderby= " successOrders "+ sort;
            }
            return orderby;
    }

    /**
     * 员工维度数据统计
     *
     * 一定是查询员工下的数据
     *
     * @param context
     * @param response
     * @return
     */
//    private DeviceStatisticsResponse historyDeviceStatisticsForStaff(final DataIndicatorsContext context,
//                                                                     final DeviceStatisticsResponse response,
//                                                                     final UserDataAuthorityContext userDataAuthorityContext){
//        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
//        response.setStartTime(searchDateContext.getStartTime().getTime());
//        response.setEndTime(searchDateContext.getEndTime().getTime());
//        // 回收设备
//        RecoveryDeviceSearchModel recoveryDeviceSearchModel= new RecoveryDeviceSearchModel();
//        recoveryDeviceSearchModel.setStartTime(searchDateContext.getStartTime());
//        recoveryDeviceSearchModel.setEndTime(searchDateContext.getEndTime());
//        recoveryDeviceSearchModel.setSellerId(context.getSellerId());
//        Integer recoveryCount= mchPercentageStatisticsDAO.recoveryDeviceCount(recoveryDeviceSearchModel);
//        response.setRecoveryDeviceCount(recoveryCount);
//
//        recoveryDeviceSearchModel.setStartTime(searchDateContext.getStartYoYTime());
//        recoveryDeviceSearchModel.setEndTime(searchDateContext.getEndYoYTime());
//        Integer recoveryCountYoY= mchPercentageStatisticsDAO.recoveryDeviceCount(recoveryDeviceSearchModel);
//        response.setRecoveryDeviceCountYoY(calculateYoY(recoveryCount, recoveryCountYoY));
//
//        recoveryDeviceSearchModel.setStartTime(searchDateContext.getStartMoMTime());
//        recoveryDeviceSearchModel.setEndTime(searchDateContext.getEndMoMTime());
//        Integer recoveryCountMoM= mchPercentageStatisticsDAO.recoveryDeviceCount(recoveryDeviceSearchModel);
//        response.setRecoveryDeviceCountMoM(calculateMoM(recoveryCount, recoveryCountMoM));
//
//        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
//
//        // 门店设备
//        StatisticsDeviceSearchModel statisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
//        statisticsDeviceSearchModel.setDeviceLocation(1);
//        statisticsDeviceSearchModel.setSellerId(context.getSellerId());
//        statisticsDeviceSearchModel.setStartInstalledTime(searchDateContext.getStartTime());
//        statisticsDeviceSearchModel.setEndInstalledTime(searchDateContext.getEndTime());
//        // 设备类型条件
//        statisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//        Integer shopDeviceCount=mchPercentageStatisticsDAO.statisticsDeviceCount(statisticsDeviceSearchModel);
//        response.setShopDeviceCount(shopDeviceCount);
//
//        statisticsDeviceSearchModel.setStartInstalledTime(searchDateContext.getStartMoMTime());
//        statisticsDeviceSearchModel.setEndInstalledTime(searchDateContext.getEndMoMTime());
//        Integer shopDeviceCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(statisticsDeviceSearchModel);
//        response.setShopDeviceCountMoM(calculateMoM(shopDeviceCount, shopDeviceCountMoM));
//
//        statisticsDeviceSearchModel.setStartInstalledTime(searchDateContext.getStartYoYTime());
//        statisticsDeviceSearchModel.setEndInstalledTime(searchDateContext.getEndYoYTime());
//        Integer shopDeviceCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(statisticsDeviceSearchModel);
//        response.setShopDeviceCountYoY(calculateYoY(shopDeviceCount, shopDeviceCountYoY));
//
//        // 市场充电宝
//        StatisticsDeviceSearchModel marketStatisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
//        marketStatisticsDeviceSearchModel.setDeviceLocation(1);
//        marketStatisticsDeviceSearchModel.setSellerId(context.getSellerId());
//        // 充电宝
//        marketStatisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//        marketStatisticsDeviceSearchModel.setStartInstalledTime(searchDateContext.getStartTime());
//        marketStatisticsDeviceSearchModel.setEndInstalledTime(searchDateContext.getEndTime());
//        Integer marketPowerBankCount=mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsDeviceSearchModel);
//        response.setMarketPowerBankCount(marketPowerBankCount);
//
//        marketStatisticsDeviceSearchModel.setStartInstalledTime(searchDateContext.getStartMoMTime());
//        marketStatisticsDeviceSearchModel.setEndInstalledTime(searchDateContext.getEndMoMTime());
//        Integer marketPowerBankCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsDeviceSearchModel);
//        response.setMarketPowerBankCountMoM(calculateMoM(marketPowerBankCount, marketPowerBankCountMoM));
//
//        marketStatisticsDeviceSearchModel.setStartInstalledTime(searchDateContext.getStartYoYTime());
//        marketStatisticsDeviceSearchModel.setEndInstalledTime(searchDateContext.getEndYoYTime());
//        Integer marketPowerBankCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsDeviceSearchModel);
//        response.setMarketPowerBankCountYoY(calculateYoY(marketPowerBankCount, marketPowerBankCountYoY));
//
//        // 设备产单率
//
//        List<Long> shopIds= shopStatisticService.getShopIdsBySeller(context.getSellerId());
//        DeviceOrderCountModel deviceOrderCountModel= new DeviceOrderCountModel();
//        if(CollectionUtils.isEmpty(shopIds)){
//            deviceOrderCountModel.setShopIds(Arrays.asList(-1L));
//        }else{
//            deviceOrderCountModel.setShopIds(shopIds);
//        }
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//        // 产单设备数
//        Integer deviceOrderCount=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(shopDeviceCount>0){
//            response.setPowerBankOrderRatio(divideRatio(deviceOrderCount,shopDeviceCount));
//        }
//
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartYoYTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndYoYTime());
//        Integer deviceOrderCountYoY=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(shopDeviceCountYoY>0){
//            response.setPowerBankOrderRatioYoY(calculateYoY(divideRatio(deviceOrderCount,shopDeviceCount),
//                    divideRatio(deviceOrderCountYoY,shopDeviceCountYoY)));
//        }
//
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//        Integer deviceOrderCountMoM=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(shopDeviceCountMoM>0){
//            response.setPowerBankOrderRatioMoM(calculateMoM(divideRatio(deviceOrderCount,shopDeviceCount),
//                    divideRatio(deviceOrderCountMoM,shopDeviceCountMoM)));
//        }
//
//        return response;
//    }

    /**
     * 根账户维度数据统计
     * 所有数据
     *
     * @param context
     * @param response
     * @return
     */
//    private DeviceStatisticsResponse historyDeviceStatisticsForAdmin(final DataIndicatorsContext context, DeviceStatisticsResponse response){
//        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
//        response.setStartTime(searchDateContext.getStartTime().getTime());
//        response.setEndTime(searchDateContext.getEndTime().getTime());
//        // 回收设备
//        RecoveryDeviceSearchModel recoveryDeviceSearchModel= new RecoveryDeviceSearchModel();
//        recoveryDeviceSearchModel.setStartTime(searchDateContext.getStartTime());
//        recoveryDeviceSearchModel.setEndTime(searchDateContext.getEndTime());
//        Integer recoveryCount= mchPercentageStatisticsDAO.recoveryDeviceCount(recoveryDeviceSearchModel);
//        response.setRecoveryDeviceCount(recoveryCount);
//
//        recoveryDeviceSearchModel.setStartTime(searchDateContext.getStartYoYTime());
//        recoveryDeviceSearchModel.setEndTime(searchDateContext.getEndYoYTime());
//        Integer recoveryCountYoY= mchPercentageStatisticsDAO.recoveryDeviceCount(recoveryDeviceSearchModel);
//        response.setRecoveryDeviceCountYoY(calculateYoY(recoveryCount, recoveryCountYoY));
//
//        recoveryDeviceSearchModel.setStartTime(searchDateContext.getStartMoMTime());
//        recoveryDeviceSearchModel.setEndTime(searchDateContext.getEndMoMTime());
//        Integer recoveryCountMoM= mchPercentageStatisticsDAO.recoveryDeviceCount(recoveryDeviceSearchModel);
//        response.setRecoveryDeviceCountMoM(calculateMoM(recoveryCount, recoveryCountMoM));
//
//        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
//        // 设备总数
//        StatisticsDeviceSearchModel allStatisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
//        allStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartTime());
//        allStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndTime());
//        allStatisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//        Integer deviceAllCount=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsDeviceSearchModel);
//        response.setDeviceAllCount(deviceAllCount);
//
//        allStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartYoYTime());
//        allStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndYoYTime());
//        Integer deviceAllCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsDeviceSearchModel);
//        response.setDeviceAllCountYoY(calculateYoY(deviceAllCount, deviceAllCountYoY));
//
//        allStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartMoMTime());
//        allStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndMoMTime());
//        Integer deviceAllCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsDeviceSearchModel);
//        response.setDeviceAllCountMoM(calculateMoM(deviceAllCount, deviceAllCountMoM));
//
//        // 仓库设备
//        StatisticsDeviceSearchModel warehouseStatisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
//        warehouseStatisticsDeviceSearchModel.setDeviceLocation(2);
//        warehouseStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartTime());
//        warehouseStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndTime());
//        warehouseStatisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//        Integer warehouseDeviceCount=mchPercentageStatisticsDAO.statisticsDeviceCount(warehouseStatisticsDeviceSearchModel);
//        response.setWarehouseDeviceCount(warehouseDeviceCount);
//
//        warehouseStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartYoYTime());
//        warehouseStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndYoYTime());
//        Integer warehouseDeviceCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(warehouseStatisticsDeviceSearchModel);
//        response.setWarehouseDeviceCountYoY(calculateYoY(warehouseDeviceCount, warehouseDeviceCountYoY));
//
//        warehouseStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartMoMTime());
//        warehouseStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndMoMTime());
//        Integer warehouseDeviceCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(warehouseStatisticsDeviceSearchModel);
//        response.setWarehouseDeviceCountMoM(calculateMoM(warehouseDeviceCount, warehouseDeviceCountMoM));
//
//        // 门店设备
//        StatisticsDeviceSearchModel shopStatisticsDeviceSearchModel= new StatisticsDeviceSearchModel();
//        shopStatisticsDeviceSearchModel.setDeviceLocation(1);
//        shopStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartTime());
//        allStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndTime());
//        shopStatisticsDeviceSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//
//        Integer shopDeviceCount=mchPercentageStatisticsDAO.statisticsDeviceCount(shopStatisticsDeviceSearchModel);
//        response.setShopDeviceCount(shopDeviceCount);
//
//        shopStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartYoYTime());
//        shopStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndYoYTime());
//        Integer shopDeviceCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(shopStatisticsDeviceSearchModel);
//        response.setShopDeviceCountYoY(calculateYoY(shopDeviceCount, shopDeviceCountYoY));
//
//        shopStatisticsDeviceSearchModel.setStartCreateTime(searchDateContext.getStartMoMTime());
//        shopStatisticsDeviceSearchModel.setEndCreateTime(searchDateContext.getEndMoMTime());
//        Integer shopDeviceCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(shopStatisticsDeviceSearchModel);
//        response.setShopDeviceCountMoM(calculateMoM(shopDeviceCount, shopDeviceCountMoM));
//
//        // 仓库充电宝
//        StatisticsDeviceSearchModel warehouseStatisticsPowerBankSearchModel = new StatisticsDeviceSearchModel();
//        warehouseStatisticsPowerBankSearchModel.setDeviceLocation(2);
//        warehouseStatisticsPowerBankSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//        warehouseStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartTime());
//        warehouseStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndTime());
//        Integer warehousePowerBankCount=mchPercentageStatisticsDAO.statisticsDeviceCount(warehouseStatisticsPowerBankSearchModel);
//        response.setWarehousePowerBankCount(warehousePowerBankCount);
//        warehouseStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartYoYTime());
//        warehouseStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndYoYTime());
//        Integer warehousePowerBankCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(warehouseStatisticsPowerBankSearchModel);
//        response.setWarehousePowerBankCountYoY(calculateYoY(warehousePowerBankCount, warehousePowerBankCountYoY));
//
//        warehouseStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartMoMTime());
//        warehouseStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndMoMTime());
//        Integer warehousePowerBankCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(warehouseStatisticsPowerBankSearchModel);
//        response.setWarehousePowerBankCountMoM(calculateMoM(warehousePowerBankCount, warehousePowerBankCountMoM));
//
//
//
//        // 市场充电宝
//        StatisticsDeviceSearchModel marketStatisticsPowerBankSearchModel = new StatisticsDeviceSearchModel();
//        marketStatisticsPowerBankSearchModel.setDeviceLocation(1);
//        // 充电宝
//        marketStatisticsPowerBankSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//        marketStatisticsPowerBankSearchModel.setStartCreateTime(searchDateContext.getStartTime());
//        marketStatisticsPowerBankSearchModel.setEndCreateTime(searchDateContext.getEndTime());
//        Integer marketPowerBankCount=mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsPowerBankSearchModel);
//        response.setMarketPowerBankCount(marketPowerBankCount);
//
//        marketStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartMoMTime());
//        marketStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndMoMTime());
//        Integer marketPowerBankCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsPowerBankSearchModel);
//        response.setMarketPowerBankCountMoM(calculateMoM(marketPowerBankCount, marketPowerBankCountMoM));
//
//        marketStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartYoYTime());
//        marketStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndYoYTime());
//        Integer marketPowerBankCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(marketStatisticsPowerBankSearchModel);
//        response.setMarketPowerBankCountYoY(calculateYoY(marketPowerBankCount, marketPowerBankCountYoY));
//
//        // 充电宝总数
//        StatisticsDeviceSearchModel allStatisticsPowerBankSearchModel = new StatisticsDeviceSearchModel();
//        allStatisticsPowerBankSearchModel.setDeviceLocation(1);
//        // 充电宝
//        allStatisticsPowerBankSearchModel.setDeviceInfoIds(deviceInfoDTOS.stream()
//                .filter(deviceInfoBO -> !DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
//                .map(DeviceInfoBO::getId).collect(Collectors.toList()));
//        allStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartTime());
//        allStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndTime());
//
//        Integer powerBankAllCount=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsPowerBankSearchModel);
//        response.setPowerBankAllCount(powerBankAllCount);
//
//        allStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartYoYTime());
//        allStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndYoYTime());
//        Integer powerBankAllCountYoY=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsPowerBankSearchModel);
//        response.setPowerBankAllCountYoY(calculateYoY(powerBankAllCount, powerBankAllCountYoY));
//
//        allStatisticsPowerBankSearchModel.setStartInstalledTime(searchDateContext.getStartMoMTime());
//        allStatisticsPowerBankSearchModel.setEndInstalledTime(searchDateContext.getEndMoMTime());
//        Integer powerBankAllCountMoM=mchPercentageStatisticsDAO.statisticsDeviceCount(allStatisticsPowerBankSearchModel);
//        response.setPowerBankAllCountMoM(calculateMoM(powerBankAllCount, powerBankAllCountMoM));
//        // TODO 设备产单率
//        DeviceOrderCountModel deviceOrderCountModel= new DeviceOrderCountModel();
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//        Integer deviceOrderCount=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(shopDeviceCount>0){
//            response.setPowerBankOrderRatio(divideRatio(deviceOrderCount,shopDeviceCount));
//        }
//
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartYoYTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndYoYTime());
//        Integer deviceOrderCountYoY=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(shopDeviceCountYoY>0){
//            response.setPowerBankOrderRatioYoY(calculateYoY(divideRatio(deviceOrderCount,shopDeviceCount),
//                    divideRatio(deviceOrderCountYoY,shopDeviceCountYoY)));
//        }
//
//        deviceOrderCountModel.setStartLoanTime(searchDateContext.getStartMoMTime());
//        deviceOrderCountModel.setEndLoanTime(searchDateContext.getEndMoMTime());
//        Integer deviceOrderCountMoM=orderBoxStatisticsDAO.deviceOrderCount(deviceOrderCountModel);
//        if(shopDeviceCountMoM>0){
//            response.setPowerBankOrderRatioMoM(calculateMoM(divideRatio(deviceOrderCount,shopDeviceCount),
//                    divideRatio(deviceOrderCountMoM,shopDeviceCountMoM)));
//        }
//
//        return response;
//    }

    /**
     * 分成相关数据统计汇总
     * 实时从分成记录表汇总
     *
     * @param response
     * @param context
     * @return
     */
    private BusinessStatisticsResponse buildDivideStatisticsResponse(BusinessStatisticsResponse response,
                                                                     final DataIndicatorsContext context,
                                                                     final UserDataAuthorityContext userDataAuthorityContext,
                                                                     final String currencyCode) {
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        log.info("数据查询时间:{}", JsonUtil.beanToJson(searchDateContext));
        // 分成支出订单
        DivideStatisticsResponse divideStatisticsPayResponse = divideStatistics(searchDateContext.getStartTime(),
                searchDateContext.getEndTime(), userDataAuthorityContext.getWrite().getUserIds(), BalanceTypeEnum.INCOME);
        // 分成支出订单同比
        DivideStatisticsResponse divideStatisticsYoYPayResponse = divideStatistics(searchDateContext.getStartYoYTime(),
                searchDateContext.getEndYoYTime(), userDataAuthorityContext.getWrite().getUserIds(), BalanceTypeEnum.INCOME);
        // 分成支出订单环比
        DivideStatisticsResponse divideStatisticsMoMPayResponse = divideStatistics(searchDateContext.getStartMoMTime(),
                searchDateContext.getEndMoMTime(),userDataAuthorityContext.getWrite().getUserIds(), BalanceTypeEnum.INCOME);

        // 分成退款订单
        DivideStatisticsResponse divideStatisticsRefundResponse = divideStatistics(searchDateContext.getStartTime(),
                searchDateContext.getEndTime(),userDataAuthorityContext.getWrite().getUserIds(), BalanceTypeEnum.EXPEND);
        // 分成退款订单同比
        DivideStatisticsResponse divideStatisticsYoYRefundResponse = divideStatistics(searchDateContext.getStartYoYTime(),
                searchDateContext.getEndYoYTime(),userDataAuthorityContext.getWrite().getUserIds(), BalanceTypeEnum.EXPEND);
        // 分成退款订单环比
        DivideStatisticsResponse divideStatisticsMoMRefundResponse = divideStatistics(searchDateContext.getStartMoMTime(),
                searchDateContext.getEndMoMTime(),userDataAuthorityContext.getWrite().getUserIds(), BalanceTypeEnum.EXPEND);
        // 分成订单
        response.setDivideOrders(divideStatisticsPayResponse.getDivideCount() - divideStatisticsRefundResponse.getDivideCount());
        response.setDivideOrdersYoY(calculateYoY(divideStatisticsPayResponse.getDivideCount() - divideStatisticsRefundResponse.getDivideCount(),

                divideStatisticsYoYPayResponse.getDivideCount() - divideStatisticsYoYRefundResponse.getDivideCount()));
        response.setDivideOrdersMoM(calculateMoM(divideStatisticsPayResponse.getDivideCount() - divideStatisticsRefundResponse.getDivideCount(),
                divideStatisticsMoMPayResponse.getDivideCount() - divideStatisticsMoMRefundResponse.getDivideCount()));
        CurrencyEnum currencyEnum= currencyManager.getCurrency(currencyCode);
        MultiCurrencyMoney divideExpendMoney = new MultiCurrencyMoney(0,currencyEnum.getCurrencyCode());
        divideExpendMoney.setCent(divideStatisticsPayResponse.getDivideAmount() - divideStatisticsRefundResponse.getDivideAmount());

        // 分成支出金额
        response.setDivideExpendAmount(divideExpendMoney.getAmount().toString());
        response.setDivideExpendAmountYoY(calculateYoY(divideStatisticsPayResponse.getDivideAmount() - divideStatisticsRefundResponse.getDivideAmount(),
                divideStatisticsYoYPayResponse.getDivideAmount() - divideStatisticsYoYRefundResponse.getDivideAmount()));
        response.setDivideExpendAmountMoM(calculateMoM(divideStatisticsPayResponse.getDivideAmount() - divideStatisticsRefundResponse.getDivideAmount(),
                divideStatisticsMoMPayResponse.getDivideAmount() - divideStatisticsMoMRefundResponse.getDivideAmount()));

        // 分成支出占比计算
        response.setDivideExpendRatio(String.valueOf(divideRatio(divideStatisticsPayResponse.getDivideAmount(), divideStatisticsPayResponse.getBillAmount())));
        response.setDivideExpendRatioYoY(calculateYoY(divideRatio(divideStatisticsPayResponse.getDivideAmount(), divideStatisticsPayResponse.getBillAmount()),
                divideRatio(divideStatisticsYoYPayResponse.getDivideAmount(), divideStatisticsYoYPayResponse.getBillAmount())));
        response.setDivideExpendRatioMoM(calculateMoM(divideRatio(divideStatisticsPayResponse.getDivideAmount(), divideStatisticsPayResponse.getBillAmount()),
                divideRatio(divideStatisticsMoMPayResponse.getDivideAmount(), divideStatisticsMoMPayResponse.getBillAmount())));
        return response;
    }

    /**
     * 分成信息统计
     * 实时查询分成明细表统计返回
     *
     * @param startTime
     * @param endTime
     * @param sellerId
     * @return
     */
    private DivideStatisticsResponse divideStatistics(Date startTime, Date endTime, List<Long> sellerIds, BalanceTypeEnum balanceTypeEnum) {
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setStartBizTime(startTime);
        percentageDetail.setEndBizTime(endTime);
        percentageDetail.setBizType(balanceTypeEnum.getId());
        percentageDetail.setSellerIds(sellerIds);
        DivideStatisticsResponse divideStatisticsResponse = percentageDetailService.divideStatistics(percentageDetail);
        if (Objects.nonNull(divideStatisticsResponse)) {
            if (Objects.nonNull(divideStatisticsResponse.getDivideAmount())) {
                divideStatisticsResponse.setDivideAmount(divideStatisticsResponse.getDivideAmount());
            } else {
                divideStatisticsResponse.setDivideAmount(0L);
            }
            if (Objects.nonNull(divideStatisticsResponse.getDivideCount())) {
                divideStatisticsResponse.setDivideCount(divideStatisticsResponse.getDivideCount());
            } else {
                divideStatisticsResponse.setDivideCount(0);
            }
            if (Objects.nonNull(divideStatisticsResponse.getBillAmount())) {
                divideStatisticsResponse.setBillAmount(divideStatisticsResponse.getBillAmount());
            } else {
                divideStatisticsResponse.setBillAmount(0L);
            }
        } else {
            divideStatisticsResponse = new DivideStatisticsResponse();
            divideStatisticsResponse.setDivideCount(0);
            divideStatisticsResponse.setDivideAmount(0L);
            divideStatisticsResponse.setBillAmount(0L);
        }
        return divideStatisticsResponse;
    }

    /**
     * 环比计算
     * 公式：环比增长率 = (本期值 - 上期值) / 上期值 * 100%
     *
     * @param current
     * @param prev
     * @return
     */
    private static Double calculateMoM(double current, double prev) {
        if(prev==0){
            return null;
        }
        double result = (current - prev) / Math.max(prev, 1) * 100;
        return Math.round(result * 100.0) / 100.0;
    }

    /**
     * 同比计算
     * 公式：同比较率 = (本期值 / 去年同期值 - 1) * 100%
     *
     * @param current
     * @param prev
     * @return
     */
    private static Double calculateYoY(double current, double prev) {
        if(prev==0){
            return null;
        }
        double result = (current / Math.max(prev, 1) - 1) * 100;
        return Math.round(result * 100.0) / 100.0;
    }

    /**
     * 除法计算，返回百分比的值
     *
     * @param a
     * @param b
     * @return
     */
    private static Double divideRatio(double a, double b) {

        double result = (a / b) * 100;
        return Math.round(result * 100.0) / 100.0;
    }

}