/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.service;

import com.chargebolt.aeacus.common.AeacusConstsnts;
import com.chargebolt.aeacus.entity.dataobject.OssUserDO;
import com.chargebolt.aeacus.service.manager.UserManager;
import com.chargebolt.api.request.DataDeviceDetailRequest;
import com.chargebolt.api.request.DataMerchantDetailRequest;
import com.chargebolt.api.request.DataOrderDetailRequest;
import com.chargebolt.api.request.DataShopDetailRequest;
import com.chargebolt.api.response.DeviceDetailListResponse;
import com.chargebolt.api.response.DeviceDetailResponse;
import com.chargebolt.api.response.MerchantDetailListResponse;
import com.chargebolt.api.response.MerchantDetailResponse;
import com.chargebolt.api.response.OrderDetailListResponse;
import com.chargebolt.api.response.OrderDetailResponse;
import com.chargebolt.api.response.ShopDetailListResponse;
import com.chargebolt.api.response.ShopDetailResponse;
import com.chargebolt.commons.enums.language.DataDetailLanguageEnum;
import com.chargebolt.commons.enums.language.DeviceDetailIndicatorsLanguageEnum;
import com.chargebolt.commons.enums.language.DeviceDetailInstallStateLanguageEnum;
import com.chargebolt.commons.enums.language.DeviceDetailStateLanguageEnum;
import com.chargebolt.commons.enums.language.MerchantDetailIndicatorsLanguageEnum;
import com.chargebolt.commons.enums.language.MerchantDivideLanguageEnum;
import com.chargebolt.commons.enums.language.OrderDetailIndicatorsLanguageEnum;
import com.chargebolt.commons.enums.language.OrderDetailStateLanguageEnum;
import com.chargebolt.commons.enums.language.ShopDetailIndicatorsLanguageEnum;
import com.chargebolt.commons.enums.language.ShopStatusLanguageEnum;
import com.chargebolt.common.utils.CycleDateUtil;
import com.chargebolt.commons.enums.MqttStatusEnum;
import com.chargebolt.context.UserDataAuthorityContext;
import com.chargebolt.currency.CurrencyManager;
import com.chargebolt.dao.statistics.rds.OrderBoxStatisticsDAO;
import com.chargebolt.dao.statistics.pg.dto.OrderDetailDTO;
import com.chargebolt.dao.statistics.rds.ShopMerchantDeviceStatisticsDAO;
import com.chargebolt.dao.statistics.rds.dto.DeviceDetailDTO;
import com.chargebolt.dao.statistics.rds.dto.MerchantDetailDTO;
import com.chargebolt.dao.statistics.rds.model.DeviceDetailListSearchModel;
import com.chargebolt.dao.statistics.rds.model.MerchantDetailListSearchModel;
import com.chargebolt.dao.pg.model.OrderDataStatisticsModel;
import com.chargebolt.service.context.DataIndicatorsContext;
import com.chargebolt.service.context.SearchDateContext;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.demeter.biz.service.DeviceInfoService;
import so.dian.demeter.common.enums.DeviceStorageTypeEnum;
import so.dian.demeter.pojo.bo.DeviceInfoBO;
import so.dian.eros.common.util.DeviceUtil;
import so.dian.hermes.client.pojo.enums.OrderStatusEnum;
import so.dian.mofa3.lang.money.CurrencyEnum;
import so.dian.mofa3.lang.money.MoneyFormatter;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.talos.biz.service.ShopStatisticService;
import so.dian.talos.biz.service.TalosShopService;
import so.dian.talos.common.enums.ShopStatusEnum;
import so.dian.talos.pojo.entity.ShopDO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DetailIndicatorsServiceImpl.java, v 1.0 2024-01-09 3:33 PM Exp $
 */
@Slf4j
@Service
public class DetailIndicatorsServiceImpl implements DatailIndicatorsService {
    private final OrderBoxStatisticsDAO orderBoxStatisticsDAO;
    private final TalosShopService talosShopService;
    private final ShopStatisticService shopStatisticService;
    private final UserManager userManager;
    private final CurrencyManager currencyManager;
    private final ShopMerchantDeviceStatisticsDAO shopMerchantDeviceStatisticsDAO;
    private final DeviceInfoService deviceInfoService;
    public DetailIndicatorsServiceImpl(ObjectProvider<OrderBoxStatisticsDAO> orderBoxStatisticsDAOProvider,
                                      ObjectProvider<TalosShopService> talosShopServiceProvider,
                                      ObjectProvider<ShopStatisticService> shopStatisticServiceProvider,
                                      ObjectProvider<UserManager> userManagerProvider,
                                       ObjectProvider<CurrencyManager> currencyManagerProvider,
                                       ObjectProvider<ShopMerchantDeviceStatisticsDAO> shopMerchantDeviceStatisticsDAOProvider,
                                       ObjectProvider<DeviceInfoService> deviceInfoServiceProvider){
        this.orderBoxStatisticsDAO= orderBoxStatisticsDAOProvider.getIfUnique();
        this.talosShopService= talosShopServiceProvider.getIfUnique();
        this.shopStatisticService= shopStatisticServiceProvider.getIfUnique();
        this.userManager= userManagerProvider.getIfUnique();
        this.currencyManager= currencyManagerProvider.getIfUnique();
        this.shopMerchantDeviceStatisticsDAO= shopMerchantDeviceStatisticsDAOProvider.getIfUnique();
        this.deviceInfoService= deviceInfoServiceProvider.getIfUnique();
    }
    @Override
    public OrderDetailListResponse orderDetailList(final DataOrderDetailRequest request, final UserDataAuthorityContext userDataAuthorityContext) {
        OrderDetailListResponse response= new OrderDetailListResponse();
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        OrderDataStatisticsModel model= new OrderDataStatisticsModel();

        // request.getShopId() 和 request.getSellerId() 不应该同时传入
        if(Objects.isNull(request.getShopId())){
            response.setShopName(DataDetailLanguageEnum.ALL.getEnumValue());
            // 数据权限，用户拥有权限的门店
            model.setShopIds(userDataAuthorityContext.getWrite().getShopIds());
        }else{
            model.setShopId(request.getShopId());
            ShopDO shopDO= talosShopService.queryById(request.getShopId());
            response.setShopName(Objects.nonNull(shopDO)?shopDO.getName():"");
        }
        if(Objects.isNull(request.getSellerId())){
            response.setPersonName(DataDetailLanguageEnum.ALL.getEnumValue());
            // 数据权限，用户维度拥有的门店
            model.setShopIds(userDataAuthorityContext.getWrite().getShopIds());
        }else{
            if(Objects.equals(userDataAuthorityContext.getAuthorityLevel(), AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_1)){
                model.setShopIds(Arrays.asList(request.getSellerId()));
            }else{
                if(userDataAuthorityContext.getWrite().getUserIds().contains(request.getSellerId())){
                    List<Long> shopIds=shopStatisticService.getShopIdsBySeller(request.getSellerId());
                    if(CollectionUtils.isNotEmpty(shopIds)){
                        model.setShopIds(shopIds);
                    }else{
                        model.setShopId(-1L);
                    }
                }else{
                    model.setShopId(-1L);
                }
            }

            OssUserDO userDO= userManager.getById(request.getSellerId());
            response.setPersonName(Objects.nonNull(userDO)?userDO.getFullName():"");
        }

        model.setStartCreateTime(searchDateContext.getStartTime());
        model.setEndCreateTime(searchDateContext.getEndTime());
        switch (request.getIndicators()){
            case 1:
                // 成功订单，已支付
                model.setOrderStatus(OrderStatusEnum.PAID.getCode());
                response.setIndicators(OrderDetailIndicatorsLanguageEnum.SUCCESS_ORDERS.getEnumValue());
                response.setStateDesc(OrderDetailStateLanguageEnum.PAID.getEnumValue());
                response.setOrderAmountDesc(">=0");
                break;
            case 2:
                // 付费订单，已支付，金额>0
                model.setPayOrderGT0(Boolean.TRUE);
                model.setOrderStatus(OrderStatusEnum.PAID.getCode());
                response.setIndicators(OrderDetailIndicatorsLanguageEnum.PAID_ORDERS.getEnumValue());
                response.setStateDesc(OrderDetailStateLanguageEnum.PAID.getEnumValue());
                response.setOrderAmountDesc(">0");
                break;
            case 3:
                // 退款订单
                model.setOrderStatus(OrderStatusEnum.REFUND.getCode());
                response.setIndicators(OrderDetailIndicatorsLanguageEnum.REFUND_ORDERS.getEnumValue());
                response.setStateDesc(OrderDetailStateLanguageEnum.REFUNDING.getEnumValue());
                response.setOrderAmountDesc(">0");
                break;
            case 4:
                // 封顶价订单，已支付
                model.setCappingOrder(Boolean.TRUE);
                model.setOrderStatus(OrderStatusEnum.PAID.getCode());
                response.setIndicators(OrderDetailIndicatorsLanguageEnum.SALES_ORDERS.getEnumValue());
                response.setStateDesc(OrderDetailStateLanguageEnum.PAID.getEnumValue());
                response.setOrderAmountDesc(">0");
                break;
            case 5:
                // 0元订单，金额=0
                model.setPayOrderGT0(Boolean.FALSE);
                model.setOrderStatus(OrderStatusEnum.PAID.getCode());
                response.setIndicators(OrderDetailIndicatorsLanguageEnum.FREE_ORDERS.getEnumValue());
                response.setStateDesc(OrderDetailStateLanguageEnum.PAID.getEnumValue());
                response.setOrderAmountDesc("=0");
                break;
            }
        Page<OrderDetailDTO> orderDetailDTOPage= PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE)
                .doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        orderBoxStatisticsDAO.getOrderDetail(model);
                    }
                });
        response.setTotal((int)orderDetailDTOPage.getTotal());
        List<Long> allShopId = orderDetailDTOPage.getResult().stream()
                .map(OrderDetailDTO::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ShopDO> shopDOList= talosShopService.queryListByIds(allShopId);
        List<OrderDetailResponse> list= new ArrayList<>();
        if(CollectionUtils.isNotEmpty(orderDetailDTOPage.getResult())){
            for(OrderDetailDTO orderDetailDTO:orderDetailDTOPage.getResult()){
                OrderDetailResponse orderDetailResponse= new OrderDetailResponse();
                orderDetailResponse.setOrderNo(orderDetailDTO.getOrderNo());
                CurrencyEnum currencyEnum= currencyManager.getCurrency(orderDetailDTO.getCurrency());
                String currency=currencyEnum.getCurrencyCode();
                if(Objects.nonNull(orderDetailDTO.getPayAmount())){
                    MultiCurrencyMoney orderMoney= new MultiCurrencyMoney(0, currency);
                    orderMoney.setCent(orderDetailDTO.getPayAmount());
                    orderDetailResponse.setOrderAmount(orderMoney.getAmount().toString());
                    orderDetailResponse.setOrderAmountFormatted(MoneyFormatter.format(orderMoney));
                    orderDetailResponse.setCurrencyCode(currencyEnum.getCurrencyCode());
                    orderDetailResponse.setCurrencySymbol(currencyEnum.getCurrencyLabel());
                    orderDetailResponse.setCurrencyLabel(currencyEnum.getCurrencyLabel());
                }
                orderDetailResponse.setState(orderDetailDTO.getState());
                orderDetailResponse.setStateDesc(OrderDetailStateLanguageEnum.getDescrById(orderDetailDTO.getState()));
                orderDetailResponse.setDeviceNo(orderDetailDTO.getDeviceNo());
                orderDetailResponse.setShopName(shopDOList.stream()
                       .filter(shopDO -> shopDO.getId().equals(orderDetailDTO.getShopId()))
                       .map(ShopDO::getName)
                       .findFirst().orElse(""));
                orderDetailResponse.setLoanStamp(Objects.isNull(orderDetailDTO.getLoanTime())?0L:orderDetailDTO.getLoanTime().getTime());
                orderDetailResponse.setPowerBankNo(orderDetailDTO.getPowerBankNo());
                orderDetailResponse.setReturnStamp(Objects.isNull(orderDetailDTO.getReturnTime())?0L:orderDetailDTO.getReturnTime().getTime());
                list.add(orderDetailResponse);
            }
        }
        response.setList(list);
        return response;
    }

    @Override
    public ShopDetailListResponse shopDetailList(final DataShopDetailRequest request, final UserDataAuthorityContext userDataAuthorityContext) {
        ShopDetailListResponse response= new ShopDetailListResponse();
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());

        ShopDO queryShopDO = new ShopDO();
        // request.getShopId() 和 request.getSellerId() 不应该同时传入
        if(Objects.isNull(request.getShopId())){
            response.setShopName(DataDetailLanguageEnum.ALL.getEnumValue());
            // 数据权限，用户拥有权限的门店
            queryShopDO.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        }else{
            if(userDataAuthorityContext.getWrite().getShopIds().contains(request.getShopId())){
                queryShopDO.setId(request.getShopId());
                ShopDO shopDO= talosShopService.queryById(request.getShopId());
                response.setShopName(Objects.nonNull(shopDO)?shopDO.getName():"");
            }else{
                queryShopDO.setId(-1L);
            }
        }
        if(Objects.isNull(request.getSellerId())){
            response.setPersonName(DataDetailLanguageEnum.ALL.getEnumValue());
            // 数据权限，用户拥有权限的门店
            queryShopDO.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        }else{
            if(Objects.equals(userDataAuthorityContext.getAuthorityLevel(), AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_1)){
                queryShopDO.setSellerId(request.getSellerId());
                OssUserDO userDO= userManager.getById(request.getSellerId());
                response.setPersonName(Objects.nonNull(userDO)?userDO.getFullName():"");
            }else{
                if(userDataAuthorityContext.getWrite().getUserIds().contains(request.getSellerId())){
                    queryShopDO.setSellerId(request.getSellerId());
                    OssUserDO userDO= userManager.getById(request.getSellerId());
                    response.setPersonName(Objects.nonNull(userDO)?userDO.getFullName():"");
                }else{
                    queryShopDO.setSellerId(-1L);
                }
            }
        }
        String orderBy= " s.create_time DESC";
        switch (request.getIndicators()) {
            case 1:
                response.setIndicators(ShopDetailIndicatorsLanguageEnum.ALL_SHOP.getEnumValue());
                response.setStateDesc(DataDetailLanguageEnum.ALL.getEnumValue());
                queryShopDO.setStartCreateTime(searchDateContext.getStartTime());
                queryShopDO.setEndCreateTime(searchDateContext.getEndTime());
                orderBy= " s.create_time DESC";
                break;
            case 2:
                response.setStateDesc(ShopStatusLanguageEnum.getTranslationByValue(ShopStatusLanguageEnum.INSTALLED.getValue()));
                response.setIndicators(ShopDetailIndicatorsLanguageEnum.INSTALLED_SHOP.getEnumValue());
                queryShopDO.setStatus(ShopStatusEnum.INSTALLED.getStatus());
                queryShopDO.setStartInstalledTime(searchDateContext.getStartTime());
                queryShopDO.setEndInstalledTime(searchDateContext.getEndTime());
                orderBy= " s.install_time DESC";
                break;
            }
        Page<ShopDO> shopDOPage= PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE).setOrderBy(orderBy)
                .doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        shopStatisticService.listRecord(queryShopDO);
                    }
                });
        response.setTotal((int) shopDOPage.getTotal());
        List<ShopDetailResponse> list= new ArrayList<>();
        if(CollectionUtils.isNotEmpty(shopDOPage.getResult())){
            for(ShopDO orderDetailDTO:shopDOPage.getResult()){
                ShopDetailResponse shopDetailResponse= new ShopDetailResponse();
                shopDetailResponse.setShopId(orderDetailDTO.getId());
                shopDetailResponse.setShopName(orderDetailDTO.getName());
                shopDetailResponse.setState(orderDetailDTO.getStatus());
                shopDetailResponse.setStateDesc(ShopStatusLanguageEnum.getTranslationByValue(orderDetailDTO.getStatus()));
                shopDetailResponse.setContactName(orderDetailDTO.getContactName());
                shopDetailResponse.setContactMobile(orderDetailDTO.getContactMobile());
                shopDetailResponse.setAddress(orderDetailDTO.getAddress());
                list.add(shopDetailResponse);
            }
        }
        response.setList(list);
        return response;
    }

    @Override
    public MerchantDetailListResponse merchantDetailList(final DataMerchantDetailRequest request, UserDataAuthorityContext userDataAuthorityContext) {
        MerchantDetailListResponse response= new MerchantDetailListResponse();
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());

        MerchantDetailListSearchModel model= new MerchantDetailListSearchModel();
        String orderBy= "merchantCreateTime DESC";
        switch (request.getIndicators()) {
            case 1:
                response.setIndicators(MerchantDetailIndicatorsLanguageEnum.ALL_MERCHANT.getEnumValue());
                response.setStateDesc(DataDetailLanguageEnum.ALL.getEnumValue());
                model.setStartCreateTime(searchDateContext.getStartTime());
                model.setEndCreateTime(searchDateContext.getEndTime());
                orderBy= "merchantCreateTime DESC";
                break;
            case 2:
                response.setIndicators(MerchantDetailIndicatorsLanguageEnum.OPEN_DIVIDE.getEnumValue());
                response.setStateDesc(MerchantDivideLanguageEnum.ACTIVATED.getEnumValue());
                model.setStartPercentageStamp(searchDateContext.getStartTime().getTime());
                model.setEndPercentageStamp(searchDateContext.getEndTime().getTime());
                model.setOpenDivide(1);
                orderBy= "openDivideStamp DESC";
                break;

        }
        if(Objects.isNull(request.getSellerId())){
            response.setPersonName(DataDetailLanguageEnum.ALL.getEnumValue());
            // 数据权限，拥有权限的用户列表
            model.setSellerIds(userDataAuthorityContext.getWrite().getUserIds());
        }else{
            if(Objects.equals(userDataAuthorityContext.getAuthorityLevel(), AeacusConstsnts.USER_DATA_AUTHORITY_LEVEL_1)){
                model.setSellerId(request.getSellerId());
                OssUserDO userDO= userManager.getById(request.getSellerId());
                response.setPersonName(Objects.nonNull(userDO)?userDO.getFullName():"");
            }else{
                if(userDataAuthorityContext.getWrite().getUserIds().contains(request.getSellerId())) {
                    model.setSellerId(request.getSellerId());
                    OssUserDO userDO= userManager.getById(request.getSellerId());
                    response.setPersonName(Objects.nonNull(userDO)?userDO.getFullName():"");
                }else{
                    model.setSellerId(-1L);
                }
            }
        }
        Page<MerchantDetailDTO> merchantDOPage= PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE).setOrderBy(orderBy)
                .doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        shopMerchantDeviceStatisticsDAO.dataMerchantDetailList(model);
                    }
                });
        response.setTotal((int) merchantDOPage.getTotal());
        List<MerchantDetailResponse> list= new ArrayList<>();
        if(CollectionUtils.isNotEmpty(merchantDOPage.getResult())){
            for(MerchantDetailDTO merchantDetailDTO:merchantDOPage.getResult()){
                MerchantDetailResponse merchantDetailResponse= new MerchantDetailResponse();
                merchantDetailResponse.setMerchantId(merchantDetailDTO.getId());
                merchantDetailResponse.setMerchantName(merchantDetailDTO.getMerchantName());
                merchantDetailResponse.setDividendState(merchantDetailDTO.getOpenDivide());
                merchantDetailResponse.setDividendStateDesc(MerchantDivideLanguageEnum.getDescrById(merchantDetailDTO.getOpenDivide()));
                if(Objects.nonNull(merchantDetailDTO.getRate())){
                    merchantDetailResponse.setDividendRate(merchantDetailDTO.getRate().toString());
                }
                merchantDetailResponse.setContactName(merchantDetailDTO.getContactName());
                merchantDetailResponse.setContactMobile(merchantDetailDTO.getContactMobile());
                merchantDetailResponse.setSellerName(merchantDetailDTO.getSellerName());
                list.add(merchantDetailResponse);
            }
        }
        response.setList(list);
        return response;
    }

    @Override
    public DeviceDetailListResponse deviceDetailList(final DataDeviceDetailRequest request, final UserDataAuthorityContext dataAuthorityContext) {
        DeviceDetailListResponse response= new DeviceDetailListResponse();
        DataIndicatorsContext context= new DataIndicatorsContext();
        context.setCycle(request.getCycle());
        context.setTime(request.getTime());
        SearchDateContext searchDateContext = CycleDateUtil.searchDateContext(context);
        response.setStartTime(searchDateContext.getStartTime().getTime());
        response.setEndTime(searchDateContext.getEndTime().getTime());
        List<DeviceInfoBO> deviceInfoDTOS = deviceInfoService.getAll();
        List<Long> deviceTypeIds= deviceInfoDTOS.stream()
                .filter(deviceInfoBO -> DeviceUtil.isBox(deviceInfoBO.getSubDeviceType()))
                .map(DeviceInfoBO::getId).collect(Collectors.toList());
        DeviceDetailListSearchModel model=new DeviceDetailListSearchModel();
        model.setDeviceInfoIds(deviceTypeIds);
        // request.getShopId() 和 request.getSellerId() 不应该同时传入
        if(Objects.isNull(request.getSellerId())){
            response.setPersonName(DataDetailLanguageEnum.ALL.getEnumValue());
            // 数据权限，拥有权限的用户列表
            model.setSellerIds(dataAuthorityContext.getWrite().getUserIds());
        }else{
            if(dataAuthorityContext.getWrite().getUserIds().contains(request.getSellerId())) {
                model.setSellerId(request.getSellerId());
                OssUserDO userDO= userManager.getById(request.getSellerId());
                response.setPersonName(Objects.nonNull(userDO)?userDO.getFullName():"");
            }else{
                model.setSellerId(-1L);
            }
        }
        if(Objects.isNull(request.getShopId())){
            response.setShopName(DataDetailLanguageEnum.ALL.getEnumValue());
            // 数据权限，拥有权限的用户列表
            model.setShopIds(dataAuthorityContext.getWrite().getShopIds());
        }else{
            if(dataAuthorityContext.getWrite().getShopIds().contains(request.getShopId())){
                model.setShopId(request.getShopId());
                ShopDO shopDO= talosShopService.queryById(request.getShopId());
                response.setShopName(Objects.nonNull(shopDO)?shopDO.getName():"");
            }else{
                model.setShopId(-1L);
            }
        }
        String orderBy= " d.create_time desc";
        response.setIndicators(DeviceDetailIndicatorsLanguageEnum.getTranslationByValue(request.getIndicators()));
        // 设备安装时间
        model.setStartInstallTime(searchDateContext.getStartTime());
        model.setEndInstallTime(searchDateContext.getEndTime());
        model.setStorageTypes(Arrays.asList(DeviceStorageTypeEnum.SHOP.getCode(), DeviceStorageTypeEnum.USER.getCode()));
        switch (request.getIndicators()) {
            case 1:
                response.setStateDesc(DeviceDetailStateLanguageEnum.ONLINE.getEnumValue());
                model.setOnlineState(MqttStatusEnum.ONLINE.getCode());
                orderBy= " d.update_time desc";
                break;
            case 2:
                response.setStateDesc(DeviceDetailStateLanguageEnum.OFFLINE.getEnumValue());
                model.setOnlineState(MqttStatusEnum.OFFLINE.getCode());
                orderBy= " d.update_time desc";
                break;
            default:
                response.setStateDesc(DataDetailLanguageEnum.ALL.getEnumValue());
//            case 3:
//            case 5:
//                response.setIndicators(DeviceDetailIndicatorsLanguageEnum.getTranslationByValue(DeviceDetailIndicatorsLanguageEnum.INSTALLED_DEVICES.getValue()));
//                response.setStateDesc(DataDetailLanguageEnum.ALL.getEnumValue());
//                // 设备安装时间
//                model.setStartInstallTime(searchDateContext.getStartTime());
//                model.setEndInstallTime(searchDateContext.getEndTime());
//                model.setStorageTypes(Arrays.asList(DeviceStorageTypeEnum.SHOP.getCode(), DeviceStorageTypeEnum.USER.getCode()));
//                orderBy= " s.install_time desc";
//                break;
//            case 4:
//                response.setStateDesc(DataDetailLanguageEnum.ALL.getEnumValue());
//                // 设备回收时间 FIXME 目前回收后直接到仓库
//                model.setStartRecoveryTime(searchDateContext.getStartTime());
//                model.setEndRecoveryTime(searchDateContext.getEndTime());
//                model.setStorageTypes(Arrays.asList(DeviceStorageTypeEnum.WAREHOUSE.getCode()));
//                orderBy= " d.create_time desc";
//                break;
//
//            case 6:
//                response.setStateDesc(DataDetailLanguageEnum.ALL.getEnumValue());
//                // 设备入库时间
//                model.setStartCreateTime(searchDateContext.getStartTime());
//                model.setEndCreateTime(searchDateContext.getEndTime());
//                model.setStorageTypes(Arrays.asList(DeviceStorageTypeEnum.WAREHOUSE.getCode()));
//                orderBy= " d.create_time desc";
//                break;
        }
        Page<DeviceDetailDTO> deviceDetailDTOPage= PageHelper.startPage(request.getPageNum(),
                        request.getPageSize(), Boolean.TRUE).setOrderBy(orderBy)
                .doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        shopMerchantDeviceStatisticsDAO.dataDeviceDetailList(model);
                        // 设备回收
//                        if(Objects.equals(request.getIndicators(), 4)){
//                            shopMerchantDeviceStatisticsDAO.dataDeviceRecoveryDetailList(model);
//                        }else{
//                            shopMerchantDeviceStatisticsDAO.dataDeviceDetailList(model);
//                        }
                    }
                });
        response.setTotal((int) deviceDetailDTOPage.getTotal());
        List<DeviceDetailResponse> list= new ArrayList<>();
        if(CollectionUtils.isNotEmpty(deviceDetailDTOPage.getResult())){
            for(DeviceDetailDTO deviceDetailDTO:deviceDetailDTOPage.getResult()){
                DeviceDetailResponse merchantDetailResponse= new DeviceDetailResponse();
                merchantDetailResponse.setDeviceNo(deviceDetailDTO.getDeviceNo());
                merchantDetailResponse.setDeviceName(deviceDetailDTO.getDeviceName());
                if(Objects.nonNull(deviceDetailDTO.getOnlineState())){
                    merchantDetailResponse.setOnlineState(deviceDetailDTO.getOnlineState());
                    merchantDetailResponse.setOnlineStateDesc(DeviceDetailStateLanguageEnum.getDescrById(deviceDetailDTO.getOnlineState()));
                }
                merchantDetailResponse.setStorageType(deviceDetailDTO.getStorageType());
//                if(Objects.equals(deviceDetailDTO.getStorageType(), DeviceStorageTypeEnum.SHOP.getCode())){
//                    merchantDetailResponse.setInstallStateDesc(DeviceDetailInstallStateLanguageEnum.INSTALLED.getEnumValue());
//                }else if (Objects.nonNull(deviceDetailDTO.getRecoveryTime()) && Objects.equals(deviceDetailDTO.getStorageType(), DeviceStorageTypeEnum.WAREHOUSE.getCode())){
//                    merchantDetailResponse.setInstallStateDesc(DeviceDetailInstallStateLanguageEnum.RECYCLED.getEnumValue());
//                }else {
//                    merchantDetailResponse.setInstallStateDesc(DeviceDetailInstallStateLanguageEnum.WAREHOUSE.getEnumValue());
//                }
                // 只统计已安装
                merchantDetailResponse.setInstallStateDesc(DeviceDetailInstallStateLanguageEnum.INSTALLED.getEnumValue());
                merchantDetailResponse.setShopName(deviceDetailDTO.getShopName());
                merchantDetailResponse.setSellerName(deviceDetailDTO.getSellerName());
                if(Objects.nonNull(deviceDetailDTO.getLastUpdateTime())){
                    merchantDetailResponse.setLastUpdateStamp(deviceDetailDTO.getLastUpdateTime().getTime());
                }
                if(Objects.nonNull(deviceDetailDTO.getRecoveryTime())){
                    merchantDetailResponse.setRecoveryStamp(deviceDetailDTO.getRecoveryTime().getTime());
                }
                list.add(merchantDetailResponse);
            }
        }
        response.setList(list);
        return response;
    }


}