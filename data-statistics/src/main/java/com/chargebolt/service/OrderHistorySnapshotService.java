/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.service;

import com.chargebolt.dao.statistics.rds.dto.GenericOrderStatisticsDTO;
import com.chargebolt.dao.statistics.rds.dto.RefundOrderStatisticsDTO;
import com.chargebolt.dao.statistics.rds.dto.SuccessOrderStatisticsDTO;
import com.chargebolt.dao.statistics.rds.model.OrderHistorySnapshot;
import so.dian.hermes.client.pojo.dto.order.OrderDTO;
import so.dian.hermes.client.pojo.dto.oss.OssBoxOrderDTO;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: OrderHistorySnapshotService.java, v 0.1 2023-12-25 10:01:49 Exp $
 */

public interface OrderHistorySnapshotService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<OrderHistorySnapshot>     返回结果
     */
    List<OrderHistorySnapshot> listRecord(OrderHistorySnapshot model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return OrderHistorySnapshot     返回结果
     */
    OrderHistorySnapshot getRecord(OrderHistorySnapshot model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(OrderHistorySnapshot model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(OrderHistorySnapshot model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(OrderHistorySnapshot model);

    int updateHistorySnapshot(OssBoxOrderDTO model);
    /**
     * 成功订单统计
     *
     * @param model
     * @return
     */
    SuccessOrderStatisticsDTO successOrderStatistics(OrderHistorySnapshot model);

    /**
     * 退款订单统计
     *
     * @param model
     * @return
     */
    RefundOrderStatisticsDTO refundOrderStatistics(OrderHistorySnapshot model);

    /**
     * 快照表通用统计
     *
     * @param model
     * @return
     */
    GenericOrderStatisticsDTO genericOrderStatistics(OrderHistorySnapshot model);
}