/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.service.context;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: SearchDateContext.java, v 1.0 2023-12-26 10:46 AM Exp $
 */
@Data
public class SearchDateContext {
    private Date startTime;
    private Date endTime;

    private Date startYoYTime;
    private Date endYoYTime;

    private Date startMoMTime;
    private Date endMoMTime;
}