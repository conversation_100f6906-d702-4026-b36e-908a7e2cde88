/*
 * Dian.so Inc.
 * Copyright (c) 2016-2023 All Rights Reserved.
 */
package com.chargebolt.task;

import com.chargebolt.bill.api.response.DivideStatisticsResponse;
import com.chargebolt.bill.dao.model.PercentageDetail;
import com.chargebolt.bill.service.PercentageDetailService;
import com.chargebolt.commons.enums.MerchantTypeEnum;
import com.chargebolt.dao.statistics.rds.model.OrderHistorySnapshot;
import com.chargebolt.ezreal.response.tenant.TenantAgentInfoResponse;
import com.chargebolt.fund.common.enums.BalanceTypeEnum;
import com.chargebolt.dao.pg.model.OrderDataStatisticsModel;
import com.chargebolt.dao.pg.model.ShopOrderStatisticsDO;
import com.chargebolt.service.OrderDataStatisticsService;
import com.chargebolt.service.OrderHistorySnapshotService;
import com.chargebolt.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import so.dian.hermes.client.pojo.enums.OrderStatusEnum;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.talos.biz.service.TalosShopService;
import so.dian.talos.pojo.entity.ShopDO;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderSnapshotTask.java, v 1.0 2023-12-25 10:09 AM Exp $
 */
@Component
@Slf4j
public class OrderSnapshotTask {
    private static final String DAILY_SNAPSHOT_DATA_TASK_LOCK = "__CACHE_DAILY_SNAPSHOT_DATA_TASK_LOCK";
    private final OrderHistorySnapshotService orderHistorySnapshotService;
    private final OrderDataStatisticsService orderDataStatisticsService;
    private final RedissonClient redissonClient;
    private final TalosShopService talosShopService;
    private final PercentageDetailService percentageDetailService;
    private final TenantService tenantService;
    public OrderSnapshotTask(ObjectProvider<OrderHistorySnapshotService> orderHistorySnapshotServiceProvider,
                             ObjectProvider<OrderDataStatisticsService> orderDataStatisticsServiceProvider,
                             ObjectProvider<RedissonClient> redissonClientProvider,
                             ObjectProvider<TalosShopService> talosShopServiceProvider,
                             ObjectProvider<PercentageDetailService> percentageDetailServiceProvider,
                             ObjectProvider<TenantService> tenantServiceProvider
                             ) {
        this.orderHistorySnapshotService = orderHistorySnapshotServiceProvider.getIfUnique();
        this.orderDataStatisticsService= orderDataStatisticsServiceProvider.getIfUnique();
        this.redissonClient= redissonClientProvider.getIfUnique();
        this.talosShopService= talosShopServiceProvider.getIfUnique();
        this.percentageDetailService = percentageDetailServiceProvider.getIfUnique();
        this.tenantService= tenantServiceProvider.getIfUnique();
    }
    @Scheduled(cron = "0 0 8 * * ?")
    public void task(){
        this.dailyOrderDataSnapshot(null);
    }

    /**
     * 订单快照数据，只处理最后一级产生订单的门店和商户
     *
     * @param time
     */
    public void dailyOrderDataSnapshot(Date time) {
        log.info("开始执行每日订单数据快照");

        RLock lock = redissonClient.getLock(DAILY_SNAPSHOT_DATA_TASK_LOCK);
        try{
            if (lock.tryLock()) {
                Date startTime=null;
                Date endTime=null;
                // 不传时间，默认为-1天
                if(Objects.isNull(time)){
                    startTime= new DateBuild().addToDay(-1).start().toDate();
                    endTime= new DateBuild().addToDay(-1).end().toDate();
                }else{
                    startTime= new DateBuild(time).start().toDate();
                    endTime= new DateBuild(time).end().toDate();
                }
                // 获取每日有订单的门店数
                List<Long> shopIds= orderDataStatisticsService.getShopIds(startTime, endTime);
                if(CollectionUtils.isEmpty(shopIds)){
                    log.info("没有订单");
                    return;
                }
                for(Long shopId:shopIds){
                    TenantAgentInfoResponse tenantAgentInfoResponse= tenantService.getTopTenantInfoByShopId(shopId);
                    OrderHistorySnapshot orderHistorySnapshot= new OrderHistorySnapshot();
                    orderHistorySnapshot.setCurrency(tenantAgentInfoResponse.getCurrencyCode());
                    orderHistorySnapshot.setOrderDate(Long.valueOf(new DateBuild(startTime).formatter(DateBuild.SIMPLE_DATE)));
                    // 获取门店、门店负责人、商户信息
                    ShopDO shopDO=talosShopService.queryById(shopId);
                    // 未绑定门店、商户的设备，放到默认门店和商户
                    if(Objects.isNull(shopDO)){
                        orderHistorySnapshot.setShopId(0L);
                        orderHistorySnapshot.setMerchantId(0L);
                        orderHistorySnapshot.setSellerId(0L);
                    }else{
                        orderHistorySnapshot.setShopId(shopDO.getId());
                        orderHistorySnapshot.setMerchantId(Objects.isNull(shopDO.getMerchantId())?0:shopDO.getMerchantId());
                        orderHistorySnapshot.setSellerId(shopDO.getSellerId());
                        orderHistorySnapshot.setAgentId(tenantAgentInfoResponse.getAgentId());
                    }
                    OrderDataStatisticsModel queryCountModel= new OrderDataStatisticsModel();
                    queryCountModel.setStartCreateTime(startTime);
                    queryCountModel.setEndCreateTime(endTime);
                    queryCountModel.setShopId(shopId);
                    // 条件多次反复使用，修改注意条件组合
                    // 订单总数
                    log.info("订单总数查询:{}", JsonUtil.beanToJson(queryCountModel));
                    ShopOrderStatisticsDO orderCount= orderDataStatisticsService.getOrderCountAndAmount(queryCountModel);
                    orderHistorySnapshot.setOrderCount(orderCount.getOrderCount());

                    queryCountModel.setOrderStatus(OrderStatusEnum.PAID.getCode());
                    // 成功订单数
                    log.info("成功订单数查询:{}", JsonUtil.beanToJson(queryCountModel));
                    ShopOrderStatisticsDO successOrderCount= orderDataStatisticsService.getOrderCountAndAmount(queryCountModel);
                    orderHistorySnapshot.setSuccessOrderCount(successOrderCount.getOrderCount());
                    queryCountModel.setPayOrderGT0(Boolean.TRUE);
                    // 支付金额>0订单
                    log.info("支付金额>0单数查询:{}", JsonUtil.beanToJson(queryCountModel));
                    ShopOrderStatisticsDO payCount= orderDataStatisticsService.getOrderCountAndAmount(queryCountModel);
                    orderHistorySnapshot.setPayCount(payCount.getOrderCount());
                    if(Objects.isNull(payCount.getPayAmount())){
                        orderHistorySnapshot.setPayAmount(0L);
                    }else{
                        orderHistorySnapshot.setPayAmount(payCount.getPayAmount());
                    }

                    queryCountModel.setPayOrderGT0(Boolean.FALSE);
                    // 0元订单
                    log.info("0元订单数查询:{}", JsonUtil.beanToJson(queryCountModel));
                    ShopOrderStatisticsDO zeroOrderCount= orderDataStatisticsService.getOrderCountAndAmount(queryCountModel);
                    orderHistorySnapshot.setZeroOrderCount(zeroOrderCount.getOrderCount());
                    // 清除0元订单条件
                    queryCountModel.setPayOrderGT0(null);

                    // 退款订单
                    queryCountModel.setOrderStatus(OrderStatusEnum.REFUND.getCode());
                    log.info("退款订单数查询:{}", JsonUtil.beanToJson(queryCountModel));
                    ShopOrderStatisticsDO refundCount= orderDataStatisticsService.getOrderCountAndAmount(queryCountModel);
                    orderHistorySnapshot.setRefundCount(refundCount.getOrderCount());
                    if(Objects.isNull(refundCount.getRefundAmount())){
                        orderHistorySnapshot.setRefundAmount(0L);
                    }else{
                        orderHistorySnapshot.setRefundAmount(orderCount.getRefundAmount());
                    }

                    // 封顶价订单
                    queryCountModel.setOrderStatus(OrderStatusEnum.PAID.getCode());
                    queryCountModel.setCappingOrder(Boolean.TRUE);
                    log.info("封顶价订单数查询:{}", JsonUtil.beanToJson(queryCountModel));
                    ShopOrderStatisticsDO cappingCount= orderDataStatisticsService.getOrderCountAndAmount(queryCountModel);
                    orderHistorySnapshot.setCappingCount(cappingCount.getOrderCount());
                    if(Objects.isNull(cappingCount.getPayAmount())){
                        orderHistorySnapshot.setCappingAmount(0L);
                    }else{
                        orderHistorySnapshot.setCappingAmount(cappingCount.getPayAmount());
                    }

                    // 分成订单
                    PercentageDetail queryPercentageDetail= new PercentageDetail();
                    queryPercentageDetail.setShopId(shopId);
                    queryPercentageDetail.setSellerId(shopDO.getSellerId());
                    queryPercentageDetail.setMerchantId(shopDO.getMerchantId());
                    queryPercentageDetail.setMerchantType(MerchantTypeEnum.MERCHANT.getId());
                    queryPercentageDetail.setStartBizTime(startTime);
                    queryPercentageDetail.setEndBizTime(endTime);
                    queryPercentageDetail.setBizType(BalanceTypeEnum.INCOME.getId());
                    log.info("分成订单统计查询:{}", JsonUtil.beanToJson(queryPercentageDetail));
                    DivideStatisticsResponse divideStatistics= percentageDetailService.divideStatistics(queryPercentageDetail);
                    if(Objects.isNull(divideStatistics)){
                        orderHistorySnapshot.setDivideOrderCount(0);
                        orderHistorySnapshot.setDivideOrderAmount(0L);
                    }else{
                        if(Objects.isNull(divideStatistics.getDivideAmount())){
                            orderHistorySnapshot.setDivideOrderAmount(0L);
                        }else{
                            orderHistorySnapshot.setDivideOrderAmount(divideStatistics.getDivideAmount());
                        }
                        orderHistorySnapshot.setDivideOrderCount(divideStatistics.getDivideCount());
                    }

                    // 查询快照数据是否存在
                    OrderHistorySnapshot queryOrderHistorySnapshot= new OrderHistorySnapshot();
                    queryOrderHistorySnapshot.setShopId(shopId);
                    queryOrderHistorySnapshot.setMerchantId(shopDO.getMerchantId());
                    queryOrderHistorySnapshot.setSellerId(shopDO.getSellerId());
                    queryOrderHistorySnapshot.setAgentId(shopDO.getAgentId());
                    queryOrderHistorySnapshot.setOrderDate(Long.valueOf(new DateBuild(startTime).formatter(DateBuild.SIMPLE_DATE)));
                    queryOrderHistorySnapshot= orderHistorySnapshotService.getRecord(queryOrderHistorySnapshot);
                    if(Objects.isNull(queryOrderHistorySnapshot)){
                        orderHistorySnapshotService.saveRecord(orderHistorySnapshot);
                    }else{
                        orderHistorySnapshot.setId(queryOrderHistorySnapshot.getId());
                        BeanUtils.copyProperties(orderHistorySnapshot, queryOrderHistorySnapshot);
                        orderHistorySnapshotService.updateRecord(queryOrderHistorySnapshot);
                    }
                }

            }else{
                log.info("每日订单数据快照任务正在执行中,请勿重复执行");
            }
        }catch (Exception e) {
            log.error("每日订单数据快照任务异常", e);
        }finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("执行每日订单数据快照完成");
    }

}