<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.dao.statistics.rds.ShopMerchantDeviceStatisticsDAO">
<!-- FIXME 有跨库查询-->

  <!-- 商户列表 -->
  <select id="dataMerchantDetailList" parameterType="com.chargebolt.dao.statistics.rds.model.MerchantDetailListSearchModel"
          resultType="com.chargebolt.dao.statistics.rds.dto.MerchantDetailDTO">
    select m.id, m.name as merchantName, m.contact_name as contactName, m.contact_mobile as contactMobile,ou.name as sellerName,
           m.is_open as openDivide,t1.current_rate as rate, m.create_time as merchantCreateTime, t1.gmt_create as openDivideStamp
    from cb_talos.merchant m
           left join cb_talos.merchant_user_relation mur on m.id = mur.merchant_id
           left join cb_aeacus.oss_user ou on ou.id= mur.user_id
           left join (SELECT mch.id as mchId,percentage.current_rate,percentage.gmt_create
                      FROM cb_talos.merchant mch
                             INNER JOIN cb_icarus.percentage_record percentage
                                        ON mch.id = percentage.merchant_id
                                          AND percentage.id = (SELECT MAX(id) FROM cb_icarus.percentage_record WHERE merchant_id = mch.id)
                      where mch.is_open=1 and mch.deleted=0) t1 on t1.mchId= m.id
    where m.deleted=0
    <if test="sellerId!= null">
      and m.id in (select s1.merchant_id from cb_talos.merchant_user_relation s1 where s1.user_id= #{sellerId})
    </if>
    <if test="sellerIds != null and sellerIds.size() > 0">
      and m.id in (select s1.merchant_id from cb_talos.merchant_user_relation s1 where s1.user_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
      )
    </if>

    <if test="openDivide!= null">
      and m.is_open= #{openDivide}
    </if>

    <if test="startCreateTime != null and endCreateTime != null" >
      <![CDATA[
            and m.create_time>=#{startCreateTime} and m.create_time<=#{endCreateTime}
        ]]>
    </if>

    <if test="startPercentageStamp != null and endPercentageStamp != null" >
      <![CDATA[
            and t1.gmt_create>=#{startPercentageStamp} and t1.gmt_create<=#{endPercentageStamp}
        ]]>
    </if>
  </select>

  <select id="dataDeviceDetailList" parameterType="com.chargebolt.dao.statistics.rds.model.DeviceDetailListSearchModel"
          resultType="com.chargebolt.dao.statistics.rds.dto.DeviceDetailDTO">
    select d.device_no as deviceNo, di.name as deviceName,d.online_status as onlineState,d.storage_type as storageType, s.name as shopName,
    ou.name as sellerName, d.update_time as lastUpdateTime
    from cb_demeter.device d
    left join cb_demeter.device_info di on di.id= d.device_info_id
    left join cb_talos.shop s on s.id= d.storage_id
    left join cb_aeacus.oss_user ou on ou.id= s.seller_id

    where d.deleted= 0

    <if test="sellerId!= null">
      and s.seller_id= #{sellerId}
    </if>
    <if test="sellerIds != null and sellerIds.size() > 0">
      and s.seller_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>

    <if test="shopId!= null">
      and s.id= #{shopId}
    </if>
    <if test="shopIds != null and shopIds.size() > 0">
      and s.id in
      <foreach collection="shopIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>

    <if test="onlineState!= null">
      and d.online_status= #{onlineState}
    </if>

    <if test="storageTypes!= null">
      and d.storage_type in
      <foreach collection="storageTypes" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>

    <if test="deviceInfoIds != null" >
      and d.device_info_id in
      <foreach collection="deviceInfoIds" item="deviceInfoId" separator="," open="(" close=")">
        #{deviceInfoId}
      </foreach>
    </if>
    <if test="startCreateTime != null and endCreateTime != null" >
      <![CDATA[
            and d.create_time>=#{startCreateTime} and d.create_time<=#{endCreateTime}
        ]]>
    </if>

    <if test="startInstallTime != null and endInstallTime != null" >
      <![CDATA[
            and s.install_time>=#{startInstallTime} and s.install_time<=#{endInstallTime}
        ]]>
    </if>

  </select>

<!--  <select id="dataDeviceRecoveryDetailList" parameterType="com.chargebolt.dao.statistics.rds.model.DeviceDetailListSearchModel"-->
<!--          resultType="com.chargebolt.dao.statistics.rds.dto.DeviceDetailDTO">-->
<!--    select d.device_no as deviceNo, di.name as deviceName,d.online_status as onlineState,d.storage_type as storageType, s.name as shopName,-->
<!--    ou.name as sellerName, d.update_time as lastUpdateTime, sdo2.recoveryTime  as recoveryTime-->
<!--    FROM cb_demeter.device d-->
<!--    LEFT JOIN cb_demeter.device_info di ON di.id = d.device_info_id-->
<!--    LEFT JOIN cb_talos.shop s ON s.id = d.storage_id-->
<!--    LEFT JOIN cb_aeacus.oss_user ou ON ou.id = s.seller_id-->
<!--    LEFT JOIN (-->
<!--    SELECT device_no, max(create_time) as recoveryTime-->
<!--    FROM-->
<!--    cb_demeter.storage_device_operate sdo1-->
<!--    WHERE-->
<!--    sdo1.operate_type = 2-->
<!--    <if test="sellerId!= null">-->
<!--    AND sdo1.account_id = #{sellerId}-->
<!--    </if>-->
<!--    <if test="shopId!= null">-->
<!--      and sdo1.shop_id= #{shopId}-->
<!--    </if>-->
<!--    <if test="startRecoveryTime != null and endRecoveryTime != null" >-->
<!--      <![CDATA[-->
<!--            and sdo1.create_time>=#{startRecoveryTime} and sdo1.create_time<=#{endRecoveryTime}-->
<!--        ]]>-->
<!--    </if>-->
<!--    group by sdo1.device_no-->
<!--    ) sdo2 ON d.device_no = sdo2.device_no-->
<!--    WHERE d.deleted = 0 and sdo2.device_no = d.device_no-->

<!--    <if test="onlineState!= null">-->
<!--      and d.online_status= #{onlineState}-->
<!--    </if>-->
<!--    <if test="startRecoveryTime != null and endRecoveryTime != null" >-->
<!--      <![CDATA[-->
<!--            and sdo2.recoveryTime>=#{startRecoveryTime} and sdo2.recoveryTime<=#{endRecoveryTime}-->
<!--        ]]>-->
<!--    </if>-->
<!--  </select>-->

  <!-- 门店统计，截止到当前时间 -->
  <select id="shopAllSummary" parameterType="com.chargebolt.dao.statistics.rds.model.ShopSearchModel"
          resultType="com.chargebolt.dao.statistics.rds.dto.ShopSummaryDTO">
    SELECT
    COALESCE(SUM(shopCount), 0) AS shopCount,
    COALESCE(SUM(merchantCount), 0) AS merchantCount,
    COALESCE(SUM(percentageCount), 0) AS percentageCount
    FROM (SELECT seller_id, sum(shopCount) as shopCount, sum(merchantCount) as merchantCount, sum(percentageCount) as percentageCount
    FROM
    (
    SELECT seller_id,  shopCount, 0 AS merchantCount, 0 AS percentageCount
    FROM
    (
    SELECT seller_id, COALESCE(COUNT(1), 0) AS shopCount
    FROM cb_talos.shop
    WHERE deleted = 0
    and status = 2
    <if test="sellerIds != null and sellerIds.size() > 0">
      and seller_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    GROUP BY seller_id
    ) AS t2

    UNION ALL

    SELECT seller_id,0 AS shopCount, merchantCount, 0 AS percentageCount
    FROM
    (
    SELECT mchr.user_id as seller_id, COALESCE(COUNT(DISTINCT m.id), 0) AS merchantCount
    FROM cb_talos.merchant m
    left join cb_talos.merchant_user_relation mchr on m.id = mchr.merchant_id
    where m.deleted = 0
    <if test="sellerIds != null and sellerIds.size() > 0">
      and mchr.user_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    GROUP BY mchr.user_id
    ) AS t3

    UNION ALL

    SELECT seller_id, 0 AS shopCount, 0 AS merchantCount, percentageCount
    FROM
    (
    SELECT s5.seller_id,
    COALESCE(COUNT(DISTINCT s5.merchant_id), 0) AS percentageCount
    FROM
    (
    SELECT DISTINCT s4.user_id seller_id, s4.merchant_id, tt1.currentRate
    FROM  cb_talos.merchant_user_relation s4
    RIGHT JOIN
    (
    SELECT tt.id AS merchantId, tt.current_rate AS currentRate, tt.gmt_create AS createStamp
    FROM
    (
    SELECT mch.id, percentage.current_rate, percentage.gmt_create
    FROM cb_talos.merchant mch
    INNER JOIN cb_icarus.percentage_record percentage ON mch.id = percentage.merchant_id
    AND percentage.id = (SELECT MAX(id) FROM cb_icarus.percentage_record WHERE merchant_id = mch.id AND deleted = 0)
    WHERE mch.is_open = 1 AND mch.deleted = 0

    ) tt
    ) tt1 ON tt1.merchantId = s4.merchant_id
    WHERE s4.deleted = 0 AND s4.merchant_id IS NOT NULL
    <if test="sellerIds != null and sellerIds.size() > 0">
      and s4.user_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    ) s5  GROUP BY s5.seller_id
    ) AS t4
    ) AS aggregated_data
    where seller_id is not null
    GROUP BY seller_id) dd
  </select>

  <select id="shopSummaryBySeller" parameterType="com.chargebolt.dao.statistics.rds.model.ShopSearchModel"
          resultType="com.chargebolt.dao.statistics.rds.dto.ShopSummaryDTO">
    SELECT seller_id as sellerId,
           COALESCE(SUM(shopCount), 0) AS shopCount,
           COALESCE(SUM(merchantCount), 0) AS merchantCount,
           COALESCE(SUM(percentageCount), 0) AS percentageCount
    FROM (SELECT seller_id, sum(shopCount) as shopCount, sum(merchantCount) as merchantCount, sum(percentageCount) as percentageCount
    FROM
          (
        SELECT seller_id,  shopCount, 0 AS merchantCount, 0 AS percentageCount
        FROM
          (
            SELECT seller_id, COALESCE(COUNT(1), 0) AS shopCount
            FROM cb_talos.shop
            WHERE deleted = 0
              and status = 2
    <if test="sellerIds != null and sellerIds.size() > 0">
      and seller_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
        GROUP BY seller_id
          ) AS t2

        UNION ALL

        SELECT seller_id,0 AS shopCount, merchantCount, 0 AS percentageCount
        FROM
          (
            SELECT mchr.user_id as seller_id, COALESCE(COUNT(DISTINCT m.id), 0) AS merchantCount
            FROM cb_talos.merchant m
            left join cb_talos.merchant_user_relation mchr on m.id = mchr.merchant_id
            where m.deleted = 0
    <if test="sellerIds != null and sellerIds.size() > 0">
      and mchr.user_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
        GROUP BY mchr.user_id
          ) AS t3

        UNION ALL

        SELECT seller_id, 0 AS shopCount, 0 AS merchantCount, percentageCount
        FROM
          (
    SELECT s5.seller_id,
    COALESCE(COUNT(DISTINCT s5.merchant_id), 0) AS percentageCount
    FROM
    (
    SELECT DISTINCT s4.user_id seller_id, s4.merchant_id, tt1.currentRate
    FROM  cb_talos.merchant_user_relation s4
    RIGHT JOIN
    (
    SELECT tt.id AS merchantId, tt.current_rate AS currentRate, tt.gmt_create AS createStamp
    FROM
    (
    SELECT mch.id, percentage.current_rate, percentage.gmt_create
    FROM cb_talos.merchant mch
    INNER JOIN cb_icarus.percentage_record percentage ON mch.id = percentage.merchant_id
    AND percentage.id = (SELECT MAX(id) FROM cb_icarus.percentage_record WHERE merchant_id = mch.id AND deleted = 0)
    WHERE mch.is_open = 1 AND mch.deleted = 0

    ) tt
    ) tt1 ON tt1.merchantId = s4.merchant_id
    WHERE s4.deleted = 0 AND s4.merchant_id IS NOT NULL
    <if test="sellerIds != null and sellerIds.size() > 0">
      and s4.user_id in
      <foreach collection="sellerIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    ) s5  GROUP BY s5.seller_id
          ) AS t4
      ) AS aggregated_data
    where seller_id is not null
    GROUP BY seller_id) dd group by dd.seller_id
  </select>

  <!-- 设备统计 -->
  <select id="deviceAllSellerSummary" parameterType="com.chargebolt.dao.statistics.rds.model.DeviceSummarySearchModel"
          resultType="com.chargebolt.dao.statistics.rds.dto.DeviceSummaryDTO">
    SELECT COALESCE(SUM(CASE WHEN d.device_info_id in ( 1,2,4,5,6 ) THEN 1 ELSE 0 END),0) AS deviceCount,
    COALESCE(SUM(CASE WHEN d.online_status = 1 and d.storage_type in(3,4) and d.device_info_id in
    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
     THEN 1 ELSE 0 END),0) AS onlineDeviceCount,
    COALESCE(SUM(CASE WHEN d.online_status = 0 and d.storage_type in(3,4) and d.device_info_id in
    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
     THEN 1 ELSE 0 END),0) AS offlineDeviceCount,
    COALESCE((
    select sum(t.recoveryCount) from(
    select account_id, count(distinct(device_no)) as recoveryCount from cb_demeter.storage_device_operate sdo
    where sdo.operate_type=2 and sdo.deleted=0
    <![CDATA[
      and sdo.create_time>= #{startTime} and sdo.create_time<=#{endTime}
       ]]>
      <if test="sellerId != null">
        and sdo.account_id=#{sellerId}
      </if>
    group by account_id) t
    ),0) as recoveryDeviceCount,
    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) and d.device_info_id in
    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
     THEN 1 ELSE 0 END),0) AS shopDeviceCount,
    COALESCE(SUM(CASE WHEN d.storage_type in(1,2) AND d.device_info_id IN (1,2,4,5,6) THEN 1 ELSE 0 END),0) AS warehouseDeviceCount,
    COALESCE(COUNT(DISTINCT CASE WHEN d.device_info_id in
    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    THEN d.id END),0) AS powerBankAllCount,
    COALESCE(SUM(CASE WHEN d.storage_type = 1 AND d.device_info_id in
    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">
    #{item}
  </foreach>
     THEN 1 ELSE 0 END),0) AS warehousePowerBankCount,
    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) AND d.device_info_id in
    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
     THEN 1 ELSE 0 END),0) AS marketPowerBankCount
    FROM cb_demeter.device d
    left join cb_talos.shop p on p.id= d.storage_id and p.deleted=0
    WHERE d.deleted=0
    <![CDATA[
    and d.create_time>= #{startTime} and d.create_time<=#{endTime}
     ]]>
    <if test="sellerId != null">
      and p.seller_id=#{sellerId}
    </if>
  </select>

  <select id="deviceAllShopSummary" parameterType="com.chargebolt.dao.statistics.rds.model.DeviceSummarySearchModel"
          resultType="com.chargebolt.dao.statistics.rds.dto.DeviceSummaryDTO">
    SELECT COALESCE(COUNT(1),0) AS deviceCount,
    COALESCE(SUM(CASE WHEN d.online_status = 1 and d.storage_type in(3,4) and d.device_info_id in
    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    THEN 1 ELSE 0 END),0) AS onlineDeviceCount,
    COALESCE(SUM(CASE WHEN d.online_status = 0 and d.storage_type in(3,4) and d.device_info_id in
    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    THEN 1 ELSE 0 END),0) AS offlineDeviceCount,
    COALESCE((
    select sum(t.recoveryCount) from(
    select shop_id, count(distinct(device_no)) as recoveryCount from cb_demeter.storage_device_operate sdo
    where sdo.operate_type=2 and sdo.deleted=0
    <![CDATA[
      and sdo.create_time>= #{startTime} and sdo.create_time<=#{endTime}
       ]]>
    <if test="sellerId != null">
      and sdo.account_id=#{sellerId}
    </if>
    group by shop_id) t
    ),0) as recoveryDeviceCount,
    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) and d.device_info_id in
    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    THEN 1 ELSE 0 END),0) AS shopDeviceCount,
    COALESCE(SUM(CASE WHEN d.storage_type in(1,2) AND d.device_info_id IN (1,2,4,5,6) THEN 1 ELSE 0 END),0) AS warehouseDeviceCount,
    COALESCE(COUNT(DISTINCT CASE WHEN d.device_info_id in
    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    THEN d.id END),0) AS powerBankAllCount,
    COALESCE(SUM(CASE WHEN d.storage_type = 1 AND d.device_info_id in
    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    THEN 1 ELSE 0 END),0) AS warehousePowerBankCount,
    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) AND d.device_info_id in
    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    THEN 1 ELSE 0 END),0) AS marketPowerBankCount
    FROM cb_demeter.device d
    left join cb_talos.shop p on p.id= d.storage_id and p.deleted=0
    WHERE d.deleted=0
    <![CDATA[
    and d.create_time>= #{startTime} and d.create_time<=#{endTime}
     ]]>
    <if test="sellerId != null">
      and p.seller_id=#{sellerId}
    </if>
  </select>

<!--  <select id="deviceSummaryByShop" parameterType="com.chargebolt.dao.statistics.rds.model.DeviceSummarySearchModel"-->
<!--          resultType="com.chargebolt.dao.statistics.rds.dto.DeviceSummaryDTO">-->
<!--    select * from (-->
<!--    SELECT p.id as shopId,-->
<!--    p.name as title,-->
<!--    COALESCE(SUM(CASE WHEN d.online_status = 1 and d.storage_type in(3,4) and d.device_info_id in-->
<!--    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--    THEN 1 ELSE 0 END),0) AS onlineDeviceCount,-->
<!--    COALESCE(SUM(CASE WHEN d.online_status = 0 and d.storage_type in(3,4) and d.device_info_id in-->
<!--    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--    THEN 1 ELSE 0 END),0) AS offlineDeviceCount,-->
<!--    COALESCE(tsdo.recoveryCount, 0) as recoveryDeviceCount,-->
<!--    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) and d.device_info_id in-->
<!--    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--     THEN 1 ELSE 0 END),0) AS shopDeviceCount,-->
<!--    COALESCE(COUNT(DISTINCT CASE WHEN d.device_info_id in-->
<!--    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--     THEN d.id END),0) AS powerBankAllCount,-->
<!--    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) AND d.device_info_id in-->
<!--    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--     THEN 1 ELSE 0 END),0) AS marketPowerBankCount-->
<!--    FROM cb_demeter.device d-->
<!--    left join cb_talos.shop p on p.id= d.storage_id and p.deleted=0-->
<!--    left join (select shop_id as shopId, count(distinct(device_no)) as recoveryCount from cb_demeter.storage_device_operate sdo-->
<!--    where sdo.operate_type=2 and sdo.deleted=0-->
<!--    <![CDATA[-->
<!--          and sdo.create_time>= #{startTime} and sdo.create_time<=#{endTime}-->
<!--           ]]>-->
<!--    <if test="sellerId != null">-->
<!--      and sdo.account_id=#{sellerId}-->
<!--    </if>-->
<!--    group by account_id) tsdo on tsdo.shopId= p.id-->
<!--    WHERE d.deleted=0-->
<!--    <![CDATA[-->
<!--    and d.create_time>= #{startTime} and d.create_time<=#{endTime}-->
<!--     ]]>-->
<!--    <if test="sellerId != null">-->
<!--      and p.seller_id=#{sellerId}-->
<!--    </if>-->
<!--    group by p.id) t-->
<!--  </select>-->

<!--  <select id="deviceSummaryBySeller" parameterType="com.chargebolt.dao.statistics.rds.model.DeviceSummarySearchModel"-->
<!--          resultType="com.chargebolt.dao.statistics.rds.dto.DeviceSummaryDTO">-->
<!--    select * from (-->
<!--    SELECT p.seller_id as sellerId,-->
<!--    COALESCE(SUM(CASE WHEN d.online_status = 1 and d.storage_type in(3,4) and d.device_info_id in-->
<!--    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--    THEN 1 ELSE 0 END),0) AS onlineDeviceCount,-->
<!--    COALESCE(SUM(CASE WHEN d.online_status = 0 and d.storage_type in(3,4) and d.device_info_id in-->
<!--    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--    THEN 1 ELSE 0 END),0) AS offlineDeviceCount,-->
<!--    COALESCE(tsdo.recoveryCount, 0) as recoveryDeviceCount,-->
<!--    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) and d.device_info_id in-->
<!--    <foreach collection="deviceTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--    THEN 1 ELSE 0 END),0) AS shopDeviceCount,-->
<!--    COALESCE(COUNT(DISTINCT CASE WHEN d.device_info_id in-->
<!--    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--    THEN d.id END),0) AS powerBankAllCount,-->
<!--    COALESCE(SUM(CASE WHEN d.storage_type in(3,4) AND d.device_info_id in-->
<!--    <foreach collection="boxTypeIds" item="item" separator="," open="(" close=")">-->
<!--      #{item}-->
<!--    </foreach>-->
<!--    THEN 1 ELSE 0 END),0) AS marketPowerBankCount-->
<!--    FROM cb_demeter.device d-->
<!--    left join cb_talos.shop p on p.id= d.storage_id and p.deleted=0-->
<!--    left join (select account_id as sellerId, count(distinct(device_no)) as recoveryCount from cb_demeter.storage_device_operate sdo-->
<!--    where sdo.operate_type=2 and sdo.deleted=0-->
<!--        <![CDATA[-->
<!--          and sdo.create_time>= #{startTime} and sdo.create_time<=#{endTime}-->
<!--           ]]>-->
<!--        <if test="sellerId != null">-->
<!--          and sdo.account_id=#{sellerId}-->
<!--        </if>-->
<!--    group by account_id) tsdo on tsdo.sellerId= p.seller_id-->
<!--    WHERE d.deleted=0-->
<!--    <![CDATA[-->
<!--    and d.create_time>= #{startTime} and d.create_time<=#{endTime}-->
<!--     ]]>-->
<!--    <if test="sellerId != null">-->
<!--      and p.seller_id=#{sellerId}-->
<!--    </if>-->
<!--    group by p.seller_id) t-->
<!--  </select>-->


</mapper>