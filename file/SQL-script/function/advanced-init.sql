create table aeacus.oss_group
(
    id          bigint auto_increment comment '主键'
        primary key,
    name        varchar(64)  default ''                not null comment '组名',
    type        tinyint      default 0                 not null comment '类型：1.权限',
    creator     varchar(32)  default ''                not null comment '创建人',
    create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updator     varchar(32)  default ''                not null comment '修改人',
    update_time datetime     default CURRENT_TIMESTAMP not null comment '更新时间',
    deleted     tinyint      default 0                 not null comment '归档标识',
    remark      varchar(256) default ''                not null comment '备注'
)
    comment 'oss组表' charset = utf8mb4;

create index idx_create_time
    on oss_group (create_time);

INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (1, 'Order List', 1, '', '2019-04-25 02:52:22', '', '2019-04-25 02:52:22', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (2, 'Role Management', 1, '', '2019-04-25 02:52:22', '', '2019-04-25 02:52:22', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (3, 'User Management', 1, '', '2019-04-25 02:52:25', '', '2019-04-25 02:52:25', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (4, 'Store Management', 1, '', '2019-05-27 14:31:10', '', '2019-05-27 14:31:15', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (5, 'Merchant Management', 1, '', '2019-05-27 14:31:36', '', '2019-05-27 14:31:41', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (6, 'Device', 1, '', '2019-06-18 04:25:23', '', '2019-06-18 04:25:23', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (7, 'Store Type', 1, '', '2019-06-20 08:55:04', '', '2019-06-20 08:55:04', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (8, 'Coupon Management', 1, '', '2019-11-20 07:49:30', '', '2019-11-20 07:49:30', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (9, 'Activity Management', 1, '', '2019-12-16 02:42:32', '', '2019-12-16 02:42:32', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (10, 'Billing Management', 1, '', '2020-11-17 17:00:00', '', '2020-11-17 17:00:00', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (11, 'Member Management', 1, '云谷', '2021-04-16 03:03:25', '', '2021-04-16 03:03:25', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (13, 'Capital Account', 1, '', '2023-11-13 17:48:24', '', '2023-11-13 17:48:24', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (14, 'Bill Management', 1, '', '2023-11-13 17:48:48', '', '2023-11-13 17:48:48', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (15, 'Sharing Details', 1, '', '2023-11-13 17:48:48', '', '2023-11-13 17:48:48', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (17, 'Data Panel', 1, '', '2023-12-06 16:54:00', '', '2023-12-06 16:54:10', 0, '');
INSERT INTO aeacus.oss_group (id, name, type, creator, create_time, updator, update_time, deleted, remark) VALUES (18, 'Base Config', 1, '', '2023-12-07 18:14:35', '', '2023-12-07 18:14:39', 0, '');



create table oss_icon
(
    id      int auto_increment
        primary key,
    title   varchar(20)   not null,
    img     varchar(200)  null,
    url     varchar(200)  null,
    action  varchar(50)   null,
    role_id int           not null,
    `order` int default 0 not null,
    type    int default 1 not null
)
    charset = utf8mb4;

INSERT INTO aeacus.oss_icon (id, title, img, url, action, role_id, `order`, type) VALUES (2, 'Maintenanc', 'icon-shebeijianxiu', null, 'DEVICE_REPAIRE', 0, 0, 2);
INSERT INTO aeacus.oss_icon (id, title, img, url, action, role_id, `order`, type) VALUES (3, 'New store', 'icon-xinjianmendian', '/sign/shop', 'SIGN_SHOP', 0, 0, 1);
INSERT INTO aeacus.oss_icon (id, title, img, url, action, role_id, `order`, type) VALUES (4, 'Store', 'icon-wodemendian', '/shop/preShopList', 'SELLER_SHOP', 0, 0, 1);
INSERT INTO aeacus.oss_icon (id, title, img, url, action, role_id, `order`, type) VALUES (5, 'Ownership', 'icon-mendianfenpei', '/allocate/shop', 'ALLOCATE_SHOP', 0, 0, 1);
INSERT INTO aeacus.oss_icon (id, title, img, url, action, role_id, `order`, type) VALUES (6, 'Device manage', 'icon-beijiankushebei', '/device-manage', 'DEVICE_MANAGE', 0, 0, 1);
INSERT INTO aeacus.oss_icon (id, title, img, url, action, role_id, `order`, type) VALUES (7, 'Merchant', 'icon-tuanduiguanli', '/merchant/list', 'MERCHANT_MANAGE', 0, 0, 1);




create table oss_permission
(
    id          int auto_increment
        primary key,
    create_time datetime               not null,
    update_time datetime               null,
    code        varchar(10)            not null,
    type        int                    not null,
    creator     int                    not null,
    updater     int                    null,
    img         varchar(500)           null,
    url         varchar(500)           null,
    description varchar(100)           null,
    group_id    bigint      default 0  not null comment '组id',
    rely_ids    varchar(64) default '' not null comment '依赖权限ids',
    constraint code_uindex
        unique (code)
)
    comment '权限表' charset = utf8mb4;

INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (0, '2019-04-25 02:52:22', null, '0', 1, 1, null, 'img', 'url', 'Super Permission', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (11, '2019-03-19 14:22:59', '2019-03-19 14:22:59', '10000', 1, 1, 1, 'img', 'url', 'Warehouse', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (12, '2019-03-19 14:22:59', '2019-03-19 14:22:59', '10001', 1, 1, 1, 'img', 'url', 'Device Install', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (13, '2019-03-19 14:22:59', '2019-03-19 14:22:59', '10002', 1, 1, 1, 'img', 'url', 'Device Maintenance', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (14, '2019-03-19 14:22:59', '2019-03-19 14:22:59', '10003', 1, 1, 1, 'img', 'url', 'Warehouse Scan', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (15, '2019-03-19 14:22:59', '2019-03-19 14:22:59', '10004', 1, 1, 1, 'img', 'url', 'Device Scan', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (16, '2019-03-20 03:47:06', '2019-03-20 03:47:08', '20001', 1, 1, 1, 'img', 'url', 'View', 1, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (18, '2019-04-25 02:52:35', null, '20002', 1, 1, null, 'img', 'url', 'Cancel Order', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (19, '2019-04-25 02:52:35', null, '20004', 1, 1, null, 'img', 'url', 'Stop Billing', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (20, '2019-04-25 02:52:35', null, '20005', 1, 1, null, 'img', 'url', 'End Billing', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (21, '2019-04-25 02:52:35', null, '20007', 1, 1, null, 'img', 'url', 'Modify the Price', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (22, '2019-04-25 02:52:35', null, '20008', 1, 1, null, 'img', 'url', 'Refund', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (23, '2019-04-25 02:52:47', null, '30101', 1, 1, null, 'img', 'url', 'View', 2, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (24, '2019-04-25 02:52:47', null, '30102', 1, 1, null, 'img', 'url', 'New Role', 2, '23');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (25, '2019-04-25 02:52:47', null, '30103', 1, 1, null, 'img', 'url', 'Edit', 2, '23');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (26, '2019-04-25 02:52:47', null, '30104', 1, 1, null, 'img', 'url', 'Delete', 2, '23');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (27, '2019-04-25 02:52:47', null, '30201', 1, 1, null, 'img', 'url', 'View', 3, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (28, '2019-04-25 02:52:47', null, '30202', 1, 1, null, 'img', 'url', 'New User', 3, '27');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (29, '2019-04-25 02:52:47', null, '30203', 1, 1, null, 'img', 'url', 'Edit User', 3, '27');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (30, '2019-04-25 02:52:47', null, '30204', 1, 1, null, 'img', 'url', 'Reset Password', 3, '27');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (31, '2019-04-25 02:52:47', null, '30205', 1, 1, null, 'img', 'url', 'Disable User', 3, '27');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (32, '2019-04-25 02:52:47', null, '30302', 1, 1, null, 'img', 'url', 'New Department', 3, '27');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (33, '2019-04-25 02:52:47', null, '30303', 1, 1, null, 'img', 'url', 'Edit Department', 3, '27');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (34, '2019-04-25 02:52:47', null, '30304', 1, 1, null, 'img', 'url', 'Delete Department', 3, '27');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (35, '2019-05-09 06:50:00', null, '20011', 1, 1, null, 'img', 'url', 'Order Export', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (36, '2019-04-19 17:23:56', null, '40001', 1, 1, null, 'img', 'url', 'View', 4, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (37, '2019-05-27 14:22:03', null, '40002', 1, 1, null, 'img', 'url', 'New Store', 4, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (38, '2019-05-27 14:23:13', null, '40003', 1, 1, null, 'img', 'url', 'Edit', 4, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (39, '2019-05-27 14:23:58', null, '40004', 1, 1, null, 'img', 'url', 'Delete', 4, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (40, '2019-05-27 14:24:31', null, '40005', 1, 1, null, 'img', 'url', 'Contract Termination/Renew Contract', 4, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (41, '2019-05-27 14:25:18', null, '40006', 1, 1, null, 'img', 'url', 'Relate Merchant', 5, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (42, '2019-05-27 14:26:34', null, '40007', 1, 1, null, 'img', 'url', 'Unbind  Merchant', 5, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (43, '2019-05-27 14:27:07', null, '40008', 1, 1, null, 'img', 'url', 'Edit Store Tips', 4, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (44, '2019-05-27 14:28:02', null, '60001', 1, 1, null, 'img', 'url', 'View', 5, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (45, '2019-05-27 14:29:10', null, '60002', 1, 1, null, 'img', 'url', 'New Merchant', 5, '44');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (46, '2019-05-27 14:30:00', null, '60003', 1, 1, null, 'img', 'url', 'Edit', 5, '44');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (47, '2019-05-27 14:30:25', null, '60004', 1, 1, null, 'img', 'url', 'Delete', 5, '44');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (48, '2019-06-18 04:26:13', null, '70001', 1, 1, null, 'img', 'url', 'Device List', 6, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (49, '2019-06-20 08:56:31', null, '80001', 1, 1, null, 'img', 'url', 'View', 7, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (50, '2019-06-20 08:58:18', null, '80002', 1, 1, null, 'img', 'url', 'New Type', 7, '49');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (51, '2019-06-20 08:58:18', null, '80003', 1, 1, null, 'img', 'url', 'Edit Type', 7, '49');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (52, '2019-06-20 08:58:19', null, '80004', 1, 1, null, 'img', 'url', 'Delete Type', 7, '49');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (53, '2019-07-12 02:42:55', null, '40009', 1, 1, null, 'img', 'url', 'Export', 4, '36');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (54, '2019-07-30 03:41:12', null, '70002', 1, 1, null, 'img', 'url', 'Device Log', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (55, '2019-11-20 07:50:35', null, '20009', 1, 1, null, 'img', 'url', 'Deposit List', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (56, '2019-11-20 07:50:36', null, '20010', 1, 1, null, 'img', 'url', 'Deposit Refund', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (57, '2019-11-20 07:51:44', null, '90001', 1, 1, null, 'img', 'url', 'View', 8, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (58, '2019-11-20 07:52:46', null, '90002', 1, 1, null, 'img', 'url', 'New Coupon', 8, '57');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (59, '2019-11-20 07:52:46', null, '90003', 1, 1, null, 'img', 'url', 'Edit Coupon', 8, '57');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (60, '2019-11-20 07:52:47', null, '90004', 1, 1, null, 'img', 'url', 'New Coupon Issue', 8, '57');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (68, '2019-12-16 02:42:32', null, '11001', 1, 1, null, 'img', 'url', 'View', 9, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (69, '2019-12-16 02:42:32', null, '11002', 1, 1, null, 'img', 'url', 'New Activity', 9, '61');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (70, '2019-12-16 02:42:32', null, '11003', 1, 1, null, 'img', 'url', 'Edit Activity', 9, '61');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (71, '2019-12-16 02:42:32', null, '11004', 1, 1, null, 'img', 'url', 'New Activity Launch', 9, '61');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (72, '2019-12-16 02:42:32', null, '11005', 1, 1, null, 'img', 'url', 'Edit Activity Launch', 9, '61');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (73, '2019-12-16 02:42:32', null, '11006', 1, 1, null, 'img', 'url', 'Online & Offline', 9, '61');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (74, '2019-12-16 02:42:32', null, '11007', 1, 1, null, 'img', 'url', 'Adjust Activity Launch Position', 9, '61');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (75, '2019-04-16 09:51:58', null, '20012', 1, 1, null, 'img', 'url', 'Pre-authorization List', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (76, '2019-04-16 09:51:58', null, '20013', 1, 1, null, 'img', 'url', 'Pre-authorization Unfreeze', 1, '16');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (77, '2020-11-17 17:00:00', null, '12001', 1, 1, null, 'img', 'url', 'Price Adjustment', 10, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (78, '2021-04-16 11:04:09', null, '50001', 1, 1, null, 'img', 'url', 'View', 11, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (79, '2021-04-16 11:05:21', null, '50002', 1, 1, null, 'img', 'url', 'Edit', 11, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (80, '2021-04-16 11:06:24', null, '50003', 1, 1, null, 'img', 'url', 'New Type', 11, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (81, '2021-04-16 11:07:00', null, '50004', 1, 1, null, 'img', 'url', 'Export', 11, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (82, '2021-04-16 11:07:43', null, '50005', 1, 1, null, 'img', 'url', 'Refund', 11, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (83, '2023-08-15 17:12:50', null, '10005', 1, 1, null, 'img', 'url', 'Device Detection', 0, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (84, '2023-11-02 10:33:40', null, '60005', 1, 1, null, 'img', 'url', 'Reset Percentage', 5, '44');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (85, '2023-11-02 10:34:01', null, '60006', 1, 1, null, 'img', 'url', 'Close Share', 5, '44');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (86, '2023-11-02 10:33:53', null, '60007', 1, 1, null, 'img', 'url', 'Open Share', 5, '44');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (87, '2023-11-02 10:34:03', null, '60008', 1, 1, null, 'img', 'url', 'View Details', 5, '44');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (88, '2023-11-13 17:51:38', null, '130002', 1, 1, null, 'img', 'url', 'View Details', 13, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (89, '2023-11-13 17:51:56', null, '140002', 1, 1, null, 'img', 'url', 'Add Manual Bill', 14, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (90, '2023-11-13 17:51:58', null, '140003', 1, 1, null, 'img', 'url', 'View Details', 14, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (91, '2023-11-14 16:34:17', null, '140001', 1, 1, null, 'img', 'url', 'Bill Management', 14, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (92, '2023-11-14 16:36:11', null, '130001', 1, 1, null, 'img', 'url', 'Capital Account', 13, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (93, '2023-11-14 16:36:11', null, '150001', 1, 1, null, 'img', 'url', 'Sharing Details', 15, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (94, '2023-11-14 16:36:11', null, '170001', 1, 1, null, 'img', 'url', 'Real time data', 17, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (95, '2023-11-14 16:36:11', null, '170002', 1, 1, null, 'img', 'url', 'Offline data', 17, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (96, '2023-12-07 18:18:22', '2023-12-07 18:18:29', '180001', 1, 1, null, 'img', 'url', 'Them config', 18, '');
INSERT INTO aeacus.oss_permission (id, create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (101, '2023-11-02 10:34:03', null, '60009', 1, 1, null, 'img', 'url', 'Redistribution', 5, '44');
