CREATE TABLE cb_lhc.user_account_agent_mapping (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        user_id BIGINT NOT NULL COMMENT '用户ID',
        agent_id BIGINT NOT NULL COMMENT '代理商ID',
        account_id BIGINT NOT NULL COMMENT '押金账户ID',
        currency_code VARCHAR(6) NOT NULL COMMENT '币种代码',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_user_id (user_id),
        KEY idx_agent_id (agent_id),
        KEY idx_account_id (account_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '用户押金账户关联代理商表';

CREATE TABLE cb_lhc.user_balance_agent_mapping (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        user_id BIGINT NOT NULL COMMENT '用户ID',
        agent_id BIGINT NOT NULL COMMENT '代理商ID',
        account_id BIGINT NOT NULL COMMENT '押金账户ID',
        currency_code VARCHAR(6) NOT NULL COMMENT '币种代码',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_user_id (user_id),
        KEY idx_agent_id (agent_id),
        KEY idx_account_id (account_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '用户押金流水关联代理商表';



# cb_icarus
# 国家和地区
CREATE TABLE cb_icarus.regionals (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        name VARCHAR(100) NOT NULL COMMENT '名称 没有对应语种的默认显示',
        default_lang VARCHAR(10) NOT NULL COMMENT '官方使用语言',
        regional_code VARCHAR(10) NOT NULL COMMENT '国家地区代码',
        phone_code VARCHAR(20) NOT NULL COMMENT '国家地区区号',
        currency_code VARCHAR(6) NOT NULL COMMENT '币种代码',
        regional_emoji VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '国旗emoji图标',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '国家和地区(包含区号，币种)';

# cb_talos
CREATE TABLE cb_talos.agent_base_config (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        agent_id BIGINT NOT NULL COMMENT '代理商ID',
        service_fee_rate double NOT NULL COMMENT '服务费费率，百分比值',
        regional_code VARCHAR(10) NOT NULL COMMENT '国家地区代码',
        default_lang VARCHAR(10) NOT NULL COMMENT '官方使用语言，BCP 47值',
        currency_code VARCHAR(6) NOT NULL COMMENT '币种代码',
        regional_currency VARCHAR(6) NOT NULL COMMENT '币种所属地区',
        zone_utc VARCHAR(10) NOT NULL COMMENT '时区 UTC',
        date_format VARCHAR(30) NOT NULL COMMENT '常用时间格式',
        sys_version BIGINT NOT NULL COMMENT '软件版本ID',
        agent_level TINYINT NOT NULL DEFAULT '1' COMMENT '代理商层级，越高层级越深',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_agent_id (agent_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '代理商基础配置表';

CREATE TABLE cb_talos.tenant_agent_related (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        agent_id BIGINT NOT NULL COMMENT '代理商ID',
        tenant_id BIGINT NOT NULL COMMENT '租户ID',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '租户、代理商关系表';

CREATE TABLE cb_talos.tenant (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        name VARCHAR(200) NOT NULL COMMENT '租户名称',
        source_type TINYINT NOT NULL DEFAULT '1' COMMENT '来源：1平台签约 2多级代理',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '租户表（大部分数据依赖关联的agent）';

CREATE TABLE cb_talos.tenant_ext_config (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        config_key VARCHAR(50) NOT NULL COMMENT '配置key',
        config_value VARCHAR(1000) NOT NULL COMMENT '配置值，复杂对象使用json字符串，注意长度',
        tenant_id BIGINT NOT NULL COMMENT '租户ID',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_tenant_id (tenant_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '租户表扩展信息表，表记录信息为辅助扩展，缺少不影响系统正常运行';

CREATE TABLE cb_talos.payment_method (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        payment_method VARCHAR(1000) NOT NULL COMMENT '支付方式',
        tenant_id BIGINT NOT NULL COMMENT '租户ID',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_tenant_id (tenant_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '支付方式，用于记录租户需要使用的支付方式和排序';

CREATE TABLE cb_talos.protocol (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        title VARCHAR(200) NOT NULL COMMENT '协议标题',
        code VARCHAR(20) NOT NULL COMMENT '协议code，对应有协议类型',
        client_type TINYINT NOT NULL COMMENT '适配端 1 H5',
        content MEDIUMTEXT COMMENT '协议内容，富文本',
        lang VARCHAR(10) NOT NULL COMMENT '语言，BCP 47值',
        tenant_id BIGINT NOT NULL COMMENT '租户ID',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_tenant_id (tenant_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '协议';


CREATE TABLE cb_talos.currency_exchange (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        client_code VARCHAR(20) NOT NULL COMMENT '端code，微信、支付宝',
        current_currency VARCHAR(6) NOT NULL COMMENT '当前币种',
        target_currency VARCHAR(6) NOT NULL COMMENT '目标币种',
        regional_currency VARCHAR(6) NOT NULL COMMENT '目标币种所属地区',
        exchange_rate double NOT NULL COMMENT '币种兑换倍率',
        tenant_id BIGINT NOT NULL COMMENT '租户ID',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_tenant_id (tenant_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '币种汇率，一般是对应支付端转换';




# cb_aeacus
CREATE TABLE cb_aeacus.role_sources_mapping (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        role_id BIGINT NOT NULL COMMENT '角色ID',
        resource_id BIGINT NOT NULL COMMENT '资源ID',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_role_id (role_id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '角色、资源关系表';

CREATE TABLE cb_aeacus.system_resources (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        parent_code VARCHAR(128) NOT NULL COMMENT '父级资源code',
        code VARCHAR(128) NOT NULL COMMENT '资源code',
        name VARCHAR(30) NOT NULL COMMENT '资源名称',
        resource_type TINYINT NOT NULL COMMENT '资源类型 1菜单 2功能',
        icon VARCHAR(100) COMMENT '资源logo',
        url VARCHAR(200) COMMENT '页面地址',
        notes VARCHAR(500) COMMENT '备注信息',
        sort TINYINT NOT NULL COMMENT '排序',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_code (code),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '系统资源表';

CREATE TABLE cb_aeacus.version_template (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        name VARCHAR(30) NOT NULL COMMENT '版本模板名称',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '版本模板表';

CREATE TABLE cb_aeacus.template_sources_mapping (
        id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
        template_id BIGINT NOT NULL COMMENT '版本模板ID',
        resource_id BIGINT NOT NULL COMMENT '资源ID',
        deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
        gmt_create BIGINT NOT NULL COMMENT '创建时间',
        gmt_update BIGINT NOT NULL COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY idx_gmt_create (gmt_create),
        KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '角色、资源关系表';

-- 增加字段
ALTER TABLE cb_talos.shop_type ADD agent_id BIGINT default 0 COMMENT '代理商ID';

ALTER TABLE cb_demeter.device
ADD COLUMN agent_id BIGINT NOT NULL COMMENT '代理商ID',
ADD COLUMN seller_id BIGINT NOT NULL COMMENT '员工ID';


-- 2024-12-10 新增脚本
