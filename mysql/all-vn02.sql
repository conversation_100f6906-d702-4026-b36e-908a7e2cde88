-- dml
-- fund_account
ALTER TABLE cb_account.fund_account ADD merchant_type TINYINT(1) COMMENT '商户类型 1商家 2代理商';
 
-- fund_balance
ALTER TABLE cb_account.fund_balance ADD merchant_type TINYINT(1) COMMENT '商户类型 1商家 2代理商';
-- oss_user
ALTER TABLE cb_aeacus.oss_user ADD agent_id BIGINT COMMENT '代理商ID';
CREATE INDEX idx_agent_id ON cb_aeacus.oss_user(agent_id);
 
-- oss_department
ALTER TABLE cb_aeacus.oss_department ADD agent_id BIGINT COMMENT '代理商ID';
CREATE INDEX idx_agent_id ON cb_aeacus.oss_department(agent_id);
 
-- oss_role
ALTER TABLE cb_aeacus.oss_role ADD agent_id BIGINT COMMENT '代理商ID';
CREATE INDEX idx_agent_id ON cb_aeacus.oss_role(agent_id);
 
-- order_history_snapshot
ALTER TABLE cb_icarus.order_history_snapshot ADD agent_id BIGINT COMMENT '代理商ID';
CREATE INDEX idx_agent_id ON cb_icarus.order_history_snapshot(agent_id);
 
-- operate_record
ALTER TABLE cb_icarus.operate_record ADD content text COMMENT '操作记录JSON';
 
-- percentage_detail
ALTER TABLE cb_icarus.percentage_detail ADD merchant_type TINYINT(1) COMMENT '商户类型 1商家 2代理商';
 
-- percentage_record
ALTER TABLE cb_icarus.percentage_record ADD merchant_type TINYINT(1) COMMENT '商户类型 1商家 2代理商';
 
-- shop_daily_bill
ALTER TABLE cb_icarus.shop_daily_bill ADD merchant_type TINYINT(1) COMMENT '商户类型 1商家 2代理商';
 
-- mch_daily_bill
ALTER TABLE cb_icarus.mch_daily_bill ADD merchant_type TINYINT(1) COMMENT '商户类型 1商家 2代理商';
 
-- shop
ALTER TABLE cb_talos.shop ADD agent_id BIGINT COMMENT '代理商ID';
CREATE INDEX idx_agent_id ON cb_talos.shop(agent_id);
 
-- merchant
ALTER TABLE cb_talos.merchant ADD agent_id BIGINT COMMENT '代理商ID';
CREATE INDEX idx_agent_id ON cb_talos.merchant(agent_id);
 
ALTER TABLE cb_icarus.percentage_detail MODIFY COLUMN `state` TINYINT(2) COMMENT '状态：1 未出账 2 已出账';
 
 
CREATE TABLE cb_hera.payment_vietqr_mapping
(
    id                  BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
    trade_no            VARCHAR(64) NOT NULL COMMENT '交易订单号',
    transaction_id            VARCHAR(64) NOT NULL COMMENT 'vietqr transaction id',
    reference_number            VARCHAR(64) NOT NULL COMMENT 'transaction reference number for the refund',
    bank_account            VARCHAR(32) NOT NULL COMMENT 'customers bank account',
    deleted              TINYINT(1) DEFAULT '0' NOT NULL COMMENT '逻辑删除：0 未删除，1 已删除',
    gmt_create           BIGINT NOT NULL COMMENT '创建时间',
    gmt_update           BIGINT NOT NULL COMMENT '更新时间',
 
    PRIMARY KEY (id),
    KEY idx_trade_noe (trade_no),
    KEY idx_transaction_id (transaction_id),
    KEY idx_reference_number (reference_number),
    KEY idx_gmt_create (gmt_create),
    KEY idx_gmt_update (gmt_update)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '支付凭证-vietqr关联表';
 
CREATE TABLE cb_talos.agent (
                                id BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
                                agent_name VARCHAR(128) NOT NULL COMMENT '代理商名称',
                                contract_name VARCHAR(128) NOT NULL COMMENT '联系人名称',
                                nation_code VARCHAR(20) NOT NULL COMMENT '区号',
                                contract_mobile VARCHAR(20) NOT NULL COMMENT '联系人电话',
                                province_code VARCHAR(20) NOT NULL COMMENT '省code',
                                province_name VARCHAR(50) NOT NULL COMMENT '省名称',
                                city_code VARCHAR(20) NOT NULL COMMENT '市code',
                                city_name VARCHAR(50) NOT NULL COMMENT '市名称',
                                address VARCHAR(255) NOT NULL COMMENT '详细地址',
                                state INT NOT NULL COMMENT '状态 1启用 2禁用',
                                open_state INT NOT NULL COMMENT '分成状态 1未开通 2已开通',
                                parent_id BIGINT NOT NULL COMMENT '上级代理商ID',
                                seller_id BIGINT NOT NULL COMMENT '负责人ID',
                                deleted TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0 未删除，1 已删除',
                                gmt_create BIGINT NOT NULL COMMENT '创建时间',
                                gmt_update BIGINT NOT NULL COMMENT '更新时间',
                                PRIMARY KEY (id),
                                KEY idx_contract_mobile (contract_mobile),
                                KEY idx_parent_id (parent_id),
                                KEY idx_seller_id (seller_id),
                                KEY idx_gmt_create (gmt_create),
                                KEY idx_gmt_update (gmt_update)
) ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '代理商';
 
 
 




 
-- ddl
-- oss_icon
INSERT INTO cb_aeacus.oss_icon (id,title, img, url, action, role_id, `order`, type) VALUES (9,'Agent', 'icon-wodekehu', '/agent/list', 'AGENT_MANAGE', 0, 0, 1);
INSERT INTO cb_aeacus.oss_icon (id,title, img, url, action, role_id, `order`, type) VALUES (10,'Agent store', 'icon-kehumendian', '/agent/shops', 'AGENT_STORE', 0, 0, 1);
INSERT INTO cb_aeacus.oss_icon (id,title, img, url, action, role_id, `order`, type) VALUES (11,'Order', 'icon-dingdanguanli', '/orders', 'AGENT_ORDERS', 0, 0, 1);
 
 
INSERT INTO cb_aeacus.oss_group (id,name, type, create_time, update_time, deleted) VALUES (19, 'Agent Management', 1, '2024-07-22 10:37:00', '2024-07-22 10:37:00', 0);
INSERT INTO cb_aeacus.oss_group (id,name, type, create_time, update_time, deleted) VALUES (20, 'Agent Capital', 1,  now(), now(), 0);
 
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190001', 1, 1, null, 'img', 'url', 'View', 19);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190002', 1, 1, null, 'img', 'url', 'New Agent', 19);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190003', 1, 1, null, 'img', 'url', 'Edit', 19);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190004', 1, 1, null, 'img', 'url', 'Cooperate', 19);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190005', 1, 1, null, 'img', 'url', 'Redistribution', 19);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190006', 1, 1, null, 'img', 'url', 'Enable/Disable', 19);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190007', 1, 1, null, 'img', 'url', 'Reset Password', 19);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '190008', 1, 1, null, 'img', 'url', 'Edit Percentage', 19);
 
 
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '200001', 1, 1, null, 'img', 'url', 'Capital Account', 20);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '200002', 1, 1, null, 'img', 'url', 'Sharing Details', 20);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '200003', 1, 1, null, 'img', 'url', 'Bill Management', 20);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id) VALUES (now(), now(), '200004', 1, 1, null, 'img', 'url', 'My Capital', 20);
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (now(), now(), '40010', 1, 1, null, 'img', 'url', 'Price adjustment', 4, '36');
INSERT INTO cb_aeacus.oss_permission (create_time, update_time, code, type, creator, updater, img, url, description, group_id, rely_ids) VALUES (now(), now(), '40011', 1, 1, null, 'img', 'url', 'Agent Store List(pc)', 4, '36');
 
-- 初始化默认代理商
INSERT INTO `cb_talos`.`agent` (`id`,`agent_name`,`contract_name`,`nation_code`,`contract_mobile`,`province_code`,`province_name`,`city_code`,`city_name`,`address`,`state`,`open_state`,`parent_id`,`seller_id`,`deleted`,`gmt_create`,`gmt_update`) VALUES (0,'chargeBolt','chargeBolt','+86','12345678901','00','00','00','00','hangzhou',1,1,-1,128,0,1719455537897,1719455537897);
 
 