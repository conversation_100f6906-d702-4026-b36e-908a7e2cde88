package com.chargebolt.theseus.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.chargebolt.theseus.domain.I18nTextsDO;
import com.chargebolt.theseus.dto.I18nKV;

public interface I18nTextsDAO {

    List<I18nTextsDO> queryList();

    String getText(@Param("langId") Integer langId, @Param("key") String key);

    I18nTextsDO getTextDO(@Param("langId") Integer langId, @Param("key") String key);

    Boolean addText(I18nTextsDO i18nTexts);

    List<I18nTextsDO> getTextByPrefixKey(@Param("langId") Integer langId, @Param("prefixKey") String prefixKey);

    Boolean deleteText(@Param("key") String key, @Param("eventTime") long eventTime,
            @Param("updateTime") long updateTime);

    Boolean updateText(I18nTextsDO i18nTextsDO);

    List<I18nKV> batchGetText(@Param("langId") Integer langId, @Param("keys") List<String> keys);

    /**
     * 批量查询文本DO对象
     * 
     * @param langId 语言ID
     * @param keys   key列表
     * @return 文本DO列表
     */
    List<I18nTextsDO> batchGetTextDO(@Param("langId") Integer langId, @Param("keys") List<String> keys);

    /**
     * 批量插入文本
     * 
     * @param textList 文本列表
     * @return 插入成功的数量
     */
    Integer batchAddText(@Param("textList") List<I18nTextsDO> textList);

    /**
     * 批量更新文本
     * 
     * @param textList 文本列表
     * @return 更新成功的数量
     */
    Integer batchUpdateText(@Param("textList") List<I18nTextsDO> textList);

    /**
     * 批量删除文本
     * 
     * @param keys       需要删除的key列表
     * @param eventTime  事件时间，只有更新时间早于该时间的记录才会被删除
     * @param updateTime 删除时的更新时间
     * @return 删除成功的数量
     */
    Integer batchDeleteText(@Param("keys") List<String> keys,
            @Param("eventTime") long eventTime,
            @Param("updateTime") long updateTime);
}
