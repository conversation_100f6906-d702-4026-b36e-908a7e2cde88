package com.chargebolt.theseus.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.sdk.service.alimt20181012.AsyncClient;
import com.aliyun.sdk.service.alimt20181012.models.GetBatchTranslateRequest;
import com.aliyun.sdk.service.alimt20181012.models.GetBatchTranslateResponse;
import com.aliyun.sdk.service.alimt20181012.models.GetDetectLanguageRequest;
import com.aliyun.sdk.service.alimt20181012.models.GetDetectLanguageResponse;
import com.aliyun.sdk.service.alimt20181012.models.TranslateGeneralRequest;
import com.aliyun.sdk.service.alimt20181012.models.TranslateGeneralResponse;
import com.chargebolt.theseus.dto.TranslationResult;
import com.chargebolt.theseus.service.TranslateApi;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AliTranslateService implements TranslateApi {

    /**
     * 翻译任务线程池
     * 核心线程数为10，最大线程数为20，空闲线程存活时间30秒
     * 使用CallerRunsPolicy避免任务丢失，当线程池满时由调用线程执行
     */
    private final ExecutorService translationExecutor = new ThreadPoolExecutor(
            5, // 核心线程数
            20, // 最大线程数
            30, // 空闲线程存活时间
            TimeUnit.SECONDS, // 时间单位
            new LinkedBlockingQueue<>(200), // 任务队列，增加容量
            new ThreadFactoryBuilder()
                    .setNameFormat("translate-pool-%d")
                    .setDaemon(false)
                    .setPriority(Thread.NORM_PRIORITY)
                    .build(), // 使用 Guava 的 ThreadFactoryBuilder 创建线程工厂
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略，调用者运行，避免任务丢失
    );

    /**
     * 阿里云翻译客户端
     */
    @Resource
    private AsyncClient asyncClient;

    @Value("${aliyun.access-key-id:}")
    private String accessKeyId;

    @Value("${aliyun.access-key-secret:}")
    private String accessKeySecret;

    @Value("${aliyun.region-id:cn-hangzhou}")
    private String regionId;

    @Value("${aliyun.endpoint:mt.aliyuncs.com}")
    private String endpoint;

    /**
     * 检测文本的语言
     */
    @Override
    public String detectLanguage(String text) throws Exception {
        GetDetectLanguageRequest request = GetDetectLanguageRequest.builder()
                .sourceText(text)
                .build();

        CompletableFuture<GetDetectLanguageResponse> future = asyncClient.getDetectLanguage(request);
        GetDetectLanguageResponse response = future.get();

        if (response != null && response.getBody() != null) {
            String lang = response.getBody().getDetectedLanguage();
            return lang;
        }

        return "";
    }

    /**
     * 批量翻译到单一目标语言
     * 
     * @param sourceTexts    源文本映射，键为文本ID，值为待翻译文本
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 翻译结果映射，键与sourceTexts相同，值为翻译后的文本
     * @throws Exception 翻译过程中可能出现的异常
     */
    private Map<String, String> batchTranslate(Map<String, String> sourceTexts, String sourceLanguage,
            String targetLanguage)
            throws Exception {
        if (sourceTexts == null || sourceTexts.isEmpty()) {
            return Collections.emptyMap();
        }

        if (sourceLanguage == null || sourceLanguage.isEmpty() || targetLanguage == null || targetLanguage.isEmpty()) {
            throw new IllegalArgumentException("Source and target languages cannot be empty");
        }

        GetBatchTranslateRequest request = GetBatchTranslateRequest.builder()
                .sourceText(JSON.toJSONString(sourceTexts))
                .sourceLanguage(sourceLanguage)
                .targetLanguage(targetLanguage)
                .formatType("text")
                .apiType("translate_standard")
                .scene("general")
                .build();

        CompletableFuture<GetBatchTranslateResponse> future = asyncClient.getBatchTranslate(request);
        GetBatchTranslateResponse response = future.get();
        log.info("response: {}", JSON.toJSONString(response.getBody()));

        Map<String, String> translationMap = new HashMap<>();
        if (response.getBody() != null) {
            // 增加对翻译结果列表的空值判断，防止阿里云API在业务失败时返回null
            Object translatedListObject = response.getBody().getTranslatedList();
            if (translatedListObject == null) {
                log.warn("阿里云批量翻译返回的TranslatedList为null, response: {}", JSON.toJSONString(response.getBody()));
                return translationMap; // 返回空的map
            }

            // 使用 JSON 库处理翻译结果，避免类型转换问题
            String jsonResult = JSON.toJSONString(translatedListObject);
            JSONArray jsonArray = JSON.parseArray(jsonResult);

            // 再次检查解析后的jsonArray是否为null
            if (jsonArray == null) {
                log.warn("解析后的翻译结果jsonArray为null, jsonResult: {}", jsonResult);
                return translationMap;
            }

            // 遍历翻译结果，构建结果映射
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                if (item.containsKey("index") && item.containsKey("translated")) {
                    String originalKey = item.getString("index");
                    String translated = item.getString("translated");

                    // 直接使用返回的index作为key，因为它就是原始的key
                    if (sourceTexts.containsKey(originalKey)) {
                        translationMap.put(originalKey, translated);
                        log.debug("翻译完成: {} -> {}", originalKey, translated);
                    } else {
                        log.warn("翻译结果中的key不存在于原始数据中: {}", originalKey);
                    }
                }
            }
        }

        return translationMap;
    }

    /**
     * 把 texts 的类型改成 Map<String,String>，key 为文本的索引，value 为文本内容
     */
    @Override
    public TranslationResult translate(Map<String, String> texts, String targetLanguage) throws Exception {
        return translate(texts, targetLanguage, null);
    }

    @Override
    public TranslationResult translate(Map<String, String> texts, String targetLanguage, String sourceLanguage)
            throws Exception {
        if (texts == null || texts.isEmpty() || targetLanguage == null || targetLanguage.isEmpty()) {
            return new TranslationResult(sourceLanguage, Collections.emptyMap());
        }

        // 如果没有提供源语言，则进行语言检测
        if (sourceLanguage == null || sourceLanguage.isEmpty()) {
            long startTime = System.currentTimeMillis();
            String firstText = texts.values().iterator().next();
            sourceLanguage = detectLanguage(firstText);
            log.info("检测到源语言: {}, 耗时: {}ms", sourceLanguage, System.currentTimeMillis() - startTime);
        }

        // 如果源语言与目标语言相同，直接返回原文
        if (sourceLanguage.equals(targetLanguage)) {
            // 创建一个新的映射，键与原始文本相同，值也与原始文本相同
            Map<String, String> sameLanguageMap = new HashMap<>(texts);
            return new TranslationResult(sourceLanguage, sameLanguageMap);
        }

        // 批量翻译
        Map<String, String> translationMap = batchTranslate(texts, sourceLanguage, targetLanguage);
        return new TranslationResult(sourceLanguage, translationMap);
    }

    @Override
    public Map<String, TranslationResult> batchTranslate(Map<String, String> texts, List<String> targetLanguages,
            String sourceLanguage) throws Exception {
        long startTime = System.currentTimeMillis();

        // 如果没有提供源语言，则进行语言检测
        if (sourceLanguage == null || sourceLanguage.isEmpty()) {
            String firstText = texts.values().iterator().next();
            sourceLanguage = detectLanguage(firstText);
            log.info("检测到源语言: {}, 耗时: {}ms", sourceLanguage, System.currentTimeMillis() - startTime);
        }

        // 过滤掉与源语言相同的目标语言
        final String finalSourceLanguage = sourceLanguage;
        List<String> filteredTargetLanguages = new ArrayList<>();
        for (String lang : targetLanguages) {
            if (!lang.equals(finalSourceLanguage)) {
                filteredTargetLanguages.add(lang);
            }
        }

        // 使用串行方式处理翻译，避免嵌套并发导致线程池耗尽
        // 由于调用方已经在使用并发处理，这里改为串行以避免线程池竞争
        Map<String, TranslationResult> results = new ConcurrentHashMap<>();

        for (String targetLang : filteredTargetLanguages) {
            try {
                long langStartTime = System.currentTimeMillis();
                log.info("开始翻译到语言: {}, 文本数量: {}", targetLang, texts.size());

                Map<String, String> translationMap = batchTranslate(texts, finalSourceLanguage, targetLang);
                TranslationResult result = new TranslationResult(finalSourceLanguage, translationMap);
                results.put(targetLang, result);

                log.info("翻译到 {} 完成, 耗时: {}ms", targetLang, System.currentTimeMillis() - langStartTime);
            } catch (Exception e) {
                log.error("翻译到 {} 失败: {}", targetLang, e.getMessage(), e);
                // 翻译失败时，添加空的翻译结果，避免调用方出现空指针异常
                results.put(targetLang, new TranslationResult(finalSourceLanguage, Collections.emptyMap()));
            }
        }

        log.info("所有翻译任务完成，总耗时: {}ms", System.currentTimeMillis() - startTime);
        return results;
    }

    /**
     * 并发批量翻译到多个目标语言（受控并发版本）
     * 当调用方没有使用并发时，可以使用此方法进行并发翻译
     *
     * @param texts 待翻译的文本列表
     * @param targetLanguages 目标语言列表
     * @param sourceLanguage 源语言代码，如果为null则自动检测
     * @return 翻译结果映射，key为目标语言，value为翻译结果
     */
    public Map<String, TranslationResult> batchTranslateConcurrent(Map<String, String> texts, List<String> targetLanguages,
            String sourceLanguage) throws Exception {
        long startTime = System.currentTimeMillis();

        // 如果没有提供源语言，则进行语言检测
        if (sourceLanguage == null || sourceLanguage.isEmpty()) {
            String firstText = texts.values().iterator().next();
            sourceLanguage = detectLanguage(firstText);
            log.info("检测到源语言: {}, 耗时: {}ms", sourceLanguage, System.currentTimeMillis() - startTime);
        }

        // 过滤掉与源语言相同的目标语言
        final String finalSourceLanguage = sourceLanguage;
        List<String> filteredTargetLanguages = new ArrayList<>();
        for (String lang : targetLanguages) {
            if (!lang.equals(finalSourceLanguage)) {
                filteredTargetLanguages.add(lang);
            }
        }

        // 并发翻译到所有目标语言，但限制并发数量
        Map<String, TranslationResult> results = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (String targetLang : filteredTargetLanguages) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    long langStartTime = System.currentTimeMillis();
                    log.info("开始并发翻译到语言: {}, 文本数量: {}", targetLang, texts.size());

                    Map<String, String> translationMap = batchTranslate(texts, finalSourceLanguage, targetLang);
                    TranslationResult result = new TranslationResult(finalSourceLanguage, translationMap);
                    results.put(targetLang, result);

                    log.info("并发翻译到 {} 完成, 耗时: {}ms", targetLang, System.currentTimeMillis() - langStartTime);
                } catch (Exception e) {
                    log.error("并发翻译到 {} 失败: {}", targetLang, e.getMessage(), e);
                    // 翻译失败时，添加空的翻译结果
                    results.put(targetLang, new TranslationResult(finalSourceLanguage, Collections.emptyMap()));
                }
            }, translationExecutor);

            futures.add(future);
        }

        // 等待所有翻译任务完成
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.get(90, TimeUnit.SECONDS); // 增加超时时间到90秒
        } catch (Exception e) {
            log.warn("并发翻译超时，部分翻译可能未完成: {}", e.getMessage());
        }

        log.info("所有并发翻译任务完成，总耗时: {}ms", System.currentTimeMillis() - startTime);
        return results;
    }

    /**
     * 在应用关闭时关闭线程池
     */
    @PreDestroy
    public void destroy() {
        // 关闭线程池
        translationExecutor.shutdown();
        try {
            if (!translationExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                translationExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            translationExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 注意：客户端在 AliyunConfig 中关闭
    }

    @Override
    public String translate(String text, String targetLanguage) {
        try {
            String sourceLanguage = detectLanguage(text);
            TranslateGeneralRequest request = TranslateGeneralRequest.builder()
                    .formatType("text")
                    .scene("general")
                    .sourceText(text)
                    .sourceLanguage(sourceLanguage)
                    .targetLanguage(targetLanguage)
                    .build();
            CompletableFuture<TranslateGeneralResponse> translateGeneral = asyncClient.translateGeneral(request);
            TranslateGeneralResponse response = translateGeneral.get();
            if (response != null && response.getBody() != null && response.getBody().getData() != null) {
                return response.getBody().getData().getTranslated();
            }
        } catch (Exception e) {
            log.error("翻译失败: {}", e.getMessage());
        }
        return null;
    }

}
