<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.theseus.dao.ConfigDAO">

    <select id="getConfigByKey" resultType="com.chargebolt.theseus.domain.ConfigDO"> SELECT * FROM
        cb_theseus.config WHERE `key`=#{key} AND status = 1 </select>

    <update id="update"> UPDATE cb_theseus.`config` SET `value` = #{value}, `status` = #{status},
        `update_time` = now() WHERE `key` = #{key}; </update>

</mapper>